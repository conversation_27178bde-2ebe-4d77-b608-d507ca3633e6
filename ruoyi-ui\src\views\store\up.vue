<template>
  <div class="up-container">
    <!-- 顶部标签页 -->
    <div class="materials-tabs">
      <div class="tab-buttons">
        <el-button
          :type="materialTab === 'sucai' ? 'primary' : 'default'"
          @click="switchTab('sucai')"
          class="tab-button">
          素材上传
        </el-button>
        <el-button
          :type="materialTab === 'bgm' ? 'primary' : 'default'"
          @click="switchTab('bgm')"
          class="tab-button">
          BGM上传
        </el-button>

        <!-- BGM免费下载按钮 -->
        <el-button
          v-if="materialTab === 'bgm'"
          type="success"
          size="small"
          icon="el-icon-download"
          @click="openBGMDownloadSite"
          class="bgm-download-button">
          免费在线下载
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="materials-main">
      <!-- 左侧文件夹树 -->
      <div class="folder-sidebar">
        <div class="folder-header">
          <div class="folder-actions">
            <el-button
              type="primary"
              size="small"
              icon="el-icon-upload2"
              @click="showUploadDialog"
              style="height: 32px !important; width: 100% !important; max-width: 120px !important;">
              上传
            </el-button>
            <el-button
              type="success"
              size="small"
              icon="el-icon-folder-add"
              @click="showCreateFolderDialog"
              style="height: 32px !important; width: 100% !important; max-width: 120px !important;">
              新建文件夹
            </el-button>
          </div>
        </div>

        <div class="folder-list">
          <!-- 只有OSS配置成功后才显示文件夹 -->
          <div v-if="ossInitialized">
            <div
              v-for="folder in currentFolderTree"
              :key="folder.id"
              class="folder-item"
              :class="{ active: selectedFolder === folder.id }"
              @click="selectFolder(folder)"
              @contextmenu.prevent="showFolderContextMenu($event, folder)">
              <i class="folder-icon">📁</i>
              <span class="folder-name">{{ folder.name }}</span>
              <span class="folder-count">{{ folder.count }}</span>
            </div>
          </div>


          <!-- OSS未配置时的提示 -->
          <div v-else class="no-oss-tip">
            <div class="tip-icon">⚙️</div>
            <div class="tip-text">请先配置OSS存储</div>
            <el-button type="primary" size="small" @click="openOSSConfig">配置OSS</el-button>
          </div>
        </div>

        <!-- 文件夹右键菜单 -->
        <div
          v-show="contextMenuVisible"
          class="context-menu"
          :style="{ left: contextMenuX + 'px', top: contextMenuY + 'px' }"
          @click.stop>
          <div class="menu-item" @click="renameFolderAction">
            <i class="el-icon-edit"></i>
            重命名文件夹
          </div>
          <div class="menu-item" @click="deleteFolderAction" v-if="contextMenuFolder && (contextMenuFolder.name || contextMenuFolder) !== '总'">
            <i class="el-icon-delete"></i>
            删除文件夹
          </div>
        </div>
      </div>

      <!-- 右侧文件区域 -->
      <div class="files-area">
        <!-- OSS配置成功后显示文件管理界面 -->
        <div v-if="ossInitialized" class="files-toolbar">
          <div class="toolbar-left">
            <el-checkbox v-model="selectAll" @change="handleSelectAll">全选</el-checkbox>
            <span class="file-actions">
              <span class="action-text" @click="handlePreview" :disabled="selectedFiles.length !== 1">预览</span>
              <span class="action-text" @click="handleRename">重命名</span>
              <span class="action-text" @click="handleDelete">删除</span>
            </span>
          </div>
          <div class="toolbar-right">
            <span class="file-count">共 {{ filteredMaterialList.length }} 项</span>
            <div class="pagination-info">
              <span>{{ currentPage }}</span>
              <span>/</span>
              <span>{{ totalPages }}</span>
              <span>页</span>
            </div>
          </div>
        </div>

        <!-- 文件列表 -->
        <div class="files-content">
          <!-- OSS未配置时的提示 -->
          <div v-if="!ossInitialized" class="no-oss-files-tip">
            <div class="tip-content">
              <div class="tip-icon">☁️</div>
              <div class="tip-title">请先配置OSS存储</div>
              <div class="tip-description">配置OSS后即可开始上传和管理文件</div>
              <el-button type="primary" @click="openOSSConfig">配置OSS存储</el-button>
            </div>
          </div>

          <!-- OSS已配置时显示文件列表 -->
          <div v-else-if="filteredMaterialList.length === 0" class="empty-state">
            <div class="empty-icon">📁</div>
            <div class="empty-text">暂无文件</div>
          </div>
          <div v-else>
            <!-- 文件操作工具栏 -->
            <div class="file-toolbar">
              <div class="toolbar-left">
                <el-checkbox v-model="selectAll" @change="handleSelectAll">全选</el-checkbox>
                <span class="selected-count" v-if="selectedFiles.length > 0">
                  已选择 {{ selectedFiles.length }} 个文件
                </span>
              </div>
              <div class="toolbar-right">
                <el-button
                  v-if="selectedFiles.length > 0"
                  type="danger"
                  size="small"
                  @click="handleDelete">
                  删除选中
                </el-button>
              </div>
            </div>

            <div class="file-grid">
            <div
              v-for="file in paginatedMaterials"
              :key="file.id"
              class="file-card"
              :class="{
                selected: selectedFiles.includes(file.id),
                hovered: file.isHovered,
                enlarged: file.isPlaying,
                'scale-enlarged': file.isScaled
              }"
              :style="file.isScaled && file.displayWidth && file.displayHeight ? {
                width: file.displayWidth + 'px',
                height: file.displayHeight + 'px'
              } : {}"
              @click="toggleFileSelection(file.id)"
              @dblclick="handleFileDoubleClick(file)">

              <!-- 文件选择框 -->
              <div class="file-checkbox">
                <el-checkbox :value="selectedFiles.includes(file.id)" @change="toggleFileSelection(file.id)"></el-checkbox>
              </div>

              <!-- 文件缩略图 -->
              <div class="file-thumbnail">
                <!-- 视频缩略图 -->
                <div v-if="file.type === 'video'" class="video-thumbnail"
                     :class="{ playing: file.isPlaying }"
                     @mouseenter="onVideoMouseEnter(file)"
                     @mouseleave="onVideoMouseLeave(file)"
                     @mousemove="onVideoMouseMove(file)"
                     @click.stop="toggleVideoPlayWithScale(file)">
                  <video
                    :ref="`video-${file.id}`"
                    :src="getFileUrl(file)"
                    preload="metadata"
                    :muted="!file.isPlaying"
                    class="thumbnail-video"
                    @loadedmetadata="onVideoLoaded"
                    @ended="onVideoEnded(file)"
                    @pause="onVideoPaused(file)"
                    @play="onVideoPlayed(file)"
                    @timeupdate="onVideoTimeUpdate(file)"
                    @loadeddata="onVideoLoadedData(file)">
                  </video>

                  <!-- 小视频的简洁播放按钮 -->
                  <div v-if="!file.isScaled" class="simple-play-overlay">
                    <div class="play-button">
                      <i :class="file.isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'"></i>
                    </div>
                  </div>

                  <!-- 放大视频的进度条 - 悬浮在视频底部 -->
                  <div v-if="file.isScaled" class="video-controls-overlay">
                    <div class="progress-container" @click.stop="seekVideo($event, file)">
                      <div class="progress-track">
                        <div class="progress-fill" :style="{ width: getVideoProgress(file) + '%' }"></div>
                        <div class="progress-thumb" :style="{ left: getVideoProgress(file) + '%' }"></div>
                      </div>
                    </div>
                    <div class="controls-bottom">
                      <div class="play-pause-btn" @click.stop="toggleVideoPlayWithScale(file)">
                        <i :class="file.isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'"></i>
                      </div>
                      <div class="time-display">
                        <span class="current-time">{{ formatTime(getCurrentTime(file)) }}</span>
                        <span class="separator">/</span>
                        <span class="total-time">{{ formatTime(getDuration(file)) }}</span>
                      </div>
                    </div>
                  </div>

                  <div class="duration-badge" v-if="file.duration && !file.isScaled">{{ file.duration }}</div>
                </div>

                <!-- 音频缩略图 -->
                <div v-else-if="file.type === 'audio'" class="audio-thumbnail"
                     :class="{ playing: file.isPlaying }"
                     @mouseenter="onAudioMouseEnter(file)"
                     @mouseleave="onAudioMouseLeave(file)"
                     @mousemove="onAudioMouseMove(file)">
                  <audio
                    :ref="`audio-${file.id}`"
                    :src="getFileUrl(file)"
                    preload="metadata"
                    @ended="onAudioEnded(file)"
                    @pause="onAudioPaused(file)"
                    @play="onAudioPlayed(file)">
                  </audio>
                  <div class="audio-icon">🎵</div>
                  <div class="audio-waveform">
                    <div class="wave-bar" v-for="i in 12" :key="i"></div>
                  </div>
                  <div class="play-overlay-audio"
                       @click.stop="toggleAudioPlay(file)"
                       :class="{
                         playing: file.isPlaying,
                         visible: file.showControls || !file.isPlaying,
                         hidden: file.isPlaying && !file.showControls
                       }">
                    <i :class="file.isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'"></i>
                  </div>
                  <div class="duration-badge" v-if="file.duration">{{ file.duration }}</div>
                </div>

                <!-- 图片缩略图 -->
                <div v-else-if="file.type === 'image'" class="image-thumbnail"
                     @click.stop="previewFile(file)"
                     @mouseenter="onImageMouseEnter(file)"
                     @mouseleave="onImageMouseLeave(file)">
                  <img
                    :src="getFileUrl(file)"
                    :alt="file.name"
                    class="thumbnail-image"
                    @error="onImageError">
                  <div class="image-overlay">
                    <i class="el-icon-zoom-in"></i>
                  </div>
                </div>

                <!-- 其他文件类型 -->
                <div v-else class="file-icon-thumbnail">
                  <i :class="getSimpleFileIcon(file.type)"></i>
                </div>
              </div>

              <!-- 文件信息 -->
              <div class="file-info">
                <div class="file-name" :title="file.name">{{ file.name }}</div>
                <div class="file-meta">
                  <span class="file-size">{{ formatFileSize(file.size) }}</span>
                  <span class="file-time">{{ file.uploadTime }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- OSS配置对话框 -->
    <el-dialog
      title="设置存储"
      :visible.sync="ossConfigVisible"
      width="600px"
      :close-on-click-modal="false"
      class="oss-config-dialog"
    >
      <div class="oss-config-content">
        <!-- 存储方式选择 -->
        <div class="storage-type-section">
          <div class="section-label">存储方式</div>
          <div class="storage-options">
            <el-radio v-model="ossConfig.storageType" label="oss" class="storage-radio">
              <span class="radio-text">阿里云OSS</span>
            </el-radio>
          </div>
          <div class="storage-description">
            切换阿里云OSS后，素材库需要重新上传至阿里云OSS
          </div>
        </div>

        <!-- OSS配置表单 -->
        <div class="oss-form-section">
          <div class="form-row">
            <div class="form-label required">存储空间名称</div>
            <el-input
              v-model="ossConfig.bucket"
              placeholder="jkhghfgddedb"
              class="form-input"
            />
          </div>

          <div class="form-row">
            <div class="form-label required">ACCESS_KEY</div>
            <el-input
              v-model="ossConfig.accessKeyId"
              placeholder="LTAI5tSgfoZwykU9M1qvThgq"
              class="form-input"
            />
          </div>

          <div class="form-row">
            <div class="form-label required">SECRET_KEY</div>
            <el-input
              v-model="ossConfig.accessKeySecret"
              placeholder="******************************"
              class="form-input"
              show-password
            />
          </div>

          <div class="form-row">
            <div class="form-label required">空间域名</div>
            <el-input
              v-model="ossConfig.endpoint"
              placeholder="https://jkhghfgddedb.oss-cn-shanghai.aliyuncs.com"
              class="form-input"
            />
            <div class="form-hint">
              请补全http://或https://，例如https://static.cloud.com
            </div>
          </div>

          <!-- 状态选择 -->
          <div class="form-row">
            <div class="form-label">状态</div>
            <div class="status-options">
              <el-radio v-model="ossConfig.status" label="disabled" class="status-radio">
                <span class="radio-text">关闭</span>
              </el-radio>
              <el-radio v-model="ossConfig.status" label="enabled" class="status-radio">
                <span class="radio-text">开启</span>
              </el-radio>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="ossConfigVisible = false" class="cancel-btn">取消</el-button>
        <el-button type="primary" @click="saveOSSConfig" :loading="testingOSS" class="confirm-btn">
          {{ testingOSS ? '测试连接中...' : '确定' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 新建文件夹对话框 -->
    <el-dialog
      title="新建文件夹"
      :visible.sync="createFolderVisible"
      width="400px"
      :close-on-click-modal="false">
      <el-form :model="createFolderForm" label-width="80px">
        <el-form-item label="文件夹名">
          <el-input
            v-model="createFolderForm.name"
            placeholder="请输入文件夹名称"
            @keyup.enter.native="createFolder">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="createFolderVisible = false">取消</el-button>
        <el-button type="primary" @click="createFolder">确定</el-button>
      </div>
    </el-dialog>

    <!-- 文件预览对话框 -->
    <el-dialog
      title="文件预览"
      :visible.sync="previewVisible"
      width="80%"
      :close-on-click-modal="false"
      class="preview-dialog"
      @close="stopAllMedia">
      <div class="preview-content" v-if="previewFile">
        <div class="preview-header">
          <h3>{{ previewFile.name }}</h3>
          <div class="file-info">
            <span>大小: {{ formatFileSize(previewFile.size) }}</span>
            <span>上传时间: {{ previewFile.uploadTime }}</span>
          </div>
        </div>

        <!-- 视频预览 -->
        <div v-if="currentPreviewFile && currentPreviewFile.type === 'video'" class="video-preview">
          <video
            ref="previewVideo"
            :src="currentPreviewFile.url"
            controls
            preload="metadata"
            style="width: 100%; max-height: 500px;">
            您的浏览器不支持视频播放
          </video>
        </div>

        <!-- 音频预览 -->
        <div v-else-if="currentPreviewFile && currentPreviewFile.type === 'audio'" class="audio-preview">
          <div class="audio-player">
            <audio
              ref="previewAudio"
              :src="currentPreviewFile.url"
              controls
              preload="metadata"
              style="width: 100%;">
              您的浏览器不支持音频播放
            </audio>
          </div>
          <div class="audio-info">
            <div class="audio-icon">🎵</div>
            <div class="audio-details">
              <p><strong>{{ currentPreviewFile.name }}</strong></p>
              <p v-if="currentPreviewFile.duration">时长: {{ currentPreviewFile.duration }}</p>
              <p v-if="currentPreviewFile.bitrate">比特率: {{ currentPreviewFile.bitrate }}</p>
            </div>
          </div>
        </div>

        <!-- 图片预览 -->
        <div v-else-if="currentPreviewFile && currentPreviewFile.type === 'image'" class="image-preview">
          <img
            :src="currentPreviewFile.url"
            :alt="currentPreviewFile.name"
            style="max-width: 100%; max-height: 500px;">
        </div>

        <!-- 不支持预览的文件 -->
        <div v-else class="unsupported-preview">
          <div class="unsupported-icon">📄</div>
          <p>此文件类型不支持预览</p>
          <el-button type="primary" @click="downloadFile(previewFile)">下载文件</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 上传对话框 -->
    <el-dialog :title="uploadDialogTitle" :visible.sync="uploadDialogVisible" width="600px">
      <div class="upload-content">
        <!-- 存储方式选择 -->
        <div class="storage-selector" style="margin-bottom: 20px;">
          <span style="margin-right: 10px;">存储方式：</span>
          <el-radio-group v-model="storageType" size="small">
            <el-radio label="local">本地存储</el-radio>
            <el-radio label="oss">阿里云OSS</el-radio>
          </el-radio-group>
          <el-button
            v-if="storageType === 'oss'"
            type="text"
            size="small"
            @click="openOSSConfig"
            style="margin-left: 10px;"
          >
            <i class="el-icon-setting"></i> 配置OSS
          </el-button>
          <span
            v-if="storageType === 'oss'"
            :class="ossInitialized ? 'text-success' : 'text-danger'"
            style="margin-left: 10px; font-size: 12px;"
          >
            {{ ossInitialized ? '✓ 已配置' : '✗ 未配置' }}
          </span>
        </div>

        <el-upload
          class="upload-dragger"
          ref="upload"
          action="#"
          :multiple="true"
          :file-list="fileList"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :on-progress="handleUploadProgress"
          :on-remove="handleRemove"
          :on-change="handleFileChange"
          :auto-upload="false"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">
            {{ uploadTipText }}
          </div>
        </el-upload>

        <!-- 上传进度显示 -->
        <div v-if="uploading && Object.keys(uploadProgress).length > 0" class="upload-progress" style="margin-top: 20px;">
          <h4>上传进度</h4>
          <div v-for="(progress, fileName) in uploadProgress" :key="fileName" class="progress-item">
            <div class="progress-info">
              <span class="file-name">{{ fileName }}</span>
              <span class="progress-text">{{ progress }}%</span>
            </div>
            <el-progress :percentage="progress" :show-text="false"></el-progress>
          </div>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="uploadDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitUpload" :loading="uploading">
          {{ uploading ? '上传中...' : '开始上传' }}
        </el-button>
      </div>
    </el-dialog>



    </div>
  </div>
</template>

<script>
import { initOSSClient, uploadFilesToOSS, getOSSFileList, deleteFileFromOSS, getOSSClient } from '@/utils/ossUpload'
import { getToken } from '@/utils/auth'
import store from '@/store'

export default {
  name: 'StorerUp',
  data() {
    return {
      // 界面控制
      materialTab: 'sucai', // sucai, bgm
      selectedFolder: 1,
      selectAll: false,
      selectedFiles: [],
      currentPage: 1,
      pageSize: 10,

      // 分别为BGM和素材维护文件夹结构
      bgmFolderTree: [],
      sucaiFolderTree: [],

      // 上传相关
      uploadDialogVisible: false,
      uploading: false,
      fileList: [],
      uploadForm: {},
      uploadProgress: {}, // 上传进度
      storageType: 'oss', // 存储方式: local | oss

      // OSS配置相关
      ossConfigVisible: false,
      testingOSS: false,
      ossConfig: {
        storageType: 'oss', // 存储方式
        bucket: '', // 存储空间名称
        accessKeyId: '', // ACCESS_KEY
        accessKeySecret: '', // SECRET_KEY
        endpoint: '', // 空间域名
        status: 'enabled' // 状态：enabled/disabled
      },
      ossInitialized: false, // OSS是否已初始化

      // 文件夹管理
      currentFolder: '总', // 当前选中的文件夹
      folderDialogVisible: false, // 文件夹管理对话框
      newFolderName: '', // 新文件夹名称
      renameFolderName: '', // 重命名文件夹名称
      selectedFolder: '', // 选中的文件夹

      // 右键菜单
      contextMenuVisible: false,
      contextMenuX: 0,
      contextMenuY: 0,
      contextMenuFolder: null,

      // 新建文件夹
      createFolderVisible: false,
      createFolderForm: {
        name: ''
      },

      // 文件预览
      previewVisible: false,
      currentPreviewFile: null,

      // 控制按钮隐藏定时器
      controlTimers: {},



      // BGM文件数据 (abgm/admin/总/ 文件夹) - 添加测试数据
      bgmList: [
        {
          id: 'bgm1',
          name: '测试音频.mp3',
          type: 'audio',
          size: 512000,
          uploadTime: '2024-01-01 12:00:00',
          duration: '02:30',
          url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
        }
      ],

      // 素材文件数据 (asucai/admin/总/ 文件夹) - 添加测试数据
      sucaiList: [
        {
          id: 'sucai1',
          name: '测试视频.mp4',
          type: 'video',
          size: 1024000,
          uploadTime: '2024-01-01 12:00:00',
          duration: '00:30',
          url: 'https://www.w3schools.com/html/mov_bbb.mp4'
        },
        {
          id: 'sucai2',
          name: '测试图片.jpg',
          type: 'image',
          size: 256000,
          uploadTime: '2024-01-01 12:00:00',
          url: 'https://picsum.photos/400/300'
        },
        {
          id: 'sucai3',
          name: '测试音频.mp3',
          type: 'audio',
          size: 512000,
          uploadTime: '2024-01-01 12:00:00',
          duration: '01:45',
          url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
        }
      ]
    }
  },
  computed: {
    // 根据当前标签页返回对应的文件列表
    currentMaterialList() {
      switch (this.materialTab) {
        case 'bgm':
          return this.bgmList
        case 'sucai':
          return this.sucaiList
        default:
          return this.sucaiList
      }
    },

    // 根据当前标签页返回对应的文件夹列表
    currentFolderTree() {
      switch (this.materialTab) {
        case 'bgm':
          return this.bgmFolderTree
        case 'sucai':
          return this.sucaiFolderTree
        default:
          return this.sucaiFolderTree
      }
    },

    filteredMaterialList() {
      let list = this.currentMaterialList

      // 按文件夹过滤（简化版本）
      if (this.selectedFolder !== 1) {
        // 这里可以根据实际需求添加过滤逻辑
        // 目前显示所有文件
      }

      return list
    },

    paginatedMaterials() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredMaterialList.slice(start, end)
    },

    totalPages() {
      return Math.ceil(this.filteredMaterialList.length / this.pageSize)
    },

    // 上传对话框相关计算属性
    uploadDialogTitle() {
      switch (this.materialTab) {
        case 'bgm':
          return '上传BGM文件'
        case 'sucai':
          return '上传素材文件'
        default:
          return '上传文件'
      }
    },

    uploadTipText() {
      switch (this.materialTab) {
        case 'bgm':
          return '支持 MP3、WAV、FLAC、AAC、M4A、OGG、WMA 等音频格式，单个文件不超过100MB'
        case 'sucai':
          return '支持各种视频、音频、图片格式，单个文件不超过500MB'
        default:
          return '请选择要上传的文件'
      }
    },


  },
  async created() {
    // 先加载OSS配置
    this.loadOSSConfig()

    // 如果OSS已配置，则加载文件列表
    if (this.ossInitialized) {
      await this.loadMaterialList()
    }
  },
  methods: {
    // BGM免费下载
    openBGMDownloadSite() {
      window.open('https://www.buguyy.top/', '_blank')
      this.$message.success('正在打开免费BGM下载网站...')
    },

    // 基础方法
    async loadMaterialList() {
      console.log('加载素材列表')

      // 如果OSS已初始化，从OSS加载文件列表
      if (this.ossInitialized) {
        await this.loadFilesFromOSS()
      } else {
        console.log('OSS未初始化，跳过文件加载')
      }

      // 更新文件夹计数
      this.updateCurrentTabFolderCounts()
    },

    // 从OSS加载文件列表
    async loadFilesFromOSS() {
      try {
        const client = getOSSClient()
        const user = this.getCurrentUser()
        const currentFolder = this.getCurrentFolder()

        // 清空现有列表
        this.bgmList = []
        this.sucaiList = []

        // 加载BGM文件
        await this.loadFilesFromFolder('abgm', user, currentFolder, 'bgm')

        // 加载素材文件
        await this.loadFilesFromFolder('asucai', user, currentFolder, 'sucai')

        console.log(`从OSS加载文件完成 - BGM: ${this.bgmList.length}个, 素材: ${this.sucaiList.length}个`)

      } catch (error) {
        console.error('从OSS加载文件列表失败:', error)
      }
    },

    // 从指定文件夹加载文件
    async loadFilesFromFolder(baseFolder, user, folder, listType) {
      try {
        const client = getOSSClient()
        const prefix = `${baseFolder}/${user}/${folder}/`

        const result = await client.list({
          prefix: prefix,
          'max-keys': 1000
        })

        if (result.objects) {
          const files = result.objects
            .filter(obj => !obj.name.endsWith('.keep') && !obj.name.endsWith('/'))
            .map((obj, index) => {
              const fileName = obj.name.split('/').pop()
              const fileExtension = fileName.toLowerCase().split('.').pop()

              // 判断文件类型
              const audioExts = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']
              const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v']

              let fileType = 'unknown'
              if (audioExts.includes(fileExtension)) {
                fileType = 'audio'
              } else if (videoExts.includes(fileExtension)) {
                fileType = 'video'
              }

              return {
                id: Date.now() + Math.random() + index,
                name: fileName,
                type: fileType,
                size: obj.size,
                uploadTime: new Date(obj.lastModified).toLocaleString().slice(0, 16),
                duration: fileType === 'video' ? '00:02:30' : '00:03:45', // 默认值，实际应该从文件元数据获取
                resolution: fileType === 'video' ? '1920x1080' : undefined,
                bitrate: fileType === 'audio' ? '128kbps' : undefined,
                url: `https://${this.ossConfig.bucket}.${this.ossConfig.endpoint.replace('https://', '').replace(this.ossConfig.bucket + '.', '')}/${obj.name}`,
                ossFileName: obj.name,
                folder: `${baseFolder}/${user}/${folder}`
              }
            })

          // 添加到对应的列表
          if (listType === 'bgm') {
            this.bgmList.push(...files)
          } else if (listType === 'sucai') {
            this.sucaiList.push(...files)
          }
        }

      } catch (error) {
        console.error(`从文件夹 ${baseFolder}/${user}/${folder} 加载文件失败:`, error)
      }
    },

    // OSS配置相关方法
    loadOSSConfig() {
      // 从localStorage加载OSS配置
      const savedConfig = localStorage.getItem('ossConfig')
      if (savedConfig) {
        try {
          this.ossConfig = { ...this.ossConfig, ...JSON.parse(savedConfig) }
          if (this.ossConfig.accessKeyId && this.ossConfig.accessKeySecret) {
            this.initializeOSS()
          }
        } catch (error) {
          console.error('加载OSS配置失败:', error)
        }
      }
    },

    async saveOSSConfig() {
      // 验证必填字段
      if (!this.ossConfig.bucket) {
        this.$message.error('请输入存储空间名称')
        return
      }
      if (!this.ossConfig.accessKeyId) {
        this.$message.error('请输入ACCESS_KEY')
        return
      }
      if (!this.ossConfig.accessKeySecret) {
        this.$message.error('请输入SECRET_KEY')
        return
      }
      if (!this.ossConfig.endpoint) {
        this.$message.error('请输入空间域名')
        return
      }

      // 验证域名格式
      if (!this.ossConfig.endpoint.startsWith('http://') && !this.ossConfig.endpoint.startsWith('https://')) {
        this.$message.error('空间域名必须以http://或https://开头')
        return
      }

      // 验证是否为标准的OSS域名格式
      try {
        const url = new URL(this.ossConfig.endpoint)
        const hostname = url.hostname
        const match = hostname.match(/^([^.]+)\.oss-([^.]+)\.aliyuncs\.com$/)

        if (!match) {
          this.$message.error('请输入标准的OSS域名格式，如：https://bucket.oss-region.aliyuncs.com')
          return
        }

        const [, bucket, region] = match
        if (bucket !== this.ossConfig.bucket) {
          this.$message.error(`域名中的存储桶名称(${bucket})与配置的存储桶名称(${this.ossConfig.bucket})不匹配`)
          return
        }
      } catch (error) {
        this.$message.error('域名格式不正确')
        return
      }

      this.testingOSS = true

      try {
        // 直接使用配置初始化OSS客户端
        await this.initializeOSS()

        // 保存配置到localStorage
        localStorage.setItem('ossConfig', JSON.stringify(this.ossConfig))

        this.ossConfigVisible = false
        this.$message.success('OSS配置保存成功！')

      } catch (error) {
        console.error('OSS配置测试失败:', error)
        this.$message.error(`OSS配置失败: ${error.message}`)
      } finally {
        this.testingOSS = false
      }
    },

    async initializeOSS() {
      try {
        const client = initOSSClient(this.ossConfig)
        this.ossInitialized = true

        // OSS初始化成功后，获取用户的文件夹列表
        await this.loadUserFoldersFromOSS()

        console.log('OSS客户端初始化成功')
        return client
      } catch (error) {
        this.ossInitialized = false
        console.error('OSS客户端初始化失败:', error)
        throw error
      }
    },

    openOSSConfig() {
      this.ossConfigVisible = true
    },

    // 显示新建文件夹对话框
    showCreateFolderDialog() {
      this.createFolderForm.name = ''
      this.createFolderVisible = true
    },

    // 创建文件夹
    async createFolder() {
      console.log('🚀 开始创建文件夹')

      if (!this.createFolderForm.name || this.createFolderForm.name.trim() === '') {
        this.$message.error('文件夹名称不能为空')
        return
      }

      const folderName = this.createFolderForm.name.trim()
      console.log('📁 文件夹名称:', folderName)
      console.log('📋 当前标签页:', this.materialTab)

      // 检查当前标签页的文件夹是否已存在
      const currentFolders = this.currentFolderTree
      console.log('📂 当前文件夹列表:', currentFolders.map(f => f.name))

      if (currentFolders.some(folder => folder.name === folderName)) {
        this.$message.error(`${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹已存在`)
        return
      }

      try {
        console.log('✅ 开始创建本地文件夹记录')

        // 先创建本地文件夹记录
        const newFolder = {
          id: Date.now() + Math.random(), // 使用时间戳避免ID冲突
          name: folderName,
          count: 0
        }

        if (this.materialTab === 'bgm') {
          this.bgmFolderTree.push(newFolder)
          console.log('📁 已添加到BGM文件夹树')
        } else {
          this.sucaiFolderTree.push(newFolder)
          console.log('📁 已添加到素材文件夹树')
        }

        console.log('✅ 本地文件夹创建成功')
        this.createFolderVisible = false
        this.$message.success(`${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹创建成功`)

        // 异步处理OSS创建，不阻塞UI
        if (this.storageType === 'oss' && this.ossInitialized) {
          this.createOSSFolderAsync(folderName)
        }

      } catch (error) {
        console.error('❌ 创建文件夹失败:', error)
        this.$message.error(`创建失败: ${error.message}`)
      }
    },

    // 异步创建OSS文件夹，不阻塞UI
    async createOSSFolderAsync(folderName) {
      try {
        console.log('🌐 开始异步创建OSS文件夹:', folderName)
        await this.createOSSFolderForCurrentTab(folderName)
        console.log('✅ OSS文件夹创建成功')
      } catch (ossError) {
        console.warn('⚠️ OSS文件夹创建失败:', ossError.message)
      }
    },

    // OSS文件夹创建（旧方法，为两种类型都创建）
    async createOSSFolder(folderName) {
      try {
        const client = getOSSClient()
        const user = this.getCurrentUser()

        // 为两种类型创建空文件夹（通过上传空文件实现）
        const baseFolders = ['abgm', 'asucai']

        for (const baseFolder of baseFolders) {
          const folderPath = `${baseFolder}/${user}/${folderName}/.keep`

          // 上传一个空文件来创建文件夹结构
          await client.put(folderPath, Buffer.from(''))
          console.log(`OSS文件夹创建: ${folderPath}`)
        }

        return true
      } catch (error) {
        console.error('OSS文件夹创建失败:', error)
        throw error
      }
    },

    // 只为当前标签页创建OSS文件夹
    async createOSSFolderForCurrentTab(folderName) {
      try {
        console.log(`开始创建OSS文件夹: ${folderName} (${this.materialTab})`)

        if (!folderName || !folderName.trim()) {
          throw new Error('文件夹名称不能为空')
        }

        const client = getOSSClient()
        if (!client) {
          throw new Error('OSS客户端未初始化')
        }

        const user = this.getCurrentUser()
        if (!user) {
          throw new Error('无法获取当前用户信息')
        }

        // 根据当前标签页确定基础文件夹
        const baseFolder = this.materialTab === 'bgm' ? 'abgm' : 'asucai'
        const folderPath = `${baseFolder}/${user}/${folderName}/.keep`

        console.log(`OSS文件夹路径: ${folderPath}`)

        // 上传一个空文件来创建文件夹结构
        await client.put(folderPath, Buffer.from(''))
        console.log(`OSS文件夹创建成功 (${this.materialTab}): ${folderPath}`)

        return true
      } catch (error) {
        console.error(`OSS文件夹创建失败 (${this.materialTab}):`, error)
        throw new Error(`OSS文件夹创建失败: ${error.message}`)
      }
    },

    // 处理文件双击事件
    handleFileDoubleClick(file) {
      // 只有视频、音频、图片类型才支持预览
      if (['video', 'audio', 'image'].includes(file.type)) {
        this.previewFile(file)
      }
    },

    // 切换视频播放/暂停
    toggleVideoPlay(file) {
      const videoRef = this.$refs[`video-${file.id}`]
      if (videoRef && videoRef.length > 0) {
        const video = videoRef[0]

        if (video.paused) {
          // 暂停所有其他视频
          this.pauseAllVideos()

          // 取消静音并播放当前视频
          video.muted = false
          video.play()
          this.$set(file, 'isPlaying', true)
          this.$set(file, 'showControls', true)

          // 开始3秒隐藏定时器
          this.startControlTimer(file)
        } else {
          // 暂停当前视频并静音
          video.pause()
          video.muted = true
          this.$set(file, 'isPlaying', false)
          this.$set(file, 'showControls', true)

          // 清除隐藏定时器
          this.clearControlTimer(file.id)
        }
      }
    },

    // 切换视频播放/暂停并缩放
    toggleVideoPlayWithScale(file) {
      const videoRef = this.$refs[`video-${file.id}`]
      if (videoRef && videoRef.length > 0) {
        const video = videoRef[0]

        if (video.paused) {
          // 暂停所有其他视频并取消缩放
          this.pauseAllVideosAndResetScale()

          // 等待视频元数据加载完成
          const handleLoadedMetadata = () => {
            // 获取视频的真实尺寸
            const videoWidth = video.videoWidth
            const videoHeight = video.videoHeight

            if (videoWidth && videoHeight) {
              // 计算合适的显示尺寸（最大宽度800px，最大高度600px）
              const maxWidth = Math.min(800, window.innerWidth * 0.8)
              const maxHeight = Math.min(600, window.innerHeight * 0.8)

              const aspectRatio = videoWidth / videoHeight
              let displayWidth, displayHeight

              if (aspectRatio > maxWidth / maxHeight) {
                // 视频比较宽，以宽度为准
                displayWidth = maxWidth
                displayHeight = maxWidth / aspectRatio
              } else {
                // 视频比较高，以高度为准
                displayHeight = maxHeight
                displayWidth = maxHeight * aspectRatio
              }

              // 设置视频的显示尺寸
              this.$set(file, 'displayWidth', Math.round(displayWidth))
              this.$set(file, 'displayHeight', Math.round(displayHeight))

              console.log(`视频 ${file.name} 真实尺寸: ${videoWidth}x${videoHeight}, 显示尺寸: ${displayWidth}x${displayHeight}`)
            }

            // 移除事件监听器
            video.removeEventListener('loadedmetadata', handleLoadedMetadata)
          }

          // 如果元数据已经加载，直接处理；否则等待加载
          if (video.readyState >= 1) {
            handleLoadedMetadata()
          } else {
            video.addEventListener('loadedmetadata', handleLoadedMetadata)
          }

          // 播放当前视频并放大
          video.muted = false
          video.play()
          this.$set(file, 'isPlaying', true)
          this.$set(file, 'isScaled', true) // 设置放大状态
          this.$set(file, 'showControls', true)

          console.log(`视频 ${file.name} 开始播放并放大，isScaled: ${file.isScaled}`)

          // 添加背景遮罩
          this.addBackdrop()

          // 强制更新DOM以显示进度条
          this.$nextTick(() => {
            console.log('DOM更新完成，进度条应该显示')
          })

          // 开始3秒隐藏定时器
          this.startControlTimer(file)
        } else {
          // 暂停视频并恢复大小
          video.pause()
          video.muted = true
          this.$set(file, 'isPlaying', false)
          this.$set(file, 'isScaled', false) // 取消放大状态
          this.$set(file, 'displayWidth', null) // 清除显示宽度
          this.$set(file, 'displayHeight', null) // 清除显示高度
          this.$set(file, 'showControls', true)

          console.log(`视频 ${file.name} 已暂停并恢复大小`)

          // 移除背景遮罩
          this.removeBackdrop()

          // 清除隐藏定时器
          this.clearControlTimer(file.id)
        }
      }
    },

    // 暂停所有视频
    pauseAllVideos() {
      // 遍历所有文件，暂停正在播放的视频
      this.paginatedMaterials.forEach(file => {
        if (file.type === 'video' && file.isPlaying) {
          const videoRef = this.$refs[`video-${file.id}`]
          if (videoRef && videoRef.length > 0) {
            const video = videoRef[0]
            video.pause()
            video.muted = true
            this.$set(file, 'isPlaying', false)
          }
        }
      })
    },

    // 暂停所有视频并重置缩放
    pauseAllVideosAndResetScale() {
      // 遍历所有文件，暂停正在播放的视频并重置缩放
      this.paginatedMaterials.forEach(file => {
        if (file.type === 'video') {
          if (file.isPlaying) {
            const videoRef = this.$refs[`video-${file.id}`]
            if (videoRef && videoRef.length > 0) {
              const video = videoRef[0]
              video.pause()
              video.muted = true
              this.$set(file, 'isPlaying', false)
            }
          }
          // 重置缩放状态和显示尺寸
          this.$set(file, 'isScaled', false)
          this.$set(file, 'displayWidth', null)
          this.$set(file, 'displayHeight', null)
        }
      })

      // 移除背景遮罩
      this.removeBackdrop()
    },

    // 添加背景遮罩
    addBackdrop() {
      // 移除已存在的遮罩
      this.removeBackdrop()

      const backdrop = document.createElement('div')
      backdrop.className = 'scale-enlarged-backdrop'
      backdrop.id = 'video-scale-backdrop'
      document.body.appendChild(backdrop)

      // 点击遮罩关闭放大
      backdrop.addEventListener('click', () => {
        this.pauseAllVideosAndResetScale()
      })
    },

    // 移除背景遮罩
    removeBackdrop() {
      const backdrop = document.getElementById('video-scale-backdrop')
      if (backdrop) {
        backdrop.remove()
      }
    },

    // 获取视频播放进度百分比
    getVideoProgress(file) {
      const videoRef = this.$refs[`video-${file.id}`]
      if (videoRef && videoRef.length > 0) {
        const video = videoRef[0]
        if (video.duration && video.currentTime) {
          return (video.currentTime / video.duration) * 100
        }
      }
      return 0
    },

    // 获取当前播放时间
    getCurrentTime(file) {
      const videoRef = this.$refs[`video-${file.id}`]
      if (videoRef && videoRef.length > 0) {
        const video = videoRef[0]
        return video.currentTime || 0
      }
      return 0
    },

    // 获取视频总时长
    getDuration(file) {
      const videoRef = this.$refs[`video-${file.id}`]
      if (videoRef && videoRef.length > 0) {
        const video = videoRef[0]
        return video.duration || 0
      }
      return 0
    },

    // 格式化时间显示
    formatTime(seconds) {
      if (!seconds || isNaN(seconds)) return '0:00'

      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = Math.floor(seconds % 60)
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
    },

    // 点击进度条跳转
    seekVideo(event, file) {
      const videoRef = this.$refs[`video-${file.id}`]
      if (videoRef && videoRef.length > 0) {
        const video = videoRef[0]
        const progressBar = event.currentTarget.querySelector('.progress-track')
        const rect = progressBar.getBoundingClientRect()
        const clickX = event.clientX - rect.left
        const percentage = clickX / rect.width
        const newTime = percentage * video.duration

        if (newTime >= 0 && newTime <= video.duration) {
          video.currentTime = newTime
        }
      }
    },

    // 视频时间更新事件
    onVideoTimeUpdate(file) {
      // 强制更新进度条显示
      this.$forceUpdate()
    },

    // 视频数据加载完成事件
    onVideoLoadedData(file) {
      // 视频数据加载完成，可以获取时长等信息
      console.log(`视频 ${file.name} 数据加载完成`)
    },

    // 切换音频播放/暂停
    toggleAudioPlay(file) {
      const audioRef = this.$refs[`audio-${file.id}`]
      if (audioRef && audioRef.length > 0) {
        const audio = audioRef[0]

        if (audio.paused) {
          // 暂停所有其他音频和视频
          this.pauseAllMedia()

          // 播放当前音频
          audio.play()
          this.$set(file, 'isPlaying', true)
          this.$set(file, 'showControls', true)

          // 开始3秒隐藏定时器
          this.startControlTimer(file)
        } else {
          // 暂停当前音频
          audio.pause()
          this.$set(file, 'isPlaying', false)
          this.$set(file, 'showControls', true)

          // 清除隐藏定时器
          this.clearControlTimer(file.id)
        }
      }
    },

    // 暂停所有媒体（音频和视频）
    pauseAllMedia() {
      this.pauseAllVideos()
      this.pauseAllAudios()
    },

    // 暂停所有音频
    pauseAllAudios() {
      this.paginatedMaterials.forEach(file => {
        if (file.type === 'audio' && file.isPlaying) {
          const audioRef = this.$refs[`audio-${file.id}`]
          if (audioRef && audioRef.length > 0) {
            audioRef[0].pause()
            this.$set(file, 'isPlaying', false)
          }
        }
      })
    },

    // 预览文件
    previewFile(file) {
      // 构建文件URL
      if (!file.url && this.ossInitialized) {
        // 如果没有URL，构建OSS URL
        const endpoint = this.ossConfig.endpoint.replace('https://', '').replace('http://', '')
        file.url = `https://${endpoint}/${file.ossFileName}`
      }

      this.currentPreviewFile = file
      this.previewVisible = true
    },

    // 获取文件URL
    getFileUrl(file) {
      if (file.url) {
        return file.url
      }

      if (this.ossInitialized && file.ossFileName) {
        const endpoint = this.ossConfig.endpoint.replace('https://', '').replace('http://', '')
        return `https://${endpoint}/${file.ossFileName}`
      }

      return ''
    },

    // 停止所有媒体播放
    stopAllMedia() {
      // 停止预览对话框中的视频播放
      if (this.$refs.previewVideo) {
        this.$refs.previewVideo.pause()
        this.$refs.previewVideo.currentTime = 0
      }

      // 停止预览对话框中的音频播放
      if (this.$refs.previewAudio) {
        this.$refs.previewAudio.pause()
        this.$refs.previewAudio.currentTime = 0
      }

      // 停止缩略图中的所有媒体播放
      this.pauseAllMedia()

      // 清空预览文件
      this.currentPreviewFile = null
    },

    // 视频加载完成
    onVideoLoaded(event) {
      // 可以在这里获取视频的第一帧作为缩略图
      const video = event.target
      video.currentTime = 1 // 跳到第1秒获取缩略图
    },

    // 视频播放结束
    onVideoEnded(file) {
      this.$set(file, 'isPlaying', false)
      this.$set(file, 'isScaled', false)
      this.$set(file, 'displayWidth', null)
      this.$set(file, 'displayHeight', null)
      this.removeBackdrop()
    },

    // 视频暂停
    onVideoPaused(file) {
      this.$set(file, 'isPlaying', false)
      // 如果是放大状态，也要清理
      if (file.isScaled) {
        this.$set(file, 'isScaled', false)
        this.$set(file, 'displayWidth', null)
        this.$set(file, 'displayHeight', null)
        this.removeBackdrop()
      }

      // 同步Portal视频
      if (this.hoveredFile && this.hoveredFile.id === file.id) {
        this.$nextTick(() => {
          this.syncPortalVideo()
        })
      }
    },

    // 视频开始播放
    onVideoPlayed(file) {
      this.$set(file, 'isPlaying', true)

      // 同步Portal视频
      if (this.hoveredFile && this.hoveredFile.id === file.id) {
        this.$nextTick(() => {
          this.syncPortalVideo()
        })
      }
    },

    // 音频播放结束
    onAudioEnded(file) {
      this.$set(file, 'isPlaying', false)
    },

    // 音频暂停
    onAudioPaused(file) {
      this.$set(file, 'isPlaying', false)
    },

    // 音频开始播放
    onAudioPlayed(file) {
      this.$set(file, 'isPlaying', true)
    },

    // 图片加载错误
    onImageError(event) {
      // 图片加载失败时显示默认图标
      event.target.style.display = 'none'
      event.target.parentNode.innerHTML = '<i class="el-icon-picture-outline"></i>'
    },

    // 视频鼠标进入事件
    onVideoMouseEnter(file) {
      console.log('视频鼠标进入:', file.name, '显示控制按钮')
      this.$set(file, 'showControls', true)
      this.clearControlTimer(file.id)
    },

    // 视频鼠标离开事件
    onVideoMouseLeave(file) {
      if (file.isPlaying) {
        this.startControlTimer(file)
      } else {
        this.$set(file, 'showControls', false)
      }
    },

    // 视频鼠标移动事件
    onVideoMouseMove(file) {
      this.$set(file, 'showControls', true)
      if (file.isPlaying) {
        this.clearControlTimer(file.id)
        this.startControlTimer(file)
      }
    },

    // 音频鼠标进入事件
    onAudioMouseEnter(file) {
      console.log('音频鼠标进入:', file.name)
      this.$set(file, 'showControls', true)
      this.clearControlTimer(file.id)
    },

    // 音频鼠标离开事件
    onAudioMouseLeave(file) {
      if (file.isPlaying) {
        this.startControlTimer(file)
      } else {
        this.$set(file, 'showControls', false)
      }
    },

    // 音频鼠标移动事件
    onAudioMouseMove(file) {
      this.$set(file, 'showControls', true)
      if (file.isPlaying) {
        this.clearControlTimer(file.id)
        this.startControlTimer(file)
      }
    },

    // 图片鼠标进入事件
    onImageMouseEnter(file) {
      console.log('图片鼠标进入:', file.name)
    },

    // 图片鼠标离开事件
    onImageMouseLeave(file) {
      console.log('图片鼠标离开:', file.name)
    },

    // 开始控制按钮隐藏定时器
    startControlTimer(file) {
      this.clearControlTimer(file.id)
      this.controlTimers[file.id] = setTimeout(() => {
        this.$set(file, 'showControls', false)
      }, 3000) // 3秒后隐藏
    },

    // 清除控制按钮隐藏定时器
    clearControlTimer(fileId) {
      if (this.controlTimers[fileId]) {
        clearTimeout(this.controlTimers[fileId])
        delete this.controlTimers[fileId]
      }
    },





    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 下载文件
    downloadFile(file) {
      if (file.url) {
        const link = document.createElement('a')
        link.href = file.url
        link.download = file.name
        link.target = '_blank'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        this.$message.error('文件链接不可用')
      }
    },

    // 从OSS获取用户文件夹列表（分别为BGM和素材）
    async loadUserFoldersFromOSS() {
      try {
        const client = getOSSClient()
        const user = this.getCurrentUser()

        // 分别获取BGM和素材的文件夹
        await this.loadFoldersForType('abgm', 'bgmFolderTree')
        await this.loadFoldersForType('asucai', 'sucaiFolderTree')

        console.log('BGM文件夹:', this.bgmFolderTree.map(f => f.name))
        console.log('素材文件夹:', this.sucaiFolderTree.map(f => f.name))

      } catch (error) {
        console.error('获取OSS文件夹列表失败:', error)
        // 如果获取失败，至少显示默认的"总"文件夹
        this.bgmFolderTree = [{ id: 1, name: '总', count: 0 }]
        this.sucaiFolderTree = [{ id: 1, name: '总', count: 0 }]
      }
    },

    // 为指定类型加载文件夹
    async loadFoldersForType(baseFolder, treeProperty) {
      try {
        const client = getOSSClient()
        const user = this.getCurrentUser()
        const folderSet = new Set()
        const prefix = `${baseFolder}/${user}/`

        // 列出该前缀下的所有对象
        const result = await client.list({
          prefix: prefix,
          delimiter: '/',
          'max-keys': 1000
        })

        // 从commonPrefixes中提取文件夹名称
        if (result.prefixes) {
          result.prefixes.forEach(prefixInfo => {
            const fullPrefix = prefixInfo.name || prefixInfo
            // 提取文件夹名称：abgm/admin/总/ -> 总
            if (fullPrefix && typeof fullPrefix === 'string') {
              const folderName = fullPrefix.replace(prefix, '').replace('/', '')
              if (folderName) {
                folderSet.add(folderName)
              }
            }
          })
        }

        // 如果有objects，也从中提取文件夹名称
        if (result.objects) {
          result.objects.forEach(obj => {
            const objectKey = obj.name
            if (objectKey.startsWith(prefix)) {
              const relativePath = objectKey.replace(prefix, '')
              const folderName = relativePath.split('/')[0]
              if (folderName && folderName !== '.keep') {
                folderSet.add(folderName)
              }
            }
          })
        }

        // 转换为文件夹树格式
        const folders = Array.from(folderSet).map((folderName, index) => ({
          id: Date.now() + index, // 使用时间戳避免ID冲突
          name: folderName,
          count: 0 // 稍后可以计算实际文件数量
        }))

        // 确保"总"文件夹存在
        if (!folderSet.has('总')) {
          folders.unshift({ id: Date.now(), name: '总', count: 0 })
        }

        // 更新对应的文件夹树
        if (baseFolder === 'abgm') {
          this.bgmFolderTree = folders
        } else {
          this.sucaiFolderTree = folders
        }

        console.log(`${baseFolder} 文件夹加载完成:`, folders.map(f => f.name))

      } catch (error) {
        console.error(`获取 ${baseFolder} 文件夹列表失败:`, error)
        // 如果获取失败，至少显示默认的"总"文件夹
        const defaultFolder = [{ id: Date.now(), name: '总', count: 0 }]
        if (baseFolder === 'abgm') {
          this.bgmFolderTree = defaultFolder
        } else {
          this.sucaiFolderTree = defaultFolder
        }
      }
    },

    // 更新文件夹的文件数量
    async updateFolderFileCounts() {
      try {
        const client = getOSSClient()
        const user = this.getCurrentUser()

        // 由于现在使用分离的文件夹树，这个方法暂时禁用
        // 可以根据需要为每个标签页单独更新文件数量
        console.log('文件夹数量更新已改为分离模式，请使用具体的标签页更新方法')
        return

        for (let folder of []) { // 临时禁用
          let totalCount = 0

          // 检查两种类型的文件夹中的文件数量
          const baseFolders = ['abgm', 'asucai']

          for (const baseFolder of baseFolders) {
            const prefix = `${baseFolder}/${user}/${folder.name}/`

            try {
              const result = await client.list({
                prefix: prefix,
                'max-keys': 1000
              })

              if (result.objects) {
                // 过滤掉.keep文件
                const fileCount = result.objects.filter(obj =>
                  !obj.name.endsWith('.keep') && !obj.name.endsWith('/')
                ).length
                totalCount += fileCount
              }
            } catch (error) {
              console.warn(`获取文件夹 ${folder.name} 在 ${baseFolder} 中的文件数量失败:`, error)
            }
          }

          folder.count = totalCount
        }

        console.log('文件夹文件数量更新完成:', this.simpleFolderTree)

      } catch (error) {
        console.error('更新文件夹文件数量失败:', error)
      }
    },

    // 文件夹右键菜单相关方法
    showFolderContextMenu(event, folder) {
      this.contextMenuVisible = true
      this.contextMenuX = event.clientX
      this.contextMenuY = event.clientY
      this.contextMenuFolder = folder

      // 点击其他地方隐藏菜单
      document.addEventListener('click', this.hideFolderContextMenu)
    },

    hideFolderContextMenu() {
      this.contextMenuVisible = false
      this.contextMenuFolder = null
      document.removeEventListener('click', this.hideFolderContextMenu)
    },

    // 重命名文件夹
    async renameFolderAction() {
      if (!this.contextMenuFolder) {
        this.$message.error('未选择文件夹')
        return
      }

      const folderName = this.contextMenuFolder.name || this.contextMenuFolder

      this.$prompt('请输入新的文件夹名称', '重命名文件夹', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: folderName
      }).then(async ({ value }) => {
        try {
          if (!value || value.trim() === '') {
            this.$message.error('文件夹名称不能为空')
            return
          }

          const newFolderName = value.trim()
          const oldFolderName = this.contextMenuFolder.name || this.contextMenuFolder

          if (newFolderName === oldFolderName) {
            this.hideFolderContextMenu()
            return
          }

          // 如果使用OSS存储，需要重命名OSS中的文件夹
          if (this.storageType === 'oss' && this.ossInitialized) {
            await this.renameOSSFolder(oldFolderName, newFolderName)
          }

          // 更新前端文件夹名称
          if (typeof this.contextMenuFolder === 'object') {
            this.contextMenuFolder.name = newFolderName
          }

          // 如果当前选中的是被重命名的文件夹，更新当前文件夹
          if (this.currentFolder === oldFolderName) {
            this.currentFolder = newFolderName
          }

          this.$message.success('文件夹重命名成功')
          this.hideFolderContextMenu()

        } catch (error) {
          console.error('重命名文件夹失败:', error)
          this.$message.error(`重命名失败: ${error.message}`)
        }
      })
    },

    // 删除文件夹
    async deleteFolderAction() {
      console.log('删除文件夹操作开始，contextMenuFolder:', this.contextMenuFolder)

      if (!this.contextMenuFolder) {
        this.$message.error('未选择文件夹')
        return
      }

      // 安全地获取文件夹名称
      let folderName
      if (typeof this.contextMenuFolder === 'string') {
        folderName = this.contextMenuFolder
      } else if (this.contextMenuFolder && this.contextMenuFolder.name) {
        folderName = this.contextMenuFolder.name
      } else {
        console.error('无法获取文件夹名称，contextMenuFolder:', this.contextMenuFolder)
        this.$message.error('无法获取文件夹名称')
        return
      }

      console.log('准备删除文件夹:', folderName)

      if (folderName === '总') {
        this.$message.warning('不能删除"总"文件夹')
        return
      }

      this.$confirm(`确定要删除文件夹"${folderName}"吗？文件夹内的所有文件也会被删除！`, '删除文件夹', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 使用之前获取的folderName，避免在异步操作中contextMenuFolder变为null
          const targetFolderName = folderName

          // 如果使用OSS存储，只删除当前标签页的OSS文件夹
          if (this.storageType === 'oss' && this.ossInitialized) {
            try {
              await this.deleteOSSFolderForCurrentTab(targetFolderName)
              console.log(`${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹删除成功`)
            } catch (ossError) {
              console.warn('OSS文件夹删除失败，但继续删除前端记录:', ossError.message)
              // 即使OSS删除失败，也继续删除前端记录（可能是空文件夹）
            }
          }

          // 从前端移除文件夹相关的文件
          this.removeFilesFromFolder(targetFolderName)

          // 如果当前选中的是被删除的文件夹，切换到"总"文件夹
          if (this.currentFolder === targetFolderName) {
            this.currentFolder = '总'
          }

          this.$message.success('文件夹删除成功')
          this.hideFolderContextMenu()

        } catch (error) {
          console.error('删除文件夹失败:', error)
          this.$message.error(`删除失败: ${error.message}`)
        }
      })
    },

    // 获取当前用户名
    getCurrentUser() {
      // 从store中获取用户信息
      return this.$store.state.user.name || 'admin'
    },

    // 获取当前文件夹名称
    getCurrentFolder() {
      return this.currentFolder || '总'
    },

    // 构建OSS上传路径
    buildOSSPath(baseFolder) {
      const user = this.getCurrentUser()
      const folder = this.getCurrentFolder()
      return `${baseFolder}/${user}/${folder}`
    },

    // OSS文件复制和删除（用于重命名）
    async copyAndDeleteOSSFile(oldPath, newPath) {
      try {
        const client = getOSSClient()

        // 复制文件到新路径
        await client.copy(newPath, oldPath)
        console.log(`OSS文件复制成功: ${oldPath} -> ${newPath}`)

        // 删除原文件
        await client.delete(oldPath)
        console.log(`OSS原文件删除成功: ${oldPath}`)

        return true
      } catch (error) {
        console.error('OSS文件复制删除失败:', error)
        throw error
      }
    },

    // OSS文件夹重命名
    async renameOSSFolder(oldFolderName, newFolderName) {
      try {
        const client = getOSSClient()
        const user = this.getCurrentUser()

        // 获取两种类型文件夹的路径
        const baseFolders = ['abgm', 'asucai']

        for (const baseFolder of baseFolders) {
          const oldPrefix = `${baseFolder}/${user}/${oldFolderName}/`
          const newPrefix = `${baseFolder}/${user}/${newFolderName}/`

          // 列出文件夹中的所有文件
          const result = await client.list({
            prefix: oldPrefix,
            'max-keys': 1000
          })

          if (result.objects && result.objects.length > 0) {
            // 复制所有文件到新路径
            for (const obj of result.objects) {
              const newKey = obj.name.replace(oldPrefix, newPrefix)
              await client.copy(newKey, obj.name)
              console.log(`OSS文件夹重命名: ${obj.name} -> ${newKey}`)
            }

            // 删除原文件
            for (const obj of result.objects) {
              await client.delete(obj.name)
            }
          }
        }

        return true
      } catch (error) {
        console.error('OSS文件夹重命名失败:', error)
        throw error
      }
    },

    // OSS文件夹删除
    async deleteOSSFolder(folderName) {
      try {
        console.log('开始删除OSS文件夹:', folderName)

        if (!folderName) {
          throw new Error('文件夹名称不能为空')
        }

        const client = getOSSClient()
        if (!client) {
          throw new Error('OSS客户端未初始化')
        }

        const user = this.getCurrentUser()
        console.log('当前用户:', user)

        // 获取两种类型文件夹的路径
        const baseFolders = ['abgm', 'asucai']
        let totalDeletedFiles = 0

        for (const baseFolder of baseFolders) {
          const prefix = `${baseFolder}/${user}/${folderName}/`
          console.log('检查OSS路径:', prefix)

          // 列出文件夹中的所有文件
          const result = await client.list({
            prefix: prefix,
            'max-keys': 1000
          })

          console.log(`路径 ${prefix} 下找到 ${result.objects ? result.objects.length : 0} 个文件`)

          if (result.objects && result.objects.length > 0) {
            // 删除所有文件
            for (const obj of result.objects) {
              await client.delete(obj.name)
              console.log(`OSS文件删除: ${obj.name}`)
              totalDeletedFiles++
            }
          }
        }

        console.log(`OSS文件夹删除完成，共删除 ${totalDeletedFiles} 个文件`)
        return true
      } catch (error) {
        console.error('OSS文件夹删除失败:', error)
        throw new Error(`删除OSS文件夹失败: ${error.message}`)
      }
    },

    // 只删除当前标签页的OSS文件夹
    async deleteOSSFolderForCurrentTab(folderName) {
      try {
        console.log(`开始删除${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹:`, folderName)

        if (!folderName) {
          throw new Error('文件夹名称不能为空')
        }

        const client = getOSSClient()
        if (!client) {
          throw new Error('OSS客户端未初始化')
        }

        const user = this.getCurrentUser()
        const baseFolder = this.materialTab === 'bgm' ? 'abgm' : 'asucai'
        const prefix = `${baseFolder}/${user}/${folderName}/`

        console.log('删除OSS路径:', prefix)

        // 列出文件夹中的所有文件
        const result = await client.list({
          prefix: prefix,
          'max-keys': 1000
        })

        let deletedFiles = 0
        if (result.objects && result.objects.length > 0) {
          // 删除所有文件
          for (const obj of result.objects) {
            await client.delete(obj.name)
            console.log(`OSS文件删除: ${obj.name}`)
            deletedFiles++
          }
        }

        console.log(`${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹删除完成，共删除 ${deletedFiles} 个文件`)
        return true
      } catch (error) {
        console.error(`删除${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹失败:`, error)
        throw new Error(`删除${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹失败: ${error.message}`)
      }
    },

    // 从前端移除文件夹相关的文件（只删除当前标签页的）
    removeFilesFromFolder(folderName) {
      if (this.materialTab === 'bgm') {
        // 只移除BGM列表中该文件夹的文件
        this.bgmList = this.bgmList.filter(file => {
          const fileFolderName = file.folder ? file.folder.split('/').pop() : '总'
          return fileFolderName !== folderName
        })

        // 从BGM文件夹树中移除
        const treeIndex = this.bgmFolderTree.findIndex(folder => folder.name === folderName)
        if (treeIndex > -1) {
          this.bgmFolderTree.splice(treeIndex, 1)
          console.log(`从BGM文件夹树中移除: ${folderName}`)
        }
      } else {
        // 只移除素材列表中该文件夹的文件
        this.sucaiList = this.sucaiList.filter(file => {
          const fileFolderName = file.folder ? file.folder.split('/').pop() : '总'
          return fileFolderName !== folderName
        })

        // 从素材文件夹树中移除
        const treeIndex = this.sucaiFolderTree.findIndex(folder => folder.name === folderName)
        if (treeIndex > -1) {
          this.sucaiFolderTree.splice(treeIndex, 1)
          console.log(`从素材文件夹树中移除: ${folderName}`)
        }
      }
    },

    // 文件夹选择
    async selectFolder(folder) {
      this.selectedFolder = folder.id
      this.currentFolder = folder.name
      console.log('选择文件夹:', folder.name)

      // 切换文件夹时重新加载文件列表
      if (this.ossInitialized) {
        await this.loadMaterialList()
      }

      // 更新文件夹计数
      this.updateCurrentTabFolderCounts()
    },

    // 更新当前标签页的文件夹计数
    updateCurrentTabFolderCounts() {
      const currentMaterials = this.currentMaterialList
      const currentFolders = this.currentFolderTree

      // 为每个文件夹计算文件数量
      currentFolders.forEach(folder => {
        if (folder.name === '总') {
          // "总"文件夹显示所有文件数量
          folder.count = currentMaterials.length
        } else {
          // 其他文件夹只计算属于该文件夹的文件
          folder.count = currentMaterials.filter(file => {
            const fileFolderName = file.folder ? file.folder.split('/').pop() : '总'
            return fileFolderName === folder.name
          }).length
        }
      })

      console.log(`${this.materialTab} 文件夹计数更新:`, currentFolders.map(f => `${f.name}(${f.count})`))
    },

    // 文件选择相关方法
    handleSelectAll(checked) {
      if (checked) {
        this.selectedFiles = this.paginatedMaterials.map(f => f.id)
      } else {
        this.selectedFiles = []
      }
    },

    // 预览文件
    handlePreview() {
      if (this.selectedFiles.length !== 1) {
        this.$message.warning('请选择一个文件进行预览')
        return
      }

      const file = this.currentMaterialList.find(f => f.id === this.selectedFiles[0])
      if (!file) {
        this.$message.error('文件不存在')
        return
      }

      // 构建文件URL
      if (!file.url && this.ossInitialized) {
        // 如果没有URL，构建OSS URL
        const endpoint = this.ossConfig.endpoint.replace('https://', '').replace('http://', '')
        file.url = `https://${endpoint}/${file.ossFileName}`
      }

      this.currentPreviewFile = file
      this.previewVisible = true
    },

    toggleFileSelection(fileId) {
      const index = this.selectedFiles.indexOf(fileId)
      if (index > -1) {
        this.selectedFiles.splice(index, 1)
      } else {
        this.selectedFiles.push(fileId)
      }

      // 更新全选状态
      this.selectAll = this.selectedFiles.length === this.paginatedMaterials.length
    },

    getSimpleFileIcon(type) {
      const iconMap = {
        'video': '🎬',
        'image': '🖼️',
        'audio': '🎵'
      }
      return iconMap[type] || '📄'
    },

    // 标签页切换
    switchTab(tab) {
      this.materialTab = tab
      this.currentPage = 1 // 重置页码
      this.selectedFiles = [] // 清空选择
      this.selectAll = false

      const tabNames = {
        'bgm': 'BGM',
        'sucai': '素材'
      }
      this.$message.success(`切换到${tabNames[tab] || tab}上传页面`)

      // 切换标签页后更新文件夹计数
      this.$nextTick(() => {
        this.updateCurrentTabFolderCounts()
      })
    },

    // 文件操作
    async handleRename() {
      if (this.selectedFiles.length === 0) {
        this.$message.warning('请先选择要重命名的文件')
        return
      }
      if (this.selectedFiles.length > 1) {
        this.$message.warning('一次只能重命名一个文件')
        return
      }

      const file = this.currentMaterialList.find(f => f.id === this.selectedFiles[0])
      this.$prompt('请输入新的文件名', '重命名文件', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: file.name
      }).then(async ({ value }) => {
        try {
          // 验证新文件名
          if (!value || value.trim() === '') {
            this.$message.error('文件名不能为空')
            return
          }

          const newFileName = value.trim()

          // 如果使用OSS存储，需要重命名OSS中的文件
          if (this.storageType === 'oss' && this.ossInitialized) {
            // 构建原文件路径
            let oldOssFilePath = ''
            if (file.ossFileName) {
              oldOssFilePath = file.ossFileName
            } else {
              const baseFolderName = this.materialTab === 'bgm' ? 'abgm' : 'asucai'
              const fullPath = this.buildOSSPath(baseFolderName)
              oldOssFilePath = `${fullPath}/${file.name}`
            }

            // 构建新文件路径
            const baseFolderName = this.materialTab === 'bgm' ? 'abgm' : 'asucai'
            const fullPath = this.buildOSSPath(baseFolderName)
            const newOssFilePath = `${fullPath}/${newFileName}`

            console.log(`OSS重命名: ${oldOssFilePath} -> ${newOssFilePath}`)

            try {
              // OSS不支持直接重命名，需要复制后删除
              await this.copyAndDeleteOSSFile(oldOssFilePath, newOssFilePath)

              // 更新文件信息
              file.name = newFileName
              file.ossFileName = newOssFilePath

              this.$message.success('重命名成功')
            } catch (error) {
              console.error('OSS重命名失败:', error)
              this.$message.error(`重命名失败: ${error.message}`)
            }
          } else {
            // 本地存储，直接更新文件名
            file.name = newFileName
            this.$message.success('重命名成功')
          }

        } catch (error) {
          console.error('重命名失败:', error)
          this.$message.error(`重命名失败: ${error.message}`)
        }
      })
    },

    async handleDelete() {
      if (this.selectedFiles.length === 0) {
        this.$message.warning('请先选择要删除的文件')
        return
      }

      this.$confirm(`确定要删除选中的 ${this.selectedFiles.length} 个文件吗？删除后无法恢复！`, '删除文件', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const currentList = this.materialTab === 'bgm' ? this.bgmList : this.sucaiList

          // 获取要删除的文件信息
          const filesToDelete = this.selectedFiles.map(fileId => {
            return currentList.find(f => f.id === fileId)
          }).filter(file => file) // 过滤掉找不到的文件

          console.log('准备删除的文件:', filesToDelete)

          // 如果使用OSS存储，删除OSS中的文件
          if (this.storageType === 'oss' && this.ossInitialized) {
            const deletePromises = filesToDelete.map(async (file) => {
              try {
                // 构建OSS文件路径
                let ossFilePath = ''
                if (file.ossFileName) {
                  // 如果有OSS文件名，直接使用
                  ossFilePath = file.ossFileName
                } else {
                  // 否则根据文件夹和文件名构建路径
                  const baseFolderName = this.materialTab === 'bgm' ? 'abgm' : 'asucai'
                  const fullPath = this.buildOSSPath(baseFolderName)
                  ossFilePath = `${fullPath}/${file.name}`
                }

                console.log(`删除OSS文件: ${ossFilePath}`)
                await deleteFileFromOSS(ossFilePath)
                return { success: true, file: file.name }
              } catch (error) {
                console.error(`删除OSS文件失败: ${file.name}`, error)
                return { success: false, file: file.name, error: error.message }
              }
            })

            const deleteResults = await Promise.all(deletePromises)

            // 检查删除结果
            const failedDeletes = deleteResults.filter(result => !result.success)
            if (failedDeletes.length > 0) {
              console.warn('部分文件删除失败:', failedDeletes)
              this.$message.warning(`${failedDeletes.length} 个文件删除失败，但已从列表中移除`)
            }
          }

          // 从前端列表中删除文件
          this.selectedFiles.forEach(fileId => {
            const index = currentList.findIndex(f => f.id === fileId)
            if (index > -1) {
              currentList.splice(index, 1)
            }
          })

          this.selectedFiles = []
          this.selectAll = false
          this.$message.success('删除成功')

        } catch (error) {
          console.error('删除失败:', error)
          this.$message.error(`删除失败: ${error.message}`)
        }
      })
    },
    showUploadDialog() {
      this.uploadDialogVisible = true
      this.fileList = []
      this.uploadForm = {}
    },

    beforeUpload(file) {
      // 添加调试信息
      console.log('上传文件信息:', {
        name: file.name,
        type: file.type,
        size: file.size,
        currentTab: this.materialTab
      })

      // 根据标签页检查文件大小
      let maxSize = 500 // 默认500MB
      if (this.materialTab === 'bgm') {
        maxSize = 100 // BGM文件100MB
      }

      const isValidSize = file.size / 1024 / 1024 < maxSize
      if (!isValidSize) {
        this.$message.error(`文件大小不能超过 ${maxSize}MB！`)
        return false
      }

      // 根据标签页检查文件格式
      const extension = file.name.toLowerCase().split('.').pop()
      const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v']
      const audioExts = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']
      const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']

      if (this.materialTab === 'bgm' && !audioExts.includes(extension)) {
        this.$message.error(`BGM只支持音频文件！支持格式：${audioExts.join(', ')}`)
        return false
      }

      if (this.materialTab === 'sucai') {
        const allExts = [...videoExts, ...audioExts, ...imageExts]
        if (!allExts.includes(extension)) {
          this.$message.error(`素材支持视频、音频、图片文件！支持格式：${allExts.join(', ')}`)
          return false
        }
      }

      return true
    },

    isValidFileType(type, fileName) {
      // 获取文件扩展名作为备用验证
      const extension = fileName.toLowerCase().split('.').pop()

      if (this.materialTab === 'video') {
        const videoTypes = [
          'video/mp4', 'video/mpeg', 'video/quicktime', 'video/x-msvideo',
          'video/x-ms-wmv', 'video/x-flv', 'video/webm', 'video/3gpp',
          'video/mp2t', 'video/x-m4v'
        ]
        const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v', 'ts']

        return videoTypes.includes(type) || videoExtensions.includes(extension)
      } else {
        const audioTypes = [
          'audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/x-wav',
          'audio/flac', 'audio/aac', 'audio/mp4', 'audio/x-m4a',
          'audio/ogg', 'audio/webm'
        ]
        const audioExtensions = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']

        return audioTypes.includes(type) || audioExtensions.includes(extension)
      }
    },
    handleUploadSuccess(response, file) {
      // 创建新的文件对象
      const newFile = {
        id: Date.now() + Math.random(),
        name: file.name,
        type: this.materialTab === 'video' ? 'video' : 'audio',
        size: file.size,
        uploadTime: new Date().toLocaleString().slice(0, 16),
        duration: '00:00:00', // 实际应用中应该从服务器返回
        resolution: this.materialTab === 'video' ? '1920x1080' : undefined,
        bitrate: this.materialTab === 'music' ? '128kbps' : undefined
      }

      // 添加到对应的列表
      if (this.materialTab === 'video') {
        this.videoList.unshift(newFile)
      } else {
        this.musicList.unshift(newFile)
      }

      this.$message.success(`${file.name} 上传成功！`)
    },

    handleUploadError(err, file) {
      this.$message.error(`${file.name} 上传失败！`)
    },

    handleUploadProgress(event, file) {
      // 计算上传进度百分比
      const progress = Math.round((event.loaded / event.total) * 100)

      // 更新进度条显示
      this.$set(this.uploadProgress, file.name, progress)

      console.log(`文件 ${file.name} 上传进度: ${progress}%`)
    },

    handleFileChange(file, fileList) {
      console.log('文件变化:', file)
      console.log('当前文件列表:', fileList)
      this.fileList = fileList
    },

    handleRemove(file, fileList) {
      console.log('移除文件:', file)
      console.log('更新后的文件列表:', fileList)
      this.fileList = fileList
    },

    async submitUpload() {
      console.log('当前文件列表:', this.fileList)
      console.log('文件列表长度:', this.fileList.length)

      if (!this.fileList || this.fileList.length === 0) {
        this.$message.warning('请先选择要上传的文件')
        return
      }

      this.uploading = true
      const fileCount = this.fileList.length

      // 根据标签页确定文件夹和类型
      let baseFolderName = ''
      let fileType = ''

      switch (this.materialTab) {
        case 'bgm':
          baseFolderName = 'abgm'
          fileType = 'audio'
          break
        case 'sucai':
          baseFolderName = 'asucai'
          fileType = 'mixed' // 混合类型
          break
        default:
          baseFolderName = 'abgm'
          fileType = 'audio'
      }

      // 构建完整的OSS路径：baseFolderName/用户名/文件夹名
      const folderName = this.buildOSSPath(baseFolderName)

      console.log('当前标签页:', this.materialTab)
      console.log('基础文件夹:', baseFolderName)
      console.log('完整上传路径:', folderName)
      console.log('文件类型:', fileType)

      // 获取实际的文件对象
      const actualFiles = this.fileList.map(fileItem => {
        // Element UI 上传组件的文件对象可能有不同的结构
        return fileItem.raw || fileItem.file || fileItem
      }).filter(file => file instanceof File)

      console.log('实际文件对象:', actualFiles)

      if (actualFiles.length === 0) {
        this.$message.warning('没有找到有效的文件对象')
        return
      }

      try {
        // 初始化所有文件的进度条
        actualFiles.forEach(file => {
          this.$set(this.uploadProgress, file.name, 0)
        })

        if (this.storageType === 'oss') {
          // OSS上传
          if (!this.ossInitialized) {
            this.$message.error('OSS未配置，请先配置OSS')
            this.openOSSConfig()
            this.uploading = false
            return
          }

          // 使用OSS上传
          const uploadResults = await uploadFilesToOSS(
            actualFiles,
            fileType,
            folderName, // 使用对应的文件夹
            (index, progress, fileName, result, error) => {
              if (error) {
                this.$set(this.uploadProgress, fileName, -1) // 表示失败
              } else {
                this.$set(this.uploadProgress, fileName, progress)
              }
            }
          )

          // 处理上传结果
          uploadResults.forEach((result, index) => {
            if (result.success) {
              // 根据文件扩展名确定文件类型
              const fileExtension = result.originalName.toLowerCase().split('.').pop()
              const audioExts = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']
              const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v']
              const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']

              let actualFileType = 'unknown'
              if (audioExts.includes(fileExtension)) {
                actualFileType = 'audio'
              } else if (videoExts.includes(fileExtension)) {
                actualFileType = 'video'
              } else if (imageExts.includes(fileExtension)) {
                actualFileType = 'image'
              }

              const newFile = {
                id: Date.now() + Math.random() + index,
                name: result.originalName,
                type: actualFileType,
                size: result.size,
                uploadTime: new Date().toLocaleString().slice(0, 16),
                duration: actualFileType === 'video' ? '00:02:30' : (actualFileType === 'audio' ? '00:03:45' : undefined),
                resolution: actualFileType === 'video' ? '1920x1080' : undefined,
                bitrate: actualFileType === 'audio' ? '128kbps' : undefined,
                url: result.url,
                ossFileName: result.fileName,
                folder: folderName
              }

              // 根据标签页添加到对应列表
              switch (this.materialTab) {
                case 'bgm':
                  this.bgmList.unshift(newFile)
                  break
                case 'sucai':
                  this.sucaiList.unshift(newFile)
                  break
              }
            }
          })

          this.$message.success(`成功上传 ${uploadResults.filter(r => r.success).length} 个文件到阿里云OSS！`)

        } else {
          // 本地模拟上传
          actualFiles.forEach((file) => {
            // 进度已在上面初始化，这里直接开始模拟进度更新

            // 模拟进度更新
            const progressInterval = setInterval(() => {
              const currentProgress = this.uploadProgress[file.name] || 0
              if (currentProgress < 100) {
                this.$set(this.uploadProgress, file.name, Math.min(currentProgress + Math.random() * 30, 100))
              } else {
                clearInterval(progressInterval)
              }
            }, 200)
          })

          // 模拟上传完成
          setTimeout(() => {
            // 为每个文件创建新记录
            actualFiles.forEach((file, index) => {
              // 根据文件扩展名确定文件类型
              const fileExtension = file.name.toLowerCase().split('.').pop()
              const audioExts = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']
              const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v']
              const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']

              let actualFileType = 'unknown'
              if (audioExts.includes(fileExtension)) {
                actualFileType = 'audio'
              } else if (videoExts.includes(fileExtension)) {
                actualFileType = 'video'
              } else if (imageExts.includes(fileExtension)) {
                actualFileType = 'image'
              }

              const newFile = {
                id: Date.now() + Math.random() + index,
                name: file.name,
                type: actualFileType,
                size: file.size,
                uploadTime: new Date().toLocaleString().slice(0, 16),
                duration: actualFileType === 'video' ? '00:02:30' : (actualFileType === 'audio' ? '00:03:45' : undefined),
                resolution: actualFileType === 'video' ? '1920x1080' : undefined,
                bitrate: actualFileType === 'audio' ? '128kbps' : undefined,
                folder: folderName,
                url: URL.createObjectURL(file) // 为本地文件创建预览URL
              }

              // 根据标签页添加到对应列表
              switch (this.materialTab) {
                case 'bgm':
                  this.bgmList.unshift(newFile)
                  break
                case 'sucai':
                  this.sucaiList.unshift(newFile)
                  break
              }
            })

            this.$message.success(`成功上传 ${fileCount} 个文件！`)

            // 上传完成后更新文件夹计数
            this.updateCurrentTabFolderCounts()
          }, 2000)
        }

        this.uploading = false
        this.uploadDialogVisible = false
        this.fileList = []
        this.uploadProgress = {}

      } catch (error) {
        this.uploading = false
        this.$message.error(`上传失败：${error.message}`)
        console.error('上传错误:', error)
      }
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  }
}
</script>

<style lang="scss" scoped>
/* 素材管理界面 - 完全按照图片设计 */
.up-container {
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  margin: 0;
  height: 100vh;
}

/* 顶部标签页 */
.materials-tabs {
  background: white;
  padding: 0;
  border-bottom: 1px solid #e8e8e8;
}

.tab-buttons {
  display: flex;
  gap: 0;
  align-items: center;
}

.tab-button {
  border-radius: 20px !important;
  margin: 10px 5px;
  padding: 8px 20px;
  border: none;
  font-size: 14px;
}

.tab-button.el-button--primary {
  background: #4a90e2;
  color: white;
}

.tab-button.el-button--default {
  background: #e8e8e8;
  color: #666;
}

/* BGM免费下载按钮样式 */
.bgm-download-button {
  margin-left: 20px !important;
  border-radius: 15px !important;
  padding: 6px 16px !important;
  font-size: 13px !important;
  background: linear-gradient(135deg, #67b26f 0%, #4ca2cd 100%) !important;
  border: none !important;
  color: white !important;
  box-shadow: 0 2px 8px rgba(76, 162, 205, 0.3) !important;
  transition: all 0.3s ease !important;
}

.bgm-download-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(76, 162, 205, 0.4) !important;
  background: linear-gradient(135deg, #5a9f63 0%, #3d8bb8 100%) !important;
}

.bgm-download-button:active {
  transform: translateY(0) !important;
}

/* 主要内容区域 */
.materials-main {
  flex: 1;
  display: flex;
  background: white;
}

/* 左侧文件夹树 - 按照图片样式 */
.folder-sidebar {
  width: 200px;
  background: white;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

.folder-header {
  padding: 8px 12px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #fafafa;
}

.folder-actions {
  display: flex !important;
  flex-direction: column !important; /* 垂直排列 */
  align-items: flex-start !important; /* 左对齐 */
  gap: 8px; /* 上下间距 */
  padding: 8px 0;
}

/* 使用更强的选择器覆盖Element UI样式 */
.folder-actions .el-button,
.folder-actions .el-button.el-button--small,
.folder-actions .el-button.el-button--primary,
.folder-actions .el-button.el-button--success {
  border-radius: 6px !important;
  font-weight: 500 !important;
  font-size: 13px !important;
  height: 32px !important;
  width: 100% !important; /* 垂直排列时占满宽度 */
  max-width: 120px !important; /* 限制最大宽度 */
  min-height: 32px !important;
  max-height: 32px !important;
  line-height: 1 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 16px !important;
  margin: 0 !important;
  transition: all 0.3s ease;
  flex-shrink: 0;
  vertical-align: baseline !important;
  box-sizing: border-box !important;
  border-width: 1px !important;
}

.folder-actions .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 图标样式 */
.folder-actions .el-button i {
  margin-right: 4px !important;
  line-height: 1 !important;
  vertical-align: middle !important;
}

/* 按钮文字样式 */
.folder-actions .el-button span {
  line-height: 1 !important;
  vertical-align: middle !important;
}

.add-folder-icon {
  font-size: 16px;
  color: #666;
  cursor: pointer;
}

.folder-text {
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

.folder-list {
  flex: 1;
  padding: 10px 0;
}

.folder-item {
  display: flex;
  align-items: center;
  padding: 8px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
  gap: 8px;
}

.folder-item:hover {
  background: #f5f5f5;
}

.folder-item.active {
  background: #e6f3ff;
}

.folder-icon {
  font-size: 16px;
}

.folder-name {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.folder-count {
  font-size: 12px;
  color: #999;
}

/* 右侧文件区域 */
.files-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

/* 文件操作栏 */
.files-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.file-actions {
  display: flex;
  gap: 15px;
}

.action-text {
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

.action-text:hover {
  color: #4a90e2;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 14px;
  color: #666;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 2px;
}

/* 文件内容区域 */
.files-content {
  flex: 1;
  overflow: auto;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.empty-text {
  font-size: 14px;
}

/* 文件表格 */
.files-table {
  width: 100%;
}

.table-header {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.table-body {
  background: white;
}

.table-row {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-row.selected {
  background: #e6f3ff;
}

.header-cell,
.cell {
  display: flex;
  align-items: center;
}

.checkbox-cell {
  width: 50px;
  justify-content: center;
}

.name-cell {
  flex: 1;
  min-width: 200px;
}

.size-cell {
  width: 100px;
}

.time-cell {
  width: 150px;
}

.file-icon {
  margin-right: 8px;
  font-size: 16px;
}

.file-name {
  font-size: 14px;
  color: #333;
}

/* 上传进度样式 */
.upload-progress {
  .progress-item {
    margin-bottom: 15px;

    .progress-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;

      .file-name {
        font-size: 14px;
        color: #333;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .progress-text {
        font-size: 12px;
        color: #666;
        margin-left: 10px;
      }
    }
  }
}

.storage-selector {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.text-success {
  color: #67c23a;
  font-weight: 500;
}

.text-danger {
  color: #f56c6c;
  font-weight: 500;
}

/* OSS配置对话框样式 */
.oss-config-dialog {
  .el-dialog__header {
    padding: 20px 20px 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    padding: 20px;
    border-top: 1px solid #f0f0f0;
  }
}

.oss-config-content {
  padding: 20px;
}

.storage-type-section {
  margin-bottom: 30px;

  .section-label {
    font-size: 14px;
    color: #333;
    margin-bottom: 15px;
    font-weight: 500;
  }

  .storage-options {
    margin-bottom: 10px;
  }

  .storage-radio {
    .radio-text {
      font-size: 14px;
      color: #333;
    }
  }

  .storage-description {
    font-size: 12px;
    color: #999;
    line-height: 1.5;
  }
}

.oss-form-section {
  .form-row {
    margin-bottom: 20px;

    .form-label {
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;

      &.required::before {
        content: '*';
        color: #f56c6c;
        margin-right: 4px;
      }
    }

    .form-input {
      width: 100%;

      .el-input__inner {
        border-radius: 4px;
        border: 1px solid #dcdfe6;
        padding: 0 15px;
        height: 40px;
        line-height: 40px;

        &:focus {
          border-color: #409eff;
        }
      }
    }

    .form-hint {
      font-size: 12px;
      color: #999;
      margin-top: 5px;
      line-height: 1.4;
    }
  }

  .status-options {
    display: flex;
    gap: 20px;

    .status-radio {
      .radio-text {
        font-size: 14px;
        color: #333;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;

  .cancel-btn {
    padding: 8px 20px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    background: #fff;
    color: #606266;

    &:hover {
      color: #409eff;
      border-color: #c6e2ff;
      background-color: #ecf5ff;
    }
  }

  .confirm-btn {
    padding: 8px 20px;
    border-radius: 4px;
    background: #409eff;
    border-color: #409eff;
    color: #fff;

    &:hover {
      background: #66b1ff;
      border-color: #66b1ff;
    }
  }
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 9999;
  min-width: 120px;
  padding: 4px 0;

  .menu-item {
    padding: 8px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #606266;
    transition: all 0.3s;

    i {
      margin-right: 8px;
      font-size: 14px;
    }

    &:hover {
      background-color: #f5f7fa;
      color: #409eff;
    }

    &:active {
      background-color: #e6f7ff;
    }
  }
}

/* 文件夹项样式增强 */
.folder-item {
  position: relative;
  user-select: none;

  &:hover {
    background-color: #f5f7fa;
  }

  &.active {
    background-color: #e6f7ff;
    color: #409eff;
  }
}

/* 文件夹操作按钮样式已在上面定义，这里删除重复定义 */

/* OSS未配置提示样式 */
.no-oss-tip {
  padding: 20px;
  text-align: center;
  color: #909399;

  .tip-icon {
    font-size: 32px;
    margin-bottom: 10px;
  }

  .tip-text {
    margin-bottom: 15px;
    font-size: 14px;
  }
}

.no-oss-files-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;

  .tip-content {
    text-align: center;

    .tip-icon {
      font-size: 64px;
      margin-bottom: 20px;
    }

    .tip-title {
      font-size: 18px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 10px;
    }

    .tip-description {
      font-size: 14px;
      color: #909399;
      margin-bottom: 20px;
    }
  }
}

/* 文件工具栏样式 */
.file-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .selected-count {
      color: #409eff;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .toolbar-right {
    display: flex;
    gap: 8px;
  }
}

/* 文件网格样式 */
.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 16px;
  padding: 16px;
}

.file-card {
  position: relative;
  background: #fff;
  border: 2px solid transparent;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1;

  &:hover {
    border-color: #409eff;
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);
    transform: translateY(-2px);
  }

  &.selected {
    border-color: #409eff;
    background-color: #f0f8ff;
  }

  // 播放时放大效果
  &.enlarged {
    transform: scale(1.15) translateY(-8px);
    z-index: 10;
    box-shadow: 0 8px 32px rgba(64, 158, 255, 0.4);
    border-color: #409eff;

    .file-thumbnail {
      border-radius: 8px;
    }
  }


}

// 视频放大样式 - 现代播放器设计
.file-card.scale-enlarged {
  position: fixed !important;
  top: 50vh !important;
  left: 50vw !important;
  transform: translate(-50%, -50%) !important;
  z-index: 9999999 !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4),
              0 0 0 2px rgba(255, 255, 255, 0.1) !important;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;

  // 隐藏文件信息
  .file-info {
    display: none !important;
  }

  // 视频区域
  .file-thumbnail {
    height: 100% !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    position: relative !important;

    .video-thumbnail {
      border-radius: 12px !important;
      position: relative !important;

      .thumbnail-video {
        border-radius: 12px !important;
      }
    }
  }
}

// 背景遮罩
.scale-enlarged-backdrop {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(8px) !important;
  z-index: 9999998 !important;
  animation: backdropFadeIn 0.3s ease !important;
}

// 发光边框动画
@keyframes borderGlow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

// 背景淡入动画
@keyframes backdropFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 小视频简洁播放按钮
.simple-play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  transition: all 0.3s ease;

  .play-button {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;

    i {
      font-size: 16px;
      color: #333;
      margin-left: 2px; // 播放图标视觉居中
    }

    &:hover {
      transform: scale(1.1);
      background: white;
    }
  }
}

// 放大视频的现代控件
.video-controls-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 20px 16px 12px;
  border-radius: 0 0 12px 12px;
  opacity: 0;
  transition: opacity 0.3s ease;

  .progress-container {
    margin-bottom: 12px;
    cursor: pointer;

    .progress-track {
      position: relative;
      height: 3px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 2px;

      .progress-fill {
        height: 100%;
        background: #fff;
        border-radius: 2px;
        transition: width 0.1s ease;
      }

      .progress-thumb {
        position: absolute;
        top: 50%;
        width: 10px;
        height: 10px;
        background: white;
        border-radius: 50%;
        transform: translate(-50%, -50%);
        opacity: 0;
        transition: all 0.2s ease;
      }
    }

    &:hover .progress-thumb {
      opacity: 1;
    }
  }

  .controls-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .play-pause-btn {
      width: 32px;
      height: 32px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;

      i {
        font-size: 14px;
        color: white;
        margin-left: 1px;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }

    .time-display {
      font-size: 12px;
      color: white;
      font-family: 'SF Mono', Monaco, monospace;

      .separator {
        margin: 0 4px;
        opacity: 0.7;
      }
    }
  }
}

// 鼠标悬停时显示控件
.video-thumbnail:hover .video-controls-overlay {
  opacity: 1;
}

// Portal悬停放大层样式
.hover-portal-layer {
  position: fixed !important;
  z-index: 999999 !important;
  pointer-events: auto !important;
  transition: all 0.3s ease !important;

  .hover-file-card {
    width: 100% !important;
    height: 100% !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 20px 60px rgba(64, 158, 255, 0.8) !important;
    border: 3px solid #409eff !important;
    background: #ffffff !important;
    transform: scale(1) !important;
    animation: portalFadeIn 0.3s ease !important;

    // 确保可见性
    opacity: 1 !important;
    display: block !important;

    // 视频样式
    .video-thumbnail {
      width: 100% !important;
      height: calc(100% - 60px) !important;
      position: relative !important;
      background: #000 !important;
      border-radius: 8px 8px 0 0 !important;

      .thumbnail-video {
        width: 100% !important;
        height: 100% !important;
        object-fit: cover !important;
        border-radius: 8px 8px 0 0 !important;
      }

      .duration-badge {
        position: absolute !important;
        bottom: 8px !important;
        right: 8px !important;
        background: rgba(0, 0, 0, 0.8) !important;
        color: white !important;
        padding: 4px 8px !important;
        border-radius: 4px !important;
        font-size: 12px !important;
      }

      // Portal视频控制按钮
      .portal-video-controls {
        position: absolute !important;
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8)) !important;
        padding: 20px 16px 12px !important;
        opacity: 0 !important;
        transform: translateY(10px) !important;
        transition: all 0.3s ease !important;
        pointer-events: auto !important;

        &.visible {
          opacity: 1 !important;
          transform: translateY(0) !important;
        }

        .portal-play-btn {
          position: absolute !important;
          top: -30px !important;
          left: 50% !important;
          transform: translateX(-50%) !important;
          width: 48px !important;
          height: 48px !important;
          background: rgba(64, 158, 255, 0.9) !important;
          border: none !important;
          border-radius: 50% !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          cursor: pointer !important;
          transition: all 0.3s ease !important;

          &:hover {
            background: #409eff !important;
            transform: translateX(-50%) scale(1.1) !important;
          }

          i {
            font-size: 20px !important;
            color: white !important;
          }
        }

        .portal-info {
          color: white !important;
          font-size: 12px !important;
          text-align: center !important;
          background: rgba(0, 0, 0, 0.5) !important;
          padding: 4px 8px !important;
          border-radius: 4px !important;
          margin-top: 8px !important;
        }
      }
    }

    // 音频样式
    .audio-thumbnail {
      width: 100% !important;
      height: calc(100% - 60px) !important;
      position: relative !important;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
      border-radius: 8px 8px 0 0 !important;
      display: flex !important;
      flex-direction: column !important;
      align-items: center !important;
      justify-content: center !important;

      .audio-icon {
        font-size: 48px !important;
        margin-bottom: 16px !important;
        color: white !important;
      }

      .audio-waveform {
        display: flex !important;
        align-items: center !important;
        gap: 3px !important;

        .wave-bar {
          width: 4px !important;
          background: rgba(255, 255, 255, 0.8) !important;
          border-radius: 2px !important;
          animation: audioWave 1.5s ease-in-out infinite !important;

          &:nth-child(1) { height: 20px !important; animation-delay: 0s !important; }
          &:nth-child(2) { height: 35px !important; animation-delay: 0.1s !important; }
          &:nth-child(3) { height: 25px !important; animation-delay: 0.2s !important; }
          &:nth-child(4) { height: 40px !important; animation-delay: 0.3s !important; }
          &:nth-child(5) { height: 30px !important; animation-delay: 0.4s !important; }
          &:nth-child(6) { height: 45px !important; animation-delay: 0.5s !important; }
          &:nth-child(7) { height: 35px !important; animation-delay: 0.6s !important; }
          &:nth-child(8) { height: 25px !important; animation-delay: 0.7s !important; }
          &:nth-child(9) { height: 40px !important; animation-delay: 0.8s !important; }
          &:nth-child(10) { height: 30px !important; animation-delay: 0.9s !important; }
          &:nth-child(11) { height: 35px !important; animation-delay: 1.0s !important; }
          &:nth-child(12) { height: 20px !important; animation-delay: 1.1s !important; }
        }
      }

      .duration-badge {
        position: absolute !important;
        bottom: 8px !important;
        right: 8px !important;
        background: rgba(0, 0, 0, 0.8) !important;
        color: white !important;
        padding: 4px 8px !important;
        border-radius: 4px !important;
        font-size: 12px !important;
      }
    }

    // 图片样式
    .image-thumbnail {
      width: 100% !important;
      height: calc(100% - 60px) !important;
      position: relative !important;
      border-radius: 8px 8px 0 0 !important;
      overflow: hidden !important;

      .thumbnail-image {
        width: 100% !important;
        height: 100% !important;
        object-fit: cover !important;
        border-radius: 8px 8px 0 0 !important;
      }
    }

    // 文件信息
    .file-info {
      height: 60px !important;
      padding: 12px 16px !important;
      background: #f8f9fa !important;
      border-radius: 0 0 8px 8px !important;

      .file-name {
        font-size: 14px !important;
        font-weight: 600 !important;
        color: #333 !important;
        margin-bottom: 4px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }

      .file-meta {
        display: flex !important;
        gap: 12px !important;
        font-size: 12px !important;
        color: #666 !important;
      }
    }
  }
}

// Portal动画
@keyframes portalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes audioWave {
  0%, 100% {
    transform: scaleY(0.5);
    opacity: 0.7;
  }
  50% {
    transform: scaleY(1);
    opacity: 1;
  }
}

.file-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 2px;
}

.file-thumbnail {
  width: 100%;
  height: 120px;
  position: relative;
  overflow: hidden;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;


}

/* 视频缩略图样式 */
.video-thumbnail {
  position: relative;
  width: 100%;
  height: 100%;

  .thumbnail-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }



  .duration-badge {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
  }
}

/* 音频缩略图样式 */
.audio-thumbnail {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.4s ease;

  &:hover {
    transform: scale(1.05);
  }

  &.playing {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  }

  .audio-icon {
    font-size: 32px;
    margin-bottom: 8px;
  }

  .audio-waveform {
    display: flex;
    align-items: flex-end;
    gap: 2px;
    height: 20px;

    .wave-bar {
      width: 3px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 2px;
      animation: wave 1.5s ease-in-out infinite;

      &:nth-child(1) { height: 8px; animation-delay: 0s; }
      &:nth-child(2) { height: 12px; animation-delay: 0.1s; }
      &:nth-child(3) { height: 16px; animation-delay: 0.2s; }
      &:nth-child(4) { height: 10px; animation-delay: 0.3s; }
      &:nth-child(5) { height: 14px; animation-delay: 0.4s; }
      &:nth-child(6) { height: 18px; animation-delay: 0.5s; }
      &:nth-child(7) { height: 12px; animation-delay: 0.6s; }
      &:nth-child(8) { height: 8px; animation-delay: 0.7s; }
      &:nth-child(9) { height: 15px; animation-delay: 0.8s; }
      &:nth-child(10) { height: 11px; animation-delay: 0.9s; }
      &:nth-child(11) { height: 9px; animation-delay: 1s; }
      &:nth-child(12) { height: 13px; animation-delay: 1.1s; }
    }
  }

  .play-overlay-audio {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    opacity: 1;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      background: rgba(0, 0, 0, 0.9);
      transform: translate(-50%, -50%) scale(1.1);
    }

    &.playing {
      background: rgba(255, 0, 0, 0.8);

      &:hover {
        background: rgba(255, 0, 0, 1);
      }
    }

    &.visible {
      opacity: 1;
      visibility: visible;
    }

    &.hidden {
      opacity: 0;
      visibility: hidden;
    }
  }

  .duration-badge {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
  }
}

@keyframes wave {
  0%, 100% { transform: scaleY(0.5); }
  50% { transform: scaleY(1); }
}

/* 图片缩略图样式 */
.image-thumbnail {
  position: relative;
  width: 100%;
  height: 100%;
  cursor: pointer;
  overflow: hidden;

  &:hover {
    .thumbnail-image {
      transform: scale(1.05);
    }

    .image-overlay {
      opacity: 1;
    }
  }

  .thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;

    i {
      color: white;
      font-size: 24px;
    }
  }
}

/* 文件图标缩略图样式 */
.file-icon-thumbnail {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: #909399;
}



/* 文件信息样式 */
.file-card .file-info {
  padding: 12px;

  .file-name {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .file-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #909399;

    .file-size {
      font-weight: 500;
    }
  }
}

/* 文件预览对话框样式 */
.preview-dialog {
  .el-dialog__body {
    padding: 20px;
  }
}

.preview-content {
  .preview-header {
    margin-bottom: 20px;

    h3 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 18px;
    }

    .file-info {
      display: flex;
      gap: 20px;
      color: #909399;
      font-size: 14px;
    }
  }

  .video-preview {
    text-align: center;

    video {
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
  }

  .audio-preview {
    .audio-player {
      margin-bottom: 20px;
      text-align: center;
    }

    .audio-info {
      display: flex;
      align-items: center;
      padding: 20px;
      background-color: #f8f9fa;
      border-radius: 8px;

      .audio-icon {
        font-size: 48px;
        margin-right: 20px;
      }

      .audio-details {
        p {
          margin: 5px 0;
          color: #606266;
        }
      }
    }
  }

  .image-preview {
    text-align: center;

    img {
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
  }

  .unsupported-preview {
    text-align: center;
    padding: 40px;

    .unsupported-icon {
      font-size: 64px;
      margin-bottom: 20px;
    }

    p {
      color: #909399;
      margin-bottom: 20px;
    }
  }
}

/* OSS未配置提示样式 */
.no-oss-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #909399;

  .tip-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .tip-text {
    font-size: 14px;
    margin-bottom: 16px;
    color: #606266;
  }
}

.no-oss-files-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;

  .tip-content {
    text-align: center;

    .tip-icon {
      font-size: 64px;
      margin-bottom: 20px;
    }

    .tip-title {
      font-size: 18px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 12px;
    }

    .tip-description {
      font-size: 14px;
      color: #909399;
      margin-bottom: 24px;
    }
  }
}





/* 响应式设计 */
@media (max-width: 1200px) {
  .folder-sidebar {
    width: 150px;
  }
}

@media (max-width: 768px) {
  .materials-main {
    flex-direction: column;
  }

  .folder-sidebar {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid #e8e8e8;
  }

  .files-toolbar {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .toolbar-right {
    align-self: flex-end;
  }
}
</style>
