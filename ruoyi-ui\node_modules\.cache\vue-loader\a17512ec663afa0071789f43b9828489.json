{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\up.vue?vue&type=style&index=0&id=ee083fba&lang=scss&scoped=true", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\up.vue", "mtime": 1754971237942}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753759480805}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753759474011}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753759476521}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1753759475309}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQovKiDntKDmnZDnrqHnkIbnlYzpnaIgLSDlrozlhajmjInnhaflm77niYforr7orqEgKi8NCi51cC1jb250YWluZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBtYXJnaW46IDA7DQogIGhlaWdodDogMTAwdmg7DQp9DQoNCi8qIOmhtumDqOagh+etvumhtSAqLw0KLm1hdGVyaWFscy10YWJzIHsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIHBhZGRpbmc6IDA7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZThlOGU4Ow0KfQ0KDQoudGFiLWJ1dHRvbnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDA7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi50YWItYnV0dG9uIHsNCiAgYm9yZGVyLXJhZGl1czogMjBweCAhaW1wb3J0YW50Ow0KICBtYXJnaW46IDEwcHggNXB4Ow0KICBwYWRkaW5nOiA4cHggMjBweDsNCiAgYm9yZGVyOiBub25lOw0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi50YWItYnV0dG9uLmVsLWJ1dHRvbi0tcHJpbWFyeSB7DQogIGJhY2tncm91bmQ6ICM0YTkwZTI7DQogIGNvbG9yOiB3aGl0ZTsNCn0NCg0KLnRhYi1idXR0b24uZWwtYnV0dG9uLS1kZWZhdWx0IHsNCiAgYmFja2dyb3VuZDogI2U4ZThlODsNCiAgY29sb3I6ICM2NjY7DQp9DQoNCi8qIEJHTeWFjei0ueS4i+i9veaMiemSruagt+W8jyAqLw0KLmJnbS1kb3dubG9hZC1idXR0b24gew0KICBtYXJnaW4tbGVmdDogMjBweCAhaW1wb3J0YW50Ow0KICBib3JkZXItcmFkaXVzOiAxNXB4ICFpbXBvcnRhbnQ7DQogIHBhZGRpbmc6IDZweCAxNnB4ICFpbXBvcnRhbnQ7DQogIGZvbnQtc2l6ZTogMTNweCAhaW1wb3J0YW50Ow0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjdiMjZmIDAlLCAjNGNhMmNkIDEwMCUpICFpbXBvcnRhbnQ7DQogIGJvcmRlcjogbm9uZSAhaW1wb3J0YW50Ow0KICBjb2xvcjogd2hpdGUgIWltcG9ydGFudDsNCiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoNzYsIDE2MiwgMjA1LCAwLjMpICFpbXBvcnRhbnQ7DQogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2UgIWltcG9ydGFudDsNCn0NCg0KLmJnbS1kb3dubG9hZC1idXR0b246aG92ZXIgew0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCkgIWltcG9ydGFudDsNCiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDc2LCAxNjIsIDIwNSwgMC40KSAhaW1wb3J0YW50Ow0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNWE5ZjYzIDAlLCAjM2Q4YmI4IDEwMCUpICFpbXBvcnRhbnQ7DQp9DQoNCi5iZ20tZG93bmxvYWQtYnV0dG9uOmFjdGl2ZSB7DQogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKSAhaW1wb3J0YW50Ow0KfQ0KDQovKiDkuLvopoHlhoXlrrnljLrln58gKi8NCi5tYXRlcmlhbHMtbWFpbiB7DQogIGZsZXg6IDE7DQogIGRpc3BsYXk6IGZsZXg7DQogIGJhY2tncm91bmQ6IHdoaXRlOw0KfQ0KDQovKiDlt6bkvqfmlofku7blpLnmoJEgLSDmjInnhaflm77niYfmoLflvI8gKi8NCi5mb2xkZXItc2lkZWJhciB7DQogIHdpZHRoOiAyMDBweDsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIGJvcmRlci1yaWdodDogMXB4IHNvbGlkICNlOGU4ZTg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQp9DQoNCi5mb2xkZXItaGVhZGVyIHsNCiAgcGFkZGluZzogOHB4IDEycHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZThlOGU4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFmYWZhOw0KfQ0KDQouZm9sZGVyLWFjdGlvbnMgew0KICBkaXNwbGF5OiBmbGV4ICFpbXBvcnRhbnQ7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW4gIWltcG9ydGFudDsgLyog5Z6C55u05o6S5YiXICovDQogIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0ICFpbXBvcnRhbnQ7IC8qIOW3puWvuem9kCAqLw0KICBnYXA6IDhweDsgLyog5LiK5LiL6Ze06LedICovDQogIHBhZGRpbmc6IDhweCAwOw0KfQ0KDQovKiDkvb/nlKjmm7TlvLrnmoTpgInmi6nlmajopobnm5ZFbGVtZW50IFVJ5qC35byPICovDQouZm9sZGVyLWFjdGlvbnMgLmVsLWJ1dHRvbiwNCi5mb2xkZXItYWN0aW9ucyAuZWwtYnV0dG9uLmVsLWJ1dHRvbi0tc21hbGwsDQouZm9sZGVyLWFjdGlvbnMgLmVsLWJ1dHRvbi5lbC1idXR0b24tLXByaW1hcnksDQouZm9sZGVyLWFjdGlvbnMgLmVsLWJ1dHRvbi5lbC1idXR0b24tLXN1Y2Nlc3Mgew0KICBib3JkZXItcmFkaXVzOiA2cHggIWltcG9ydGFudDsNCiAgZm9udC13ZWlnaHQ6IDUwMCAhaW1wb3J0YW50Ow0KICBmb250LXNpemU6IDEzcHggIWltcG9ydGFudDsNCiAgaGVpZ2h0OiAzMnB4ICFpbXBvcnRhbnQ7DQogIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7IC8qIOWeguebtOaOkuWIl+aXtuWNoOa7oeWuveW6piAqLw0KICBtYXgtd2lkdGg6IDEyMHB4ICFpbXBvcnRhbnQ7IC8qIOmZkOWItuacgOWkp+WuveW6piAqLw0KICBtaW4taGVpZ2h0OiAzMnB4ICFpbXBvcnRhbnQ7DQogIG1heC1oZWlnaHQ6IDMycHggIWltcG9ydGFudDsNCiAgbGluZS1oZWlnaHQ6IDEgIWltcG9ydGFudDsNCiAgZGlzcGxheTogaW5saW5lLWZsZXggIWltcG9ydGFudDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlciAhaW1wb3J0YW50Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlciAhaW1wb3J0YW50Ow0KICBwYWRkaW5nOiAwIDE2cHggIWltcG9ydGFudDsNCiAgbWFyZ2luOiAwICFpbXBvcnRhbnQ7DQogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQogIGZsZXgtc2hyaW5rOiAwOw0KICB2ZXJ0aWNhbC1hbGlnbjogYmFzZWxpbmUgIWltcG9ydGFudDsNCiAgYm94LXNpemluZzogYm9yZGVyLWJveCAhaW1wb3J0YW50Ow0KICBib3JkZXItd2lkdGg6IDFweCAhaW1wb3J0YW50Ow0KfQ0KDQouZm9sZGVyLWFjdGlvbnMgLmVsLWJ1dHRvbjpob3ZlciB7DQogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTsNCiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCn0NCg0KLyog5Zu+5qCH5qC35byPICovDQouZm9sZGVyLWFjdGlvbnMgLmVsLWJ1dHRvbiBpIHsNCiAgbWFyZ2luLXJpZ2h0OiA0cHggIWltcG9ydGFudDsNCiAgbGluZS1oZWlnaHQ6IDEgIWltcG9ydGFudDsNCiAgdmVydGljYWwtYWxpZ246IG1pZGRsZSAhaW1wb3J0YW50Ow0KfQ0KDQovKiDmjInpkq7mloflrZfmoLflvI8gKi8NCi5mb2xkZXItYWN0aW9ucyAuZWwtYnV0dG9uIHNwYW4gew0KICBsaW5lLWhlaWdodDogMSAhaW1wb3J0YW50Ow0KICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlICFpbXBvcnRhbnQ7DQp9DQoNCi5hZGQtZm9sZGVyLWljb24gew0KICBmb250LXNpemU6IDE2cHg7DQogIGNvbG9yOiAjNjY2Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQp9DQoNCi5mb2xkZXItdGV4dCB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICM2NjY7DQogIGN1cnNvcjogcG9pbnRlcjsNCn0NCg0KLmZvbGRlci1saXN0IHsNCiAgZmxleDogMTsNCiAgcGFkZGluZzogMTBweCAwOw0KfQ0KDQouZm9sZGVyLWl0ZW0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nOiA4cHggMTVweDsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIDAuMnM7DQogIGdhcDogOHB4Ow0KfQ0KDQouZm9sZGVyLWl0ZW06aG92ZXIgew0KICBiYWNrZ3JvdW5kOiAjZjVmNWY1Ow0KfQ0KDQouZm9sZGVyLWl0ZW0uYWN0aXZlIHsNCiAgYmFja2dyb3VuZDogI2U2ZjNmZjsNCn0NCg0KLmZvbGRlci1pY29uIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KfQ0KDQouZm9sZGVyLW5hbWUgew0KICBmbGV4OiAxOw0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjMzMzOw0KfQ0KDQouZm9sZGVyLWNvdW50IHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjb2xvcjogIzk5OTsNCn0NCg0KLyog5Y+z5L6n5paH5Lu25Yy65Z+fICovDQouZmlsZXMtYXJlYSB7DQogIGZsZXg6IDE7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGJhY2tncm91bmQ6IHdoaXRlOw0KfQ0KDQovKiDmlofku7bmk43kvZzmoI8gKi8NCi5maWxlcy10b29sYmFyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nOiAxMnB4IDIwcHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZThlOGU4Ow0KICBiYWNrZ3JvdW5kOiAjZmFmYWZhOw0KfQ0KDQoudG9vbGJhci1sZWZ0IHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiAxNXB4Ow0KfQ0KDQouZmlsZS1hY3Rpb25zIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZ2FwOiAxNXB4Ow0KfQ0KDQouYWN0aW9uLXRleHQgew0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjNjY2Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQp9DQoNCi5hY3Rpb24tdGV4dDpob3ZlciB7DQogIGNvbG9yOiAjNGE5MGUyOw0KfQ0KDQoudG9vbGJhci1yaWdodCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogMTVweDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogIzY2NjsNCn0NCg0KLnBhZ2luYXRpb24taW5mbyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogMnB4Ow0KfQ0KDQovKiDmlofku7blhoXlrrnljLrln58gKi8NCi5maWxlcy1jb250ZW50IHsNCiAgZmxleDogMTsNCiAgb3ZlcmZsb3c6IGF1dG87DQp9DQoNCi5lbXB0eS1zdGF0ZSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBoZWlnaHQ6IDMwMHB4Ow0KICBjb2xvcjogIzk5OTsNCn0NCg0KLmVtcHR5LWljb24gew0KICBmb250LXNpemU6IDQ4cHg7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQoNCi5lbXB0eS10ZXh0IHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KDQovKiDmlofku7booajmoLwgKi8NCi5maWxlcy10YWJsZSB7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQoudGFibGUtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgcGFkZGluZzogMTJweCAyMHB4Ow0KICBiYWNrZ3JvdW5kOiAjZmFmYWZhOw0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U4ZThlODsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogIzY2NjsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLnRhYmxlLWJvZHkgew0KICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCn0NCg0KLnRhYmxlLXJvdyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHBhZGRpbmc6IDEycHggMjBweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7DQogIGN1cnNvcjogcG9pbnRlcjsNCiAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciAwLjJzOw0KfQ0KDQoudGFibGUtcm93OmhvdmVyIHsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCn0NCg0KLnRhYmxlLXJvdy5zZWxlY3RlZCB7DQogIGJhY2tncm91bmQ6ICNlNmYzZmY7DQp9DQoNCi5oZWFkZXItY2VsbCwNCi5jZWxsIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLmNoZWNrYm94LWNlbGwgew0KICB3aWR0aDogNTBweDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQp9DQoNCi5uYW1lLWNlbGwgew0KICBmbGV4OiAxOw0KICBtaW4td2lkdGg6IDIwMHB4Ow0KfQ0KDQouc2l6ZS1jZWxsIHsNCiAgd2lkdGg6IDEwMHB4Ow0KfQ0KDQoudGltZS1jZWxsIHsNCiAgd2lkdGg6IDE1MHB4Ow0KfQ0KDQouZmlsZS1pY29uIHsNCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogIGZvbnQtc2l6ZTogMTZweDsNCn0NCg0KLmZpbGUtbmFtZSB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICMzMzM7DQp9DQoNCi8qIOS4iuS8oOi/m+W6puagt+W8jyAqLw0KLnVwbG9hZC1wcm9ncmVzcyB7DQogIC5wcm9ncmVzcy1pdGVtIHsNCiAgICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KDQogICAgLnByb2dyZXNzLWluZm8gew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBtYXJnaW4tYm90dG9tOiA1cHg7DQoNCiAgICAgIC5maWxlLW5hbWUgew0KICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgIGNvbG9yOiAjMzMzOw0KICAgICAgICBmbGV4OiAxOw0KICAgICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgICAgIH0NCg0KICAgICAgLnByb2dyZXNzLXRleHQgew0KICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgIGNvbG9yOiAjNjY2Ow0KICAgICAgICBtYXJnaW4tbGVmdDogMTBweDsNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KLnN0b3JhZ2Utc2VsZWN0b3Igew0KICBwYWRkaW5nOiAxNXB4Ow0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlOGU4ZTg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGZsZXgtd3JhcDogd3JhcDsNCiAgZ2FwOiAxMHB4Ow0KfQ0KDQoudGV4dC1zdWNjZXNzIHsNCiAgY29sb3I6ICM2N2MyM2E7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQoNCi50ZXh0LWRhbmdlciB7DQogIGNvbG9yOiAjZjU2YzZjOw0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQovKiBPU1PphY3nva7lr7nor53moYbmoLflvI8gKi8NCi5vc3MtY29uZmlnLWRpYWxvZyB7DQogIC5lbC1kaWFsb2dfX2hlYWRlciB7DQogICAgcGFkZGluZzogMjBweCAyMHB4IDA7DQogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7DQogIH0NCg0KICAuZWwtZGlhbG9nX19ib2R5IHsNCiAgICBwYWRkaW5nOiAwOw0KICB9DQoNCiAgLmVsLWRpYWxvZ19fZm9vdGVyIHsNCiAgICBwYWRkaW5nOiAyMHB4Ow0KICAgIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZjBmMGYwOw0KICB9DQp9DQoNCi5vc3MtY29uZmlnLWNvbnRlbnQgew0KICBwYWRkaW5nOiAyMHB4Ow0KfQ0KDQouc3RvcmFnZS10eXBlLXNlY3Rpb24gew0KICBtYXJnaW4tYm90dG9tOiAzMHB4Ow0KDQogIC5zZWN0aW9uLWxhYmVsIHsNCiAgICBmb250LXNpemU6IDE0cHg7DQogICAgY29sb3I6ICMzMzM7DQogICAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgICBmb250LXdlaWdodDogNTAwOw0KICB9DQoNCiAgLnN0b3JhZ2Utb3B0aW9ucyB7DQogICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgfQ0KDQogIC5zdG9yYWdlLXJhZGlvIHsNCiAgICAucmFkaW8tdGV4dCB7DQogICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICBjb2xvcjogIzMzMzsNCiAgICB9DQogIH0NCg0KICAuc3RvcmFnZS1kZXNjcmlwdGlvbiB7DQogICAgZm9udC1zaXplOiAxMnB4Ow0KICAgIGNvbG9yOiAjOTk5Ow0KICAgIGxpbmUtaGVpZ2h0OiAxLjU7DQogIH0NCn0NCg0KLm9zcy1mb3JtLXNlY3Rpb24gew0KICAuZm9ybS1yb3cgew0KICAgIG1hcmdpbi1ib3R0b206IDIwcHg7DQoNCiAgICAuZm9ybS1sYWJlbCB7DQogICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICBjb2xvcjogIzMzMzsNCiAgICAgIG1hcmdpbi1ib3R0b206IDhweDsNCg0KICAgICAgJi5yZXF1aXJlZDo6YmVmb3JlIHsNCiAgICAgICAgY29udGVudDogJyonOw0KICAgICAgICBjb2xvcjogI2Y1NmM2YzsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiA0cHg7DQogICAgICB9DQogICAgfQ0KDQogICAgLmZvcm0taW5wdXQgew0KICAgICAgd2lkdGg6IDEwMCU7DQoNCiAgICAgIC5lbC1pbnB1dF9faW5uZXIgew0KICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkY2RmZTY7DQogICAgICAgIHBhZGRpbmc6IDAgMTVweDsNCiAgICAgICAgaGVpZ2h0OiA0MHB4Ow0KICAgICAgICBsaW5lLWhlaWdodDogNDBweDsNCg0KICAgICAgICAmOmZvY3VzIHsNCiAgICAgICAgICBib3JkZXItY29sb3I6ICM0MDllZmY7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQoNCiAgICAuZm9ybS1oaW50IHsNCiAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgIGNvbG9yOiAjOTk5Ow0KICAgICAgbWFyZ2luLXRvcDogNXB4Ow0KICAgICAgbGluZS1oZWlnaHQ6IDEuNDsNCiAgICB9DQogIH0NCg0KICAuc3RhdHVzLW9wdGlvbnMgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZ2FwOiAyMHB4Ow0KDQogICAgLnN0YXR1cy1yYWRpbyB7DQogICAgICAucmFkaW8tdGV4dCB7DQogICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgY29sb3I6ICMzMzM7DQogICAgICB9DQogICAgfQ0KICB9DQp9DQoNCi5kaWFsb2ctZm9vdGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsNCiAgZ2FwOiAxMHB4Ow0KDQogIC5jYW5jZWwtYnRuIHsNCiAgICBwYWRkaW5nOiA4cHggMjBweDsNCiAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgYm9yZGVyOiAxcHggc29saWQgI2RjZGZlNjsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgIGNvbG9yOiAjNjA2MjY2Ow0KDQogICAgJjpob3ZlciB7DQogICAgICBjb2xvcjogIzQwOWVmZjsNCiAgICAgIGJvcmRlci1jb2xvcjogI2M2ZTJmZjsNCiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlY2Y1ZmY7DQogICAgfQ0KICB9DQoNCiAgLmNvbmZpcm0tYnRuIHsNCiAgICBwYWRkaW5nOiA4cHggMjBweDsNCiAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgYmFja2dyb3VuZDogIzQwOWVmZjsNCiAgICBib3JkZXItY29sb3I6ICM0MDllZmY7DQogICAgY29sb3I6ICNmZmY7DQoNCiAgICAmOmhvdmVyIHsNCiAgICAgIGJhY2tncm91bmQ6ICM2NmIxZmY7DQogICAgICBib3JkZXItY29sb3I6ICM2NmIxZmY7DQogICAgfQ0KICB9DQp9DQoNCi8qIOWPs+mUruiPnOWNleagt+W8jyAqLw0KLmNvbnRleHQtbWVudSB7DQogIHBvc2l0aW9uOiBmaXhlZDsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTdlZDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBib3gtc2hhZG93OiAwIDJweCAxMnB4IDAgcmdiYSgwLCAwLCAwLCAwLjEpOw0KICB6LWluZGV4OiA5OTk5Ow0KICBtaW4td2lkdGg6IDEyMHB4Ow0KICBwYWRkaW5nOiA0cHggMDsNCg0KICAubWVudS1pdGVtIHsNCiAgICBwYWRkaW5nOiA4cHggMTZweDsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICBjb2xvcjogIzYwNjI2NjsNCiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zczsNCg0KICAgIGkgew0KICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogICAgICBmb250LXNpemU6IDE0cHg7DQogICAgfQ0KDQogICAgJjpob3ZlciB7DQogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KICAgICAgY29sb3I6ICM0MDllZmY7DQogICAgfQ0KDQogICAgJjphY3RpdmUgew0KICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2U2ZjdmZjsNCiAgICB9DQogIH0NCn0NCg0KLyog5paH5Lu25aS56aG55qC35byP5aKe5by6ICovDQouZm9sZGVyLWl0ZW0gew0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIHVzZXItc2VsZWN0OiBub25lOw0KDQogICY6aG92ZXIgew0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQogIH0NCg0KICAmLmFjdGl2ZSB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2U2ZjdmZjsNCiAgICBjb2xvcjogIzQwOWVmZjsNCiAgfQ0KfQ0KDQovKiDmlofku7blpLnmk43kvZzmjInpkq7moLflvI/lt7LlnKjkuIrpnaLlrprkuYnvvIzov5nph4zliKDpmaTph43lpI3lrprkuYkgKi8NCg0KLyogT1NT5pyq6YWN572u5o+Q56S65qC35byPICovDQoubm8tb3NzLXRpcCB7DQogIHBhZGRpbmc6IDIwcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgY29sb3I6ICM5MDkzOTk7DQoNCiAgLnRpcC1pY29uIHsNCiAgICBmb250LXNpemU6IDMycHg7DQogICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgfQ0KDQogIC50aXAtdGV4dCB7DQogICAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgICBmb250LXNpemU6IDE0cHg7DQogIH0NCn0NCg0KLm5vLW9zcy1maWxlcy10aXAgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgaGVpZ2h0OiA0MDBweDsNCg0KICAudGlwLWNvbnRlbnQgew0KICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCg0KICAgIC50aXAtaWNvbiB7DQogICAgICBmb250LXNpemU6IDY0cHg7DQogICAgICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICAgIH0NCg0KICAgIC50aXAtdGl0bGUgew0KICAgICAgZm9udC1zaXplOiAxOHB4Ow0KICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgIGNvbG9yOiAjMzAzMTMzOw0KICAgICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgICB9DQoNCiAgICAudGlwLWRlc2NyaXB0aW9uIHsNCiAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgIGNvbG9yOiAjOTA5Mzk5Ow0KICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgICB9DQogIH0NCn0NCg0KLyog5paH5Lu25bel5YW35qCP5qC35byPICovDQouZmlsZS10b29sYmFyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nOiAxMnB4IDE2cHg7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTllY2VmOw0KDQogIC50b29sYmFyLWxlZnQgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBnYXA6IDE2cHg7DQoNCiAgICAuc2VsZWN0ZWQtY291bnQgew0KICAgICAgY29sb3I6ICM0MDllZmY7DQogICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICBmb250LXdlaWdodDogNTAwOw0KICAgIH0NCiAgfQ0KDQogIC50b29sYmFyLXJpZ2h0IHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGdhcDogOHB4Ow0KICB9DQp9DQoNCi8qIOaWh+S7tue9keagvOagt+W8jyAqLw0KLmZpbGUtZ3JpZCB7DQogIGRpc3BsYXk6IGdyaWQ7DQogIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZmlsbCwgbWlubWF4KDE2MHB4LCAxZnIpKTsNCiAgZ2FwOiAxNnB4Ow0KICBwYWRkaW5nOiAxNnB4Ow0KfQ0KDQouZmlsZS1jYXJkIHsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3JkZXI6IDJweCBzb2xpZCB0cmFuc3BhcmVudDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBjdXJzb3I6IHBvaW50ZXI7DQogIHRyYW5zaXRpb246IGFsbCAwLjRzIGVhc2U7DQogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQogIHotaW5kZXg6IDE7DQoNCiAgJjpob3ZlciB7DQogICAgYm9yZGVyLWNvbG9yOiAjNDA5ZWZmOw0KICAgIGJveC1zaGFkb3c6IDAgNHB4IDE2cHggcmdiYSg2NCwgMTU4LCAyNTUsIDAuMik7DQogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpOw0KICB9DQoNCiAgJi5zZWxlY3RlZCB7DQogICAgYm9yZGVyLWNvbG9yOiAjNDA5ZWZmOw0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmMGY4ZmY7DQogIH0NCg0KICAvLyDmkq3mlL7ml7bmlL7lpKfmlYjmnpwNCiAgJi5lbmxhcmdlZCB7DQogICAgdHJhbnNmb3JtOiBzY2FsZSgxLjE1KSB0cmFuc2xhdGVZKC04cHgpOw0KICAgIHotaW5kZXg6IDEwOw0KICAgIGJveC1zaGFkb3c6IDAgOHB4IDMycHggcmdiYSg2NCwgMTU4LCAyNTUsIDAuNCk7DQogICAgYm9yZGVyLWNvbG9yOiAjNDA5ZWZmOw0KDQogICAgLmZpbGUtdGh1bWJuYWlsIHsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgICB9DQogIH0NCg0KDQp9DQoNCi8vIOinhumikeaUvuWkp+agt+W8jyAtIOeOsOS7o+aSreaUvuWZqOiuvuiuoQ0KLmZpbGUtY2FyZC5zY2FsZS1lbmxhcmdlZCB7DQogIHBvc2l0aW9uOiBmaXhlZCAhaW1wb3J0YW50Ow0KICB0b3A6IDUwdmggIWltcG9ydGFudDsNCiAgbGVmdDogNTB2dyAhaW1wb3J0YW50Ow0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgtNTAlLCAtNTAlKSAhaW1wb3J0YW50Ow0KICB6LWluZGV4OiA5OTk5OTk5ICFpbXBvcnRhbnQ7DQogIGJvcmRlci1yYWRpdXM6IDEycHggIWltcG9ydGFudDsNCiAgb3ZlcmZsb3c6IGhpZGRlbiAhaW1wb3J0YW50Ow0KICBib3gtc2hhZG93OiAwIDIwcHggNjBweCByZ2JhKDAsIDAsIDAsIDAuNCksDQogICAgICAgICAgICAgIDAgMCAwIDJweCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgIWltcG9ydGFudDsNCiAgdHJhbnNpdGlvbjogYWxsIDAuNHMgY3ViaWMtYmV6aWVyKDAuMjUsIDAuNDYsIDAuNDUsIDAuOTQpICFpbXBvcnRhbnQ7DQoNCiAgLy8g6ZqQ6JeP5paH5Lu25L+h5oGvDQogIC5maWxlLWluZm8gew0KICAgIGRpc3BsYXk6IG5vbmUgIWltcG9ydGFudDsNCiAgfQ0KDQogIC8vIOinhumikeWMuuWfnw0KICAuZmlsZS10aHVtYm5haWwgew0KICAgIGhlaWdodDogMTAwJSAhaW1wb3J0YW50Ow0KICAgIGJvcmRlci1yYWRpdXM6IDEycHggIWltcG9ydGFudDsNCiAgICBvdmVyZmxvdzogaGlkZGVuICFpbXBvcnRhbnQ7DQogICAgcG9zaXRpb246IHJlbGF0aXZlICFpbXBvcnRhbnQ7DQoNCiAgICAudmlkZW8tdGh1bWJuYWlsIHsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDEycHggIWltcG9ydGFudDsNCiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZSAhaW1wb3J0YW50Ow0KDQogICAgICAudGh1bWJuYWlsLXZpZGVvIHsNCiAgICAgICAgYm9yZGVyLXJhZGl1czogMTJweCAhaW1wb3J0YW50Ow0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0KDQovLyDog4zmma/pga7nvakNCi5zY2FsZS1lbmxhcmdlZC1iYWNrZHJvcCB7DQogIHBvc2l0aW9uOiBmaXhlZCAhaW1wb3J0YW50Ow0KICB0b3A6IDAgIWltcG9ydGFudDsNCiAgbGVmdDogMCAhaW1wb3J0YW50Ow0KICB3aWR0aDogMTAwdncgIWltcG9ydGFudDsNCiAgaGVpZ2h0OiAxMDB2aCAhaW1wb3J0YW50Ow0KICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuNykgIWltcG9ydGFudDsNCiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDhweCkgIWltcG9ydGFudDsNCiAgei1pbmRleDogOTk5OTk5OCAhaW1wb3J0YW50Ow0KICBhbmltYXRpb246IGJhY2tkcm9wRmFkZUluIDAuM3MgZWFzZSAhaW1wb3J0YW50Ow0KfQ0KDQovLyDlj5HlhYnovrnmoYbliqjnlLsNCkBrZXlmcmFtZXMgYm9yZGVyR2xvdyB7DQogIDAlLCAxMDAlIHsNCiAgICBvcGFjaXR5OiAxOw0KICAgIHRyYW5zZm9ybTogc2NhbGUoMSk7DQogIH0NCiAgNTAlIHsNCiAgICBvcGFjaXR5OiAwLjg7DQogICAgdHJhbnNmb3JtOiBzY2FsZSgxLjAyKTsNCiAgfQ0KfQ0KDQovLyDog4zmma/mt6HlhaXliqjnlLsNCkBrZXlmcmFtZXMgYmFja2Ryb3BGYWRlSW4gew0KICBmcm9tIHsNCiAgICBvcGFjaXR5OiAwOw0KICB9DQogIHRvIHsNCiAgICBvcGFjaXR5OiAxOw0KICB9DQp9DQoNCi8vIOWwj+inhumikeeugOa0geaSreaUvuaMiemSrg0KLnNpbXBsZS1wbGF5LW92ZXJsYXkgew0KICBwb3NpdGlvbjogYWJzb2x1dGU7DQogIHRvcDogMDsNCiAgbGVmdDogMDsNCiAgcmlnaHQ6IDA7DQogIGJvdHRvbTogMDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4zKTsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCg0KICAucGxheS1idXR0b24gew0KICAgIHdpZHRoOiA0MHB4Ow0KICAgIGhlaWdodDogNDBweDsNCiAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOSk7DQogICAgYm9yZGVyLXJhZGl1czogNTAlOw0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4yKTsNCiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KDQogICAgaSB7DQogICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICBjb2xvcjogIzMzMzsNCiAgICAgIG1hcmdpbi1sZWZ0OiAycHg7IC8vIOaSreaUvuWbvuagh+inhuinieWxheS4rQ0KICAgIH0NCg0KICAgICY6aG92ZXIgew0KICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpOw0KICAgICAgYmFja2dyb3VuZDogd2hpdGU7DQogICAgfQ0KICB9DQp9DQoNCi8vIOaUvuWkp+inhumikeeahOeOsOS7o+aOp+S7tg0KLnZpZGVvLWNvbnRyb2xzLW92ZXJsYXkgew0KICBwb3NpdGlvbjogYWJzb2x1dGU7DQogIGJvdHRvbTogMDsNCiAgbGVmdDogMDsNCiAgcmlnaHQ6IDA7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCh0cmFuc3BhcmVudCwgcmdiYSgwLCAwLCAwLCAwLjcpKTsNCiAgcGFkZGluZzogMjBweCAxNnB4IDEycHg7DQogIGJvcmRlci1yYWRpdXM6IDAgMCAxMnB4IDEycHg7DQogIG9wYWNpdHk6IDA7DQogIHRyYW5zaXRpb246IG9wYWNpdHkgMC4zcyBlYXNlOw0KDQogIC5wcm9ncmVzcy1jb250YWluZXIgew0KICAgIG1hcmdpbi1ib3R0b206IDEycHg7DQogICAgY3Vyc29yOiBwb2ludGVyOw0KDQogICAgLnByb2dyZXNzLXRyYWNrIHsNCiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICAgIGhlaWdodDogM3B4Ow0KICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpOw0KICAgICAgYm9yZGVyLXJhZGl1czogMnB4Ow0KDQogICAgICAucHJvZ3Jlc3MtZmlsbCB7DQogICAgICAgIGhlaWdodDogMTAwJTsNCiAgICAgICAgYmFja2dyb3VuZDogI2ZmZjsNCiAgICAgICAgYm9yZGVyLXJhZGl1czogMnB4Ow0KICAgICAgICB0cmFuc2l0aW9uOiB3aWR0aCAwLjFzIGVhc2U7DQogICAgICB9DQoNCiAgICAgIC5wcm9ncmVzcy10aHVtYiB7DQogICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgICAgdG9wOiA1MCU7DQogICAgICAgIHdpZHRoOiAxMHB4Ow0KICAgICAgICBoZWlnaHQ6IDEwcHg7DQogICAgICAgIGJhY2tncm91bmQ6IHdoaXRlOw0KICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7DQogICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlKC01MCUsIC01MCUpOw0KICAgICAgICBvcGFjaXR5OiAwOw0KICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlOw0KICAgICAgfQ0KICAgIH0NCg0KICAgICY6aG92ZXIgLnByb2dyZXNzLXRodW1iIHsNCiAgICAgIG9wYWNpdHk6IDE7DQogICAgfQ0KICB9DQoNCiAgLmNvbnRyb2xzLWJvdHRvbSB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCg0KICAgIC5wbGF5LXBhdXNlLWJ0biB7DQogICAgICB3aWR0aDogMzJweDsNCiAgICAgIGhlaWdodDogMzJweDsNCiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlOw0KDQogICAgICBpIHsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICBjb2xvcjogd2hpdGU7DQogICAgICAgIG1hcmdpbi1sZWZ0OiAxcHg7DQogICAgICB9DQoNCiAgICAgICY6aG92ZXIgew0KICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMyk7DQogICAgICB9DQogICAgfQ0KDQogICAgLnRpbWUtZGlzcGxheSB7DQogICAgICBmb250LXNpemU6IDEycHg7DQogICAgICBjb2xvcjogd2hpdGU7DQogICAgICBmb250LWZhbWlseTogJ1NGIE1vbm8nLCBNb25hY28sIG1vbm9zcGFjZTsNCg0KICAgICAgLnNlcGFyYXRvciB7DQogICAgICAgIG1hcmdpbjogMCA0cHg7DQogICAgICAgIG9wYWNpdHk6IDAuNzsNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KLy8g6byg5qCH5oKs5YGc5pe25pi+56S65o6n5Lu2DQoudmlkZW8tdGh1bWJuYWlsOmhvdmVyIC52aWRlby1jb250cm9scy1vdmVybGF5IHsNCiAgb3BhY2l0eTogMTsNCn0NCg0KLy8gUG9ydGFs5oKs5YGc5pS+5aSn5bGC5qC35byPDQouaG92ZXItcG9ydGFsLWxheWVyIHsNCiAgcG9zaXRpb246IGZpeGVkICFpbXBvcnRhbnQ7DQogIHotaW5kZXg6IDk5OTk5OSAhaW1wb3J0YW50Ow0KICBwb2ludGVyLWV2ZW50czogYXV0byAhaW1wb3J0YW50Ow0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlICFpbXBvcnRhbnQ7DQoNCiAgLmhvdmVyLWZpbGUtY2FyZCB7DQogICAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDsNCiAgICBoZWlnaHQ6IDEwMCUgIWltcG9ydGFudDsNCiAgICBib3JkZXItcmFkaXVzOiAxMnB4ICFpbXBvcnRhbnQ7DQogICAgb3ZlcmZsb3c6IGhpZGRlbiAhaW1wb3J0YW50Ow0KICAgIGJveC1zaGFkb3c6IDAgMjBweCA2MHB4IHJnYmEoNjQsIDE1OCwgMjU1LCAwLjgpICFpbXBvcnRhbnQ7DQogICAgYm9yZGVyOiAzcHggc29saWQgIzQwOWVmZiAhaW1wb3J0YW50Ow0KICAgIGJhY2tncm91bmQ6ICNmZmZmZmYgIWltcG9ydGFudDsNCiAgICB0cmFuc2Zvcm06IHNjYWxlKDEpICFpbXBvcnRhbnQ7DQogICAgYW5pbWF0aW9uOiBwb3J0YWxGYWRlSW4gMC4zcyBlYXNlICFpbXBvcnRhbnQ7DQoNCiAgICAvLyDnoa7kv53lj6/op4HmgKcNCiAgICBvcGFjaXR5OiAxICFpbXBvcnRhbnQ7DQogICAgZGlzcGxheTogYmxvY2sgIWltcG9ydGFudDsNCg0KICAgIC8vIOinhumikeagt+W8jw0KICAgIC52aWRlby10aHVtYm5haWwgew0KICAgICAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDsNCiAgICAgIGhlaWdodDogY2FsYygxMDAlIC0gNjBweCkgIWltcG9ydGFudDsNCiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZSAhaW1wb3J0YW50Ow0KICAgICAgYmFja2dyb3VuZDogIzAwMCAhaW1wb3J0YW50Ow0KICAgICAgYm9yZGVyLXJhZGl1czogOHB4IDhweCAwIDAgIWltcG9ydGFudDsNCg0KICAgICAgLnRodW1ibmFpbC12aWRlbyB7DQogICAgICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7DQogICAgICAgIGhlaWdodDogMTAwJSAhaW1wb3J0YW50Ow0KICAgICAgICBvYmplY3QtZml0OiBjb3ZlciAhaW1wb3J0YW50Ow0KICAgICAgICBib3JkZXItcmFkaXVzOiA4cHggOHB4IDAgMCAhaW1wb3J0YW50Ow0KICAgICAgfQ0KDQogICAgICAuZHVyYXRpb24tYmFkZ2Ugew0KICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGUgIWltcG9ydGFudDsNCiAgICAgICAgYm90dG9tOiA4cHggIWltcG9ydGFudDsNCiAgICAgICAgcmlnaHQ6IDhweCAhaW1wb3J0YW50Ow0KICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuOCkgIWltcG9ydGFudDsNCiAgICAgICAgY29sb3I6IHdoaXRlICFpbXBvcnRhbnQ7DQogICAgICAgIHBhZGRpbmc6IDRweCA4cHggIWltcG9ydGFudDsNCiAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4ICFpbXBvcnRhbnQ7DQogICAgICAgIGZvbnQtc2l6ZTogMTJweCAhaW1wb3J0YW50Ow0KICAgICAgfQ0KDQogICAgICAvLyBQb3J0YWzop4bpopHmjqfliLbmjInpkq4NCiAgICAgIC5wb3J0YWwtdmlkZW8tY29udHJvbHMgew0KICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGUgIWltcG9ydGFudDsNCiAgICAgICAgYm90dG9tOiAwICFpbXBvcnRhbnQ7DQogICAgICAgIGxlZnQ6IDAgIWltcG9ydGFudDsNCiAgICAgICAgcmlnaHQ6IDAgIWltcG9ydGFudDsNCiAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRyYW5zcGFyZW50LCByZ2JhKDAsIDAsIDAsIDAuOCkpICFpbXBvcnRhbnQ7DQogICAgICAgIHBhZGRpbmc6IDIwcHggMTZweCAxMnB4ICFpbXBvcnRhbnQ7DQogICAgICAgIG9wYWNpdHk6IDAgIWltcG9ydGFudDsNCiAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDEwcHgpICFpbXBvcnRhbnQ7DQogICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2UgIWltcG9ydGFudDsNCiAgICAgICAgcG9pbnRlci1ldmVudHM6IGF1dG8gIWltcG9ydGFudDsNCg0KICAgICAgICAmLnZpc2libGUgew0KICAgICAgICAgIG9wYWNpdHk6IDEgIWltcG9ydGFudDsNCiAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCkgIWltcG9ydGFudDsNCiAgICAgICAgfQ0KDQogICAgICAgIC5wb3J0YWwtcGxheS1idG4gew0KICAgICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZSAhaW1wb3J0YW50Ow0KICAgICAgICAgIHRvcDogLTMwcHggIWltcG9ydGFudDsNCiAgICAgICAgICBsZWZ0OiA1MCUgIWltcG9ydGFudDsNCiAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoLTUwJSkgIWltcG9ydGFudDsNCiAgICAgICAgICB3aWR0aDogNDhweCAhaW1wb3J0YW50Ow0KICAgICAgICAgIGhlaWdodDogNDhweCAhaW1wb3J0YW50Ow0KICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoNjQsIDE1OCwgMjU1LCAwLjkpICFpbXBvcnRhbnQ7DQogICAgICAgICAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7DQogICAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlICFpbXBvcnRhbnQ7DQogICAgICAgICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50Ow0KICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXIgIWltcG9ydGFudDsNCiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlciAhaW1wb3J0YW50Ow0KICAgICAgICAgIGN1cnNvcjogcG9pbnRlciAhaW1wb3J0YW50Ow0KICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2UgIWltcG9ydGFudDsNCg0KICAgICAgICAgICY6aG92ZXIgew0KICAgICAgICAgICAgYmFja2dyb3VuZDogIzQwOWVmZiAhaW1wb3J0YW50Ow0KICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC01MCUpIHNjYWxlKDEuMSkgIWltcG9ydGFudDsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBpIHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMjBweCAhaW1wb3J0YW50Ow0KICAgICAgICAgICAgY29sb3I6IHdoaXRlICFpbXBvcnRhbnQ7DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgLnBvcnRhbC1pbmZvIHsNCiAgICAgICAgICBjb2xvcjogd2hpdGUgIWltcG9ydGFudDsNCiAgICAgICAgICBmb250LXNpemU6IDEycHggIWltcG9ydGFudDsNCiAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXIgIWltcG9ydGFudDsNCiAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuNSkgIWltcG9ydGFudDsNCiAgICAgICAgICBwYWRkaW5nOiA0cHggOHB4ICFpbXBvcnRhbnQ7DQogICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4ICFpbXBvcnRhbnQ7DQogICAgICAgICAgbWFyZ2luLXRvcDogOHB4ICFpbXBvcnRhbnQ7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQoNCiAgICAvLyDpn7PpopHmoLflvI8NCiAgICAuYXVkaW8tdGh1bWJuYWlsIHsNCiAgICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7DQogICAgICBoZWlnaHQ6IGNhbGMoMTAwJSAtIDYwcHgpICFpbXBvcnRhbnQ7DQogICAgICBwb3NpdGlvbjogcmVsYXRpdmUgIWltcG9ydGFudDsNCiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSkgIWltcG9ydGFudDsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDhweCA4cHggMCAwICFpbXBvcnRhbnQ7DQogICAgICBkaXNwbGF5OiBmbGV4ICFpbXBvcnRhbnQ7DQogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uICFpbXBvcnRhbnQ7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyICFpbXBvcnRhbnQ7DQogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlciAhaW1wb3J0YW50Ow0KDQogICAgICAuYXVkaW8taWNvbiB7DQogICAgICAgIGZvbnQtc2l6ZTogNDhweCAhaW1wb3J0YW50Ow0KICAgICAgICBtYXJnaW4tYm90dG9tOiAxNnB4ICFpbXBvcnRhbnQ7DQogICAgICAgIGNvbG9yOiB3aGl0ZSAhaW1wb3J0YW50Ow0KICAgICAgfQ0KDQogICAgICAuYXVkaW8td2F2ZWZvcm0gew0KICAgICAgICBkaXNwbGF5OiBmbGV4ICFpbXBvcnRhbnQ7DQogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXIgIWltcG9ydGFudDsNCiAgICAgICAgZ2FwOiAzcHggIWltcG9ydGFudDsNCg0KICAgICAgICAud2F2ZS1iYXIgew0KICAgICAgICAgIHdpZHRoOiA0cHggIWltcG9ydGFudDsNCiAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCkgIWltcG9ydGFudDsNCiAgICAgICAgICBib3JkZXItcmFkaXVzOiAycHggIWltcG9ydGFudDsNCiAgICAgICAgICBhbmltYXRpb246IGF1ZGlvV2F2ZSAxLjVzIGVhc2UtaW4tb3V0IGluZmluaXRlICFpbXBvcnRhbnQ7DQoNCiAgICAgICAgICAmOm50aC1jaGlsZCgxKSB7IGhlaWdodDogMjBweCAhaW1wb3J0YW50OyBhbmltYXRpb24tZGVsYXk6IDBzICFpbXBvcnRhbnQ7IH0NCiAgICAgICAgICAmOm50aC1jaGlsZCgyKSB7IGhlaWdodDogMzVweCAhaW1wb3J0YW50OyBhbmltYXRpb24tZGVsYXk6IDAuMXMgIWltcG9ydGFudDsgfQ0KICAgICAgICAgICY6bnRoLWNoaWxkKDMpIHsgaGVpZ2h0OiAyNXB4ICFpbXBvcnRhbnQ7IGFuaW1hdGlvbi1kZWxheTogMC4ycyAhaW1wb3J0YW50OyB9DQogICAgICAgICAgJjpudGgtY2hpbGQoNCkgeyBoZWlnaHQ6IDQwcHggIWltcG9ydGFudDsgYW5pbWF0aW9uLWRlbGF5OiAwLjNzICFpbXBvcnRhbnQ7IH0NCiAgICAgICAgICAmOm50aC1jaGlsZCg1KSB7IGhlaWdodDogMzBweCAhaW1wb3J0YW50OyBhbmltYXRpb24tZGVsYXk6IDAuNHMgIWltcG9ydGFudDsgfQ0KICAgICAgICAgICY6bnRoLWNoaWxkKDYpIHsgaGVpZ2h0OiA0NXB4ICFpbXBvcnRhbnQ7IGFuaW1hdGlvbi1kZWxheTogMC41cyAhaW1wb3J0YW50OyB9DQogICAgICAgICAgJjpudGgtY2hpbGQoNykgeyBoZWlnaHQ6IDM1cHggIWltcG9ydGFudDsgYW5pbWF0aW9uLWRlbGF5OiAwLjZzICFpbXBvcnRhbnQ7IH0NCiAgICAgICAgICAmOm50aC1jaGlsZCg4KSB7IGhlaWdodDogMjVweCAhaW1wb3J0YW50OyBhbmltYXRpb24tZGVsYXk6IDAuN3MgIWltcG9ydGFudDsgfQ0KICAgICAgICAgICY6bnRoLWNoaWxkKDkpIHsgaGVpZ2h0OiA0MHB4ICFpbXBvcnRhbnQ7IGFuaW1hdGlvbi1kZWxheTogMC44cyAhaW1wb3J0YW50OyB9DQogICAgICAgICAgJjpudGgtY2hpbGQoMTApIHsgaGVpZ2h0OiAzMHB4ICFpbXBvcnRhbnQ7IGFuaW1hdGlvbi1kZWxheTogMC45cyAhaW1wb3J0YW50OyB9DQogICAgICAgICAgJjpudGgtY2hpbGQoMTEpIHsgaGVpZ2h0OiAzNXB4ICFpbXBvcnRhbnQ7IGFuaW1hdGlvbi1kZWxheTogMS4wcyAhaW1wb3J0YW50OyB9DQogICAgICAgICAgJjpudGgtY2hpbGQoMTIpIHsgaGVpZ2h0OiAyMHB4ICFpbXBvcnRhbnQ7IGFuaW1hdGlvbi1kZWxheTogMS4xcyAhaW1wb3J0YW50OyB9DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLmR1cmF0aW9uLWJhZGdlIHsNCiAgICAgICAgcG9zaXRpb246IGFic29sdXRlICFpbXBvcnRhbnQ7DQogICAgICAgIGJvdHRvbTogOHB4ICFpbXBvcnRhbnQ7DQogICAgICAgIHJpZ2h0OiA4cHggIWltcG9ydGFudDsNCiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjgpICFpbXBvcnRhbnQ7DQogICAgICAgIGNvbG9yOiB3aGl0ZSAhaW1wb3J0YW50Ow0KICAgICAgICBwYWRkaW5nOiA0cHggOHB4ICFpbXBvcnRhbnQ7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDRweCAhaW1wb3J0YW50Ow0KICAgICAgICBmb250LXNpemU6IDEycHggIWltcG9ydGFudDsNCiAgICAgIH0NCiAgICB9DQoNCiAgICAvLyDlm77niYfmoLflvI8NCiAgICAuaW1hZ2UtdGh1bWJuYWlsIHsNCiAgICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7DQogICAgICBoZWlnaHQ6IGNhbGMoMTAwJSAtIDYwcHgpICFpbXBvcnRhbnQ7DQogICAgICBwb3NpdGlvbjogcmVsYXRpdmUgIWltcG9ydGFudDsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDhweCA4cHggMCAwICFpbXBvcnRhbnQ7DQogICAgICBvdmVyZmxvdzogaGlkZGVuICFpbXBvcnRhbnQ7DQoNCiAgICAgIC50aHVtYm5haWwtaW1hZ2Ugew0KICAgICAgICB3aWR0aDogMTAwJSAhaW1wb3J0YW50Ow0KICAgICAgICBoZWlnaHQ6IDEwMCUgIWltcG9ydGFudDsNCiAgICAgICAgb2JqZWN0LWZpdDogY292ZXIgIWltcG9ydGFudDsNCiAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4IDhweCAwIDAgIWltcG9ydGFudDsNCiAgICAgIH0NCiAgICB9DQoNCiAgICAvLyDmlofku7bkv6Hmga8NCiAgICAuZmlsZS1pbmZvIHsNCiAgICAgIGhlaWdodDogNjBweCAhaW1wb3J0YW50Ow0KICAgICAgcGFkZGluZzogMTJweCAxNnB4ICFpbXBvcnRhbnQ7DQogICAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhICFpbXBvcnRhbnQ7DQogICAgICBib3JkZXItcmFkaXVzOiAwIDAgOHB4IDhweCAhaW1wb3J0YW50Ow0KDQogICAgICAuZmlsZS1uYW1lIHsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4ICFpbXBvcnRhbnQ7DQogICAgICAgIGZvbnQtd2VpZ2h0OiA2MDAgIWltcG9ydGFudDsNCiAgICAgICAgY29sb3I6ICMzMzMgIWltcG9ydGFudDsNCiAgICAgICAgbWFyZ2luLWJvdHRvbTogNHB4ICFpbXBvcnRhbnQ7DQogICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXAgIWltcG9ydGFudDsNCiAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbiAhaW1wb3J0YW50Ow0KICAgICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpcyAhaW1wb3J0YW50Ow0KICAgICAgfQ0KDQogICAgICAuZmlsZS1tZXRhIHsNCiAgICAgICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50Ow0KICAgICAgICBnYXA6IDEycHggIWltcG9ydGFudDsNCiAgICAgICAgZm9udC1zaXplOiAxMnB4ICFpbXBvcnRhbnQ7DQogICAgICAgIGNvbG9yOiAjNjY2ICFpbXBvcnRhbnQ7DQogICAgICB9DQogICAgfQ0KICB9DQp9DQoNCi8vIFBvcnRhbOWKqOeUuw0KQGtleWZyYW1lcyBwb3J0YWxGYWRlSW4gew0KICBmcm9tIHsNCiAgICBvcGFjaXR5OiAwOw0KICAgIHRyYW5zZm9ybTogc2NhbGUoMC44KTsNCiAgfQ0KICB0byB7DQogICAgb3BhY2l0eTogMTsNCiAgICB0cmFuc2Zvcm06IHNjYWxlKDEpOw0KICB9DQp9DQoNCkBrZXlmcmFtZXMgYXVkaW9XYXZlIHsNCiAgMCUsIDEwMCUgew0KICAgIHRyYW5zZm9ybTogc2NhbGVZKDAuNSk7DQogICAgb3BhY2l0eTogMC43Ow0KICB9DQogIDUwJSB7DQogICAgdHJhbnNmb3JtOiBzY2FsZVkoMSk7DQogICAgb3BhY2l0eTogMTsNCiAgfQ0KfQ0KDQouZmlsZS1jaGVja2JveCB7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgdG9wOiA4cHg7DQogIGxlZnQ6IDhweDsNCiAgei1pbmRleDogMTA7DQogIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBwYWRkaW5nOiAycHg7DQp9DQoNCi5maWxlLXRodW1ibmFpbCB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDEyMHB4Ow0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIGJhY2tncm91bmQ6ICNmNWY3ZmE7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KDQoNCn0NCg0KLyog6KeG6aKR57yp55Wl5Zu+5qC35byPICovDQoudmlkZW8tdGh1bWJuYWlsIHsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiAxMDAlOw0KDQogIC50aHVtYm5haWwtdmlkZW8gew0KICAgIHdpZHRoOiAxMDAlOw0KICAgIGhlaWdodDogMTAwJTsNCiAgICBvYmplY3QtZml0OiBjb3ZlcjsNCiAgfQ0KDQoNCg0KICAuZHVyYXRpb24tYmFkZ2Ugew0KICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICBib3R0b206IDhweDsNCiAgICByaWdodDogOHB4Ow0KICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC43KTsNCiAgICBjb2xvcjogd2hpdGU7DQogICAgcGFkZGluZzogMnB4IDZweDsNCiAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgZm9udC1zaXplOiAxMnB4Ow0KICB9DQp9DQoNCi8qIOmfs+mikee8qeeVpeWbvuagt+W8jyAqLw0KLmF1ZGlvLXRodW1ibmFpbCB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMTAwJTsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGN1cnNvcjogcG9pbnRlcjsNCiAgdHJhbnNpdGlvbjogYWxsIDAuNHMgZWFzZTsNCg0KICAmOmhvdmVyIHsNCiAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpOw0KICB9DQoNCiAgJi5wbGF5aW5nIHsNCiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmY2YjZiIDAlLCAjZWU1YTI0IDEwMCUpOw0KICB9DQoNCiAgLmF1ZGlvLWljb24gew0KICAgIGZvbnQtc2l6ZTogMzJweDsNCiAgICBtYXJnaW4tYm90dG9tOiA4cHg7DQogIH0NCg0KICAuYXVkaW8td2F2ZWZvcm0gew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGZsZXgtZW5kOw0KICAgIGdhcDogMnB4Ow0KICAgIGhlaWdodDogMjBweDsNCg0KICAgIC53YXZlLWJhciB7DQogICAgICB3aWR0aDogM3B4Ow0KICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpOw0KICAgICAgYm9yZGVyLXJhZGl1czogMnB4Ow0KICAgICAgYW5pbWF0aW9uOiB3YXZlIDEuNXMgZWFzZS1pbi1vdXQgaW5maW5pdGU7DQoNCiAgICAgICY6bnRoLWNoaWxkKDEpIHsgaGVpZ2h0OiA4cHg7IGFuaW1hdGlvbi1kZWxheTogMHM7IH0NCiAgICAgICY6bnRoLWNoaWxkKDIpIHsgaGVpZ2h0OiAxMnB4OyBhbmltYXRpb24tZGVsYXk6IDAuMXM7IH0NCiAgICAgICY6bnRoLWNoaWxkKDMpIHsgaGVpZ2h0OiAxNnB4OyBhbmltYXRpb24tZGVsYXk6IDAuMnM7IH0NCiAgICAgICY6bnRoLWNoaWxkKDQpIHsgaGVpZ2h0OiAxMHB4OyBhbmltYXRpb24tZGVsYXk6IDAuM3M7IH0NCiAgICAgICY6bnRoLWNoaWxkKDUpIHsgaGVpZ2h0OiAxNHB4OyBhbmltYXRpb24tZGVsYXk6IDAuNHM7IH0NCiAgICAgICY6bnRoLWNoaWxkKDYpIHsgaGVpZ2h0OiAxOHB4OyBhbmltYXRpb24tZGVsYXk6IDAuNXM7IH0NCiAgICAgICY6bnRoLWNoaWxkKDcpIHsgaGVpZ2h0OiAxMnB4OyBhbmltYXRpb24tZGVsYXk6IDAuNnM7IH0NCiAgICAgICY6bnRoLWNoaWxkKDgpIHsgaGVpZ2h0OiA4cHg7IGFuaW1hdGlvbi1kZWxheTogMC43czsgfQ0KICAgICAgJjpudGgtY2hpbGQoOSkgeyBoZWlnaHQ6IDE1cHg7IGFuaW1hdGlvbi1kZWxheTogMC44czsgfQ0KICAgICAgJjpudGgtY2hpbGQoMTApIHsgaGVpZ2h0OiAxMXB4OyBhbmltYXRpb24tZGVsYXk6IDAuOXM7IH0NCiAgICAgICY6bnRoLWNoaWxkKDExKSB7IGhlaWdodDogOXB4OyBhbmltYXRpb24tZGVsYXk6IDFzOyB9DQogICAgICAmOm50aC1jaGlsZCgxMikgeyBoZWlnaHQ6IDEzcHg7IGFuaW1hdGlvbi1kZWxheTogMS4xczsgfQ0KICAgIH0NCiAgfQ0KDQogIC5wbGF5LW92ZXJsYXktYXVkaW8gew0KICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICB0b3A6IDUwJTsNCiAgICBsZWZ0OiA1MCU7DQogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTUwJSk7DQogICAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjcpOw0KICAgIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgICB3aWR0aDogNDBweDsNCiAgICBoZWlnaHQ6IDQwcHg7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgIGNvbG9yOiB3aGl0ZTsNCiAgICBmb250LXNpemU6IDE2cHg7DQogICAgb3BhY2l0eTogMTsNCiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KICAgIGN1cnNvcjogcG9pbnRlcjsNCg0KICAgICY6aG92ZXIgew0KICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjkpOw0KICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTUwJSkgc2NhbGUoMS4xKTsNCiAgICB9DQoNCiAgICAmLnBsYXlpbmcgew0KICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDAsIDAsIDAuOCk7DQoNCiAgICAgICY6aG92ZXIgew0KICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMCwgMCwgMSk7DQogICAgICB9DQogICAgfQ0KDQogICAgJi52aXNpYmxlIHsNCiAgICAgIG9wYWNpdHk6IDE7DQogICAgICB2aXNpYmlsaXR5OiB2aXNpYmxlOw0KICAgIH0NCg0KICAgICYuaGlkZGVuIHsNCiAgICAgIG9wYWNpdHk6IDA7DQogICAgICB2aXNpYmlsaXR5OiBoaWRkZW47DQogICAgfQ0KICB9DQoNCiAgLmR1cmF0aW9uLWJhZGdlIHsNCiAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgYm90dG9tOiA4cHg7DQogICAgcmlnaHQ6IDhweDsNCiAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuNyk7DQogICAgY29sb3I6IHdoaXRlOw0KICAgIHBhZGRpbmc6IDJweCA2cHg7DQogICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgfQ0KfQ0KDQpAa2V5ZnJhbWVzIHdhdmUgew0KICAwJSwgMTAwJSB7IHRyYW5zZm9ybTogc2NhbGVZKDAuNSk7IH0NCiAgNTAlIHsgdHJhbnNmb3JtOiBzY2FsZVkoMSk7IH0NCn0NCg0KLyog5Zu+54mH57yp55Wl5Zu+5qC35byPICovDQouaW1hZ2UtdGh1bWJuYWlsIHsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiAxMDAlOw0KICBjdXJzb3I6IHBvaW50ZXI7DQogIG92ZXJmbG93OiBoaWRkZW47DQoNCiAgJjpob3ZlciB7DQogICAgLnRodW1ibmFpbC1pbWFnZSB7DQogICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpOw0KICAgIH0NCg0KICAgIC5pbWFnZS1vdmVybGF5IHsNCiAgICAgIG9wYWNpdHk6IDE7DQogICAgfQ0KICB9DQoNCiAgLnRodW1ibmFpbC1pbWFnZSB7DQogICAgd2lkdGg6IDEwMCU7DQogICAgaGVpZ2h0OiAxMDAlOw0KICAgIG9iamVjdC1maXQ6IGNvdmVyOw0KICAgIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzIGVhc2U7DQogIH0NCg0KICAuaW1hZ2Utb3ZlcmxheSB7DQogICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgIHRvcDogMDsNCiAgICBsZWZ0OiAwOw0KICAgIHJpZ2h0OiAwOw0KICAgIGJvdHRvbTogMDsNCiAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuNCk7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgIG9wYWNpdHk6IDA7DQogICAgdHJhbnNpdGlvbjogb3BhY2l0eSAwLjNzIGVhc2U7DQoNCiAgICBpIHsNCiAgICAgIGNvbG9yOiB3aGl0ZTsNCiAgICAgIGZvbnQtc2l6ZTogMjRweDsNCiAgICB9DQogIH0NCn0NCg0KLyog5paH5Lu25Zu+5qCH57yp55Wl5Zu+5qC35byPICovDQouZmlsZS1pY29uLXRodW1ibmFpbCB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDEwMCU7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBmb250LXNpemU6IDQ4cHg7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KfQ0KDQoNCg0KLyog5paH5Lu25L+h5oGv5qC35byPICovDQouZmlsZS1jYXJkIC5maWxlLWluZm8gew0KICBwYWRkaW5nOiAxMnB4Ow0KDQogIC5maWxlLW5hbWUgew0KICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICBmb250LXdlaWdodDogNTAwOw0KICAgIGNvbG9yOiAjMzAzMTMzOw0KICAgIG1hcmdpbi1ib3R0b206IDRweDsNCiAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOw0KICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7DQogIH0NCg0KICAuZmlsZS1tZXRhIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICBmb250LXNpemU6IDEycHg7DQogICAgY29sb3I6ICM5MDkzOTk7DQoNCiAgICAuZmlsZS1zaXplIHsNCiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgfQ0KICB9DQp9DQoNCi8qIOaWh+S7tumihOiniOWvueivneahhuagt+W8jyAqLw0KLnByZXZpZXctZGlhbG9nIHsNCiAgLmVsLWRpYWxvZ19fYm9keSB7DQogICAgcGFkZGluZzogMjBweDsNCiAgfQ0KfQ0KDQoucHJldmlldy1jb250ZW50IHsNCiAgLnByZXZpZXctaGVhZGVyIHsNCiAgICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KDQogICAgaDMgew0KICAgICAgbWFyZ2luOiAwIDAgMTBweCAwOw0KICAgICAgY29sb3I6ICMzMDMxMzM7DQogICAgICBmb250LXNpemU6IDE4cHg7DQogICAgfQ0KDQogICAgLmZpbGUtaW5mbyB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgZ2FwOiAyMHB4Ow0KICAgICAgY29sb3I6ICM5MDkzOTk7DQogICAgICBmb250LXNpemU6IDE0cHg7DQogICAgfQ0KICB9DQoNCiAgLnZpZGVvLXByZXZpZXcgew0KICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCg0KICAgIHZpZGVvIHsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDEycHggMCByZ2JhKDAsIDAsIDAsIDAuMSk7DQogICAgfQ0KICB9DQoNCiAgLmF1ZGlvLXByZXZpZXcgew0KICAgIC5hdWRpby1wbGF5ZXIgew0KICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICB9DQoNCiAgICAuYXVkaW8taW5mbyB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgIHBhZGRpbmc6IDIwcHg7DQogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhOw0KICAgICAgYm9yZGVyLXJhZGl1czogOHB4Ow0KDQogICAgICAuYXVkaW8taWNvbiB7DQogICAgICAgIGZvbnQtc2l6ZTogNDhweDsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiAyMHB4Ow0KICAgICAgfQ0KDQogICAgICAuYXVkaW8tZGV0YWlscyB7DQogICAgICAgIHAgew0KICAgICAgICAgIG1hcmdpbjogNXB4IDA7DQogICAgICAgICAgY29sb3I6ICM2MDYyNjY7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAuaW1hZ2UtcHJldmlldyB7DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KDQogICAgaW1nIHsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDEycHggMCByZ2JhKDAsIDAsIDAsIDAuMSk7DQogICAgfQ0KICB9DQoNCiAgLnVuc3VwcG9ydGVkLXByZXZpZXcgew0KICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICBwYWRkaW5nOiA0MHB4Ow0KDQogICAgLnVuc3VwcG9ydGVkLWljb24gew0KICAgICAgZm9udC1zaXplOiA2NHB4Ow0KICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgICB9DQoNCiAgICBwIHsNCiAgICAgIGNvbG9yOiAjOTA5Mzk5Ow0KICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgICB9DQogIH0NCn0NCg0KLyogT1NT5pyq6YWN572u5o+Q56S65qC35byPICovDQoubm8tb3NzLXRpcCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBwYWRkaW5nOiA0MHB4IDIwcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgY29sb3I6ICM5MDkzOTk7DQoNCiAgLnRpcC1pY29uIHsNCiAgICBmb250LXNpemU6IDQ4cHg7DQogICAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgfQ0KDQogIC50aXAtdGV4dCB7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIG1hcmdpbi1ib3R0b206IDE2cHg7DQogICAgY29sb3I6ICM2MDYyNjY7DQogIH0NCn0NCg0KLm5vLW9zcy1maWxlcy10aXAgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgaGVpZ2h0OiAxMDAlOw0KICBtaW4taGVpZ2h0OiA0MDBweDsNCg0KICAudGlwLWNvbnRlbnQgew0KICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCg0KICAgIC50aXAtaWNvbiB7DQogICAgICBmb250LXNpemU6IDY0cHg7DQogICAgICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICAgIH0NCg0KICAgIC50aXAtdGl0bGUgew0KICAgICAgZm9udC1zaXplOiAxOHB4Ow0KICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgIGNvbG9yOiAjMzAzMTMzOw0KICAgICAgbWFyZ2luLWJvdHRvbTogMTJweDsNCiAgICB9DQoNCiAgICAudGlwLWRlc2NyaXB0aW9uIHsNCiAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgIGNvbG9yOiAjOTA5Mzk5Ow0KICAgICAgbWFyZ2luLWJvdHRvbTogMjRweDsNCiAgICB9DQogIH0NCn0NCg0KDQoNCg0KDQovKiDlk43lupTlvI/orr7orqEgKi8NCkBtZWRpYSAobWF4LXdpZHRoOiAxMjAwcHgpIHsNCiAgLmZvbGRlci1zaWRlYmFyIHsNCiAgICB3aWR0aDogMTUwcHg7DQogIH0NCn0NCg0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5tYXRlcmlhbHMtbWFpbiB7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgfQ0KDQogIC5mb2xkZXItc2lkZWJhciB7DQogICAgd2lkdGg6IDEwMCU7DQogICAgaGVpZ2h0OiAyMDBweDsNCiAgICBib3JkZXItcmlnaHQ6IG5vbmU7DQogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOGU4ZTg7DQogIH0NCg0KICAuZmlsZXMtdG9vbGJhciB7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsNCiAgICBnYXA6IDEwcHg7DQogIH0NCg0KICAudG9vbGJhci1yaWdodCB7DQogICAgYWxpZ24tc2VsZjogZmxleC1lbmQ7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["up.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0oFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAMA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "up.vue", "sourceRoot": "src/views/store", "sourcesContent": ["<template>\r\n  <div class=\"up-container\">\r\n    <!-- 顶部标签页 -->\r\n    <div class=\"materials-tabs\">\r\n      <div class=\"tab-buttons\">\r\n        <el-button\r\n          :type=\"materialTab === 'sucai' ? 'primary' : 'default'\"\r\n          @click=\"switchTab('sucai')\"\r\n          class=\"tab-button\">\r\n          素材上传\r\n        </el-button>\r\n        <el-button\r\n          :type=\"materialTab === 'bgm' ? 'primary' : 'default'\"\r\n          @click=\"switchTab('bgm')\"\r\n          class=\"tab-button\">\r\n          BGM上传\r\n        </el-button>\r\n\r\n        <!-- BGM免费下载按钮 -->\r\n        <el-button\r\n          v-if=\"materialTab === 'bgm'\"\r\n          type=\"success\"\r\n          size=\"small\"\r\n          icon=\"el-icon-download\"\r\n          @click=\"openBGMDownloadSite\"\r\n          class=\"bgm-download-button\">\r\n          免费在线下载\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <div class=\"materials-main\">\r\n      <!-- 左侧文件夹树 -->\r\n      <div class=\"folder-sidebar\">\r\n        <div class=\"folder-header\">\r\n          <div class=\"folder-actions\">\r\n            <el-button\r\n              type=\"primary\"\r\n              size=\"small\"\r\n              icon=\"el-icon-upload2\"\r\n              @click=\"showUploadDialog\"\r\n              style=\"height: 32px !important; width: 100% !important; max-width: 120px !important;\">\r\n              上传\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              size=\"small\"\r\n              icon=\"el-icon-folder-add\"\r\n              @click=\"showCreateFolderDialog\"\r\n              style=\"height: 32px !important; width: 100% !important; max-width: 120px !important;\">\r\n              新建文件夹\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"folder-list\">\r\n          <!-- 只有OSS配置成功后才显示文件夹 -->\r\n          <div v-if=\"ossInitialized\">\r\n            <div\r\n              v-for=\"folder in currentFolderTree\"\r\n              :key=\"folder.id\"\r\n              class=\"folder-item\"\r\n              :class=\"{ active: selectedFolder === folder.id }\"\r\n              @click=\"selectFolder(folder)\"\r\n              @contextmenu.prevent=\"showFolderContextMenu($event, folder)\">\r\n              <i class=\"folder-icon\">📁</i>\r\n              <span class=\"folder-name\">{{ folder.name }}</span>\r\n              <span class=\"folder-count\">{{ folder.count }}</span>\r\n            </div>\r\n          </div>\r\n\r\n\r\n          <!-- OSS未配置时的提示 -->\r\n          <div v-else class=\"no-oss-tip\">\r\n            <div class=\"tip-icon\">⚙️</div>\r\n            <div class=\"tip-text\">请先配置OSS存储</div>\r\n            <el-button type=\"primary\" size=\"small\" @click=\"openOSSConfig\">配置OSS</el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 文件夹右键菜单 -->\r\n        <div\r\n          v-show=\"contextMenuVisible\"\r\n          class=\"context-menu\"\r\n          :style=\"{ left: contextMenuX + 'px', top: contextMenuY + 'px' }\"\r\n          @click.stop>\r\n          <div class=\"menu-item\" @click=\"renameFolderAction\">\r\n            <i class=\"el-icon-edit\"></i>\r\n            重命名文件夹\r\n          </div>\r\n          <div class=\"menu-item\" @click=\"deleteFolderAction\" v-if=\"contextMenuFolder && (contextMenuFolder.name || contextMenuFolder) !== '总'\">\r\n            <i class=\"el-icon-delete\"></i>\r\n            删除文件夹\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧文件区域 -->\r\n      <div class=\"files-area\">\r\n        <!-- OSS配置成功后显示文件管理界面 -->\r\n        <div v-if=\"ossInitialized\" class=\"files-toolbar\">\r\n          <div class=\"toolbar-left\">\r\n            <el-checkbox v-model=\"selectAll\" @change=\"handleSelectAll\">全选</el-checkbox>\r\n            <span class=\"file-actions\">\r\n              <span class=\"action-text\" @click=\"handlePreview\" :disabled=\"selectedFiles.length !== 1\">预览</span>\r\n              <span class=\"action-text\" @click=\"handleRename\">重命名</span>\r\n              <span class=\"action-text\" @click=\"handleDelete\">删除</span>\r\n            </span>\r\n          </div>\r\n          <div class=\"toolbar-right\">\r\n            <span class=\"file-count\">共 {{ filteredMaterialList.length }} 项</span>\r\n            <div class=\"pagination-info\">\r\n              <span>{{ currentPage }}</span>\r\n              <span>/</span>\r\n              <span>{{ totalPages }}</span>\r\n              <span>页</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 文件列表 -->\r\n        <div class=\"files-content\">\r\n          <!-- OSS未配置时的提示 -->\r\n          <div v-if=\"!ossInitialized\" class=\"no-oss-files-tip\">\r\n            <div class=\"tip-content\">\r\n              <div class=\"tip-icon\">☁️</div>\r\n              <div class=\"tip-title\">请先配置OSS存储</div>\r\n              <div class=\"tip-description\">配置OSS后即可开始上传和管理文件</div>\r\n              <el-button type=\"primary\" @click=\"openOSSConfig\">配置OSS存储</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- OSS已配置时显示文件列表 -->\r\n          <div v-else-if=\"filteredMaterialList.length === 0\" class=\"empty-state\">\r\n            <div class=\"empty-icon\">📁</div>\r\n            <div class=\"empty-text\">暂无文件</div>\r\n          </div>\r\n          <div v-else>\r\n            <!-- 文件操作工具栏 -->\r\n            <div class=\"file-toolbar\">\r\n              <div class=\"toolbar-left\">\r\n                <el-checkbox v-model=\"selectAll\" @change=\"handleSelectAll\">全选</el-checkbox>\r\n                <span class=\"selected-count\" v-if=\"selectedFiles.length > 0\">\r\n                  已选择 {{ selectedFiles.length }} 个文件\r\n                </span>\r\n              </div>\r\n              <div class=\"toolbar-right\">\r\n                <el-button\r\n                  v-if=\"selectedFiles.length > 0\"\r\n                  type=\"danger\"\r\n                  size=\"small\"\r\n                  @click=\"handleDelete\">\r\n                  删除选中\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"file-grid\">\r\n            <div\r\n              v-for=\"file in paginatedMaterials\"\r\n              :key=\"file.id\"\r\n              class=\"file-card\"\r\n              :class=\"{\r\n                selected: selectedFiles.includes(file.id),\r\n                hovered: file.isHovered,\r\n                enlarged: file.isPlaying,\r\n                'scale-enlarged': file.isScaled\r\n              }\"\r\n              :style=\"file.isScaled && file.displayWidth && file.displayHeight ? {\r\n                width: file.displayWidth + 'px',\r\n                height: file.displayHeight + 'px'\r\n              } : {}\"\r\n              @click=\"toggleFileSelection(file.id)\"\r\n              @dblclick=\"handleFileDoubleClick(file)\">\r\n\r\n              <!-- 文件选择框 -->\r\n              <div class=\"file-checkbox\">\r\n                <el-checkbox :value=\"selectedFiles.includes(file.id)\" @change=\"toggleFileSelection(file.id)\"></el-checkbox>\r\n              </div>\r\n\r\n              <!-- 文件缩略图 -->\r\n              <div class=\"file-thumbnail\">\r\n                <!-- 视频缩略图 -->\r\n                <div v-if=\"file.type === 'video'\" class=\"video-thumbnail\"\r\n                     :class=\"{ playing: file.isPlaying }\"\r\n                     @mouseenter=\"onVideoMouseEnter(file)\"\r\n                     @mouseleave=\"onVideoMouseLeave(file)\"\r\n                     @mousemove=\"onVideoMouseMove(file)\"\r\n                     @click.stop=\"toggleVideoPlayWithScale(file)\">\r\n                  <video\r\n                    :ref=\"`video-${file.id}`\"\r\n                    :src=\"getFileUrl(file)\"\r\n                    preload=\"metadata\"\r\n                    :muted=\"!file.isPlaying\"\r\n                    class=\"thumbnail-video\"\r\n                    @loadedmetadata=\"onVideoLoaded\"\r\n                    @ended=\"onVideoEnded(file)\"\r\n                    @pause=\"onVideoPaused(file)\"\r\n                    @play=\"onVideoPlayed(file)\"\r\n                    @timeupdate=\"onVideoTimeUpdate(file)\"\r\n                    @loadeddata=\"onVideoLoadedData(file)\">\r\n                  </video>\r\n\r\n                  <!-- 小视频的简洁播放按钮 -->\r\n                  <div v-if=\"!file.isScaled\" class=\"simple-play-overlay\">\r\n                    <div class=\"play-button\">\r\n                      <i :class=\"file.isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'\"></i>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- 放大视频的进度条 - 悬浮在视频底部 -->\r\n                  <div v-if=\"file.isScaled\" class=\"video-controls-overlay\">\r\n                    <div class=\"progress-container\" @click.stop=\"seekVideo($event, file)\">\r\n                      <div class=\"progress-track\">\r\n                        <div class=\"progress-fill\" :style=\"{ width: getVideoProgress(file) + '%' }\"></div>\r\n                        <div class=\"progress-thumb\" :style=\"{ left: getVideoProgress(file) + '%' }\"></div>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"controls-bottom\">\r\n                      <div class=\"play-pause-btn\" @click.stop=\"toggleVideoPlayWithScale(file)\">\r\n                        <i :class=\"file.isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'\"></i>\r\n                      </div>\r\n                      <div class=\"time-display\">\r\n                        <span class=\"current-time\">{{ formatTime(getCurrentTime(file)) }}</span>\r\n                        <span class=\"separator\">/</span>\r\n                        <span class=\"total-time\">{{ formatTime(getDuration(file)) }}</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div class=\"duration-badge\" v-if=\"file.duration && !file.isScaled\">{{ file.duration }}</div>\r\n                </div>\r\n\r\n                <!-- 音频缩略图 -->\r\n                <div v-else-if=\"file.type === 'audio'\" class=\"audio-thumbnail\"\r\n                     :class=\"{ playing: file.isPlaying }\"\r\n                     @mouseenter=\"onAudioMouseEnter(file)\"\r\n                     @mouseleave=\"onAudioMouseLeave(file)\"\r\n                     @mousemove=\"onAudioMouseMove(file)\">\r\n                  <audio\r\n                    :ref=\"`audio-${file.id}`\"\r\n                    :src=\"getFileUrl(file)\"\r\n                    preload=\"metadata\"\r\n                    @ended=\"onAudioEnded(file)\"\r\n                    @pause=\"onAudioPaused(file)\"\r\n                    @play=\"onAudioPlayed(file)\">\r\n                  </audio>\r\n                  <div class=\"audio-icon\">🎵</div>\r\n                  <div class=\"audio-waveform\">\r\n                    <div class=\"wave-bar\" v-for=\"i in 12\" :key=\"i\"></div>\r\n                  </div>\r\n                  <div class=\"play-overlay-audio\"\r\n                       @click.stop=\"toggleAudioPlay(file)\"\r\n                       :class=\"{\r\n                         playing: file.isPlaying,\r\n                         visible: file.showControls || !file.isPlaying,\r\n                         hidden: file.isPlaying && !file.showControls\r\n                       }\">\r\n                    <i :class=\"file.isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'\"></i>\r\n                  </div>\r\n                  <div class=\"duration-badge\" v-if=\"file.duration\">{{ file.duration }}</div>\r\n                </div>\r\n\r\n                <!-- 图片缩略图 -->\r\n                <div v-else-if=\"file.type === 'image'\" class=\"image-thumbnail\"\r\n                     @click.stop=\"previewFile(file)\"\r\n                     @mouseenter=\"onImageMouseEnter(file)\"\r\n                     @mouseleave=\"onImageMouseLeave(file)\">\r\n                  <img\r\n                    :src=\"getFileUrl(file)\"\r\n                    :alt=\"file.name\"\r\n                    class=\"thumbnail-image\"\r\n                    @error=\"onImageError\">\r\n                  <div class=\"image-overlay\">\r\n                    <i class=\"el-icon-zoom-in\"></i>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 其他文件类型 -->\r\n                <div v-else class=\"file-icon-thumbnail\">\r\n                  <i :class=\"getSimpleFileIcon(file.type)\"></i>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 文件信息 -->\r\n              <div class=\"file-info\">\r\n                <div class=\"file-name\" :title=\"file.name\">{{ file.name }}</div>\r\n                <div class=\"file-meta\">\r\n                  <span class=\"file-size\">{{ formatFileSize(file.size) }}</span>\r\n                  <span class=\"file-time\">{{ file.uploadTime }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- OSS配置对话框 -->\r\n    <el-dialog\r\n      title=\"设置存储\"\r\n      :visible.sync=\"ossConfigVisible\"\r\n      width=\"600px\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"oss-config-dialog\"\r\n    >\r\n      <div class=\"oss-config-content\">\r\n        <!-- 存储方式选择 -->\r\n        <div class=\"storage-type-section\">\r\n          <div class=\"section-label\">存储方式</div>\r\n          <div class=\"storage-options\">\r\n            <el-radio v-model=\"ossConfig.storageType\" label=\"oss\" class=\"storage-radio\">\r\n              <span class=\"radio-text\">阿里云OSS</span>\r\n            </el-radio>\r\n          </div>\r\n          <div class=\"storage-description\">\r\n            切换阿里云OSS后，素材库需要重新上传至阿里云OSS\r\n          </div>\r\n        </div>\r\n\r\n        <!-- OSS配置表单 -->\r\n        <div class=\"oss-form-section\">\r\n          <div class=\"form-row\">\r\n            <div class=\"form-label required\">存储空间名称</div>\r\n            <el-input\r\n              v-model=\"ossConfig.bucket\"\r\n              placeholder=\"jkhghfgddedb\"\r\n              class=\"form-input\"\r\n            />\r\n          </div>\r\n\r\n          <div class=\"form-row\">\r\n            <div class=\"form-label required\">ACCESS_KEY</div>\r\n            <el-input\r\n              v-model=\"ossConfig.accessKeyId\"\r\n              placeholder=\"LTAI5tSgfoZwykU9M1qvThgq\"\r\n              class=\"form-input\"\r\n            />\r\n          </div>\r\n\r\n          <div class=\"form-row\">\r\n            <div class=\"form-label required\">SECRET_KEY</div>\r\n            <el-input\r\n              v-model=\"ossConfig.accessKeySecret\"\r\n              placeholder=\"******************************\"\r\n              class=\"form-input\"\r\n              show-password\r\n            />\r\n          </div>\r\n\r\n          <div class=\"form-row\">\r\n            <div class=\"form-label required\">空间域名</div>\r\n            <el-input\r\n              v-model=\"ossConfig.endpoint\"\r\n              placeholder=\"https://jkhghfgddedb.oss-cn-shanghai.aliyuncs.com\"\r\n              class=\"form-input\"\r\n            />\r\n            <div class=\"form-hint\">\r\n              请补全http://或https://，例如https://static.cloud.com\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 状态选择 -->\r\n          <div class=\"form-row\">\r\n            <div class=\"form-label\">状态</div>\r\n            <div class=\"status-options\">\r\n              <el-radio v-model=\"ossConfig.status\" label=\"disabled\" class=\"status-radio\">\r\n                <span class=\"radio-text\">关闭</span>\r\n              </el-radio>\r\n              <el-radio v-model=\"ossConfig.status\" label=\"enabled\" class=\"status-radio\">\r\n                <span class=\"radio-text\">开启</span>\r\n              </el-radio>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"ossConfigVisible = false\" class=\"cancel-btn\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveOSSConfig\" :loading=\"testingOSS\" class=\"confirm-btn\">\r\n          {{ testingOSS ? '测试连接中...' : '确定' }}\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 新建文件夹对话框 -->\r\n    <el-dialog\r\n      title=\"新建文件夹\"\r\n      :visible.sync=\"createFolderVisible\"\r\n      width=\"400px\"\r\n      :close-on-click-modal=\"false\">\r\n      <el-form :model=\"createFolderForm\" label-width=\"80px\">\r\n        <el-form-item label=\"文件夹名\">\r\n          <el-input\r\n            v-model=\"createFolderForm.name\"\r\n            placeholder=\"请输入文件夹名称\"\r\n            @keyup.enter.native=\"createFolder\">\r\n          </el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"createFolderVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"createFolder\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 文件预览对话框 -->\r\n    <el-dialog\r\n      title=\"文件预览\"\r\n      :visible.sync=\"previewVisible\"\r\n      width=\"80%\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"preview-dialog\"\r\n      @close=\"stopAllMedia\">\r\n      <div class=\"preview-content\" v-if=\"previewFile\">\r\n        <div class=\"preview-header\">\r\n          <h3>{{ previewFile.name }}</h3>\r\n          <div class=\"file-info\">\r\n            <span>大小: {{ formatFileSize(previewFile.size) }}</span>\r\n            <span>上传时间: {{ previewFile.uploadTime }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 视频预览 -->\r\n        <div v-if=\"currentPreviewFile && currentPreviewFile.type === 'video'\" class=\"video-preview\">\r\n          <video\r\n            ref=\"previewVideo\"\r\n            :src=\"currentPreviewFile.url\"\r\n            controls\r\n            preload=\"metadata\"\r\n            style=\"width: 100%; max-height: 500px;\">\r\n            您的浏览器不支持视频播放\r\n          </video>\r\n        </div>\r\n\r\n        <!-- 音频预览 -->\r\n        <div v-else-if=\"currentPreviewFile && currentPreviewFile.type === 'audio'\" class=\"audio-preview\">\r\n          <div class=\"audio-player\">\r\n            <audio\r\n              ref=\"previewAudio\"\r\n              :src=\"currentPreviewFile.url\"\r\n              controls\r\n              preload=\"metadata\"\r\n              style=\"width: 100%;\">\r\n              您的浏览器不支持音频播放\r\n            </audio>\r\n          </div>\r\n          <div class=\"audio-info\">\r\n            <div class=\"audio-icon\">🎵</div>\r\n            <div class=\"audio-details\">\r\n              <p><strong>{{ currentPreviewFile.name }}</strong></p>\r\n              <p v-if=\"currentPreviewFile.duration\">时长: {{ currentPreviewFile.duration }}</p>\r\n              <p v-if=\"currentPreviewFile.bitrate\">比特率: {{ currentPreviewFile.bitrate }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 图片预览 -->\r\n        <div v-else-if=\"currentPreviewFile && currentPreviewFile.type === 'image'\" class=\"image-preview\">\r\n          <img\r\n            :src=\"currentPreviewFile.url\"\r\n            :alt=\"currentPreviewFile.name\"\r\n            style=\"max-width: 100%; max-height: 500px;\">\r\n        </div>\r\n\r\n        <!-- 不支持预览的文件 -->\r\n        <div v-else class=\"unsupported-preview\">\r\n          <div class=\"unsupported-icon\">📄</div>\r\n          <p>此文件类型不支持预览</p>\r\n          <el-button type=\"primary\" @click=\"downloadFile(previewFile)\">下载文件</el-button>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 上传对话框 -->\r\n    <el-dialog :title=\"uploadDialogTitle\" :visible.sync=\"uploadDialogVisible\" width=\"600px\">\r\n      <div class=\"upload-content\">\r\n        <!-- 存储方式选择 -->\r\n        <div class=\"storage-selector\" style=\"margin-bottom: 20px;\">\r\n          <span style=\"margin-right: 10px;\">存储方式：</span>\r\n          <el-radio-group v-model=\"storageType\" size=\"small\">\r\n            <el-radio label=\"local\">本地存储</el-radio>\r\n            <el-radio label=\"oss\">阿里云OSS</el-radio>\r\n          </el-radio-group>\r\n          <el-button\r\n            v-if=\"storageType === 'oss'\"\r\n            type=\"text\"\r\n            size=\"small\"\r\n            @click=\"openOSSConfig\"\r\n            style=\"margin-left: 10px;\"\r\n          >\r\n            <i class=\"el-icon-setting\"></i> 配置OSS\r\n          </el-button>\r\n          <span\r\n            v-if=\"storageType === 'oss'\"\r\n            :class=\"ossInitialized ? 'text-success' : 'text-danger'\"\r\n            style=\"margin-left: 10px; font-size: 12px;\"\r\n          >\r\n            {{ ossInitialized ? '✓ 已配置' : '✗ 未配置' }}\r\n          </span>\r\n        </div>\r\n\r\n        <el-upload\r\n          class=\"upload-dragger\"\r\n          ref=\"upload\"\r\n          action=\"#\"\r\n          :multiple=\"true\"\r\n          :file-list=\"fileList\"\r\n          :before-upload=\"beforeUpload\"\r\n          :on-success=\"handleUploadSuccess\"\r\n          :on-error=\"handleUploadError\"\r\n          :on-progress=\"handleUploadProgress\"\r\n          :on-remove=\"handleRemove\"\r\n          :on-change=\"handleFileChange\"\r\n          :auto-upload=\"false\"\r\n          drag\r\n        >\r\n          <i class=\"el-icon-upload\"></i>\r\n          <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n          <div class=\"el-upload__tip\" slot=\"tip\">\r\n            {{ uploadTipText }}\r\n          </div>\r\n        </el-upload>\r\n\r\n        <!-- 上传进度显示 -->\r\n        <div v-if=\"uploading && Object.keys(uploadProgress).length > 0\" class=\"upload-progress\" style=\"margin-top: 20px;\">\r\n          <h4>上传进度</h4>\r\n          <div v-for=\"(progress, fileName) in uploadProgress\" :key=\"fileName\" class=\"progress-item\">\r\n            <div class=\"progress-info\">\r\n              <span class=\"file-name\">{{ fileName }}</span>\r\n              <span class=\"progress-text\">{{ progress }}%</span>\r\n            </div>\r\n            <el-progress :percentage=\"progress\" :show-text=\"false\"></el-progress>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"uploadDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitUpload\" :loading=\"uploading\">\r\n          {{ uploading ? '上传中...' : '开始上传' }}\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { initOSSClient, uploadFilesToOSS, getOSSFileList, deleteFileFromOSS, getOSSClient } from '@/utils/ossUpload'\r\nimport { getToken } from '@/utils/auth'\r\nimport store from '@/store'\r\n\r\nexport default {\r\n  name: 'StorerUp',\r\n  data() {\r\n    return {\r\n      // 界面控制\r\n      materialTab: 'sucai', // sucai, bgm\r\n      selectedFolder: 1,\r\n      selectAll: false,\r\n      selectedFiles: [],\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n\r\n      // 分别为BGM和素材维护文件夹结构\r\n      bgmFolderTree: [],\r\n      sucaiFolderTree: [],\r\n\r\n      // 上传相关\r\n      uploadDialogVisible: false,\r\n      uploading: false,\r\n      fileList: [],\r\n      uploadForm: {},\r\n      uploadProgress: {}, // 上传进度\r\n      storageType: 'oss', // 存储方式: local | oss\r\n\r\n      // OSS配置相关\r\n      ossConfigVisible: false,\r\n      testingOSS: false,\r\n      ossConfig: {\r\n        storageType: 'oss', // 存储方式\r\n        bucket: '', // 存储空间名称\r\n        accessKeyId: '', // ACCESS_KEY\r\n        accessKeySecret: '', // SECRET_KEY\r\n        endpoint: '', // 空间域名\r\n        status: 'enabled' // 状态：enabled/disabled\r\n      },\r\n      ossInitialized: false, // OSS是否已初始化\r\n\r\n      // 文件夹管理\r\n      currentFolder: '总', // 当前选中的文件夹\r\n      folderDialogVisible: false, // 文件夹管理对话框\r\n      newFolderName: '', // 新文件夹名称\r\n      renameFolderName: '', // 重命名文件夹名称\r\n      selectedFolder: '', // 选中的文件夹\r\n\r\n      // 右键菜单\r\n      contextMenuVisible: false,\r\n      contextMenuX: 0,\r\n      contextMenuY: 0,\r\n      contextMenuFolder: null,\r\n\r\n      // 新建文件夹\r\n      createFolderVisible: false,\r\n      createFolderForm: {\r\n        name: ''\r\n      },\r\n\r\n      // 文件预览\r\n      previewVisible: false,\r\n      currentPreviewFile: null,\r\n\r\n      // 控制按钮隐藏定时器\r\n      controlTimers: {},\r\n\r\n\r\n\r\n      // BGM文件数据 (abgm/admin/总/ 文件夹) - 添加测试数据\r\n      bgmList: [\r\n        {\r\n          id: 'bgm1',\r\n          name: '测试音频.mp3',\r\n          type: 'audio',\r\n          size: 512000,\r\n          uploadTime: '2024-01-01 12:00:00',\r\n          duration: '02:30',\r\n          url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'\r\n        }\r\n      ],\r\n\r\n      // 素材文件数据 (asucai/admin/总/ 文件夹) - 添加测试数据\r\n      sucaiList: [\r\n        {\r\n          id: 'sucai1',\r\n          name: '测试视频.mp4',\r\n          type: 'video',\r\n          size: 1024000,\r\n          uploadTime: '2024-01-01 12:00:00',\r\n          duration: '00:30',\r\n          url: 'https://www.w3schools.com/html/mov_bbb.mp4'\r\n        },\r\n        {\r\n          id: 'sucai2',\r\n          name: '测试图片.jpg',\r\n          type: 'image',\r\n          size: 256000,\r\n          uploadTime: '2024-01-01 12:00:00',\r\n          url: 'https://picsum.photos/400/300'\r\n        },\r\n        {\r\n          id: 'sucai3',\r\n          name: '测试音频.mp3',\r\n          type: 'audio',\r\n          size: 512000,\r\n          uploadTime: '2024-01-01 12:00:00',\r\n          duration: '01:45',\r\n          url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    // 根据当前标签页返回对应的文件列表\r\n    currentMaterialList() {\r\n      switch (this.materialTab) {\r\n        case 'bgm':\r\n          return this.bgmList\r\n        case 'sucai':\r\n          return this.sucaiList\r\n        default:\r\n          return this.sucaiList\r\n      }\r\n    },\r\n\r\n    // 根据当前标签页返回对应的文件夹列表\r\n    currentFolderTree() {\r\n      switch (this.materialTab) {\r\n        case 'bgm':\r\n          return this.bgmFolderTree\r\n        case 'sucai':\r\n          return this.sucaiFolderTree\r\n        default:\r\n          return this.sucaiFolderTree\r\n      }\r\n    },\r\n\r\n    filteredMaterialList() {\r\n      let list = this.currentMaterialList\r\n\r\n      // 按文件夹过滤（简化版本）\r\n      if (this.selectedFolder !== 1) {\r\n        // 这里可以根据实际需求添加过滤逻辑\r\n        // 目前显示所有文件\r\n      }\r\n\r\n      return list\r\n    },\r\n\r\n    paginatedMaterials() {\r\n      const start = (this.currentPage - 1) * this.pageSize\r\n      const end = start + this.pageSize\r\n      return this.filteredMaterialList.slice(start, end)\r\n    },\r\n\r\n    totalPages() {\r\n      return Math.ceil(this.filteredMaterialList.length / this.pageSize)\r\n    },\r\n\r\n    // 上传对话框相关计算属性\r\n    uploadDialogTitle() {\r\n      switch (this.materialTab) {\r\n        case 'bgm':\r\n          return '上传BGM文件'\r\n        case 'sucai':\r\n          return '上传素材文件'\r\n        default:\r\n          return '上传文件'\r\n      }\r\n    },\r\n\r\n    uploadTipText() {\r\n      switch (this.materialTab) {\r\n        case 'bgm':\r\n          return '支持 MP3、WAV、FLAC、AAC、M4A、OGG、WMA 等音频格式，单个文件不超过100MB'\r\n        case 'sucai':\r\n          return '支持各种视频、音频、图片格式，单个文件不超过500MB'\r\n        default:\r\n          return '请选择要上传的文件'\r\n      }\r\n    },\r\n\r\n\r\n  },\r\n  async created() {\r\n    // 先加载OSS配置\r\n    this.loadOSSConfig()\r\n\r\n    // 如果OSS已配置，则加载文件列表\r\n    if (this.ossInitialized) {\r\n      await this.loadMaterialList()\r\n    }\r\n  },\r\n  methods: {\r\n    // BGM免费下载\r\n    openBGMDownloadSite() {\r\n      window.open('https://www.buguyy.top/', '_blank')\r\n      this.$message.success('正在打开免费BGM下载网站...')\r\n    },\r\n\r\n    // 基础方法\r\n    async loadMaterialList() {\r\n      console.log('加载素材列表')\r\n\r\n      // 如果OSS已初始化，从OSS加载文件列表\r\n      if (this.ossInitialized) {\r\n        await this.loadFilesFromOSS()\r\n      } else {\r\n        console.log('OSS未初始化，跳过文件加载')\r\n      }\r\n\r\n      // 更新文件夹计数\r\n      this.updateCurrentTabFolderCounts()\r\n    },\r\n\r\n    // 从OSS加载文件列表\r\n    async loadFilesFromOSS() {\r\n      try {\r\n        const client = getOSSClient()\r\n        const user = this.getCurrentUser()\r\n        const currentFolder = this.getCurrentFolder()\r\n\r\n        // 清空现有列表\r\n        this.bgmList = []\r\n        this.sucaiList = []\r\n\r\n        // 加载BGM文件\r\n        await this.loadFilesFromFolder('abgm', user, currentFolder, 'bgm')\r\n\r\n        // 加载素材文件\r\n        await this.loadFilesFromFolder('asucai', user, currentFolder, 'sucai')\r\n\r\n        console.log(`从OSS加载文件完成 - BGM: ${this.bgmList.length}个, 素材: ${this.sucaiList.length}个`)\r\n\r\n      } catch (error) {\r\n        console.error('从OSS加载文件列表失败:', error)\r\n      }\r\n    },\r\n\r\n    // 从指定文件夹加载文件\r\n    async loadFilesFromFolder(baseFolder, user, folder, listType) {\r\n      try {\r\n        const client = getOSSClient()\r\n        const prefix = `${baseFolder}/${user}/${folder}/`\r\n\r\n        const result = await client.list({\r\n          prefix: prefix,\r\n          'max-keys': 1000\r\n        })\r\n\r\n        if (result.objects) {\r\n          const files = result.objects\r\n            .filter(obj => !obj.name.endsWith('.keep') && !obj.name.endsWith('/'))\r\n            .map((obj, index) => {\r\n              const fileName = obj.name.split('/').pop()\r\n              const fileExtension = fileName.toLowerCase().split('.').pop()\r\n\r\n              // 判断文件类型\r\n              const audioExts = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']\r\n              const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v']\r\n\r\n              let fileType = 'unknown'\r\n              if (audioExts.includes(fileExtension)) {\r\n                fileType = 'audio'\r\n              } else if (videoExts.includes(fileExtension)) {\r\n                fileType = 'video'\r\n              }\r\n\r\n              return {\r\n                id: Date.now() + Math.random() + index,\r\n                name: fileName,\r\n                type: fileType,\r\n                size: obj.size,\r\n                uploadTime: new Date(obj.lastModified).toLocaleString().slice(0, 16),\r\n                duration: fileType === 'video' ? '00:02:30' : '00:03:45', // 默认值，实际应该从文件元数据获取\r\n                resolution: fileType === 'video' ? '1920x1080' : undefined,\r\n                bitrate: fileType === 'audio' ? '128kbps' : undefined,\r\n                url: `https://${this.ossConfig.bucket}.${this.ossConfig.endpoint.replace('https://', '').replace(this.ossConfig.bucket + '.', '')}/${obj.name}`,\r\n                ossFileName: obj.name,\r\n                folder: `${baseFolder}/${user}/${folder}`\r\n              }\r\n            })\r\n\r\n          // 添加到对应的列表\r\n          if (listType === 'bgm') {\r\n            this.bgmList.push(...files)\r\n          } else if (listType === 'sucai') {\r\n            this.sucaiList.push(...files)\r\n          }\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error(`从文件夹 ${baseFolder}/${user}/${folder} 加载文件失败:`, error)\r\n      }\r\n    },\r\n\r\n    // OSS配置相关方法\r\n    loadOSSConfig() {\r\n      // 从localStorage加载OSS配置\r\n      const savedConfig = localStorage.getItem('ossConfig')\r\n      if (savedConfig) {\r\n        try {\r\n          this.ossConfig = { ...this.ossConfig, ...JSON.parse(savedConfig) }\r\n          if (this.ossConfig.accessKeyId && this.ossConfig.accessKeySecret) {\r\n            this.initializeOSS()\r\n          }\r\n        } catch (error) {\r\n          console.error('加载OSS配置失败:', error)\r\n        }\r\n      }\r\n    },\r\n\r\n    async saveOSSConfig() {\r\n      // 验证必填字段\r\n      if (!this.ossConfig.bucket) {\r\n        this.$message.error('请输入存储空间名称')\r\n        return\r\n      }\r\n      if (!this.ossConfig.accessKeyId) {\r\n        this.$message.error('请输入ACCESS_KEY')\r\n        return\r\n      }\r\n      if (!this.ossConfig.accessKeySecret) {\r\n        this.$message.error('请输入SECRET_KEY')\r\n        return\r\n      }\r\n      if (!this.ossConfig.endpoint) {\r\n        this.$message.error('请输入空间域名')\r\n        return\r\n      }\r\n\r\n      // 验证域名格式\r\n      if (!this.ossConfig.endpoint.startsWith('http://') && !this.ossConfig.endpoint.startsWith('https://')) {\r\n        this.$message.error('空间域名必须以http://或https://开头')\r\n        return\r\n      }\r\n\r\n      // 验证是否为标准的OSS域名格式\r\n      try {\r\n        const url = new URL(this.ossConfig.endpoint)\r\n        const hostname = url.hostname\r\n        const match = hostname.match(/^([^.]+)\\.oss-([^.]+)\\.aliyuncs\\.com$/)\r\n\r\n        if (!match) {\r\n          this.$message.error('请输入标准的OSS域名格式，如：https://bucket.oss-region.aliyuncs.com')\r\n          return\r\n        }\r\n\r\n        const [, bucket, region] = match\r\n        if (bucket !== this.ossConfig.bucket) {\r\n          this.$message.error(`域名中的存储桶名称(${bucket})与配置的存储桶名称(${this.ossConfig.bucket})不匹配`)\r\n          return\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('域名格式不正确')\r\n        return\r\n      }\r\n\r\n      this.testingOSS = true\r\n\r\n      try {\r\n        // 直接使用配置初始化OSS客户端\r\n        await this.initializeOSS()\r\n\r\n        // 保存配置到localStorage\r\n        localStorage.setItem('ossConfig', JSON.stringify(this.ossConfig))\r\n\r\n        this.ossConfigVisible = false\r\n        this.$message.success('OSS配置保存成功！')\r\n\r\n      } catch (error) {\r\n        console.error('OSS配置测试失败:', error)\r\n        this.$message.error(`OSS配置失败: ${error.message}`)\r\n      } finally {\r\n        this.testingOSS = false\r\n      }\r\n    },\r\n\r\n    async initializeOSS() {\r\n      try {\r\n        const client = initOSSClient(this.ossConfig)\r\n        this.ossInitialized = true\r\n\r\n        // OSS初始化成功后，获取用户的文件夹列表\r\n        await this.loadUserFoldersFromOSS()\r\n\r\n        console.log('OSS客户端初始化成功')\r\n        return client\r\n      } catch (error) {\r\n        this.ossInitialized = false\r\n        console.error('OSS客户端初始化失败:', error)\r\n        throw error\r\n      }\r\n    },\r\n\r\n    openOSSConfig() {\r\n      this.ossConfigVisible = true\r\n    },\r\n\r\n    // 显示新建文件夹对话框\r\n    showCreateFolderDialog() {\r\n      this.createFolderForm.name = ''\r\n      this.createFolderVisible = true\r\n    },\r\n\r\n    // 创建文件夹\r\n    async createFolder() {\r\n      console.log('🚀 开始创建文件夹')\r\n\r\n      if (!this.createFolderForm.name || this.createFolderForm.name.trim() === '') {\r\n        this.$message.error('文件夹名称不能为空')\r\n        return\r\n      }\r\n\r\n      const folderName = this.createFolderForm.name.trim()\r\n      console.log('📁 文件夹名称:', folderName)\r\n      console.log('📋 当前标签页:', this.materialTab)\r\n\r\n      // 检查当前标签页的文件夹是否已存在\r\n      const currentFolders = this.currentFolderTree\r\n      console.log('📂 当前文件夹列表:', currentFolders.map(f => f.name))\r\n\r\n      if (currentFolders.some(folder => folder.name === folderName)) {\r\n        this.$message.error(`${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹已存在`)\r\n        return\r\n      }\r\n\r\n      try {\r\n        console.log('✅ 开始创建本地文件夹记录')\r\n\r\n        // 先创建本地文件夹记录\r\n        const newFolder = {\r\n          id: Date.now() + Math.random(), // 使用时间戳避免ID冲突\r\n          name: folderName,\r\n          count: 0\r\n        }\r\n\r\n        if (this.materialTab === 'bgm') {\r\n          this.bgmFolderTree.push(newFolder)\r\n          console.log('📁 已添加到BGM文件夹树')\r\n        } else {\r\n          this.sucaiFolderTree.push(newFolder)\r\n          console.log('📁 已添加到素材文件夹树')\r\n        }\r\n\r\n        console.log('✅ 本地文件夹创建成功')\r\n        this.createFolderVisible = false\r\n        this.$message.success(`${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹创建成功`)\r\n\r\n        // 异步处理OSS创建，不阻塞UI\r\n        if (this.storageType === 'oss' && this.ossInitialized) {\r\n          this.createOSSFolderAsync(folderName)\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('❌ 创建文件夹失败:', error)\r\n        this.$message.error(`创建失败: ${error.message}`)\r\n      }\r\n    },\r\n\r\n    // 异步创建OSS文件夹，不阻塞UI\r\n    async createOSSFolderAsync(folderName) {\r\n      try {\r\n        console.log('🌐 开始异步创建OSS文件夹:', folderName)\r\n        await this.createOSSFolderForCurrentTab(folderName)\r\n        console.log('✅ OSS文件夹创建成功')\r\n      } catch (ossError) {\r\n        console.warn('⚠️ OSS文件夹创建失败:', ossError.message)\r\n      }\r\n    },\r\n\r\n    // OSS文件夹创建（旧方法，为两种类型都创建）\r\n    async createOSSFolder(folderName) {\r\n      try {\r\n        const client = getOSSClient()\r\n        const user = this.getCurrentUser()\r\n\r\n        // 为两种类型创建空文件夹（通过上传空文件实现）\r\n        const baseFolders = ['abgm', 'asucai']\r\n\r\n        for (const baseFolder of baseFolders) {\r\n          const folderPath = `${baseFolder}/${user}/${folderName}/.keep`\r\n\r\n          // 上传一个空文件来创建文件夹结构\r\n          await client.put(folderPath, Buffer.from(''))\r\n          console.log(`OSS文件夹创建: ${folderPath}`)\r\n        }\r\n\r\n        return true\r\n      } catch (error) {\r\n        console.error('OSS文件夹创建失败:', error)\r\n        throw error\r\n      }\r\n    },\r\n\r\n    // 只为当前标签页创建OSS文件夹\r\n    async createOSSFolderForCurrentTab(folderName) {\r\n      try {\r\n        console.log(`开始创建OSS文件夹: ${folderName} (${this.materialTab})`)\r\n\r\n        if (!folderName || !folderName.trim()) {\r\n          throw new Error('文件夹名称不能为空')\r\n        }\r\n\r\n        const client = getOSSClient()\r\n        if (!client) {\r\n          throw new Error('OSS客户端未初始化')\r\n        }\r\n\r\n        const user = this.getCurrentUser()\r\n        if (!user) {\r\n          throw new Error('无法获取当前用户信息')\r\n        }\r\n\r\n        // 根据当前标签页确定基础文件夹\r\n        const baseFolder = this.materialTab === 'bgm' ? 'abgm' : 'asucai'\r\n        const folderPath = `${baseFolder}/${user}/${folderName}/.keep`\r\n\r\n        console.log(`OSS文件夹路径: ${folderPath}`)\r\n\r\n        // 上传一个空文件来创建文件夹结构\r\n        await client.put(folderPath, Buffer.from(''))\r\n        console.log(`OSS文件夹创建成功 (${this.materialTab}): ${folderPath}`)\r\n\r\n        return true\r\n      } catch (error) {\r\n        console.error(`OSS文件夹创建失败 (${this.materialTab}):`, error)\r\n        throw new Error(`OSS文件夹创建失败: ${error.message}`)\r\n      }\r\n    },\r\n\r\n    // 处理文件双击事件\r\n    handleFileDoubleClick(file) {\r\n      // 只有视频、音频、图片类型才支持预览\r\n      if (['video', 'audio', 'image'].includes(file.type)) {\r\n        this.previewFile(file)\r\n      }\r\n    },\r\n\r\n    // 切换视频播放/暂停\r\n    toggleVideoPlay(file) {\r\n      const videoRef = this.$refs[`video-${file.id}`]\r\n      if (videoRef && videoRef.length > 0) {\r\n        const video = videoRef[0]\r\n\r\n        if (video.paused) {\r\n          // 暂停所有其他视频\r\n          this.pauseAllVideos()\r\n\r\n          // 取消静音并播放当前视频\r\n          video.muted = false\r\n          video.play()\r\n          this.$set(file, 'isPlaying', true)\r\n          this.$set(file, 'showControls', true)\r\n\r\n          // 开始3秒隐藏定时器\r\n          this.startControlTimer(file)\r\n        } else {\r\n          // 暂停当前视频并静音\r\n          video.pause()\r\n          video.muted = true\r\n          this.$set(file, 'isPlaying', false)\r\n          this.$set(file, 'showControls', true)\r\n\r\n          // 清除隐藏定时器\r\n          this.clearControlTimer(file.id)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 切换视频播放/暂停并缩放\r\n    toggleVideoPlayWithScale(file) {\r\n      const videoRef = this.$refs[`video-${file.id}`]\r\n      if (videoRef && videoRef.length > 0) {\r\n        const video = videoRef[0]\r\n\r\n        if (video.paused) {\r\n          // 暂停所有其他视频并取消缩放\r\n          this.pauseAllVideosAndResetScale()\r\n\r\n          // 等待视频元数据加载完成\r\n          const handleLoadedMetadata = () => {\r\n            // 获取视频的真实尺寸\r\n            const videoWidth = video.videoWidth\r\n            const videoHeight = video.videoHeight\r\n\r\n            if (videoWidth && videoHeight) {\r\n              // 计算合适的显示尺寸（最大宽度800px，最大高度600px）\r\n              const maxWidth = Math.min(800, window.innerWidth * 0.8)\r\n              const maxHeight = Math.min(600, window.innerHeight * 0.8)\r\n\r\n              const aspectRatio = videoWidth / videoHeight\r\n              let displayWidth, displayHeight\r\n\r\n              if (aspectRatio > maxWidth / maxHeight) {\r\n                // 视频比较宽，以宽度为准\r\n                displayWidth = maxWidth\r\n                displayHeight = maxWidth / aspectRatio\r\n              } else {\r\n                // 视频比较高，以高度为准\r\n                displayHeight = maxHeight\r\n                displayWidth = maxHeight * aspectRatio\r\n              }\r\n\r\n              // 设置视频的显示尺寸\r\n              this.$set(file, 'displayWidth', Math.round(displayWidth))\r\n              this.$set(file, 'displayHeight', Math.round(displayHeight))\r\n\r\n              console.log(`视频 ${file.name} 真实尺寸: ${videoWidth}x${videoHeight}, 显示尺寸: ${displayWidth}x${displayHeight}`)\r\n            }\r\n\r\n            // 移除事件监听器\r\n            video.removeEventListener('loadedmetadata', handleLoadedMetadata)\r\n          }\r\n\r\n          // 如果元数据已经加载，直接处理；否则等待加载\r\n          if (video.readyState >= 1) {\r\n            handleLoadedMetadata()\r\n          } else {\r\n            video.addEventListener('loadedmetadata', handleLoadedMetadata)\r\n          }\r\n\r\n          // 播放当前视频并放大\r\n          video.muted = false\r\n          video.play()\r\n          this.$set(file, 'isPlaying', true)\r\n          this.$set(file, 'isScaled', true) // 设置放大状态\r\n          this.$set(file, 'showControls', true)\r\n\r\n          console.log(`视频 ${file.name} 开始播放并放大，isScaled: ${file.isScaled}`)\r\n\r\n          // 添加背景遮罩\r\n          this.addBackdrop()\r\n\r\n          // 强制更新DOM以显示进度条\r\n          this.$nextTick(() => {\r\n            console.log('DOM更新完成，进度条应该显示')\r\n          })\r\n\r\n          // 开始3秒隐藏定时器\r\n          this.startControlTimer(file)\r\n        } else {\r\n          // 暂停视频并恢复大小\r\n          video.pause()\r\n          video.muted = true\r\n          this.$set(file, 'isPlaying', false)\r\n          this.$set(file, 'isScaled', false) // 取消放大状态\r\n          this.$set(file, 'displayWidth', null) // 清除显示宽度\r\n          this.$set(file, 'displayHeight', null) // 清除显示高度\r\n          this.$set(file, 'showControls', true)\r\n\r\n          console.log(`视频 ${file.name} 已暂停并恢复大小`)\r\n\r\n          // 移除背景遮罩\r\n          this.removeBackdrop()\r\n\r\n          // 清除隐藏定时器\r\n          this.clearControlTimer(file.id)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 暂停所有视频\r\n    pauseAllVideos() {\r\n      // 遍历所有文件，暂停正在播放的视频\r\n      this.paginatedMaterials.forEach(file => {\r\n        if (file.type === 'video' && file.isPlaying) {\r\n          const videoRef = this.$refs[`video-${file.id}`]\r\n          if (videoRef && videoRef.length > 0) {\r\n            const video = videoRef[0]\r\n            video.pause()\r\n            video.muted = true\r\n            this.$set(file, 'isPlaying', false)\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 暂停所有视频并重置缩放\r\n    pauseAllVideosAndResetScale() {\r\n      // 遍历所有文件，暂停正在播放的视频并重置缩放\r\n      this.paginatedMaterials.forEach(file => {\r\n        if (file.type === 'video') {\r\n          if (file.isPlaying) {\r\n            const videoRef = this.$refs[`video-${file.id}`]\r\n            if (videoRef && videoRef.length > 0) {\r\n              const video = videoRef[0]\r\n              video.pause()\r\n              video.muted = true\r\n              this.$set(file, 'isPlaying', false)\r\n            }\r\n          }\r\n          // 重置缩放状态和显示尺寸\r\n          this.$set(file, 'isScaled', false)\r\n          this.$set(file, 'displayWidth', null)\r\n          this.$set(file, 'displayHeight', null)\r\n        }\r\n      })\r\n\r\n      // 移除背景遮罩\r\n      this.removeBackdrop()\r\n    },\r\n\r\n    // 添加背景遮罩\r\n    addBackdrop() {\r\n      // 移除已存在的遮罩\r\n      this.removeBackdrop()\r\n\r\n      const backdrop = document.createElement('div')\r\n      backdrop.className = 'scale-enlarged-backdrop'\r\n      backdrop.id = 'video-scale-backdrop'\r\n      document.body.appendChild(backdrop)\r\n\r\n      // 点击遮罩关闭放大\r\n      backdrop.addEventListener('click', () => {\r\n        this.pauseAllVideosAndResetScale()\r\n      })\r\n    },\r\n\r\n    // 移除背景遮罩\r\n    removeBackdrop() {\r\n      const backdrop = document.getElementById('video-scale-backdrop')\r\n      if (backdrop) {\r\n        backdrop.remove()\r\n      }\r\n    },\r\n\r\n    // 获取视频播放进度百分比\r\n    getVideoProgress(file) {\r\n      const videoRef = this.$refs[`video-${file.id}`]\r\n      if (videoRef && videoRef.length > 0) {\r\n        const video = videoRef[0]\r\n        if (video.duration && video.currentTime) {\r\n          return (video.currentTime / video.duration) * 100\r\n        }\r\n      }\r\n      return 0\r\n    },\r\n\r\n    // 获取当前播放时间\r\n    getCurrentTime(file) {\r\n      const videoRef = this.$refs[`video-${file.id}`]\r\n      if (videoRef && videoRef.length > 0) {\r\n        const video = videoRef[0]\r\n        return video.currentTime || 0\r\n      }\r\n      return 0\r\n    },\r\n\r\n    // 获取视频总时长\r\n    getDuration(file) {\r\n      const videoRef = this.$refs[`video-${file.id}`]\r\n      if (videoRef && videoRef.length > 0) {\r\n        const video = videoRef[0]\r\n        return video.duration || 0\r\n      }\r\n      return 0\r\n    },\r\n\r\n    // 格式化时间显示\r\n    formatTime(seconds) {\r\n      if (!seconds || isNaN(seconds)) return '0:00'\r\n\r\n      const minutes = Math.floor(seconds / 60)\r\n      const remainingSeconds = Math.floor(seconds % 60)\r\n      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`\r\n    },\r\n\r\n    // 点击进度条跳转\r\n    seekVideo(event, file) {\r\n      const videoRef = this.$refs[`video-${file.id}`]\r\n      if (videoRef && videoRef.length > 0) {\r\n        const video = videoRef[0]\r\n        const progressBar = event.currentTarget.querySelector('.progress-track')\r\n        const rect = progressBar.getBoundingClientRect()\r\n        const clickX = event.clientX - rect.left\r\n        const percentage = clickX / rect.width\r\n        const newTime = percentage * video.duration\r\n\r\n        if (newTime >= 0 && newTime <= video.duration) {\r\n          video.currentTime = newTime\r\n        }\r\n      }\r\n    },\r\n\r\n    // 视频时间更新事件\r\n    onVideoTimeUpdate(file) {\r\n      // 强制更新进度条显示\r\n      this.$forceUpdate()\r\n    },\r\n\r\n    // 视频数据加载完成事件\r\n    onVideoLoadedData(file) {\r\n      // 视频数据加载完成，可以获取时长等信息\r\n      console.log(`视频 ${file.name} 数据加载完成`)\r\n    },\r\n\r\n    // 切换音频播放/暂停\r\n    toggleAudioPlay(file) {\r\n      const audioRef = this.$refs[`audio-${file.id}`]\r\n      if (audioRef && audioRef.length > 0) {\r\n        const audio = audioRef[0]\r\n\r\n        if (audio.paused) {\r\n          // 暂停所有其他音频和视频\r\n          this.pauseAllMedia()\r\n\r\n          // 播放当前音频\r\n          audio.play()\r\n          this.$set(file, 'isPlaying', true)\r\n          this.$set(file, 'showControls', true)\r\n\r\n          // 开始3秒隐藏定时器\r\n          this.startControlTimer(file)\r\n        } else {\r\n          // 暂停当前音频\r\n          audio.pause()\r\n          this.$set(file, 'isPlaying', false)\r\n          this.$set(file, 'showControls', true)\r\n\r\n          // 清除隐藏定时器\r\n          this.clearControlTimer(file.id)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 暂停所有媒体（音频和视频）\r\n    pauseAllMedia() {\r\n      this.pauseAllVideos()\r\n      this.pauseAllAudios()\r\n    },\r\n\r\n    // 暂停所有音频\r\n    pauseAllAudios() {\r\n      this.paginatedMaterials.forEach(file => {\r\n        if (file.type === 'audio' && file.isPlaying) {\r\n          const audioRef = this.$refs[`audio-${file.id}`]\r\n          if (audioRef && audioRef.length > 0) {\r\n            audioRef[0].pause()\r\n            this.$set(file, 'isPlaying', false)\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 预览文件\r\n    previewFile(file) {\r\n      // 构建文件URL\r\n      if (!file.url && this.ossInitialized) {\r\n        // 如果没有URL，构建OSS URL\r\n        const endpoint = this.ossConfig.endpoint.replace('https://', '').replace('http://', '')\r\n        file.url = `https://${endpoint}/${file.ossFileName}`\r\n      }\r\n\r\n      this.currentPreviewFile = file\r\n      this.previewVisible = true\r\n    },\r\n\r\n    // 获取文件URL\r\n    getFileUrl(file) {\r\n      if (file.url) {\r\n        return file.url\r\n      }\r\n\r\n      if (this.ossInitialized && file.ossFileName) {\r\n        const endpoint = this.ossConfig.endpoint.replace('https://', '').replace('http://', '')\r\n        return `https://${endpoint}/${file.ossFileName}`\r\n      }\r\n\r\n      return ''\r\n    },\r\n\r\n    // 停止所有媒体播放\r\n    stopAllMedia() {\r\n      // 停止预览对话框中的视频播放\r\n      if (this.$refs.previewVideo) {\r\n        this.$refs.previewVideo.pause()\r\n        this.$refs.previewVideo.currentTime = 0\r\n      }\r\n\r\n      // 停止预览对话框中的音频播放\r\n      if (this.$refs.previewAudio) {\r\n        this.$refs.previewAudio.pause()\r\n        this.$refs.previewAudio.currentTime = 0\r\n      }\r\n\r\n      // 停止缩略图中的所有媒体播放\r\n      this.pauseAllMedia()\r\n\r\n      // 清空预览文件\r\n      this.currentPreviewFile = null\r\n    },\r\n\r\n    // 视频加载完成\r\n    onVideoLoaded(event) {\r\n      // 可以在这里获取视频的第一帧作为缩略图\r\n      const video = event.target\r\n      video.currentTime = 1 // 跳到第1秒获取缩略图\r\n    },\r\n\r\n    // 视频播放结束\r\n    onVideoEnded(file) {\r\n      this.$set(file, 'isPlaying', false)\r\n      this.$set(file, 'isScaled', false)\r\n      this.$set(file, 'displayWidth', null)\r\n      this.$set(file, 'displayHeight', null)\r\n      this.removeBackdrop()\r\n    },\r\n\r\n    // 视频暂停\r\n    onVideoPaused(file) {\r\n      this.$set(file, 'isPlaying', false)\r\n      // 如果是放大状态，也要清理\r\n      if (file.isScaled) {\r\n        this.$set(file, 'isScaled', false)\r\n        this.$set(file, 'displayWidth', null)\r\n        this.$set(file, 'displayHeight', null)\r\n        this.removeBackdrop()\r\n      }\r\n\r\n      // 同步Portal视频\r\n      if (this.hoveredFile && this.hoveredFile.id === file.id) {\r\n        this.$nextTick(() => {\r\n          this.syncPortalVideo()\r\n        })\r\n      }\r\n    },\r\n\r\n    // 视频开始播放\r\n    onVideoPlayed(file) {\r\n      this.$set(file, 'isPlaying', true)\r\n\r\n      // 同步Portal视频\r\n      if (this.hoveredFile && this.hoveredFile.id === file.id) {\r\n        this.$nextTick(() => {\r\n          this.syncPortalVideo()\r\n        })\r\n      }\r\n    },\r\n\r\n    // 音频播放结束\r\n    onAudioEnded(file) {\r\n      this.$set(file, 'isPlaying', false)\r\n    },\r\n\r\n    // 音频暂停\r\n    onAudioPaused(file) {\r\n      this.$set(file, 'isPlaying', false)\r\n    },\r\n\r\n    // 音频开始播放\r\n    onAudioPlayed(file) {\r\n      this.$set(file, 'isPlaying', true)\r\n    },\r\n\r\n    // 图片加载错误\r\n    onImageError(event) {\r\n      // 图片加载失败时显示默认图标\r\n      event.target.style.display = 'none'\r\n      event.target.parentNode.innerHTML = '<i class=\"el-icon-picture-outline\"></i>'\r\n    },\r\n\r\n    // 视频鼠标进入事件\r\n    onVideoMouseEnter(file) {\r\n      console.log('视频鼠标进入:', file.name, '显示控制按钮')\r\n      this.$set(file, 'showControls', true)\r\n      this.clearControlTimer(file.id)\r\n    },\r\n\r\n    // 视频鼠标离开事件\r\n    onVideoMouseLeave(file) {\r\n      if (file.isPlaying) {\r\n        this.startControlTimer(file)\r\n      } else {\r\n        this.$set(file, 'showControls', false)\r\n      }\r\n    },\r\n\r\n    // 视频鼠标移动事件\r\n    onVideoMouseMove(file) {\r\n      this.$set(file, 'showControls', true)\r\n      if (file.isPlaying) {\r\n        this.clearControlTimer(file.id)\r\n        this.startControlTimer(file)\r\n      }\r\n    },\r\n\r\n    // 音频鼠标进入事件\r\n    onAudioMouseEnter(file) {\r\n      console.log('音频鼠标进入:', file.name)\r\n      this.$set(file, 'showControls', true)\r\n      this.clearControlTimer(file.id)\r\n    },\r\n\r\n    // 音频鼠标离开事件\r\n    onAudioMouseLeave(file) {\r\n      if (file.isPlaying) {\r\n        this.startControlTimer(file)\r\n      } else {\r\n        this.$set(file, 'showControls', false)\r\n      }\r\n    },\r\n\r\n    // 音频鼠标移动事件\r\n    onAudioMouseMove(file) {\r\n      this.$set(file, 'showControls', true)\r\n      if (file.isPlaying) {\r\n        this.clearControlTimer(file.id)\r\n        this.startControlTimer(file)\r\n      }\r\n    },\r\n\r\n    // 图片鼠标进入事件\r\n    onImageMouseEnter(file) {\r\n      console.log('图片鼠标进入:', file.name)\r\n    },\r\n\r\n    // 图片鼠标离开事件\r\n    onImageMouseLeave(file) {\r\n      console.log('图片鼠标离开:', file.name)\r\n    },\r\n\r\n    // 开始控制按钮隐藏定时器\r\n    startControlTimer(file) {\r\n      this.clearControlTimer(file.id)\r\n      this.controlTimers[file.id] = setTimeout(() => {\r\n        this.$set(file, 'showControls', false)\r\n      }, 3000) // 3秒后隐藏\r\n    },\r\n\r\n    // 清除控制按钮隐藏定时器\r\n    clearControlTimer(fileId) {\r\n      if (this.controlTimers[fileId]) {\r\n        clearTimeout(this.controlTimers[fileId])\r\n        delete this.controlTimers[fileId]\r\n      }\r\n    },\r\n\r\n\r\n\r\n\r\n\r\n    // 格式化文件大小\r\n    formatFileSize(bytes) {\r\n      if (bytes === 0) return '0 B'\r\n      const k = 1024\r\n      const sizes = ['B', 'KB', 'MB', 'GB']\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\r\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(file) {\r\n      if (file.url) {\r\n        const link = document.createElement('a')\r\n        link.href = file.url\r\n        link.download = file.name\r\n        link.target = '_blank'\r\n        document.body.appendChild(link)\r\n        link.click()\r\n        document.body.removeChild(link)\r\n      } else {\r\n        this.$message.error('文件链接不可用')\r\n      }\r\n    },\r\n\r\n    // 从OSS获取用户文件夹列表（分别为BGM和素材）\r\n    async loadUserFoldersFromOSS() {\r\n      try {\r\n        const client = getOSSClient()\r\n        const user = this.getCurrentUser()\r\n\r\n        // 分别获取BGM和素材的文件夹\r\n        await this.loadFoldersForType('abgm', 'bgmFolderTree')\r\n        await this.loadFoldersForType('asucai', 'sucaiFolderTree')\r\n\r\n        console.log('BGM文件夹:', this.bgmFolderTree.map(f => f.name))\r\n        console.log('素材文件夹:', this.sucaiFolderTree.map(f => f.name))\r\n\r\n      } catch (error) {\r\n        console.error('获取OSS文件夹列表失败:', error)\r\n        // 如果获取失败，至少显示默认的\"总\"文件夹\r\n        this.bgmFolderTree = [{ id: 1, name: '总', count: 0 }]\r\n        this.sucaiFolderTree = [{ id: 1, name: '总', count: 0 }]\r\n      }\r\n    },\r\n\r\n    // 为指定类型加载文件夹\r\n    async loadFoldersForType(baseFolder, treeProperty) {\r\n      try {\r\n        const client = getOSSClient()\r\n        const user = this.getCurrentUser()\r\n        const folderSet = new Set()\r\n        const prefix = `${baseFolder}/${user}/`\r\n\r\n        // 列出该前缀下的所有对象\r\n        const result = await client.list({\r\n          prefix: prefix,\r\n          delimiter: '/',\r\n          'max-keys': 1000\r\n        })\r\n\r\n        // 从commonPrefixes中提取文件夹名称\r\n        if (result.prefixes) {\r\n          result.prefixes.forEach(prefixInfo => {\r\n            const fullPrefix = prefixInfo.name || prefixInfo\r\n            // 提取文件夹名称：abgm/admin/总/ -> 总\r\n            if (fullPrefix && typeof fullPrefix === 'string') {\r\n              const folderName = fullPrefix.replace(prefix, '').replace('/', '')\r\n              if (folderName) {\r\n                folderSet.add(folderName)\r\n              }\r\n            }\r\n          })\r\n        }\r\n\r\n        // 如果有objects，也从中提取文件夹名称\r\n        if (result.objects) {\r\n          result.objects.forEach(obj => {\r\n            const objectKey = obj.name\r\n            if (objectKey.startsWith(prefix)) {\r\n              const relativePath = objectKey.replace(prefix, '')\r\n              const folderName = relativePath.split('/')[0]\r\n              if (folderName && folderName !== '.keep') {\r\n                folderSet.add(folderName)\r\n              }\r\n            }\r\n          })\r\n        }\r\n\r\n        // 转换为文件夹树格式\r\n        const folders = Array.from(folderSet).map((folderName, index) => ({\r\n          id: Date.now() + index, // 使用时间戳避免ID冲突\r\n          name: folderName,\r\n          count: 0 // 稍后可以计算实际文件数量\r\n        }))\r\n\r\n        // 确保\"总\"文件夹存在\r\n        if (!folderSet.has('总')) {\r\n          folders.unshift({ id: Date.now(), name: '总', count: 0 })\r\n        }\r\n\r\n        // 更新对应的文件夹树\r\n        if (baseFolder === 'abgm') {\r\n          this.bgmFolderTree = folders\r\n        } else {\r\n          this.sucaiFolderTree = folders\r\n        }\r\n\r\n        console.log(`${baseFolder} 文件夹加载完成:`, folders.map(f => f.name))\r\n\r\n      } catch (error) {\r\n        console.error(`获取 ${baseFolder} 文件夹列表失败:`, error)\r\n        // 如果获取失败，至少显示默认的\"总\"文件夹\r\n        const defaultFolder = [{ id: Date.now(), name: '总', count: 0 }]\r\n        if (baseFolder === 'abgm') {\r\n          this.bgmFolderTree = defaultFolder\r\n        } else {\r\n          this.sucaiFolderTree = defaultFolder\r\n        }\r\n      }\r\n    },\r\n\r\n    // 更新文件夹的文件数量\r\n    async updateFolderFileCounts() {\r\n      try {\r\n        const client = getOSSClient()\r\n        const user = this.getCurrentUser()\r\n\r\n        // 由于现在使用分离的文件夹树，这个方法暂时禁用\r\n        // 可以根据需要为每个标签页单独更新文件数量\r\n        console.log('文件夹数量更新已改为分离模式，请使用具体的标签页更新方法')\r\n        return\r\n\r\n        for (let folder of []) { // 临时禁用\r\n          let totalCount = 0\r\n\r\n          // 检查两种类型的文件夹中的文件数量\r\n          const baseFolders = ['abgm', 'asucai']\r\n\r\n          for (const baseFolder of baseFolders) {\r\n            const prefix = `${baseFolder}/${user}/${folder.name}/`\r\n\r\n            try {\r\n              const result = await client.list({\r\n                prefix: prefix,\r\n                'max-keys': 1000\r\n              })\r\n\r\n              if (result.objects) {\r\n                // 过滤掉.keep文件\r\n                const fileCount = result.objects.filter(obj =>\r\n                  !obj.name.endsWith('.keep') && !obj.name.endsWith('/')\r\n                ).length\r\n                totalCount += fileCount\r\n              }\r\n            } catch (error) {\r\n              console.warn(`获取文件夹 ${folder.name} 在 ${baseFolder} 中的文件数量失败:`, error)\r\n            }\r\n          }\r\n\r\n          folder.count = totalCount\r\n        }\r\n\r\n        console.log('文件夹文件数量更新完成:', this.simpleFolderTree)\r\n\r\n      } catch (error) {\r\n        console.error('更新文件夹文件数量失败:', error)\r\n      }\r\n    },\r\n\r\n    // 文件夹右键菜单相关方法\r\n    showFolderContextMenu(event, folder) {\r\n      this.contextMenuVisible = true\r\n      this.contextMenuX = event.clientX\r\n      this.contextMenuY = event.clientY\r\n      this.contextMenuFolder = folder\r\n\r\n      // 点击其他地方隐藏菜单\r\n      document.addEventListener('click', this.hideFolderContextMenu)\r\n    },\r\n\r\n    hideFolderContextMenu() {\r\n      this.contextMenuVisible = false\r\n      this.contextMenuFolder = null\r\n      document.removeEventListener('click', this.hideFolderContextMenu)\r\n    },\r\n\r\n    // 重命名文件夹\r\n    async renameFolderAction() {\r\n      if (!this.contextMenuFolder) {\r\n        this.$message.error('未选择文件夹')\r\n        return\r\n      }\r\n\r\n      const folderName = this.contextMenuFolder.name || this.contextMenuFolder\r\n\r\n      this.$prompt('请输入新的文件夹名称', '重命名文件夹', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        inputValue: folderName\r\n      }).then(async ({ value }) => {\r\n        try {\r\n          if (!value || value.trim() === '') {\r\n            this.$message.error('文件夹名称不能为空')\r\n            return\r\n          }\r\n\r\n          const newFolderName = value.trim()\r\n          const oldFolderName = this.contextMenuFolder.name || this.contextMenuFolder\r\n\r\n          if (newFolderName === oldFolderName) {\r\n            this.hideFolderContextMenu()\r\n            return\r\n          }\r\n\r\n          // 如果使用OSS存储，需要重命名OSS中的文件夹\r\n          if (this.storageType === 'oss' && this.ossInitialized) {\r\n            await this.renameOSSFolder(oldFolderName, newFolderName)\r\n          }\r\n\r\n          // 更新前端文件夹名称\r\n          if (typeof this.contextMenuFolder === 'object') {\r\n            this.contextMenuFolder.name = newFolderName\r\n          }\r\n\r\n          // 如果当前选中的是被重命名的文件夹，更新当前文件夹\r\n          if (this.currentFolder === oldFolderName) {\r\n            this.currentFolder = newFolderName\r\n          }\r\n\r\n          this.$message.success('文件夹重命名成功')\r\n          this.hideFolderContextMenu()\r\n\r\n        } catch (error) {\r\n          console.error('重命名文件夹失败:', error)\r\n          this.$message.error(`重命名失败: ${error.message}`)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 删除文件夹\r\n    async deleteFolderAction() {\r\n      console.log('删除文件夹操作开始，contextMenuFolder:', this.contextMenuFolder)\r\n\r\n      if (!this.contextMenuFolder) {\r\n        this.$message.error('未选择文件夹')\r\n        return\r\n      }\r\n\r\n      // 安全地获取文件夹名称\r\n      let folderName\r\n      if (typeof this.contextMenuFolder === 'string') {\r\n        folderName = this.contextMenuFolder\r\n      } else if (this.contextMenuFolder && this.contextMenuFolder.name) {\r\n        folderName = this.contextMenuFolder.name\r\n      } else {\r\n        console.error('无法获取文件夹名称，contextMenuFolder:', this.contextMenuFolder)\r\n        this.$message.error('无法获取文件夹名称')\r\n        return\r\n      }\r\n\r\n      console.log('准备删除文件夹:', folderName)\r\n\r\n      if (folderName === '总') {\r\n        this.$message.warning('不能删除\"总\"文件夹')\r\n        return\r\n      }\r\n\r\n      this.$confirm(`确定要删除文件夹\"${folderName}\"吗？文件夹内的所有文件也会被删除！`, '删除文件夹', {\r\n        confirmButtonText: '确定删除',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          // 使用之前获取的folderName，避免在异步操作中contextMenuFolder变为null\r\n          const targetFolderName = folderName\r\n\r\n          // 如果使用OSS存储，只删除当前标签页的OSS文件夹\r\n          if (this.storageType === 'oss' && this.ossInitialized) {\r\n            try {\r\n              await this.deleteOSSFolderForCurrentTab(targetFolderName)\r\n              console.log(`${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹删除成功`)\r\n            } catch (ossError) {\r\n              console.warn('OSS文件夹删除失败，但继续删除前端记录:', ossError.message)\r\n              // 即使OSS删除失败，也继续删除前端记录（可能是空文件夹）\r\n            }\r\n          }\r\n\r\n          // 从前端移除文件夹相关的文件\r\n          this.removeFilesFromFolder(targetFolderName)\r\n\r\n          // 如果当前选中的是被删除的文件夹，切换到\"总\"文件夹\r\n          if (this.currentFolder === targetFolderName) {\r\n            this.currentFolder = '总'\r\n          }\r\n\r\n          this.$message.success('文件夹删除成功')\r\n          this.hideFolderContextMenu()\r\n\r\n        } catch (error) {\r\n          console.error('删除文件夹失败:', error)\r\n          this.$message.error(`删除失败: ${error.message}`)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 获取当前用户名\r\n    getCurrentUser() {\r\n      // 从store中获取用户信息\r\n      return this.$store.state.user.name || 'admin'\r\n    },\r\n\r\n    // 获取当前文件夹名称\r\n    getCurrentFolder() {\r\n      return this.currentFolder || '总'\r\n    },\r\n\r\n    // 构建OSS上传路径\r\n    buildOSSPath(baseFolder) {\r\n      const user = this.getCurrentUser()\r\n      const folder = this.getCurrentFolder()\r\n      return `${baseFolder}/${user}/${folder}`\r\n    },\r\n\r\n    // OSS文件复制和删除（用于重命名）\r\n    async copyAndDeleteOSSFile(oldPath, newPath) {\r\n      try {\r\n        const client = getOSSClient()\r\n\r\n        // 复制文件到新路径\r\n        await client.copy(newPath, oldPath)\r\n        console.log(`OSS文件复制成功: ${oldPath} -> ${newPath}`)\r\n\r\n        // 删除原文件\r\n        await client.delete(oldPath)\r\n        console.log(`OSS原文件删除成功: ${oldPath}`)\r\n\r\n        return true\r\n      } catch (error) {\r\n        console.error('OSS文件复制删除失败:', error)\r\n        throw error\r\n      }\r\n    },\r\n\r\n    // OSS文件夹重命名\r\n    async renameOSSFolder(oldFolderName, newFolderName) {\r\n      try {\r\n        const client = getOSSClient()\r\n        const user = this.getCurrentUser()\r\n\r\n        // 获取两种类型文件夹的路径\r\n        const baseFolders = ['abgm', 'asucai']\r\n\r\n        for (const baseFolder of baseFolders) {\r\n          const oldPrefix = `${baseFolder}/${user}/${oldFolderName}/`\r\n          const newPrefix = `${baseFolder}/${user}/${newFolderName}/`\r\n\r\n          // 列出文件夹中的所有文件\r\n          const result = await client.list({\r\n            prefix: oldPrefix,\r\n            'max-keys': 1000\r\n          })\r\n\r\n          if (result.objects && result.objects.length > 0) {\r\n            // 复制所有文件到新路径\r\n            for (const obj of result.objects) {\r\n              const newKey = obj.name.replace(oldPrefix, newPrefix)\r\n              await client.copy(newKey, obj.name)\r\n              console.log(`OSS文件夹重命名: ${obj.name} -> ${newKey}`)\r\n            }\r\n\r\n            // 删除原文件\r\n            for (const obj of result.objects) {\r\n              await client.delete(obj.name)\r\n            }\r\n          }\r\n        }\r\n\r\n        return true\r\n      } catch (error) {\r\n        console.error('OSS文件夹重命名失败:', error)\r\n        throw error\r\n      }\r\n    },\r\n\r\n    // OSS文件夹删除\r\n    async deleteOSSFolder(folderName) {\r\n      try {\r\n        console.log('开始删除OSS文件夹:', folderName)\r\n\r\n        if (!folderName) {\r\n          throw new Error('文件夹名称不能为空')\r\n        }\r\n\r\n        const client = getOSSClient()\r\n        if (!client) {\r\n          throw new Error('OSS客户端未初始化')\r\n        }\r\n\r\n        const user = this.getCurrentUser()\r\n        console.log('当前用户:', user)\r\n\r\n        // 获取两种类型文件夹的路径\r\n        const baseFolders = ['abgm', 'asucai']\r\n        let totalDeletedFiles = 0\r\n\r\n        for (const baseFolder of baseFolders) {\r\n          const prefix = `${baseFolder}/${user}/${folderName}/`\r\n          console.log('检查OSS路径:', prefix)\r\n\r\n          // 列出文件夹中的所有文件\r\n          const result = await client.list({\r\n            prefix: prefix,\r\n            'max-keys': 1000\r\n          })\r\n\r\n          console.log(`路径 ${prefix} 下找到 ${result.objects ? result.objects.length : 0} 个文件`)\r\n\r\n          if (result.objects && result.objects.length > 0) {\r\n            // 删除所有文件\r\n            for (const obj of result.objects) {\r\n              await client.delete(obj.name)\r\n              console.log(`OSS文件删除: ${obj.name}`)\r\n              totalDeletedFiles++\r\n            }\r\n          }\r\n        }\r\n\r\n        console.log(`OSS文件夹删除完成，共删除 ${totalDeletedFiles} 个文件`)\r\n        return true\r\n      } catch (error) {\r\n        console.error('OSS文件夹删除失败:', error)\r\n        throw new Error(`删除OSS文件夹失败: ${error.message}`)\r\n      }\r\n    },\r\n\r\n    // 只删除当前标签页的OSS文件夹\r\n    async deleteOSSFolderForCurrentTab(folderName) {\r\n      try {\r\n        console.log(`开始删除${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹:`, folderName)\r\n\r\n        if (!folderName) {\r\n          throw new Error('文件夹名称不能为空')\r\n        }\r\n\r\n        const client = getOSSClient()\r\n        if (!client) {\r\n          throw new Error('OSS客户端未初始化')\r\n        }\r\n\r\n        const user = this.getCurrentUser()\r\n        const baseFolder = this.materialTab === 'bgm' ? 'abgm' : 'asucai'\r\n        const prefix = `${baseFolder}/${user}/${folderName}/`\r\n\r\n        console.log('删除OSS路径:', prefix)\r\n\r\n        // 列出文件夹中的所有文件\r\n        const result = await client.list({\r\n          prefix: prefix,\r\n          'max-keys': 1000\r\n        })\r\n\r\n        let deletedFiles = 0\r\n        if (result.objects && result.objects.length > 0) {\r\n          // 删除所有文件\r\n          for (const obj of result.objects) {\r\n            await client.delete(obj.name)\r\n            console.log(`OSS文件删除: ${obj.name}`)\r\n            deletedFiles++\r\n          }\r\n        }\r\n\r\n        console.log(`${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹删除完成，共删除 ${deletedFiles} 个文件`)\r\n        return true\r\n      } catch (error) {\r\n        console.error(`删除${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹失败:`, error)\r\n        throw new Error(`删除${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹失败: ${error.message}`)\r\n      }\r\n    },\r\n\r\n    // 从前端移除文件夹相关的文件（只删除当前标签页的）\r\n    removeFilesFromFolder(folderName) {\r\n      if (this.materialTab === 'bgm') {\r\n        // 只移除BGM列表中该文件夹的文件\r\n        this.bgmList = this.bgmList.filter(file => {\r\n          const fileFolderName = file.folder ? file.folder.split('/').pop() : '总'\r\n          return fileFolderName !== folderName\r\n        })\r\n\r\n        // 从BGM文件夹树中移除\r\n        const treeIndex = this.bgmFolderTree.findIndex(folder => folder.name === folderName)\r\n        if (treeIndex > -1) {\r\n          this.bgmFolderTree.splice(treeIndex, 1)\r\n          console.log(`从BGM文件夹树中移除: ${folderName}`)\r\n        }\r\n      } else {\r\n        // 只移除素材列表中该文件夹的文件\r\n        this.sucaiList = this.sucaiList.filter(file => {\r\n          const fileFolderName = file.folder ? file.folder.split('/').pop() : '总'\r\n          return fileFolderName !== folderName\r\n        })\r\n\r\n        // 从素材文件夹树中移除\r\n        const treeIndex = this.sucaiFolderTree.findIndex(folder => folder.name === folderName)\r\n        if (treeIndex > -1) {\r\n          this.sucaiFolderTree.splice(treeIndex, 1)\r\n          console.log(`从素材文件夹树中移除: ${folderName}`)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 文件夹选择\r\n    async selectFolder(folder) {\r\n      this.selectedFolder = folder.id\r\n      this.currentFolder = folder.name\r\n      console.log('选择文件夹:', folder.name)\r\n\r\n      // 切换文件夹时重新加载文件列表\r\n      if (this.ossInitialized) {\r\n        await this.loadMaterialList()\r\n      }\r\n\r\n      // 更新文件夹计数\r\n      this.updateCurrentTabFolderCounts()\r\n    },\r\n\r\n    // 更新当前标签页的文件夹计数\r\n    updateCurrentTabFolderCounts() {\r\n      const currentMaterials = this.currentMaterialList\r\n      const currentFolders = this.currentFolderTree\r\n\r\n      // 为每个文件夹计算文件数量\r\n      currentFolders.forEach(folder => {\r\n        if (folder.name === '总') {\r\n          // \"总\"文件夹显示所有文件数量\r\n          folder.count = currentMaterials.length\r\n        } else {\r\n          // 其他文件夹只计算属于该文件夹的文件\r\n          folder.count = currentMaterials.filter(file => {\r\n            const fileFolderName = file.folder ? file.folder.split('/').pop() : '总'\r\n            return fileFolderName === folder.name\r\n          }).length\r\n        }\r\n      })\r\n\r\n      console.log(`${this.materialTab} 文件夹计数更新:`, currentFolders.map(f => `${f.name}(${f.count})`))\r\n    },\r\n\r\n    // 文件选择相关方法\r\n    handleSelectAll(checked) {\r\n      if (checked) {\r\n        this.selectedFiles = this.paginatedMaterials.map(f => f.id)\r\n      } else {\r\n        this.selectedFiles = []\r\n      }\r\n    },\r\n\r\n    // 预览文件\r\n    handlePreview() {\r\n      if (this.selectedFiles.length !== 1) {\r\n        this.$message.warning('请选择一个文件进行预览')\r\n        return\r\n      }\r\n\r\n      const file = this.currentMaterialList.find(f => f.id === this.selectedFiles[0])\r\n      if (!file) {\r\n        this.$message.error('文件不存在')\r\n        return\r\n      }\r\n\r\n      // 构建文件URL\r\n      if (!file.url && this.ossInitialized) {\r\n        // 如果没有URL，构建OSS URL\r\n        const endpoint = this.ossConfig.endpoint.replace('https://', '').replace('http://', '')\r\n        file.url = `https://${endpoint}/${file.ossFileName}`\r\n      }\r\n\r\n      this.currentPreviewFile = file\r\n      this.previewVisible = true\r\n    },\r\n\r\n    toggleFileSelection(fileId) {\r\n      const index = this.selectedFiles.indexOf(fileId)\r\n      if (index > -1) {\r\n        this.selectedFiles.splice(index, 1)\r\n      } else {\r\n        this.selectedFiles.push(fileId)\r\n      }\r\n\r\n      // 更新全选状态\r\n      this.selectAll = this.selectedFiles.length === this.paginatedMaterials.length\r\n    },\r\n\r\n    getSimpleFileIcon(type) {\r\n      const iconMap = {\r\n        'video': '🎬',\r\n        'image': '🖼️',\r\n        'audio': '🎵'\r\n      }\r\n      return iconMap[type] || '📄'\r\n    },\r\n\r\n    // 标签页切换\r\n    switchTab(tab) {\r\n      this.materialTab = tab\r\n      this.currentPage = 1 // 重置页码\r\n      this.selectedFiles = [] // 清空选择\r\n      this.selectAll = false\r\n\r\n      const tabNames = {\r\n        'bgm': 'BGM',\r\n        'sucai': '素材'\r\n      }\r\n      this.$message.success(`切换到${tabNames[tab] || tab}上传页面`)\r\n\r\n      // 切换标签页后更新文件夹计数\r\n      this.$nextTick(() => {\r\n        this.updateCurrentTabFolderCounts()\r\n      })\r\n    },\r\n\r\n    // 文件操作\r\n    async handleRename() {\r\n      if (this.selectedFiles.length === 0) {\r\n        this.$message.warning('请先选择要重命名的文件')\r\n        return\r\n      }\r\n      if (this.selectedFiles.length > 1) {\r\n        this.$message.warning('一次只能重命名一个文件')\r\n        return\r\n      }\r\n\r\n      const file = this.currentMaterialList.find(f => f.id === this.selectedFiles[0])\r\n      this.$prompt('请输入新的文件名', '重命名文件', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        inputValue: file.name\r\n      }).then(async ({ value }) => {\r\n        try {\r\n          // 验证新文件名\r\n          if (!value || value.trim() === '') {\r\n            this.$message.error('文件名不能为空')\r\n            return\r\n          }\r\n\r\n          const newFileName = value.trim()\r\n\r\n          // 如果使用OSS存储，需要重命名OSS中的文件\r\n          if (this.storageType === 'oss' && this.ossInitialized) {\r\n            // 构建原文件路径\r\n            let oldOssFilePath = ''\r\n            if (file.ossFileName) {\r\n              oldOssFilePath = file.ossFileName\r\n            } else {\r\n              const baseFolderName = this.materialTab === 'bgm' ? 'abgm' : 'asucai'\r\n              const fullPath = this.buildOSSPath(baseFolderName)\r\n              oldOssFilePath = `${fullPath}/${file.name}`\r\n            }\r\n\r\n            // 构建新文件路径\r\n            const baseFolderName = this.materialTab === 'bgm' ? 'abgm' : 'asucai'\r\n            const fullPath = this.buildOSSPath(baseFolderName)\r\n            const newOssFilePath = `${fullPath}/${newFileName}`\r\n\r\n            console.log(`OSS重命名: ${oldOssFilePath} -> ${newOssFilePath}`)\r\n\r\n            try {\r\n              // OSS不支持直接重命名，需要复制后删除\r\n              await this.copyAndDeleteOSSFile(oldOssFilePath, newOssFilePath)\r\n\r\n              // 更新文件信息\r\n              file.name = newFileName\r\n              file.ossFileName = newOssFilePath\r\n\r\n              this.$message.success('重命名成功')\r\n            } catch (error) {\r\n              console.error('OSS重命名失败:', error)\r\n              this.$message.error(`重命名失败: ${error.message}`)\r\n            }\r\n          } else {\r\n            // 本地存储，直接更新文件名\r\n            file.name = newFileName\r\n            this.$message.success('重命名成功')\r\n          }\r\n\r\n        } catch (error) {\r\n          console.error('重命名失败:', error)\r\n          this.$message.error(`重命名失败: ${error.message}`)\r\n        }\r\n      })\r\n    },\r\n\r\n    async handleDelete() {\r\n      if (this.selectedFiles.length === 0) {\r\n        this.$message.warning('请先选择要删除的文件')\r\n        return\r\n      }\r\n\r\n      this.$confirm(`确定要删除选中的 ${this.selectedFiles.length} 个文件吗？删除后无法恢复！`, '删除文件', {\r\n        confirmButtonText: '确定删除',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          const currentList = this.materialTab === 'bgm' ? this.bgmList : this.sucaiList\r\n\r\n          // 获取要删除的文件信息\r\n          const filesToDelete = this.selectedFiles.map(fileId => {\r\n            return currentList.find(f => f.id === fileId)\r\n          }).filter(file => file) // 过滤掉找不到的文件\r\n\r\n          console.log('准备删除的文件:', filesToDelete)\r\n\r\n          // 如果使用OSS存储，删除OSS中的文件\r\n          if (this.storageType === 'oss' && this.ossInitialized) {\r\n            const deletePromises = filesToDelete.map(async (file) => {\r\n              try {\r\n                // 构建OSS文件路径\r\n                let ossFilePath = ''\r\n                if (file.ossFileName) {\r\n                  // 如果有OSS文件名，直接使用\r\n                  ossFilePath = file.ossFileName\r\n                } else {\r\n                  // 否则根据文件夹和文件名构建路径\r\n                  const baseFolderName = this.materialTab === 'bgm' ? 'abgm' : 'asucai'\r\n                  const fullPath = this.buildOSSPath(baseFolderName)\r\n                  ossFilePath = `${fullPath}/${file.name}`\r\n                }\r\n\r\n                console.log(`删除OSS文件: ${ossFilePath}`)\r\n                await deleteFileFromOSS(ossFilePath)\r\n                return { success: true, file: file.name }\r\n              } catch (error) {\r\n                console.error(`删除OSS文件失败: ${file.name}`, error)\r\n                return { success: false, file: file.name, error: error.message }\r\n              }\r\n            })\r\n\r\n            const deleteResults = await Promise.all(deletePromises)\r\n\r\n            // 检查删除结果\r\n            const failedDeletes = deleteResults.filter(result => !result.success)\r\n            if (failedDeletes.length > 0) {\r\n              console.warn('部分文件删除失败:', failedDeletes)\r\n              this.$message.warning(`${failedDeletes.length} 个文件删除失败，但已从列表中移除`)\r\n            }\r\n          }\r\n\r\n          // 从前端列表中删除文件\r\n          this.selectedFiles.forEach(fileId => {\r\n            const index = currentList.findIndex(f => f.id === fileId)\r\n            if (index > -1) {\r\n              currentList.splice(index, 1)\r\n            }\r\n          })\r\n\r\n          this.selectedFiles = []\r\n          this.selectAll = false\r\n          this.$message.success('删除成功')\r\n\r\n        } catch (error) {\r\n          console.error('删除失败:', error)\r\n          this.$message.error(`删除失败: ${error.message}`)\r\n        }\r\n      })\r\n    },\r\n    showUploadDialog() {\r\n      this.uploadDialogVisible = true\r\n      this.fileList = []\r\n      this.uploadForm = {}\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      // 添加调试信息\r\n      console.log('上传文件信息:', {\r\n        name: file.name,\r\n        type: file.type,\r\n        size: file.size,\r\n        currentTab: this.materialTab\r\n      })\r\n\r\n      // 根据标签页检查文件大小\r\n      let maxSize = 500 // 默认500MB\r\n      if (this.materialTab === 'bgm') {\r\n        maxSize = 100 // BGM文件100MB\r\n      }\r\n\r\n      const isValidSize = file.size / 1024 / 1024 < maxSize\r\n      if (!isValidSize) {\r\n        this.$message.error(`文件大小不能超过 ${maxSize}MB！`)\r\n        return false\r\n      }\r\n\r\n      // 根据标签页检查文件格式\r\n      const extension = file.name.toLowerCase().split('.').pop()\r\n      const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v']\r\n      const audioExts = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']\r\n      const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']\r\n\r\n      if (this.materialTab === 'bgm' && !audioExts.includes(extension)) {\r\n        this.$message.error(`BGM只支持音频文件！支持格式：${audioExts.join(', ')}`)\r\n        return false\r\n      }\r\n\r\n      if (this.materialTab === 'sucai') {\r\n        const allExts = [...videoExts, ...audioExts, ...imageExts]\r\n        if (!allExts.includes(extension)) {\r\n          this.$message.error(`素材支持视频、音频、图片文件！支持格式：${allExts.join(', ')}`)\r\n          return false\r\n        }\r\n      }\r\n\r\n      return true\r\n    },\r\n\r\n    isValidFileType(type, fileName) {\r\n      // 获取文件扩展名作为备用验证\r\n      const extension = fileName.toLowerCase().split('.').pop()\r\n\r\n      if (this.materialTab === 'video') {\r\n        const videoTypes = [\r\n          'video/mp4', 'video/mpeg', 'video/quicktime', 'video/x-msvideo',\r\n          'video/x-ms-wmv', 'video/x-flv', 'video/webm', 'video/3gpp',\r\n          'video/mp2t', 'video/x-m4v'\r\n        ]\r\n        const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v', 'ts']\r\n\r\n        return videoTypes.includes(type) || videoExtensions.includes(extension)\r\n      } else {\r\n        const audioTypes = [\r\n          'audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/x-wav',\r\n          'audio/flac', 'audio/aac', 'audio/mp4', 'audio/x-m4a',\r\n          'audio/ogg', 'audio/webm'\r\n        ]\r\n        const audioExtensions = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']\r\n\r\n        return audioTypes.includes(type) || audioExtensions.includes(extension)\r\n      }\r\n    },\r\n    handleUploadSuccess(response, file) {\r\n      // 创建新的文件对象\r\n      const newFile = {\r\n        id: Date.now() + Math.random(),\r\n        name: file.name,\r\n        type: this.materialTab === 'video' ? 'video' : 'audio',\r\n        size: file.size,\r\n        uploadTime: new Date().toLocaleString().slice(0, 16),\r\n        duration: '00:00:00', // 实际应用中应该从服务器返回\r\n        resolution: this.materialTab === 'video' ? '1920x1080' : undefined,\r\n        bitrate: this.materialTab === 'music' ? '128kbps' : undefined\r\n      }\r\n\r\n      // 添加到对应的列表\r\n      if (this.materialTab === 'video') {\r\n        this.videoList.unshift(newFile)\r\n      } else {\r\n        this.musicList.unshift(newFile)\r\n      }\r\n\r\n      this.$message.success(`${file.name} 上传成功！`)\r\n    },\r\n\r\n    handleUploadError(err, file) {\r\n      this.$message.error(`${file.name} 上传失败！`)\r\n    },\r\n\r\n    handleUploadProgress(event, file) {\r\n      // 计算上传进度百分比\r\n      const progress = Math.round((event.loaded / event.total) * 100)\r\n\r\n      // 更新进度条显示\r\n      this.$set(this.uploadProgress, file.name, progress)\r\n\r\n      console.log(`文件 ${file.name} 上传进度: ${progress}%`)\r\n    },\r\n\r\n    handleFileChange(file, fileList) {\r\n      console.log('文件变化:', file)\r\n      console.log('当前文件列表:', fileList)\r\n      this.fileList = fileList\r\n    },\r\n\r\n    handleRemove(file, fileList) {\r\n      console.log('移除文件:', file)\r\n      console.log('更新后的文件列表:', fileList)\r\n      this.fileList = fileList\r\n    },\r\n\r\n    async submitUpload() {\r\n      console.log('当前文件列表:', this.fileList)\r\n      console.log('文件列表长度:', this.fileList.length)\r\n\r\n      if (!this.fileList || this.fileList.length === 0) {\r\n        this.$message.warning('请先选择要上传的文件')\r\n        return\r\n      }\r\n\r\n      this.uploading = true\r\n      const fileCount = this.fileList.length\r\n\r\n      // 根据标签页确定文件夹和类型\r\n      let baseFolderName = ''\r\n      let fileType = ''\r\n\r\n      switch (this.materialTab) {\r\n        case 'bgm':\r\n          baseFolderName = 'abgm'\r\n          fileType = 'audio'\r\n          break\r\n        case 'sucai':\r\n          baseFolderName = 'asucai'\r\n          fileType = 'mixed' // 混合类型\r\n          break\r\n        default:\r\n          baseFolderName = 'abgm'\r\n          fileType = 'audio'\r\n      }\r\n\r\n      // 构建完整的OSS路径：baseFolderName/用户名/文件夹名\r\n      const folderName = this.buildOSSPath(baseFolderName)\r\n\r\n      console.log('当前标签页:', this.materialTab)\r\n      console.log('基础文件夹:', baseFolderName)\r\n      console.log('完整上传路径:', folderName)\r\n      console.log('文件类型:', fileType)\r\n\r\n      // 获取实际的文件对象\r\n      const actualFiles = this.fileList.map(fileItem => {\r\n        // Element UI 上传组件的文件对象可能有不同的结构\r\n        return fileItem.raw || fileItem.file || fileItem\r\n      }).filter(file => file instanceof File)\r\n\r\n      console.log('实际文件对象:', actualFiles)\r\n\r\n      if (actualFiles.length === 0) {\r\n        this.$message.warning('没有找到有效的文件对象')\r\n        return\r\n      }\r\n\r\n      try {\r\n        // 初始化所有文件的进度条\r\n        actualFiles.forEach(file => {\r\n          this.$set(this.uploadProgress, file.name, 0)\r\n        })\r\n\r\n        if (this.storageType === 'oss') {\r\n          // OSS上传\r\n          if (!this.ossInitialized) {\r\n            this.$message.error('OSS未配置，请先配置OSS')\r\n            this.openOSSConfig()\r\n            this.uploading = false\r\n            return\r\n          }\r\n\r\n          // 使用OSS上传\r\n          const uploadResults = await uploadFilesToOSS(\r\n            actualFiles,\r\n            fileType,\r\n            folderName, // 使用对应的文件夹\r\n            (index, progress, fileName, result, error) => {\r\n              if (error) {\r\n                this.$set(this.uploadProgress, fileName, -1) // 表示失败\r\n              } else {\r\n                this.$set(this.uploadProgress, fileName, progress)\r\n              }\r\n            }\r\n          )\r\n\r\n          // 处理上传结果\r\n          uploadResults.forEach((result, index) => {\r\n            if (result.success) {\r\n              // 根据文件扩展名确定文件类型\r\n              const fileExtension = result.originalName.toLowerCase().split('.').pop()\r\n              const audioExts = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']\r\n              const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v']\r\n              const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']\r\n\r\n              let actualFileType = 'unknown'\r\n              if (audioExts.includes(fileExtension)) {\r\n                actualFileType = 'audio'\r\n              } else if (videoExts.includes(fileExtension)) {\r\n                actualFileType = 'video'\r\n              } else if (imageExts.includes(fileExtension)) {\r\n                actualFileType = 'image'\r\n              }\r\n\r\n              const newFile = {\r\n                id: Date.now() + Math.random() + index,\r\n                name: result.originalName,\r\n                type: actualFileType,\r\n                size: result.size,\r\n                uploadTime: new Date().toLocaleString().slice(0, 16),\r\n                duration: actualFileType === 'video' ? '00:02:30' : (actualFileType === 'audio' ? '00:03:45' : undefined),\r\n                resolution: actualFileType === 'video' ? '1920x1080' : undefined,\r\n                bitrate: actualFileType === 'audio' ? '128kbps' : undefined,\r\n                url: result.url,\r\n                ossFileName: result.fileName,\r\n                folder: folderName\r\n              }\r\n\r\n              // 根据标签页添加到对应列表\r\n              switch (this.materialTab) {\r\n                case 'bgm':\r\n                  this.bgmList.unshift(newFile)\r\n                  break\r\n                case 'sucai':\r\n                  this.sucaiList.unshift(newFile)\r\n                  break\r\n              }\r\n            }\r\n          })\r\n\r\n          this.$message.success(`成功上传 ${uploadResults.filter(r => r.success).length} 个文件到阿里云OSS！`)\r\n\r\n        } else {\r\n          // 本地模拟上传\r\n          actualFiles.forEach((file) => {\r\n            // 进度已在上面初始化，这里直接开始模拟进度更新\r\n\r\n            // 模拟进度更新\r\n            const progressInterval = setInterval(() => {\r\n              const currentProgress = this.uploadProgress[file.name] || 0\r\n              if (currentProgress < 100) {\r\n                this.$set(this.uploadProgress, file.name, Math.min(currentProgress + Math.random() * 30, 100))\r\n              } else {\r\n                clearInterval(progressInterval)\r\n              }\r\n            }, 200)\r\n          })\r\n\r\n          // 模拟上传完成\r\n          setTimeout(() => {\r\n            // 为每个文件创建新记录\r\n            actualFiles.forEach((file, index) => {\r\n              // 根据文件扩展名确定文件类型\r\n              const fileExtension = file.name.toLowerCase().split('.').pop()\r\n              const audioExts = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']\r\n              const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v']\r\n              const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']\r\n\r\n              let actualFileType = 'unknown'\r\n              if (audioExts.includes(fileExtension)) {\r\n                actualFileType = 'audio'\r\n              } else if (videoExts.includes(fileExtension)) {\r\n                actualFileType = 'video'\r\n              } else if (imageExts.includes(fileExtension)) {\r\n                actualFileType = 'image'\r\n              }\r\n\r\n              const newFile = {\r\n                id: Date.now() + Math.random() + index,\r\n                name: file.name,\r\n                type: actualFileType,\r\n                size: file.size,\r\n                uploadTime: new Date().toLocaleString().slice(0, 16),\r\n                duration: actualFileType === 'video' ? '00:02:30' : (actualFileType === 'audio' ? '00:03:45' : undefined),\r\n                resolution: actualFileType === 'video' ? '1920x1080' : undefined,\r\n                bitrate: actualFileType === 'audio' ? '128kbps' : undefined,\r\n                folder: folderName,\r\n                url: URL.createObjectURL(file) // 为本地文件创建预览URL\r\n              }\r\n\r\n              // 根据标签页添加到对应列表\r\n              switch (this.materialTab) {\r\n                case 'bgm':\r\n                  this.bgmList.unshift(newFile)\r\n                  break\r\n                case 'sucai':\r\n                  this.sucaiList.unshift(newFile)\r\n                  break\r\n              }\r\n            })\r\n\r\n            this.$message.success(`成功上传 ${fileCount} 个文件！`)\r\n\r\n            // 上传完成后更新文件夹计数\r\n            this.updateCurrentTabFolderCounts()\r\n          }, 2000)\r\n        }\r\n\r\n        this.uploading = false\r\n        this.uploadDialogVisible = false\r\n        this.fileList = []\r\n        this.uploadProgress = {}\r\n\r\n      } catch (error) {\r\n        this.uploading = false\r\n        this.$message.error(`上传失败：${error.message}`)\r\n        console.error('上传错误:', error)\r\n      }\r\n    },\r\n\r\n    formatFileSize(bytes) {\r\n      if (bytes === 0) return '0 Bytes'\r\n      const k = 1024\r\n      const sizes = ['Bytes', 'KB', 'MB', 'GB']\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\r\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 素材管理界面 - 完全按照图片设计 */\r\n.up-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: #f8f9fa;\r\n  margin: 0;\r\n  height: 100vh;\r\n}\r\n\r\n/* 顶部标签页 */\r\n.materials-tabs {\r\n  background: white;\r\n  padding: 0;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.tab-buttons {\r\n  display: flex;\r\n  gap: 0;\r\n  align-items: center;\r\n}\r\n\r\n.tab-button {\r\n  border-radius: 20px !important;\r\n  margin: 10px 5px;\r\n  padding: 8px 20px;\r\n  border: none;\r\n  font-size: 14px;\r\n}\r\n\r\n.tab-button.el-button--primary {\r\n  background: #4a90e2;\r\n  color: white;\r\n}\r\n\r\n.tab-button.el-button--default {\r\n  background: #e8e8e8;\r\n  color: #666;\r\n}\r\n\r\n/* BGM免费下载按钮样式 */\r\n.bgm-download-button {\r\n  margin-left: 20px !important;\r\n  border-radius: 15px !important;\r\n  padding: 6px 16px !important;\r\n  font-size: 13px !important;\r\n  background: linear-gradient(135deg, #67b26f 0%, #4ca2cd 100%) !important;\r\n  border: none !important;\r\n  color: white !important;\r\n  box-shadow: 0 2px 8px rgba(76, 162, 205, 0.3) !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.bgm-download-button:hover {\r\n  transform: translateY(-1px) !important;\r\n  box-shadow: 0 4px 12px rgba(76, 162, 205, 0.4) !important;\r\n  background: linear-gradient(135deg, #5a9f63 0%, #3d8bb8 100%) !important;\r\n}\r\n\r\n.bgm-download-button:active {\r\n  transform: translateY(0) !important;\r\n}\r\n\r\n/* 主要内容区域 */\r\n.materials-main {\r\n  flex: 1;\r\n  display: flex;\r\n  background: white;\r\n}\r\n\r\n/* 左侧文件夹树 - 按照图片样式 */\r\n.folder-sidebar {\r\n  width: 200px;\r\n  background: white;\r\n  border-right: 1px solid #e8e8e8;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.folder-header {\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.folder-actions {\r\n  display: flex !important;\r\n  flex-direction: column !important; /* 垂直排列 */\r\n  align-items: flex-start !important; /* 左对齐 */\r\n  gap: 8px; /* 上下间距 */\r\n  padding: 8px 0;\r\n}\r\n\r\n/* 使用更强的选择器覆盖Element UI样式 */\r\n.folder-actions .el-button,\r\n.folder-actions .el-button.el-button--small,\r\n.folder-actions .el-button.el-button--primary,\r\n.folder-actions .el-button.el-button--success {\r\n  border-radius: 6px !important;\r\n  font-weight: 500 !important;\r\n  font-size: 13px !important;\r\n  height: 32px !important;\r\n  width: 100% !important; /* 垂直排列时占满宽度 */\r\n  max-width: 120px !important; /* 限制最大宽度 */\r\n  min-height: 32px !important;\r\n  max-height: 32px !important;\r\n  line-height: 1 !important;\r\n  display: inline-flex !important;\r\n  align-items: center !important;\r\n  justify-content: center !important;\r\n  padding: 0 16px !important;\r\n  margin: 0 !important;\r\n  transition: all 0.3s ease;\r\n  flex-shrink: 0;\r\n  vertical-align: baseline !important;\r\n  box-sizing: border-box !important;\r\n  border-width: 1px !important;\r\n}\r\n\r\n.folder-actions .el-button:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 图标样式 */\r\n.folder-actions .el-button i {\r\n  margin-right: 4px !important;\r\n  line-height: 1 !important;\r\n  vertical-align: middle !important;\r\n}\r\n\r\n/* 按钮文字样式 */\r\n.folder-actions .el-button span {\r\n  line-height: 1 !important;\r\n  vertical-align: middle !important;\r\n}\r\n\r\n.add-folder-icon {\r\n  font-size: 16px;\r\n  color: #666;\r\n  cursor: pointer;\r\n}\r\n\r\n.folder-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  cursor: pointer;\r\n}\r\n\r\n.folder-list {\r\n  flex: 1;\r\n  padding: 10px 0;\r\n}\r\n\r\n.folder-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 15px;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  gap: 8px;\r\n}\r\n\r\n.folder-item:hover {\r\n  background: #f5f5f5;\r\n}\r\n\r\n.folder-item.active {\r\n  background: #e6f3ff;\r\n}\r\n\r\n.folder-icon {\r\n  font-size: 16px;\r\n}\r\n\r\n.folder-name {\r\n  flex: 1;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.folder-count {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n/* 右侧文件区域 */\r\n.files-area {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: white;\r\n}\r\n\r\n/* 文件操作栏 */\r\n.files-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 20px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  background: #fafafa;\r\n}\r\n\r\n.toolbar-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 15px;\r\n}\r\n\r\n.action-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  cursor: pointer;\r\n}\r\n\r\n.action-text:hover {\r\n  color: #4a90e2;\r\n}\r\n\r\n.toolbar-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.pagination-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 2px;\r\n}\r\n\r\n/* 文件内容区域 */\r\n.files-content {\r\n  flex: 1;\r\n  overflow: auto;\r\n}\r\n\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 300px;\r\n  color: #999;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 48px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件表格 */\r\n.files-table {\r\n  width: 100%;\r\n}\r\n\r\n.table-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 20px;\r\n  background: #fafafa;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  font-size: 14px;\r\n  color: #666;\r\n  font-weight: 500;\r\n}\r\n\r\n.table-body {\r\n  background: white;\r\n}\r\n\r\n.table-row {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 20px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.table-row:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.table-row.selected {\r\n  background: #e6f3ff;\r\n}\r\n\r\n.header-cell,\r\n.cell {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.checkbox-cell {\r\n  width: 50px;\r\n  justify-content: center;\r\n}\r\n\r\n.name-cell {\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.size-cell {\r\n  width: 100px;\r\n}\r\n\r\n.time-cell {\r\n  width: 150px;\r\n}\r\n\r\n.file-icon {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n/* 上传进度样式 */\r\n.upload-progress {\r\n  .progress-item {\r\n    margin-bottom: 15px;\r\n\r\n    .progress-info {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 5px;\r\n\r\n      .file-name {\r\n        font-size: 14px;\r\n        color: #333;\r\n        flex: 1;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .progress-text {\r\n        font-size: 12px;\r\n        color: #666;\r\n        margin-left: 10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.storage-selector {\r\n  padding: 15px;\r\n  background: #f8f9fa;\r\n  border-radius: 4px;\r\n  border: 1px solid #e8e8e8;\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n}\r\n\r\n.text-success {\r\n  color: #67c23a;\r\n  font-weight: 500;\r\n}\r\n\r\n.text-danger {\r\n  color: #f56c6c;\r\n  font-weight: 500;\r\n}\r\n\r\n/* OSS配置对话框样式 */\r\n.oss-config-dialog {\r\n  .el-dialog__header {\r\n    padding: 20px 20px 0;\r\n    border-bottom: 1px solid #f0f0f0;\r\n  }\r\n\r\n  .el-dialog__body {\r\n    padding: 0;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    padding: 20px;\r\n    border-top: 1px solid #f0f0f0;\r\n  }\r\n}\r\n\r\n.oss-config-content {\r\n  padding: 20px;\r\n}\r\n\r\n.storage-type-section {\r\n  margin-bottom: 30px;\r\n\r\n  .section-label {\r\n    font-size: 14px;\r\n    color: #333;\r\n    margin-bottom: 15px;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .storage-options {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .storage-radio {\r\n    .radio-text {\r\n      font-size: 14px;\r\n      color: #333;\r\n    }\r\n  }\r\n\r\n  .storage-description {\r\n    font-size: 12px;\r\n    color: #999;\r\n    line-height: 1.5;\r\n  }\r\n}\r\n\r\n.oss-form-section {\r\n  .form-row {\r\n    margin-bottom: 20px;\r\n\r\n    .form-label {\r\n      font-size: 14px;\r\n      color: #333;\r\n      margin-bottom: 8px;\r\n\r\n      &.required::before {\r\n        content: '*';\r\n        color: #f56c6c;\r\n        margin-right: 4px;\r\n      }\r\n    }\r\n\r\n    .form-input {\r\n      width: 100%;\r\n\r\n      .el-input__inner {\r\n        border-radius: 4px;\r\n        border: 1px solid #dcdfe6;\r\n        padding: 0 15px;\r\n        height: 40px;\r\n        line-height: 40px;\r\n\r\n        &:focus {\r\n          border-color: #409eff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .form-hint {\r\n      font-size: 12px;\r\n      color: #999;\r\n      margin-top: 5px;\r\n      line-height: 1.4;\r\n    }\r\n  }\r\n\r\n  .status-options {\r\n    display: flex;\r\n    gap: 20px;\r\n\r\n    .status-radio {\r\n      .radio-text {\r\n        font-size: 14px;\r\n        color: #333;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.dialog-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n\r\n  .cancel-btn {\r\n    padding: 8px 20px;\r\n    border-radius: 4px;\r\n    border: 1px solid #dcdfe6;\r\n    background: #fff;\r\n    color: #606266;\r\n\r\n    &:hover {\r\n      color: #409eff;\r\n      border-color: #c6e2ff;\r\n      background-color: #ecf5ff;\r\n    }\r\n  }\r\n\r\n  .confirm-btn {\r\n    padding: 8px 20px;\r\n    border-radius: 4px;\r\n    background: #409eff;\r\n    border-color: #409eff;\r\n    color: #fff;\r\n\r\n    &:hover {\r\n      background: #66b1ff;\r\n      border-color: #66b1ff;\r\n    }\r\n  }\r\n}\r\n\r\n/* 右键菜单样式 */\r\n.context-menu {\r\n  position: fixed;\r\n  background: #fff;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  z-index: 9999;\r\n  min-width: 120px;\r\n  padding: 4px 0;\r\n\r\n  .menu-item {\r\n    padding: 8px 16px;\r\n    cursor: pointer;\r\n    display: flex;\r\n    align-items: center;\r\n    font-size: 14px;\r\n    color: #606266;\r\n    transition: all 0.3s;\r\n\r\n    i {\r\n      margin-right: 8px;\r\n      font-size: 14px;\r\n    }\r\n\r\n    &:hover {\r\n      background-color: #f5f7fa;\r\n      color: #409eff;\r\n    }\r\n\r\n    &:active {\r\n      background-color: #e6f7ff;\r\n    }\r\n  }\r\n}\r\n\r\n/* 文件夹项样式增强 */\r\n.folder-item {\r\n  position: relative;\r\n  user-select: none;\r\n\r\n  &:hover {\r\n    background-color: #f5f7fa;\r\n  }\r\n\r\n  &.active {\r\n    background-color: #e6f7ff;\r\n    color: #409eff;\r\n  }\r\n}\r\n\r\n/* 文件夹操作按钮样式已在上面定义，这里删除重复定义 */\r\n\r\n/* OSS未配置提示样式 */\r\n.no-oss-tip {\r\n  padding: 20px;\r\n  text-align: center;\r\n  color: #909399;\r\n\r\n  .tip-icon {\r\n    font-size: 32px;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .tip-text {\r\n    margin-bottom: 15px;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n.no-oss-files-tip {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 400px;\r\n\r\n  .tip-content {\r\n    text-align: center;\r\n\r\n    .tip-icon {\r\n      font-size: 64px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .tip-title {\r\n      font-size: 18px;\r\n      font-weight: 500;\r\n      color: #303133;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .tip-description {\r\n      font-size: 14px;\r\n      color: #909399;\r\n      margin-bottom: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n/* 文件工具栏样式 */\r\n.file-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 16px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n\r\n  .toolbar-left {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 16px;\r\n\r\n    .selected-count {\r\n      color: #409eff;\r\n      font-size: 14px;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n\r\n  .toolbar-right {\r\n    display: flex;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n/* 文件网格样式 */\r\n.file-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));\r\n  gap: 16px;\r\n  padding: 16px;\r\n}\r\n\r\n.file-card {\r\n  position: relative;\r\n  background: #fff;\r\n  border: 2px solid transparent;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  cursor: pointer;\r\n  transition: all 0.4s ease;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  z-index: 1;\r\n\r\n  &:hover {\r\n    border-color: #409eff;\r\n    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);\r\n    transform: translateY(-2px);\r\n  }\r\n\r\n  &.selected {\r\n    border-color: #409eff;\r\n    background-color: #f0f8ff;\r\n  }\r\n\r\n  // 播放时放大效果\r\n  &.enlarged {\r\n    transform: scale(1.15) translateY(-8px);\r\n    z-index: 10;\r\n    box-shadow: 0 8px 32px rgba(64, 158, 255, 0.4);\r\n    border-color: #409eff;\r\n\r\n    .file-thumbnail {\r\n      border-radius: 8px;\r\n    }\r\n  }\r\n\r\n\r\n}\r\n\r\n// 视频放大样式 - 现代播放器设计\r\n.file-card.scale-enlarged {\r\n  position: fixed !important;\r\n  top: 50vh !important;\r\n  left: 50vw !important;\r\n  transform: translate(-50%, -50%) !important;\r\n  z-index: 9999999 !important;\r\n  border-radius: 12px !important;\r\n  overflow: hidden !important;\r\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4),\r\n              0 0 0 2px rgba(255, 255, 255, 0.1) !important;\r\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;\r\n\r\n  // 隐藏文件信息\r\n  .file-info {\r\n    display: none !important;\r\n  }\r\n\r\n  // 视频区域\r\n  .file-thumbnail {\r\n    height: 100% !important;\r\n    border-radius: 12px !important;\r\n    overflow: hidden !important;\r\n    position: relative !important;\r\n\r\n    .video-thumbnail {\r\n      border-radius: 12px !important;\r\n      position: relative !important;\r\n\r\n      .thumbnail-video {\r\n        border-radius: 12px !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 背景遮罩\r\n.scale-enlarged-backdrop {\r\n  position: fixed !important;\r\n  top: 0 !important;\r\n  left: 0 !important;\r\n  width: 100vw !important;\r\n  height: 100vh !important;\r\n  background: rgba(0, 0, 0, 0.7) !important;\r\n  backdrop-filter: blur(8px) !important;\r\n  z-index: 9999998 !important;\r\n  animation: backdropFadeIn 0.3s ease !important;\r\n}\r\n\r\n// 发光边框动画\r\n@keyframes borderGlow {\r\n  0%, 100% {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    opacity: 0.8;\r\n    transform: scale(1.02);\r\n  }\r\n}\r\n\r\n// 背景淡入动画\r\n@keyframes backdropFadeIn {\r\n  from {\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n// 小视频简洁播放按钮\r\n.simple-play-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border-radius: 12px;\r\n  transition: all 0.3s ease;\r\n\r\n  .play-button {\r\n    width: 40px;\r\n    height: 40px;\r\n    background: rgba(255, 255, 255, 0.9);\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\r\n    transition: all 0.3s ease;\r\n\r\n    i {\r\n      font-size: 16px;\r\n      color: #333;\r\n      margin-left: 2px; // 播放图标视觉居中\r\n    }\r\n\r\n    &:hover {\r\n      transform: scale(1.1);\r\n      background: white;\r\n    }\r\n  }\r\n}\r\n\r\n// 放大视频的现代控件\r\n.video-controls-overlay {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));\r\n  padding: 20px 16px 12px;\r\n  border-radius: 0 0 12px 12px;\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n\r\n  .progress-container {\r\n    margin-bottom: 12px;\r\n    cursor: pointer;\r\n\r\n    .progress-track {\r\n      position: relative;\r\n      height: 3px;\r\n      background: rgba(255, 255, 255, 0.3);\r\n      border-radius: 2px;\r\n\r\n      .progress-fill {\r\n        height: 100%;\r\n        background: #fff;\r\n        border-radius: 2px;\r\n        transition: width 0.1s ease;\r\n      }\r\n\r\n      .progress-thumb {\r\n        position: absolute;\r\n        top: 50%;\r\n        width: 10px;\r\n        height: 10px;\r\n        background: white;\r\n        border-radius: 50%;\r\n        transform: translate(-50%, -50%);\r\n        opacity: 0;\r\n        transition: all 0.2s ease;\r\n      }\r\n    }\r\n\r\n    &:hover .progress-thumb {\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .controls-bottom {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .play-pause-btn {\r\n      width: 32px;\r\n      height: 32px;\r\n      background: rgba(255, 255, 255, 0.2);\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      cursor: pointer;\r\n      transition: all 0.2s ease;\r\n\r\n      i {\r\n        font-size: 14px;\r\n        color: white;\r\n        margin-left: 1px;\r\n      }\r\n\r\n      &:hover {\r\n        background: rgba(255, 255, 255, 0.3);\r\n      }\r\n    }\r\n\r\n    .time-display {\r\n      font-size: 12px;\r\n      color: white;\r\n      font-family: 'SF Mono', Monaco, monospace;\r\n\r\n      .separator {\r\n        margin: 0 4px;\r\n        opacity: 0.7;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 鼠标悬停时显示控件\r\n.video-thumbnail:hover .video-controls-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n// Portal悬停放大层样式\r\n.hover-portal-layer {\r\n  position: fixed !important;\r\n  z-index: 999999 !important;\r\n  pointer-events: auto !important;\r\n  transition: all 0.3s ease !important;\r\n\r\n  .hover-file-card {\r\n    width: 100% !important;\r\n    height: 100% !important;\r\n    border-radius: 12px !important;\r\n    overflow: hidden !important;\r\n    box-shadow: 0 20px 60px rgba(64, 158, 255, 0.8) !important;\r\n    border: 3px solid #409eff !important;\r\n    background: #ffffff !important;\r\n    transform: scale(1) !important;\r\n    animation: portalFadeIn 0.3s ease !important;\r\n\r\n    // 确保可见性\r\n    opacity: 1 !important;\r\n    display: block !important;\r\n\r\n    // 视频样式\r\n    .video-thumbnail {\r\n      width: 100% !important;\r\n      height: calc(100% - 60px) !important;\r\n      position: relative !important;\r\n      background: #000 !important;\r\n      border-radius: 8px 8px 0 0 !important;\r\n\r\n      .thumbnail-video {\r\n        width: 100% !important;\r\n        height: 100% !important;\r\n        object-fit: cover !important;\r\n        border-radius: 8px 8px 0 0 !important;\r\n      }\r\n\r\n      .duration-badge {\r\n        position: absolute !important;\r\n        bottom: 8px !important;\r\n        right: 8px !important;\r\n        background: rgba(0, 0, 0, 0.8) !important;\r\n        color: white !important;\r\n        padding: 4px 8px !important;\r\n        border-radius: 4px !important;\r\n        font-size: 12px !important;\r\n      }\r\n\r\n      // Portal视频控制按钮\r\n      .portal-video-controls {\r\n        position: absolute !important;\r\n        bottom: 0 !important;\r\n        left: 0 !important;\r\n        right: 0 !important;\r\n        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8)) !important;\r\n        padding: 20px 16px 12px !important;\r\n        opacity: 0 !important;\r\n        transform: translateY(10px) !important;\r\n        transition: all 0.3s ease !important;\r\n        pointer-events: auto !important;\r\n\r\n        &.visible {\r\n          opacity: 1 !important;\r\n          transform: translateY(0) !important;\r\n        }\r\n\r\n        .portal-play-btn {\r\n          position: absolute !important;\r\n          top: -30px !important;\r\n          left: 50% !important;\r\n          transform: translateX(-50%) !important;\r\n          width: 48px !important;\r\n          height: 48px !important;\r\n          background: rgba(64, 158, 255, 0.9) !important;\r\n          border: none !important;\r\n          border-radius: 50% !important;\r\n          display: flex !important;\r\n          align-items: center !important;\r\n          justify-content: center !important;\r\n          cursor: pointer !important;\r\n          transition: all 0.3s ease !important;\r\n\r\n          &:hover {\r\n            background: #409eff !important;\r\n            transform: translateX(-50%) scale(1.1) !important;\r\n          }\r\n\r\n          i {\r\n            font-size: 20px !important;\r\n            color: white !important;\r\n          }\r\n        }\r\n\r\n        .portal-info {\r\n          color: white !important;\r\n          font-size: 12px !important;\r\n          text-align: center !important;\r\n          background: rgba(0, 0, 0, 0.5) !important;\r\n          padding: 4px 8px !important;\r\n          border-radius: 4px !important;\r\n          margin-top: 8px !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 音频样式\r\n    .audio-thumbnail {\r\n      width: 100% !important;\r\n      height: calc(100% - 60px) !important;\r\n      position: relative !important;\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n      border-radius: 8px 8px 0 0 !important;\r\n      display: flex !important;\r\n      flex-direction: column !important;\r\n      align-items: center !important;\r\n      justify-content: center !important;\r\n\r\n      .audio-icon {\r\n        font-size: 48px !important;\r\n        margin-bottom: 16px !important;\r\n        color: white !important;\r\n      }\r\n\r\n      .audio-waveform {\r\n        display: flex !important;\r\n        align-items: center !important;\r\n        gap: 3px !important;\r\n\r\n        .wave-bar {\r\n          width: 4px !important;\r\n          background: rgba(255, 255, 255, 0.8) !important;\r\n          border-radius: 2px !important;\r\n          animation: audioWave 1.5s ease-in-out infinite !important;\r\n\r\n          &:nth-child(1) { height: 20px !important; animation-delay: 0s !important; }\r\n          &:nth-child(2) { height: 35px !important; animation-delay: 0.1s !important; }\r\n          &:nth-child(3) { height: 25px !important; animation-delay: 0.2s !important; }\r\n          &:nth-child(4) { height: 40px !important; animation-delay: 0.3s !important; }\r\n          &:nth-child(5) { height: 30px !important; animation-delay: 0.4s !important; }\r\n          &:nth-child(6) { height: 45px !important; animation-delay: 0.5s !important; }\r\n          &:nth-child(7) { height: 35px !important; animation-delay: 0.6s !important; }\r\n          &:nth-child(8) { height: 25px !important; animation-delay: 0.7s !important; }\r\n          &:nth-child(9) { height: 40px !important; animation-delay: 0.8s !important; }\r\n          &:nth-child(10) { height: 30px !important; animation-delay: 0.9s !important; }\r\n          &:nth-child(11) { height: 35px !important; animation-delay: 1.0s !important; }\r\n          &:nth-child(12) { height: 20px !important; animation-delay: 1.1s !important; }\r\n        }\r\n      }\r\n\r\n      .duration-badge {\r\n        position: absolute !important;\r\n        bottom: 8px !important;\r\n        right: 8px !important;\r\n        background: rgba(0, 0, 0, 0.8) !important;\r\n        color: white !important;\r\n        padding: 4px 8px !important;\r\n        border-radius: 4px !important;\r\n        font-size: 12px !important;\r\n      }\r\n    }\r\n\r\n    // 图片样式\r\n    .image-thumbnail {\r\n      width: 100% !important;\r\n      height: calc(100% - 60px) !important;\r\n      position: relative !important;\r\n      border-radius: 8px 8px 0 0 !important;\r\n      overflow: hidden !important;\r\n\r\n      .thumbnail-image {\r\n        width: 100% !important;\r\n        height: 100% !important;\r\n        object-fit: cover !important;\r\n        border-radius: 8px 8px 0 0 !important;\r\n      }\r\n    }\r\n\r\n    // 文件信息\r\n    .file-info {\r\n      height: 60px !important;\r\n      padding: 12px 16px !important;\r\n      background: #f8f9fa !important;\r\n      border-radius: 0 0 8px 8px !important;\r\n\r\n      .file-name {\r\n        font-size: 14px !important;\r\n        font-weight: 600 !important;\r\n        color: #333 !important;\r\n        margin-bottom: 4px !important;\r\n        white-space: nowrap !important;\r\n        overflow: hidden !important;\r\n        text-overflow: ellipsis !important;\r\n      }\r\n\r\n      .file-meta {\r\n        display: flex !important;\r\n        gap: 12px !important;\r\n        font-size: 12px !important;\r\n        color: #666 !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// Portal动画\r\n@keyframes portalFadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: scale(0.8);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n@keyframes audioWave {\r\n  0%, 100% {\r\n    transform: scaleY(0.5);\r\n    opacity: 0.7;\r\n  }\r\n  50% {\r\n    transform: scaleY(1);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.file-checkbox {\r\n  position: absolute;\r\n  top: 8px;\r\n  left: 8px;\r\n  z-index: 10;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  border-radius: 4px;\r\n  padding: 2px;\r\n}\r\n\r\n.file-thumbnail {\r\n  width: 100%;\r\n  height: 120px;\r\n  position: relative;\r\n  overflow: hidden;\r\n  background: #f5f7fa;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n\r\n}\r\n\r\n/* 视频缩略图样式 */\r\n.video-thumbnail {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .thumbnail-video {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n  }\r\n\r\n\r\n\r\n  .duration-badge {\r\n    position: absolute;\r\n    bottom: 8px;\r\n    right: 8px;\r\n    background: rgba(0, 0, 0, 0.7);\r\n    color: white;\r\n    padding: 2px 6px;\r\n    border-radius: 4px;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n/* 音频缩略图样式 */\r\n.audio-thumbnail {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n  transition: all 0.4s ease;\r\n\r\n  &:hover {\r\n    transform: scale(1.05);\r\n  }\r\n\r\n  &.playing {\r\n    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\r\n  }\r\n\r\n  .audio-icon {\r\n    font-size: 32px;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .audio-waveform {\r\n    display: flex;\r\n    align-items: flex-end;\r\n    gap: 2px;\r\n    height: 20px;\r\n\r\n    .wave-bar {\r\n      width: 3px;\r\n      background: rgba(255, 255, 255, 0.8);\r\n      border-radius: 2px;\r\n      animation: wave 1.5s ease-in-out infinite;\r\n\r\n      &:nth-child(1) { height: 8px; animation-delay: 0s; }\r\n      &:nth-child(2) { height: 12px; animation-delay: 0.1s; }\r\n      &:nth-child(3) { height: 16px; animation-delay: 0.2s; }\r\n      &:nth-child(4) { height: 10px; animation-delay: 0.3s; }\r\n      &:nth-child(5) { height: 14px; animation-delay: 0.4s; }\r\n      &:nth-child(6) { height: 18px; animation-delay: 0.5s; }\r\n      &:nth-child(7) { height: 12px; animation-delay: 0.6s; }\r\n      &:nth-child(8) { height: 8px; animation-delay: 0.7s; }\r\n      &:nth-child(9) { height: 15px; animation-delay: 0.8s; }\r\n      &:nth-child(10) { height: 11px; animation-delay: 0.9s; }\r\n      &:nth-child(11) { height: 9px; animation-delay: 1s; }\r\n      &:nth-child(12) { height: 13px; animation-delay: 1.1s; }\r\n    }\r\n  }\r\n\r\n  .play-overlay-audio {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    background: rgba(0, 0, 0, 0.7);\r\n    border-radius: 50%;\r\n    width: 40px;\r\n    height: 40px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: white;\r\n    font-size: 16px;\r\n    opacity: 1;\r\n    transition: all 0.3s ease;\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      background: rgba(0, 0, 0, 0.9);\r\n      transform: translate(-50%, -50%) scale(1.1);\r\n    }\r\n\r\n    &.playing {\r\n      background: rgba(255, 0, 0, 0.8);\r\n\r\n      &:hover {\r\n        background: rgba(255, 0, 0, 1);\r\n      }\r\n    }\r\n\r\n    &.visible {\r\n      opacity: 1;\r\n      visibility: visible;\r\n    }\r\n\r\n    &.hidden {\r\n      opacity: 0;\r\n      visibility: hidden;\r\n    }\r\n  }\r\n\r\n  .duration-badge {\r\n    position: absolute;\r\n    bottom: 8px;\r\n    right: 8px;\r\n    background: rgba(0, 0, 0, 0.7);\r\n    color: white;\r\n    padding: 2px 6px;\r\n    border-radius: 4px;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n@keyframes wave {\r\n  0%, 100% { transform: scaleY(0.5); }\r\n  50% { transform: scaleY(1); }\r\n}\r\n\r\n/* 图片缩略图样式 */\r\n.image-thumbnail {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  cursor: pointer;\r\n  overflow: hidden;\r\n\r\n  &:hover {\r\n    .thumbnail-image {\r\n      transform: scale(1.05);\r\n    }\r\n\r\n    .image-overlay {\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .thumbnail-image {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n    transition: transform 0.3s ease;\r\n  }\r\n\r\n  .image-overlay {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.4);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    opacity: 0;\r\n    transition: opacity 0.3s ease;\r\n\r\n    i {\r\n      color: white;\r\n      font-size: 24px;\r\n    }\r\n  }\r\n}\r\n\r\n/* 文件图标缩略图样式 */\r\n.file-icon-thumbnail {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 48px;\r\n  color: #909399;\r\n}\r\n\r\n\r\n\r\n/* 文件信息样式 */\r\n.file-card .file-info {\r\n  padding: 12px;\r\n\r\n  .file-name {\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    color: #303133;\r\n    margin-bottom: 4px;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n  }\r\n\r\n  .file-meta {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    font-size: 12px;\r\n    color: #909399;\r\n\r\n    .file-size {\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n/* 文件预览对话框样式 */\r\n.preview-dialog {\r\n  .el-dialog__body {\r\n    padding: 20px;\r\n  }\r\n}\r\n\r\n.preview-content {\r\n  .preview-header {\r\n    margin-bottom: 20px;\r\n\r\n    h3 {\r\n      margin: 0 0 10px 0;\r\n      color: #303133;\r\n      font-size: 18px;\r\n    }\r\n\r\n    .file-info {\r\n      display: flex;\r\n      gap: 20px;\r\n      color: #909399;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  .video-preview {\r\n    text-align: center;\r\n\r\n    video {\r\n      border-radius: 8px;\r\n      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    }\r\n  }\r\n\r\n  .audio-preview {\r\n    .audio-player {\r\n      margin-bottom: 20px;\r\n      text-align: center;\r\n    }\r\n\r\n    .audio-info {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 20px;\r\n      background-color: #f8f9fa;\r\n      border-radius: 8px;\r\n\r\n      .audio-icon {\r\n        font-size: 48px;\r\n        margin-right: 20px;\r\n      }\r\n\r\n      .audio-details {\r\n        p {\r\n          margin: 5px 0;\r\n          color: #606266;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .image-preview {\r\n    text-align: center;\r\n\r\n    img {\r\n      border-radius: 8px;\r\n      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    }\r\n  }\r\n\r\n  .unsupported-preview {\r\n    text-align: center;\r\n    padding: 40px;\r\n\r\n    .unsupported-icon {\r\n      font-size: 64px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    p {\r\n      color: #909399;\r\n      margin-bottom: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n/* OSS未配置提示样式 */\r\n.no-oss-tip {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40px 20px;\r\n  text-align: center;\r\n  color: #909399;\r\n\r\n  .tip-icon {\r\n    font-size: 48px;\r\n    margin-bottom: 16px;\r\n  }\r\n\r\n  .tip-text {\r\n    font-size: 14px;\r\n    margin-bottom: 16px;\r\n    color: #606266;\r\n  }\r\n}\r\n\r\n.no-oss-files-tip {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100%;\r\n  min-height: 400px;\r\n\r\n  .tip-content {\r\n    text-align: center;\r\n\r\n    .tip-icon {\r\n      font-size: 64px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .tip-title {\r\n      font-size: 18px;\r\n      font-weight: 500;\r\n      color: #303133;\r\n      margin-bottom: 12px;\r\n    }\r\n\r\n    .tip-description {\r\n      font-size: 14px;\r\n      color: #909399;\r\n      margin-bottom: 24px;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .folder-sidebar {\r\n    width: 150px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .materials-main {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .folder-sidebar {\r\n    width: 100%;\r\n    height: 200px;\r\n    border-right: none;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .files-toolbar {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 10px;\r\n  }\r\n\r\n  .toolbar-right {\r\n    align-self: flex-end;\r\n  }\r\n}\r\n</style>\r\n"]}]}