{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\progress.vue?vue&type=template&id=3d0766b5", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\progress.vue", "mtime": 1754974531325}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753759474020}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uICgpIHsKICB2YXIgX3ZtID0gdGhpcwogIHZhciBfaCA9IF92bS4kY3JlYXRlRWxlbWVudAogIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogIHJldHVybiBfYygKICAgICJkaXYiLAogICAgeyBzdGF0aWNDbGFzczogInZpZGVvLXByb2dyZXNzIiB9LAogICAgWwogICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogImhlYWRlci10b29sYmFyIiB9LCBbCiAgICAgICAgX2MoCiAgICAgICAgICAiZGl2IiwKICAgICAgICAgIHsgc3RhdGljQ2xhc3M6ICJ0b29sYmFyLWxlZnQiIH0sCiAgICAgICAgICBbCiAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICJlbC1idXR0b24iLAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIGF0dHJzOiB7IGljb246ICJlbC1pY29uLWFycm93LWxlZnQiIH0sCiAgICAgICAgICAgICAgICBvbjogeyBjbGljazogX3ZtLmdvQmFjayB9LAogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgW192bS5fdigi6L+U5ZueIildCiAgICAgICAgICAgICksCiAgICAgICAgICAgIF9jKCJoMiIsIHsgc3RhdGljQ2xhc3M6ICJwYWdlLXRpdGxlIiB9LCBbCiAgICAgICAgICAgICAgX2MoImkiLCB7IHN0YXRpY0NsYXNzOiAiZWwtaWNvbi12aWRlby1jYW1lcmEiIH0pLAogICAgICAgICAgICAgIF92bS5fdigiIOWJqui+kei/m+W6piAtICIgKyBfdm0uX3MoX3ZtLnRlbXBsYXRlSW5mby5uYW1lKSArICIgIiksCiAgICAgICAgICAgIF0pLAogICAgICAgICAgXSwKICAgICAgICAgIDEKICAgICAgICApLAogICAgICAgIF9jKAogICAgICAgICAgImRpdiIsCiAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAidG9vbGJhci1yaWdodCIgfSwKICAgICAgICAgIFsKICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgImVsLWJ1dHRvbiIsCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgYXR0cnM6IHsgaWNvbjogImVsLWljb24tcmVmcmVzaCIgfSwKICAgICAgICAgICAgICAgIG9uOiB7IGNsaWNrOiBfdm0ucmVmcmVzaFByb2dyZXNzIH0sCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICBbX3ZtLl92KCIg5Yi35pawICIpXQogICAgICAgICAgICApLAogICAgICAgICAgICBfYygKICAgICAgICAgICAgICAiZWwtYnV0dG9uIiwKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICBhdHRyczogeyB0eXBlOiAicHJpbWFyeSIsIGljb246ICJlbC1pY29uLXBsdXMiIH0sCiAgICAgICAgICAgICAgICBvbjogeyBjbGljazogX3ZtLnN0YXJ0TmV3QmF0Y2ggfSwKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIFtfdm0uX3YoIiDmlrDlu7rmibnmrKEgIildCiAgICAgICAgICAgICksCiAgICAgICAgICBdLAogICAgICAgICAgMQogICAgICAgICksCiAgICAgIF0pLAogICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInN0YXRzLWNhcmRzIiB9LCBbCiAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJzdGF0LWNhcmQiIH0sIFsKICAgICAgICAgIF92bS5fbSgwKSwKICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAic3RhdC1pbmZvIiB9LCBbCiAgICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAic3RhdC1udW1iZXIiIH0sIFsKICAgICAgICAgICAgICBfdm0uX3YoX3ZtLl9zKF92bS5zdGF0cy5jb21wbGV0ZWQpKSwKICAgICAgICAgICAgXSksCiAgICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAic3RhdC1sYWJlbCIgfSwgW192bS5fdigi5bey5a6M5oiQIildKSwKICAgICAgICAgIF0pLAogICAgICAgIF0pLAogICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAic3RhdC1jYXJkIiB9LCBbCiAgICAgICAgICBfdm0uX20oMSksCiAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInN0YXQtaW5mbyIgfSwgWwogICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInN0YXQtbnVtYmVyIiB9LCBbCiAgICAgICAgICAgICAgX3ZtLl92KF92bS5fcyhfdm0uc3RhdHMucHJvY2Vzc2luZykpLAogICAgICAgICAgICBdKSwKICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJzdGF0LWxhYmVsIiB9LCBbX3ZtLl92KCLlpITnkIbkuK0iKV0pLAogICAgICAgICAgXSksCiAgICAgICAgXSksCiAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJzdGF0LWNhcmQiIH0sIFsKICAgICAgICAgIF92bS5fbSgyKSwKICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAic3RhdC1pbmZvIiB9LCBbCiAgICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAic3RhdC1udW1iZXIiIH0sIFsKICAgICAgICAgICAgICBfdm0uX3YoX3ZtLl9zKF92bS5zdGF0cy5mYWlsZWQpKSwKICAgICAgICAgICAgXSksCiAgICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAic3RhdC1sYWJlbCIgfSwgW192bS5fdigi5aSx6LSlIildKSwKICAgICAgICAgIF0pLAogICAgICAgIF0pLAogICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAic3RhdC1jYXJkIiB9LCBbCiAgICAgICAgICBfdm0uX20oMyksCiAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInN0YXQtaW5mbyIgfSwgWwogICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInN0YXQtbnVtYmVyIiB9LCBbCiAgICAgICAgICAgICAgX3ZtLl92KF92bS5fcyhfdm0uc3RhdHMudG90YWwpKSwKICAgICAgICAgICAgXSksCiAgICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAic3RhdC1sYWJlbCIgfSwgW192bS5fdigi5oC76K6hIildKSwKICAgICAgICAgIF0pLAogICAgICAgIF0pLAogICAgICBdKSwKICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJ2aWRlb3MtY29udGFpbmVyIiB9LCBbCiAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJjb250YWluZXItaGVhZGVyIiB9LCBbCiAgICAgICAgICBfYygiaDMiLCBbX3ZtLl92KCLnlJ/miJDnmoTop4bpopEiKV0pLAogICAgICAgICAgX2MoCiAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAiZmlsdGVyLWNvbnRyb2xzIiB9LAogICAgICAgICAgICBbCiAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAiZWwtc2VsZWN0IiwKICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogIueKtuaAgeetm+mAiSIsCiAgICAgICAgICAgICAgICAgICAgc2l6ZTogInNtYWxsIiwKICAgICAgICAgICAgICAgICAgICBjbGVhcmFibGU6ICIiLAogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICBtb2RlbDogewogICAgICAgICAgICAgICAgICAgIHZhbHVlOiBfdm0uc3RhdHVzRmlsdGVyLAogICAgICAgICAgICAgICAgICAgIGNhbGxiYWNrOiBmdW5jdGlvbiAoJCR2KSB7CiAgICAgICAgICAgICAgICAgICAgICBfdm0uc3RhdHVzRmlsdGVyID0gJCR2CiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICBleHByZXNzaW9uOiAic3RhdHVzRmlsdGVyIiwKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgIF9jKCJlbC1vcHRpb24iLCB7IGF0dHJzOiB7IGxhYmVsOiAi5YWo6YOoIiwgdmFsdWU6ICIiIH0gfSksCiAgICAgICAgICAgICAgICAgIF9jKCJlbC1vcHRpb24iLCB7CiAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgbGFiZWw6ICLlt7LlrozmiJAiLCB2YWx1ZTogImNvbXBsZXRlZCIgfSwKICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgIF9jKCJlbC1vcHRpb24iLCB7CiAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgbGFiZWw6ICLlpITnkIbkuK0iLCB2YWx1ZTogInByb2Nlc3NpbmciIH0sCiAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICBfYygiZWwtb3B0aW9uIiwgewogICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IGxhYmVsOiAi5aSx6LSlIiwgdmFsdWU6ICJmYWlsZWQiIH0sCiAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICApLAogICAgICAgICAgICBdLAogICAgICAgICAgICAxCiAgICAgICAgICApLAogICAgICAgIF0pLAogICAgICAgIF9jKAogICAgICAgICAgImRpdiIsCiAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAidmlkZW9zLWdyaWQiIH0sCiAgICAgICAgICBbCiAgICAgICAgICAgIF92bS5fbChfdm0uZmlsdGVyZWRWaWRlb3MsIGZ1bmN0aW9uICh2aWRlbykgewogICAgICAgICAgICAgIHJldHVybiBfYygKICAgICAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICBrZXk6IHZpZGVvLmlkLAogICAgICAgICAgICAgICAgICBzdGF0aWNDbGFzczogInZpZGVvLWNhcmQiLAogICAgICAgICAgICAgICAgICBjbGFzczogdmlkZW8uc3RhdHVzLAogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJ2aWRlby10aHVtYm5haWwiIH0sIFsKICAgICAgICAgICAgICAgICAgICB2aWRlby50aHVtYm5haWwKICAgICAgICAgICAgICAgICAgICAgID8gX2MoImltZyIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBzcmM6IHZpZGVvLnRodW1ibmFpbCwgYWx0OiB2aWRlby5uYW1lIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgICA6IF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAidGh1bWJuYWlsLXBsYWNlaG9sZGVyIiB9LCBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoImkiLCB7IHN0YXRpY0NsYXNzOiAiZWwtaWNvbi12aWRlby1jYW1lcmEiIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgICB2aWRlby5zdGF0dXMgIT09ICJjb21wbGV0ZWQiCiAgICAgICAgICAgICAgICAgICAgICA/IF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAic3RhdHVzLW92ZXJsYXkiIH0sIFsKICAgICAgICAgICAgICAgICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInN0YXR1cy1jb250ZW50IiB9LCBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2aWRlby5zdGF0dXMgPT09ICJwcm9jZXNzaW5nIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IF9jKCJpIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdGljQ2xhc3M6ICJlbC1pY29uLWxvYWRpbmcgcm90YXRpbmciLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogdmlkZW8uc3RhdHVzID09PSAiZmFpbGVkIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IF9jKCJpIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdGljQ2xhc3M6ICJlbC1pY29uLXdhcm5pbmctb3V0bGluZSIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJwIiwgeyBzdGF0aWNDbGFzczogInN0YXR1cy10ZXh0IiB9LCBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF92bS5fdihfdm0uX3MoX3ZtLmdldFN0YXR1c1RleHQodmlkZW8uc3RhdHVzKSkpLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2aWRlby5zdGF0dXMgPT09ICJwcm9jZXNzaW5nIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAicHJvZ3Jlc3MtYmFyIiB9LCBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygiZGl2IiwgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0aWNDbGFzczogInByb2dyZXNzLWZpbGwiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZTogeyB3aWR0aDogdmlkZW8ucHJvZ3Jlc3MgKyAiJSIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0pCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogX3ZtLl9lKCksCiAgICAgICAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgICAgIF0pCiAgICAgICAgICAgICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgICAgICAgICAgIHZpZGVvLnN0YXR1cyA9PT0gImNvbXBsZXRlZCIKICAgICAgICAgICAgICAgICAgICAgID8gX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdGljQ2xhc3M6ICJwbGF5LWJ1dHRvbiIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbjogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGljazogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfdm0ucGxheVZpZGVvKHZpZGVvKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgIFtfYygiaSIsIHsgc3RhdGljQ2xhc3M6ICJlbC1pY29uLXZpZGVvLXBsYXkiIH0pXQogICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgICAgICAgICAgIHZpZGVvLmR1cmF0aW9uCiAgICAgICAgICAgICAgICAgICAgICA/IF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAidmlkZW8tZHVyYXRpb24iIH0sIFsKICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoX3ZtLl9zKHZpZGVvLmR1cmF0aW9uKSksCiAgICAgICAgICAgICAgICAgICAgICAgIF0pCiAgICAgICAgICAgICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJ2aWRlby1pbmZvIiB9LCBbCiAgICAgICAgICAgICAgICAgICAgX2MoImg0IiwgeyBzdGF0aWNDbGFzczogInZpZGVvLW5hbWUiIH0sIFsKICAgICAgICAgICAgICAgICAgICAgIF92bS5fdihfdm0uX3ModmlkZW8ubmFtZSkpLAogICAgICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAidmlkZW8tbWV0YSIgfSwgWwogICAgICAgICAgICAgICAgICAgICAgX2MoInNwYW4iLCB7IHN0YXRpY0NsYXNzOiAiY3JlYXRlLXRpbWUiIH0sIFsKICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLl92KF92bS5fcyh2aWRlby5jcmVhdGVUaW1lKSksCiAgICAgICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgICAgIHZpZGVvLmZpbGVTaXplCiAgICAgICAgICAgICAgICAgICAgICAgID8gX2MoInNwYW4iLCB7IHN0YXRpY0NsYXNzOiAidmlkZW8tc2l6ZSIgfSwgWwogICAgICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLl92KF92bS5fcyh2aWRlby5maWxlU2l6ZSkpLAogICAgICAgICAgICAgICAgICAgICAgICAgIF0pCiAgICAgICAgICAgICAgICAgICAgICAgIDogX3ZtLl9lKCksCiAgICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICAgICAgdmlkZW8uc3RhdHVzID09PSAiY29tcGxldGVkIgogICAgICAgICAgICAgICAgICAgICAgPyBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAiZGl2IiwKICAgICAgICAgICAgICAgICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAidmlkZW8tYWN0aW9ucyIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgImVsLWJ1dHRvbiIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZTogIm1pbmkiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvbjogImVsLWljb24tZG93bmxvYWQiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsaWNrOiBmdW5jdGlvbiAoJGV2ZW50KSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfdm0uZG93bmxvYWRWaWRlbyh2aWRlbykKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW192bS5fdigiIOS4i+i9vSAiKV0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgImVsLWJ1dHRvbiIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBzaXplOiAibWluaSIsIGljb246ICJlbC1pY29uLXNoYXJlIiB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGljazogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gX3ZtLnNoYXJlVmlkZW8odmlkZW8pCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFtfdm0uX3YoIiDliIbkuqsgIildCiAgICAgICAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJlbC1idXR0b24iLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU6ICJtaW5pIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGljb246ICJlbC1pY29uLWRlbGV0ZSIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAiZGFuZ2VyIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGljazogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gX3ZtLmRlbGV0ZVZpZGVvKHZpZGVvKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbX3ZtLl92KCIg5Yig6ZmkICIpXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICAgICAgICB2aWRlby5zdGF0dXMgPT09ICJmYWlsZWQiCiAgICAgICAgICAgICAgICAgICAgICA/IF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICAgICAgICAgICAgICAgIHsgc3RhdGljQ2xhc3M6ICJ2aWRlby1hY3Rpb25zIiB9LAogICAgICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiZWwtYnV0dG9uIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplOiAibWluaSIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpY29uOiAiZWwtaWNvbi1yZWZyZXNoIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICJwcmltYXJ5IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGljazogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gX3ZtLnJldHJ5VmlkZW8odmlkZW8pCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFtfdm0uX3YoIiDph43or5UgIildCiAgICAgICAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJlbC1idXR0b24iLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU6ICJtaW5pIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGljb246ICJlbC1pY29uLWRlbGV0ZSIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAiZGFuZ2VyIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGljazogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gX3ZtLmRlbGV0ZVZpZGVvKHZpZGVvKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbX3ZtLl92KCIg5Yig6ZmkICIpXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgKQogICAgICAgICAgICB9KSwKICAgICAgICAgICAgX3ZtLmZpbHRlcmVkVmlkZW9zLmxlbmd0aCA9PT0gMAogICAgICAgICAgICAgID8gX2MoCiAgICAgICAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAiZW1wdHktc3RhdGUiIH0sCiAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICBfYygiaSIsIHsgc3RhdGljQ2xhc3M6ICJlbC1pY29uLXZpZGVvLWNhbWVyYS1zb2xpZCIgfSksCiAgICAgICAgICAgICAgICAgICAgX2MoImgzIiwgW192bS5fdigi5pqC5peg6KeG6aKRIildKSwKICAgICAgICAgICAgICAgICAgICBfYygicCIsIFsKICAgICAgICAgICAgICAgICAgICAgIF92bS5fdign6L+Y5rKh5pyJ55Sf5oiQ5Lu75L2V6KeG6aKR77yM54K55Ye7IuaWsOW7uuaJueasoSLlvIDlp4vliarovpEnKSwKICAgICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICJlbC1idXR0b24iLAogICAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyB0eXBlOiAicHJpbWFyeSIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgb246IHsgY2xpY2s6IF92bS5zdGFydE5ld0JhdGNoIH0sCiAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgW192bS5fdigiIOaWsOW7uuaJueasoSAiKV0KICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgIF0sCiAgICAgICAgICAyCiAgICAgICAgKSwKICAgICAgXSksCiAgICAgIF9jKAogICAgICAgICJlbC1kaWFsb2ciLAogICAgICAgIHsKICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgIHRpdGxlOiAi5paw5bu65Ymq6L6R5om55qyhIiwKICAgICAgICAgICAgdmlzaWJsZTogX3ZtLmJhdGNoRGlhbG9nVmlzaWJsZSwKICAgICAgICAgICAgd2lkdGg6ICI0MDBweCIsCiAgICAgICAgICB9LAogICAgICAgICAgb246IHsKICAgICAgICAgICAgInVwZGF0ZTp2aXNpYmxlIjogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgIF92bS5iYXRjaERpYWxvZ1Zpc2libGUgPSAkZXZlbnQKICAgICAgICAgICAgfSwKICAgICAgICAgIH0sCiAgICAgICAgfSwKICAgICAgICBbCiAgICAgICAgICBfYygKICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgIHsgc3RhdGljQ2xhc3M6ICJiYXRjaC1jb25maWciIH0sCiAgICAgICAgICAgIFsKICAgICAgICAgICAgICBfYygiaDQiLCBbX3ZtLl92KF92bS5fcyhfdm0udGVtcGxhdGVJbmZvLm5hbWUpKV0pLAogICAgICAgICAgICAgIF9jKCJwIiwgW192bS5fdigi6K+36YCJ5oup6KaB55Sf5oiQ55qE6KeG6aKR5pWw6YeP77yaIildKSwKICAgICAgICAgICAgICBfYygiZWwtaW5wdXQtbnVtYmVyIiwgewogICAgICAgICAgICAgICAgYXR0cnM6IHsgbWluOiAxLCBtYXg6IDUwLCBsYWJlbDogIueUn+aIkOaVsOmHjyIgfSwKICAgICAgICAgICAgICAgIG1vZGVsOiB7CiAgICAgICAgICAgICAgICAgIHZhbHVlOiBfdm0uYmF0Y2hDb3VudCwKICAgICAgICAgICAgICAgICAgY2FsbGJhY2s6IGZ1bmN0aW9uICgkJHYpIHsKICAgICAgICAgICAgICAgICAgICBfdm0uYmF0Y2hDb3VudCA9ICQkdgogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICBleHByZXNzaW9uOiAiYmF0Y2hDb3VudCIsCiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgIF9jKCJwIiwgeyBzdGF0aWNDbGFzczogImJhdGNoLXRpcCIgfSwgWwogICAgICAgICAgICAgICAgX3ZtLl92KCLmnIDlpJrlj6/nlJ/miJA1MOadoeinhumikSIpLAogICAgICAgICAgICAgIF0pLAogICAgICAgICAgICBdLAogICAgICAgICAgICAxCiAgICAgICAgICApLAogICAgICAgICAgX2MoCiAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgc3RhdGljQ2xhc3M6ICJkaWFsb2ctZm9vdGVyIiwKICAgICAgICAgICAgICBhdHRyczogeyBzbG90OiAiZm9vdGVyIiB9LAogICAgICAgICAgICAgIHNsb3Q6ICJmb290ZXIiLAogICAgICAgICAgICB9LAogICAgICAgICAgICBbCiAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAiZWwtYnV0dG9uIiwKICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICAgICAgICBjbGljazogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgICAgICAgX3ZtLmJhdGNoRGlhbG9nVmlzaWJsZSA9IGZhbHNlCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICBbX3ZtLl92KCLlj5bmtogiKV0KICAgICAgICAgICAgICApLAogICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgImVsLWJ1dHRvbiIsCiAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHR5cGU6ICJwcmltYXJ5IiB9LAogICAgICAgICAgICAgICAgICBvbjogeyBjbGljazogX3ZtLmNvbmZpcm1OZXdCYXRjaCB9LAogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIFtfdm0uX3YoIuW8gOWni+WJqui+kSIpXQogICAgICAgICAgICAgICksCiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIDEKICAgICAgICAgICksCiAgICAgICAgXQogICAgICApLAogICAgICBfYygKICAgICAgICAiZWwtZGlhbG9nIiwKICAgICAgICB7CiAgICAgICAgICBhdHRyczogewogICAgICAgICAgICB0aXRsZTogIuinhumikemihOiniCIsCiAgICAgICAgICAgIHZpc2libGU6IF92bS5wbGF5RGlhbG9nVmlzaWJsZSwKICAgICAgICAgICAgd2lkdGg6ICI4MDBweCIsCiAgICAgICAgICB9LAogICAgICAgICAgb246IHsKICAgICAgICAgICAgInVwZGF0ZTp2aXNpYmxlIjogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgIF92bS5wbGF5RGlhbG9nVmlzaWJsZSA9ICRldmVudAogICAgICAgICAgICB9LAogICAgICAgICAgICBjbG9zZTogX3ZtLnN0b3BWaWRlbywKICAgICAgICAgIH0sCiAgICAgICAgfSwKICAgICAgICBbCiAgICAgICAgICBfdm0uY3VycmVudFZpZGVvCiAgICAgICAgICAgID8gX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJ2aWRlby1wbGF5ZXIiIH0sIFsKICAgICAgICAgICAgICAgIF9jKCJ2aWRlbyIsIHsKICAgICAgICAgICAgICAgICAgcmVmOiAidmlkZW9QbGF5ZXIiLAogICAgICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgICAgIHNyYzogX3ZtLmN1cnJlbnRWaWRlby51cmwsCiAgICAgICAgICAgICAgICAgICAgY29udHJvbHM6ICIiLAogICAgICAgICAgICAgICAgICAgIHdpZHRoOiAiMTAwJSIsCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgIG9uOiB7IGxvYWRlZG1ldGFkYXRhOiBfdm0ub25WaWRlb0xvYWRlZCB9LAogICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgXSkKICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICBdCiAgICAgICksCiAgICBdLAogICAgMQogICkKfQp2YXIgc3RhdGljUmVuZGVyRm5zID0gWwogIGZ1bmN0aW9uICgpIHsKICAgIHZhciBfdm0gPSB0aGlzCiAgICB2YXIgX2ggPSBfdm0uJGNyZWF0ZUVsZW1lbnQKICAgIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogICAgcmV0dXJuIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAic3RhdC1pY29uIGNvbXBsZXRlZCIgfSwgWwogICAgICBfYygiaSIsIHsgc3RhdGljQ2xhc3M6ICJlbC1pY29uLWNoZWNrIiB9KSwKICAgIF0pCiAgfSwKICBmdW5jdGlvbiAoKSB7CiAgICB2YXIgX3ZtID0gdGhpcwogICAgdmFyIF9oID0gX3ZtLiRjcmVhdGVFbGVtZW50CiAgICB2YXIgX2MgPSBfdm0uX3NlbGYuX2MgfHwgX2gKICAgIHJldHVybiBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInN0YXQtaWNvbiBwcm9jZXNzaW5nIiB9LCBbCiAgICAgIF9jKCJpIiwgeyBzdGF0aWNDbGFzczogImVsLWljb24tbG9hZGluZyIgfSksCiAgICBdKQogIH0sCiAgZnVuY3Rpb24gKCkgewogICAgdmFyIF92bSA9IHRoaXMKICAgIHZhciBfaCA9IF92bS4kY3JlYXRlRWxlbWVudAogICAgdmFyIF9jID0gX3ZtLl9zZWxmLl9jIHx8IF9oCiAgICByZXR1cm4gX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJzdGF0LWljb24gZmFpbGVkIiB9LCBbCiAgICAgIF9jKCJpIiwgeyBzdGF0aWNDbGFzczogImVsLWljb24tY2xvc2UiIH0pLAogICAgXSkKICB9LAogIGZ1bmN0aW9uICgpIHsKICAgIHZhciBfdm0gPSB0aGlzCiAgICB2YXIgX2ggPSBfdm0uJGNyZWF0ZUVsZW1lbnQKICAgIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogICAgcmV0dXJuIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAic3RhdC1pY29uIHRvdGFsIiB9LCBbCiAgICAgIF9jKCJpIiwgeyBzdGF0aWNDbGFzczogImVsLWljb24tZG9jdW1lbnQiIH0pLAogICAgXSkKICB9LApdCnJlbmRlci5fd2l0aFN0cmlwcGVkID0gdHJ1ZQoKZXhwb3J0IHsgcmVuZGVyLCBzdGF0aWNSZW5kZXJGbnMgfQ=="}]}