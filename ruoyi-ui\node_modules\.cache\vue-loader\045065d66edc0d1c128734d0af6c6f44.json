{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\dijin.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\dijin.vue", "mtime": 1754998477470}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753759483709}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdWaWRlb1RlbXBsYXRlTWFuYWdlcicsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOWvueivneahhueKtuaAgQ0KICAgICAgY3JlYXRlRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBiYXRjaERpYWxvZ1Zpc2libGU6IGZhbHNlLA0KDQogICAgICAvLyDlvZPliY3mk43kvZznmoTmqKHmnb8NCiAgICAgIGN1cnJlbnRUZW1wbGF0ZToge30sDQoNCiAgICAgIC8vIOaJuemHj+WJ<PERSON>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"}, {"version": 3, "sources": ["dijin.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsJA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "dijin.vue", "sourceRoot": "src/views/store", "sourcesContent": ["<template>\r\n  <div class=\"video-template-manager\">\r\n    <!-- 头部工具栏 -->\r\n    <div class=\"header-toolbar\">\r\n      <div class=\"toolbar-left\">\r\n        <h2 class=\"page-title\">\r\n          <i class=\"el-icon-video-camera\"></i>\r\n          视频剪辑模板管理\r\n        </h2>\r\n        <p class=\"page-subtitle\">基于阿里云智能媒体服务的视频剪辑模板系统</p>\r\n      </div>\r\n      <div class=\"toolbar-right\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"createNewTemplate\">\r\n          新建模板\r\n        </el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"refreshTemplates\">\r\n          刷新\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 模板网格展示区域 -->\r\n    <div class=\"templates-grid\">\r\n      <div\r\n        v-for=\"template in templates\"\r\n        :key=\"template.id\"\r\n        class=\"template-card\"\r\n        @mouseenter=\"showActions(template.id)\"\r\n        @mouseleave=\"hideActions(template.id)\"\r\n      >\r\n        <!-- 模板缩略图 -->\r\n        <div class=\"template-thumbnail\">\r\n          <img :src=\"template.thumbnail\" :alt=\"template.name\" />\r\n          <div class=\"template-duration\">{{ template.duration }}</div>\r\n\r\n          <!-- 悬停时显示的操作按钮 -->\r\n          <div\r\n            class=\"template-actions\"\r\n            :class=\"{ 'show': template.showActions }\"\r\n          >\r\n            <el-button\r\n              type=\"primary\"\r\n              size=\"small\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"editTemplate(template)\"\r\n            >\r\n              编辑模板\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              size=\"small\"\r\n              icon=\"el-icon-view\"\r\n              @click=\"viewProgress(template)\"\r\n            >\r\n              查看进度\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板信息 -->\r\n        <div class=\"template-info\">\r\n          <h4 class=\"template-name\">{{ template.name }}</h4>\r\n          <p class=\"template-desc\">{{ template.description }}</p>\r\n          <div class=\"template-meta\">\r\n            <span class=\"create-time\">{{ template.createTime }}</span>\r\n            <span class=\"template-status\" :class=\"template.status\">\r\n              {{ getStatusText(template.status) }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 空状态 -->\r\n      <div v-if=\"templates.length === 0\" class=\"empty-state\">\r\n        <i class=\"el-icon-video-camera-solid\"></i>\r\n        <h3>暂无模板</h3>\r\n        <p>点击\"新建模板\"开始创建您的第一个视频剪辑模板</p>\r\n        <el-button type=\"primary\" @click=\"createNewTemplate\">\r\n          立即创建\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 新建模板对话框 -->\r\n    <el-dialog\r\n      title=\"新建剪辑模板\"\r\n      :visible.sync=\"createDialogVisible\"\r\n      width=\"500px\"\r\n      @close=\"resetCreateForm\"\r\n    >\r\n      <el-form :model=\"createForm\" :rules=\"createRules\" ref=\"createForm\" label-width=\"100px\">\r\n        <el-form-item label=\"模板名称\" prop=\"name\">\r\n          <el-input v-model=\"createForm.name\" placeholder=\"请输入模板名称\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"模板描述\" prop=\"description\">\r\n          <el-input\r\n            type=\"textarea\"\r\n            v-model=\"createForm.description\"\r\n            placeholder=\"请输入模板描述\"\r\n            :rows=\"3\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"视频尺寸\" prop=\"resolution\">\r\n          <el-select v-model=\"createForm.resolution\" placeholder=\"选择视频尺寸\">\r\n            <el-option label=\"1080x1920 (抖音推荐)\" value=\"1080x1920\"></el-option>\r\n            <el-option label=\"720x1280 (抖音标准)\" value=\"720x1280\"></el-option>\r\n            <el-option label=\"1920x1080 (横屏)\" value=\"1920x1080\"></el-option>\r\n            <el-option label=\"1280x720 (横屏)\" value=\"1280x720\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"帧率\" prop=\"fps\">\r\n          <el-select v-model=\"createForm.fps\" placeholder=\"选择帧率\">\r\n            <el-option label=\"24 FPS\" value=\"24\"></el-option>\r\n            <el-option label=\"30 FPS\" value=\"30\"></el-option>\r\n            <el-option label=\"60 FPS\" value=\"60\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"createDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmCreate\">确定创建</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 开始剪辑对话框 -->\r\n    <el-dialog\r\n      title=\"开始批量剪辑\"\r\n      :visible.sync=\"batchDialogVisible\"\r\n      width=\"400px\"\r\n    >\r\n      <div class=\"batch-config\">\r\n        <h4>{{ currentTemplate.name }}</h4>\r\n        <p>请选择要生成的视频数量：</p>\r\n        <el-input-number\r\n          v-model=\"batchCount\"\r\n          :min=\"1\"\r\n          :max=\"50\"\r\n          label=\"生成数量\"\r\n        ></el-input-number>\r\n        <p class=\"batch-tip\">最多可生成50条视频</p>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"batchDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"startBatchClip\">开始剪辑</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'VideoTemplateManager',\r\n  data() {\r\n    return {\r\n      // 对话框状态\r\n      createDialogVisible: false,\r\n      batchDialogVisible: false,\r\n\r\n      // 当前操作的模板\r\n      currentTemplate: {},\r\n\r\n      // 批量剪辑数量\r\n      batchCount: 1,\r\n\r\n      // 新建模板表单\r\n      createForm: {\r\n        name: '',\r\n        description: '',\r\n        resolution: '1080x1920', // 默认抖音推荐尺寸\r\n        fps: '30'\r\n      },\r\n\r\n      // 表单验证规则\r\n      createRules: {\r\n        name: [\r\n          { required: true, message: '请输入模板名称', trigger: 'blur' },\r\n          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }\r\n        ],\r\n        description: [\r\n          { required: true, message: '请输入模板描述', trigger: 'blur' }\r\n        ],\r\n        resolution: [\r\n          { required: true, message: '请选择视频尺寸', trigger: 'change' }\r\n        ],\r\n        fps: [\r\n          { required: true, message: '请选择帧率', trigger: 'change' }\r\n        ]\r\n      },\r\n\r\n      // 模板列表\r\n      templates: [\r\n        {\r\n          id: 'tpl-001',\r\n          name: '产品展示模板',\r\n          description: '专业的产品展示视频模板，适合电商产品宣传',\r\n          thumbnail: 'https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=Product',\r\n          duration: '00:30',\r\n          createTime: '2025-01-18 10:30',\r\n          status: 'active',\r\n          showActions: false\r\n        },\r\n        {\r\n          id: 'tpl-002',\r\n          name: '企业宣传模板',\r\n          description: '现代化企业宣传片模板，展现公司实力',\r\n          thumbnail: 'https://via.placeholder.com/300x200/50C878/FFFFFF?text=Company',\r\n          duration: '01:20',\r\n          createTime: '2025-01-17 15:20',\r\n          status: 'active',\r\n          showActions: false\r\n        },\r\n        {\r\n          id: 'tpl-003',\r\n          name: '教育培训模板',\r\n          description: '在线教育课程介绍模板，清晰易懂',\r\n          thumbnail: 'https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=Education',\r\n          duration: '02:15',\r\n          createTime: '2025-01-16 09:45',\r\n          status: 'draft',\r\n          showActions: false\r\n        }\r\n      ]\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    // 显示操作按钮\r\n    showActions(templateId) {\r\n      const template = this.templates.find(t => t.id === templateId)\r\n      if (template) {\r\n        this.$set(template, 'showActions', true)\r\n      }\r\n    },\r\n\r\n    // 隐藏操作按钮\r\n    hideActions(templateId) {\r\n      const template = this.templates.find(t => t.id === templateId)\r\n      if (template) {\r\n        this.$set(template, 'showActions', false)\r\n      }\r\n    },\r\n\r\n    // 创建新模板\r\n    createNewTemplate() {\r\n      this.createDialogVisible = true\r\n    },\r\n\r\n    // 刷新模板列表\r\n    refreshTemplates() {\r\n      this.$message.success('模板列表已刷新')\r\n      // TODO: 调用API刷新数据\r\n    },\r\n\r\n    // 编辑模板\r\n    editTemplate(template) {\r\n      this.currentTemplate = template\r\n      // 跳转到编辑器页面\r\n      this.$router.push({\r\n        path: '/storer/editor',\r\n        query: { templateId: template.id }\r\n      })\r\n    },\r\n\r\n    // 查看剪辑进度\r\n    viewProgress(template) {\r\n      this.currentTemplate = template\r\n      // 跳转到进度页面\r\n      this.$router.push({\r\n        path: '/storer/progress',\r\n        query: { templateId: template.id }\r\n      })\r\n    },\r\n\r\n    // 确认创建模板\r\n    confirmCreate() {\r\n      this.$refs.createForm.validate((valid) => {\r\n        if (valid) {\r\n          const newTemplate = {\r\n            id: 'tpl-' + Date.now(),\r\n            name: this.createForm.name,\r\n            description: this.createForm.description,\r\n            thumbnail: 'https://via.placeholder.com/300x200/9B59B6/FFFFFF?text=新模板',\r\n            duration: '00:00',\r\n            createTime: new Date().toLocaleString().slice(0, 16),\r\n            status: 'draft',\r\n            showActions: false,\r\n            resolution: this.createForm.resolution,\r\n            fps: this.createForm.fps\r\n          }\r\n\r\n          this.templates.unshift(newTemplate)\r\n          this.createDialogVisible = false\r\n          this.$message.success('模板创建成功！')\r\n\r\n          // 自动跳转到编辑器\r\n          this.editTemplate(newTemplate)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 重置创建表单\r\n    resetCreateForm() {\r\n      this.createForm = {\r\n        name: '',\r\n        description: '',\r\n        resolution: '1080x1920', // 默认抖音推荐尺寸\r\n        fps: '30'\r\n      }\r\n      if (this.$refs.createForm) {\r\n        this.$refs.createForm.resetFields()\r\n      }\r\n    },\r\n\r\n    // 开始批量剪辑\r\n    startBatchClip() {\r\n      this.$message.success(`开始生成 ${this.batchCount} 条视频`)\r\n      this.batchDialogVisible = false\r\n      // TODO: 调用批量剪辑API\r\n\r\n      // 跳转到进度页面查看剪辑状态\r\n      this.viewProgress(this.currentTemplate)\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        'active': '已发布',\r\n        'draft': '草稿',\r\n        'processing': '处理中'\r\n      }\r\n      return statusMap[status] || status\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.video-template-manager {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 头部工具栏 */\r\n.header-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.toolbar-left .page-title {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n}\r\n\r\n.toolbar-left .page-title i {\r\n  margin-right: 10px;\r\n  color: #409EFF;\r\n}\r\n\r\n.toolbar-left .page-subtitle {\r\n  margin: 0;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.toolbar-right {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 模板网格 */\r\n.templates-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\r\n  gap: 24px;\r\n  margin-bottom: 40px;\r\n}\r\n\r\n/* 模板卡片 */\r\n.template-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.template-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* 模板缩略图 */\r\n.template-thumbnail {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 200px;\r\n  overflow: hidden;\r\n}\r\n\r\n.template-thumbnail img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.template-card:hover .template-thumbnail img {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.template-duration {\r\n  position: absolute;\r\n  bottom: 10px;\r\n  right: 10px;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  color: white;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 操作按钮 */\r\n.template-actions {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  display: flex;\r\n  gap: 10px;\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.template-actions.show {\r\n  opacity: 1;\r\n}\r\n\r\n.template-actions::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: -20px;\r\n  left: -20px;\r\n  right: -20px;\r\n  bottom: -20px;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  border-radius: 8px;\r\n  z-index: -1;\r\n}\r\n\r\n/* 模板信息 */\r\n.template-info {\r\n  padding: 20px;\r\n}\r\n\r\n.template-name {\r\n  margin: 0 0 8px 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  line-height: 1.4;\r\n}\r\n\r\n.template-desc {\r\n  margin: 0 0 16px 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.template-meta {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.create-time {\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.template-status {\r\n  padding: 4px 12px;\r\n  border-radius: 20px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n}\r\n\r\n.template-status.active {\r\n  background: #f0f9ff;\r\n  color: #409EFF;\r\n}\r\n\r\n.template-status.draft {\r\n  background: #fdf6ec;\r\n  color: #E6A23C;\r\n}\r\n\r\n.template-status.processing {\r\n  background: #f0f2f5;\r\n  color: #909399;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  grid-column: 1 / -1;\r\n  text-align: center;\r\n  padding: 80px 20px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.empty-state i {\r\n  font-size: 64px;\r\n  color: #C0C4CC;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.empty-state h3 {\r\n  margin: 0 0 12px 0;\r\n  color: #303133;\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n}\r\n\r\n.empty-state p {\r\n  margin: 0 0 24px 0;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 对话框样式 */\r\n.batch-config {\r\n  text-align: center;\r\n  padding: 20px 0;\r\n}\r\n\r\n.batch-config h4 {\r\n  margin: 0 0 20px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n}\r\n\r\n.batch-config p {\r\n  margin: 0 0 16px 0;\r\n  color: #606266;\r\n}\r\n\r\n.batch-tip {\r\n  margin-top: 12px !important;\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .video-template-manager {\r\n    padding: 15px;\r\n  }\r\n\r\n  .header-toolbar {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .toolbar-right {\r\n    justify-content: center;\r\n  }\r\n\r\n  .templates-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 16px;\r\n  }\r\n\r\n  .template-actions {\r\n    flex-direction: column;\r\n  }\r\n}\r\n</style>"]}]}