{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\router\\index.js", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\router\\index.js", "mtime": 1754974674303}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\babel.config.js", "mtime": 1744968028000}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753759483709}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1753759488589}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vue", "_interopRequireDefault", "require", "_vueR<PERSON>er", "_SimpleLayout", "<PERSON><PERSON>", "use", "Router", "constantRoutes", "exports", "path", "component", "Layout", "hidden", "children", "Promise", "resolve", "then", "_interopRequireWildcard2", "default", "meta", "title", "redirect", "name", "icon", "dynamicRoutes", "router", "mode", "scroll<PERSON>eh<PERSON>or", "y", "routes", "resetRouter", "newRouter", "matcher", "_default"], "sources": ["E:/ry-vue-flowable-xg-main/ruoyi-ui/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport Router from 'vue-router'\n\nVue.use(Router)\n\n/* Layout */\nimport Layout from '@/layout/SimpleLayout'\n\n/**\n * Note: 路由配置项\n *\n * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1\n * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面\n *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面\n *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由\n *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由\n * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击\n * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题\n * query: '{\"id\": 1, \"name\": \"ry\"}' // 访问路由的默认传递参数\n * roles: ['admin', 'common']       // 访问路由的角色权限\n * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限\n * meta : {\n    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)\n    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字\n    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg\n    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示\n    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。\n  }\n */\n\n// 公共路由\nexport const constantRoutes = [\n  {\n    path: '/redirect',\n    component: Layout,\n    hidden: true,\n    children: [\n      {\n        path: '/redirect/:path(.*)',\n        component: () => import('@/views/redirect')\n      }\n    ]\n  },\n  {\n    path: '/login',\n    component: () => import('@/views/login'),\n    hidden: true\n  },\n  {\n    path: '/update-progress',\n    component: () => import('@/views/update/UpdateProgressFixed'),\n    hidden: true,\n    meta: { title: '系统更新进度' }\n  },\n\n  {\n    path: '/404',\n    component: () => import('@/views/error/404'),\n    hidden: true\n  },\n  {\n    path: '/401',\n    component: () => import('@/views/error/401'),\n    hidden: true\n  },\n  {\n    path: '/',\n    redirect: '/storer/index',\n    hidden: true\n  },\n  {\n    path: '/index',\n    redirect: '/storer/index',\n    hidden: true\n  },\n  {\n    path: '/agent',\n    component: Layout,\n    children: [\n      {\n        path: 'list',\n        component: () => import('@/views/marketing/config.vue'),\n        name: 'MarketingConfig',\n        meta: { title: '营销配置管理', icon: 'peoples' }\n      }\n    ]\n  },\n  {\n    path: '/merchant',\n    component: Layout,\n    children: [\n      {\n        path: 'list',\n        component: () => import('@/views/merchant/list.vue'),\n        name: 'MerchantList',\n        meta: { title: '商家列表', icon: 'shopping' }\n      }\n    ]\n  },\n  {\n    path: '/finance',\n    component: Layout,\n    redirect: 'noRedirect',\n    name: 'Finance',\n    meta: { title: '财务管理', icon: 'money' },\n    children: [\n      {\n        path: 'overview',\n        component: () => import('@/views/finance/overview.vue'),\n        name: 'FinanceOverview',\n        meta: { title: '财务概览' }\n      },\n      {\n        path: 'record',\n        component: () => import('@/views/finance/record.vue'),\n        name: 'FinanceRecord',\n        meta: { title: '交易记录' }\n      }\n    ]\n  },\n  {\n    path: '/system',\n    component: Layout,\n    redirect: 'noRedirect',\n    name: 'System',\n    meta: { title: '系统管理', icon: 'system' },\n    children: [\n      {\n        path: 'menu',\n        component: () => import('@/views/system/menu/index.vue'),\n        name: 'Menu',\n        meta: { title: '菜单管理' }\n      },\n      {\n        path: 'dict',\n        component: () => import('@/views/system/dict/index.vue'),\n        name: 'Dict',\n        meta: { title: '字典管理' }\n      },\n      {\n        path: 'config',\n        component: () => import('@/views/system/config/index.vue'),\n        name: 'Config',\n        meta: { title: '参数设置' }\n      }\n    ]\n  },\n  {\n    path: '/storer',\n    component: Layout,\n    redirect: '/storer/index',\n    name: 'Storer',\n    meta: { title: '店铺管理', icon: 'shopping' },\n    children: [\n      {\n        path: 'index',\n        component: () => import('@/views/dashboard/index.vue'),\n        name: 'StorerIndex',\n        meta: { title: '工作台' }\n      },\n      {\n        path: 'store',\n        component: () => import('@/views/store/store.vue'),\n        name: 'StorerStore',\n        meta: { title: '门店列表' }\n      },\n      {\n        path: 'shipin',\n        component: () => import('@/views/store/shipin.vue'),\n        name: 'StorerShipin',\n        meta: { title: 'AI剪辑文案' }\n      },\n      {\n        path: 'dou',\n        component: () => import('@/views/store/dou.vue'),\n        name: 'StorerDou',\n        meta: { title: '抖音/快手文案' }\n      },\n\n      {\n        path: 'hong',\n        component: () => import('@/views/store/hong.vue'),\n        name: 'StorerHong',\n        meta: { title: '小红书文案' }\n      },\n      {\n        path: 'daka',\n        component: () => import('@/views/store/daka.vue'),\n        name: 'StorerDaka',\n        meta: { title: '打卡点评/朋友圈文案' }\n      },\n      {\n        path: 'up',\n        component: () => import('@/views/store/up.vue'),\n        name: 'StorerUp',\n        meta: { title: '素材上传' }\n      },\n      {\n        path: 'huati',\n        component: () => import('@/views/store/huati.vue'),\n        name: 'StorerHuati',\n        meta: { title: '话题创建' }\n      },\n      {\n        path: 'mingxi',\n        component: () => import('@/views/store/mingxi.vue'),\n        name: 'StorerMingxi',\n        meta: { title: '算力明细' }\n      },\n      {\n        path: 'sk',\n        component: () => import('@/views/store/sk.vue'),\n        name: 'StorerSk',\n        meta: { title: '待发布视频库' }\n      },\n      {\n        path: 'tk',\n        component: () => import('@/views/store/tk.vue'),\n        name: 'StorerTk',\n        meta: { title: '图片库营销活动' }\n      },\n      {\n        path: 'jiang',\n        component: () => import('@/views/store/jiang.vue'),\n        name: 'StorerJiang',\n        meta: { title: '奖品设置' }\n      },\n      {\n        path: 'zhuanp',\n        component: () => import('@/views/store/zhuanp.vue'),\n        name: 'StorerZhuanp',\n        meta: { title: '大转盘配置' }\n      },\n      {\n        path: 'daijiang',\n        component: () => import('@/views/store/daijiang.vue'),\n        name: 'StorerDaijiang',\n        meta: { title: '待领取信息' }\n      },\n      {\n        path: 'dijin',\n        component: () => import('@/views/store/dijin.vue'),\n        name: 'StorerDijin',\n        meta: { title: 'AI递进式剪辑' }\n      },\n      {\n        path: 'editor',\n        component: () => import('@/views/store/editor.vue'),\n        name: 'VideoEditor',\n        meta: { title: '视频编辑器' }\n      },\n      {\n        path: 'progress',\n        component: () => import('@/views/store/progress.vue'),\n        name: 'VideoProgress',\n        meta: { title: '剪辑进度' }\n      },\n      {\n        path: 'video-platform',\n        component: () => import('@/views/store/video-platform.vue'),\n        name: 'VideoPlatform',\n        meta: { title: '智能视频制作平台' }\n      },\n      {\n        path: 'video-demo',\n        component: () => import('@/views/store/video-demo.vue'),\n        name: 'VideoDemo',\n        meta: { title: '视频平台演示' }\n      },\n      {\n        path: 'test-page',\n        component: () => import('@/views/store/test-page.vue'),\n        name: 'TestPage',\n        meta: { title: '测试页面' }\n      },\n      {\n        path: 'djindu',\n        component: () => import('@/views/store/djindu.vue'),\n        name: 'StorerDjindu',\n        meta: { title: '递进剪辑进度' }\n      },\n      {\n        path: 'ai-test',\n        component: () => import('@/views/ai/test.vue'),\n        name: 'AiTest',\n        meta: { title: 'AI接口测试' }\n      },\n      {\n        path: 'quick-test',\n        component: () => import('@/views/ai/quick-test.vue'),\n        name: 'QuickTest',\n        meta: { title: 'AI快速测试' }\n      }\n    ]\n  },\n  // DIY编辑器路由\n  {\n    path: '/promotion/:storeId/diy',\n    component: () => import('@/views/promotion/PromotionDIYFixed.vue'),\n    name: 'PromotionDIY',\n    hidden: true,\n    meta: { title: '专业DIY页面编辑器' }\n  },\n  // NFC推广页面路由（客户访问的页面）\n  {\n    path: '/promotion/:id',\n    component: () => import('@/views/promotion/PromotionPreview.vue'),\n    name: 'PromotionPreview',\n    hidden: true,\n    meta: { title: '推广页面预览' }\n  },\n  {\n    path: '/promotion/config',\n    component: Layout,\n    name: 'PromotionConfig',\n    hidden: true,\n    children: [\n      {\n        path: 'index',\n        component: () => import('@/views/promotion/PromotionPageConfig.vue'),\n        name: 'PromotionConfigIndex',\n        meta: { title: '推广页面配置' }\n      }\n    ]\n  },\n  {\n    path: '/changelog',\n    component: Layout,\n    children: [\n      {\n        path: 'index',\n        component: () => import('@/views/changelog/index.vue'),\n        name: 'Changelog',\n        meta: { title: '更新日志', icon: 'documentation' }\n      }\n    ]\n  },\n\n  // 404 页面必须放在末尾\n  { path: '*', redirect: '/404', hidden: true }\n]\n\n// 动态路由，基于用户权限动态去加载\nexport const dynamicRoutes = []\n\nconst router = new Router({\n  mode: 'history', // 去掉url中的#\n  scrollBehavior: () => ({ y: 0 }),\n  routes: constantRoutes\n})\n\n// 重置路由\nexport function resetRouter() {\n  const newRouter = new Router({\n    mode: 'history',\n    scrollBehavior: () => ({ y: 0 }),\n    routes: constantRoutes\n  })\n  router.matcher = newRouter.matcher\n}\n\nexport default router\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;AAKA,IAAAE,aAAA,GAAAH,sBAAA,CAAAC,OAAA;AAHAG,YAAG,CAACC,GAAG,CAACC,kBAAM,CAAC;;AAEf;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACO,IAAMC,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG,CAC5B;EACEE,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEC,qBAAM;EACjBC,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,kBAAkB;MAAA;IAAA;EAC5C,CAAC;AAEL,CAAC,EACD;EACEQ,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,eAAe;IAAA;EAAA,CAAC;EACxCW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,oCAAoC;IAAA;EAAA,CAAC;EAC7DW,MAAM,EAAE,IAAI;EACZO,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAS;AAC1B,CAAC,EAED;EACEX,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mBAAmB;IAAA;EAAA,CAAC;EAC5CW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mBAAmB;IAAA;EAAA,CAAC;EAC5CW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,GAAG;EACTY,QAAQ,EAAE,eAAe;EACzBT,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,QAAQ;EACdY,QAAQ,EAAE,eAAe;EACzBT,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEC,qBAAM;EACjBE,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDqB,IAAI,EAAE,iBAAiB;IACvBH,IAAI,EAAE;MAAEC,KAAK,EAAE,QAAQ;MAAEG,IAAI,EAAE;IAAU;EAC3C,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEC,qBAAM;EACjBE,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,2BAA2B;MAAA;IAAA,CAAC;IACpDqB,IAAI,EAAE,cAAc;IACpBH,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEG,IAAI,EAAE;IAAW;EAC1C,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEC,qBAAM;EACjBU,QAAQ,EAAE,YAAY;EACtBC,IAAI,EAAE,SAAS;EACfH,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEG,IAAI,EAAE;EAAQ,CAAC;EACtCV,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDqB,IAAI,EAAE,iBAAiB;IACvBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC,EACD;IACEX,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,4BAA4B;MAAA;IAAA,CAAC;IACrDqB,IAAI,EAAE,eAAe;IACrBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC;AAEL,CAAC,EACD;EACEX,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEC,qBAAM;EACjBU,QAAQ,EAAE,YAAY;EACtBC,IAAI,EAAE,QAAQ;EACdH,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEG,IAAI,EAAE;EAAS,CAAC;EACvCV,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,+BAA+B;MAAA;IAAA,CAAC;IACxDqB,IAAI,EAAE,MAAM;IACZH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC,EACD;IACEX,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,+BAA+B;MAAA;IAAA,CAAC;IACxDqB,IAAI,EAAE,MAAM;IACZH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC,EACD;IACEX,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,iCAAiC;MAAA;IAAA,CAAC;IAC1DqB,IAAI,EAAE,QAAQ;IACdH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC;AAEL,CAAC,EACD;EACEX,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEC,qBAAM;EACjBU,QAAQ,EAAE,eAAe;EACzBC,IAAI,EAAE,QAAQ;EACdH,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEG,IAAI,EAAE;EAAW,CAAC;EACzCV,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,6BAA6B;MAAA;IAAA,CAAC;IACtDqB,IAAI,EAAE,aAAa;IACnBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAM;EACvB,CAAC,EACD;IACEX,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDqB,IAAI,EAAE,aAAa;IACnBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC,EACD;IACEX,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,0BAA0B;MAAA;IAAA,CAAC;IACnDqB,IAAI,EAAE,cAAc;IACpBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAS;EAC1B,CAAC,EACD;IACEX,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,uBAAuB;MAAA;IAAA,CAAC;IAChDqB,IAAI,EAAE,WAAW;IACjBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAU;EAC3B,CAAC,EAED;IACEX,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,wBAAwB;MAAA;IAAA,CAAC;IACjDqB,IAAI,EAAE,YAAY;IAClBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAQ;EACzB,CAAC,EACD;IACEX,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,wBAAwB;MAAA;IAAA,CAAC;IACjDqB,IAAI,EAAE,YAAY;IAClBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAa;EAC9B,CAAC,EACD;IACEX,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,sBAAsB;MAAA;IAAA,CAAC;IAC/CqB,IAAI,EAAE,UAAU;IAChBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC,EACD;IACEX,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDqB,IAAI,EAAE,aAAa;IACnBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC,EACD;IACEX,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,0BAA0B;MAAA;IAAA,CAAC;IACnDqB,IAAI,EAAE,cAAc;IACpBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC,EACD;IACEX,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,sBAAsB;MAAA;IAAA,CAAC;IAC/CqB,IAAI,EAAE,UAAU;IAChBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAS;EAC1B,CAAC,EACD;IACEX,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,sBAAsB;MAAA;IAAA,CAAC;IAC/CqB,IAAI,EAAE,UAAU;IAChBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAU;EAC3B,CAAC,EACD;IACEX,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDqB,IAAI,EAAE,aAAa;IACnBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC,EACD;IACEX,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,0BAA0B;MAAA;IAAA,CAAC;IACnDqB,IAAI,EAAE,cAAc;IACpBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAQ;EACzB,CAAC,EACD;IACEX,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,4BAA4B;MAAA;IAAA,CAAC;IACrDqB,IAAI,EAAE,gBAAgB;IACtBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAQ;EACzB,CAAC,EACD;IACEX,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDqB,IAAI,EAAE,aAAa;IACnBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAU;EAC3B,CAAC,EACD;IACEX,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,0BAA0B;MAAA;IAAA,CAAC;IACnDqB,IAAI,EAAE,aAAa;IACnBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAQ;EACzB,CAAC,EACD;IACEX,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,4BAA4B;MAAA;IAAA,CAAC;IACrDqB,IAAI,EAAE,eAAe;IACrBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC,EACD;IACEX,IAAI,EAAE,gBAAgB;IACtBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,kCAAkC;MAAA;IAAA,CAAC;IAC3DqB,IAAI,EAAE,eAAe;IACrBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAW;EAC5B,CAAC,EACD;IACEX,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDqB,IAAI,EAAE,WAAW;IACjBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAS;EAC1B,CAAC,EACD;IACEX,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,6BAA6B;MAAA;IAAA,CAAC;IACtDqB,IAAI,EAAE,UAAU;IAChBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAO;EACxB,CAAC,EACD;IACEX,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,0BAA0B;MAAA;IAAA,CAAC;IACnDqB,IAAI,EAAE,cAAc;IACpBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAS;EAC1B,CAAC,EACD;IACEX,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,qBAAqB;MAAA;IAAA,CAAC;IAC9CqB,IAAI,EAAE,QAAQ;IACdH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAS;EAC1B,CAAC,EACD;IACEX,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,2BAA2B;MAAA;IAAA,CAAC;IACpDqB,IAAI,EAAE,WAAW;IACjBH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAS;EAC1B,CAAC;AAEL,CAAC;AACD;AACA;EACEX,IAAI,EAAE,yBAAyB;EAC/BC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yCAAyC;IAAA;EAAA,CAAC;EAClEqB,IAAI,EAAE,cAAc;EACpBV,MAAM,EAAE,IAAI;EACZO,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAa;AAC9B,CAAC;AACD;AACA;EACEX,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,wCAAwC;IAAA;EAAA,CAAC;EACjEqB,IAAI,EAAE,kBAAkB;EACxBV,MAAM,EAAE,IAAI;EACZO,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAS;AAC1B,CAAC,EACD;EACEX,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,qBAAM;EACjBW,IAAI,EAAE,iBAAiB;EACvBV,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,2CAA2C;MAAA;IAAA,CAAC;IACpEqB,IAAI,EAAE,sBAAsB;IAC5BH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAS;EAC1B,CAAC;AAEL,CAAC,EACD;EACEX,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEC,qBAAM;EACjBE,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,6BAA6B;MAAA;IAAA,CAAC;IACtDqB,IAAI,EAAE,WAAW;IACjBH,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEG,IAAI,EAAE;IAAgB;EAC/C,CAAC;AAEL,CAAC;AAED;AACA;EAAEd,IAAI,EAAE,GAAG;EAAEY,QAAQ,EAAE,MAAM;EAAET,MAAM,EAAE;AAAK,CAAC,CAC9C;;AAED;AACO,IAAMY,aAAa,GAAAhB,OAAA,CAAAgB,aAAA,GAAG,EAAE;AAE/B,IAAMC,MAAM,GAAG,IAAInB,kBAAM,CAAC;EACxBoB,IAAI,EAAE,SAAS;EAAE;EACjBC,cAAc,EAAE,SAAhBA,cAAcA,CAAA;IAAA,OAAS;MAAEC,CAAC,EAAE;IAAE,CAAC;EAAA,CAAC;EAChCC,MAAM,EAAEtB;AACV,CAAC,CAAC;;AAEF;AACO,SAASuB,WAAWA,CAAA,EAAG;EAC5B,IAAMC,SAAS,GAAG,IAAIzB,kBAAM,CAAC;IAC3BoB,IAAI,EAAE,SAAS;IACfC,cAAc,EAAE,SAAhBA,cAAcA,CAAA;MAAA,OAAS;QAAEC,CAAC,EAAE;MAAE,CAAC;IAAA,CAAC;IAChCC,MAAM,EAAEtB;EACV,CAAC,CAAC;EACFkB,MAAM,CAACO,OAAO,GAAGD,SAAS,CAACC,OAAO;AACpC;AAAC,IAAAC,QAAA,GAAAzB,OAAA,CAAAU,OAAA,GAEcO,MAAM", "ignoreList": []}]}