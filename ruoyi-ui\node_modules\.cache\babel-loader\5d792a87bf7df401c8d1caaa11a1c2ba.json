{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\dijin.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\dijin.vue", "mtime": 1754998477470}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\babel.config.js", "mtime": 1744968028000}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753759483709}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "createDialogVisible", "batchDialogVisible", "currentTemplate", "batchCount", "createForm", "description", "resolution", "fps", "createRules", "required", "message", "trigger", "min", "max", "templates", "id", "thumbnail", "duration", "createTime", "status", "showActions", "methods", "templateId", "template", "find", "t", "$set", "hideActions", "createNewTemplate", "refreshTemplates", "$message", "success", "editTemplate", "$router", "push", "path", "query", "viewProgress", "confirmCreate", "_this", "$refs", "validate", "valid", "newTemplate", "Date", "now", "toLocaleString", "slice", "unshift", "resetCreateForm", "resetFields", "startBatchClip", "concat", "getStatusText", "statusMap"], "sources": ["src/views/store/dijin.vue"], "sourcesContent": ["<template>\r\n  <div class=\"video-template-manager\">\r\n    <!-- 头部工具栏 -->\r\n    <div class=\"header-toolbar\">\r\n      <div class=\"toolbar-left\">\r\n        <h2 class=\"page-title\">\r\n          <i class=\"el-icon-video-camera\"></i>\r\n          视频剪辑模板管理\r\n        </h2>\r\n        <p class=\"page-subtitle\">基于阿里云智能媒体服务的视频剪辑模板系统</p>\r\n      </div>\r\n      <div class=\"toolbar-right\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"createNewTemplate\">\r\n          新建模板\r\n        </el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"refreshTemplates\">\r\n          刷新\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 模板网格展示区域 -->\r\n    <div class=\"templates-grid\">\r\n      <div\r\n        v-for=\"template in templates\"\r\n        :key=\"template.id\"\r\n        class=\"template-card\"\r\n        @mouseenter=\"showActions(template.id)\"\r\n        @mouseleave=\"hideActions(template.id)\"\r\n      >\r\n        <!-- 模板缩略图 -->\r\n        <div class=\"template-thumbnail\">\r\n          <img :src=\"template.thumbnail\" :alt=\"template.name\" />\r\n          <div class=\"template-duration\">{{ template.duration }}</div>\r\n\r\n          <!-- 悬停时显示的操作按钮 -->\r\n          <div\r\n            class=\"template-actions\"\r\n            :class=\"{ 'show': template.showActions }\"\r\n          >\r\n            <el-button\r\n              type=\"primary\"\r\n              size=\"small\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"editTemplate(template)\"\r\n            >\r\n              编辑模板\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              size=\"small\"\r\n              icon=\"el-icon-view\"\r\n              @click=\"viewProgress(template)\"\r\n            >\r\n              查看进度\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 模板信息 -->\r\n        <div class=\"template-info\">\r\n          <h4 class=\"template-name\">{{ template.name }}</h4>\r\n          <p class=\"template-desc\">{{ template.description }}</p>\r\n          <div class=\"template-meta\">\r\n            <span class=\"create-time\">{{ template.createTime }}</span>\r\n            <span class=\"template-status\" :class=\"template.status\">\r\n              {{ getStatusText(template.status) }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 空状态 -->\r\n      <div v-if=\"templates.length === 0\" class=\"empty-state\">\r\n        <i class=\"el-icon-video-camera-solid\"></i>\r\n        <h3>暂无模板</h3>\r\n        <p>点击\"新建模板\"开始创建您的第一个视频剪辑模板</p>\r\n        <el-button type=\"primary\" @click=\"createNewTemplate\">\r\n          立即创建\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 新建模板对话框 -->\r\n    <el-dialog\r\n      title=\"新建剪辑模板\"\r\n      :visible.sync=\"createDialogVisible\"\r\n      width=\"500px\"\r\n      @close=\"resetCreateForm\"\r\n    >\r\n      <el-form :model=\"createForm\" :rules=\"createRules\" ref=\"createForm\" label-width=\"100px\">\r\n        <el-form-item label=\"模板名称\" prop=\"name\">\r\n          <el-input v-model=\"createForm.name\" placeholder=\"请输入模板名称\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"模板描述\" prop=\"description\">\r\n          <el-input\r\n            type=\"textarea\"\r\n            v-model=\"createForm.description\"\r\n            placeholder=\"请输入模板描述\"\r\n            :rows=\"3\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"视频尺寸\" prop=\"resolution\">\r\n          <el-select v-model=\"createForm.resolution\" placeholder=\"选择视频尺寸\">\r\n            <el-option label=\"1080x1920 (抖音推荐)\" value=\"1080x1920\"></el-option>\r\n            <el-option label=\"720x1280 (抖音标准)\" value=\"720x1280\"></el-option>\r\n            <el-option label=\"1920x1080 (横屏)\" value=\"1920x1080\"></el-option>\r\n            <el-option label=\"1280x720 (横屏)\" value=\"1280x720\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"帧率\" prop=\"fps\">\r\n          <el-select v-model=\"createForm.fps\" placeholder=\"选择帧率\">\r\n            <el-option label=\"24 FPS\" value=\"24\"></el-option>\r\n            <el-option label=\"30 FPS\" value=\"30\"></el-option>\r\n            <el-option label=\"60 FPS\" value=\"60\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"createDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmCreate\">确定创建</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 开始剪辑对话框 -->\r\n    <el-dialog\r\n      title=\"开始批量剪辑\"\r\n      :visible.sync=\"batchDialogVisible\"\r\n      width=\"400px\"\r\n    >\r\n      <div class=\"batch-config\">\r\n        <h4>{{ currentTemplate.name }}</h4>\r\n        <p>请选择要生成的视频数量：</p>\r\n        <el-input-number\r\n          v-model=\"batchCount\"\r\n          :min=\"1\"\r\n          :max=\"50\"\r\n          label=\"生成数量\"\r\n        ></el-input-number>\r\n        <p class=\"batch-tip\">最多可生成50条视频</p>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"batchDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"startBatchClip\">开始剪辑</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'VideoTemplateManager',\r\n  data() {\r\n    return {\r\n      // 对话框状态\r\n      createDialogVisible: false,\r\n      batchDialogVisible: false,\r\n\r\n      // 当前操作的模板\r\n      currentTemplate: {},\r\n\r\n      // 批量剪辑数量\r\n      batchCount: 1,\r\n\r\n      // 新建模板表单\r\n      createForm: {\r\n        name: '',\r\n        description: '',\r\n        resolution: '1080x1920', // 默认抖音推荐尺寸\r\n        fps: '30'\r\n      },\r\n\r\n      // 表单验证规则\r\n      createRules: {\r\n        name: [\r\n          { required: true, message: '请输入模板名称', trigger: 'blur' },\r\n          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }\r\n        ],\r\n        description: [\r\n          { required: true, message: '请输入模板描述', trigger: 'blur' }\r\n        ],\r\n        resolution: [\r\n          { required: true, message: '请选择视频尺寸', trigger: 'change' }\r\n        ],\r\n        fps: [\r\n          { required: true, message: '请选择帧率', trigger: 'change' }\r\n        ]\r\n      },\r\n\r\n      // 模板列表\r\n      templates: [\r\n        {\r\n          id: 'tpl-001',\r\n          name: '产品展示模板',\r\n          description: '专业的产品展示视频模板，适合电商产品宣传',\r\n          thumbnail: 'https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=Product',\r\n          duration: '00:30',\r\n          createTime: '2025-01-18 10:30',\r\n          status: 'active',\r\n          showActions: false\r\n        },\r\n        {\r\n          id: 'tpl-002',\r\n          name: '企业宣传模板',\r\n          description: '现代化企业宣传片模板，展现公司实力',\r\n          thumbnail: 'https://via.placeholder.com/300x200/50C878/FFFFFF?text=Company',\r\n          duration: '01:20',\r\n          createTime: '2025-01-17 15:20',\r\n          status: 'active',\r\n          showActions: false\r\n        },\r\n        {\r\n          id: 'tpl-003',\r\n          name: '教育培训模板',\r\n          description: '在线教育课程介绍模板，清晰易懂',\r\n          thumbnail: 'https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=Education',\r\n          duration: '02:15',\r\n          createTime: '2025-01-16 09:45',\r\n          status: 'draft',\r\n          showActions: false\r\n        }\r\n      ]\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    // 显示操作按钮\r\n    showActions(templateId) {\r\n      const template = this.templates.find(t => t.id === templateId)\r\n      if (template) {\r\n        this.$set(template, 'showActions', true)\r\n      }\r\n    },\r\n\r\n    // 隐藏操作按钮\r\n    hideActions(templateId) {\r\n      const template = this.templates.find(t => t.id === templateId)\r\n      if (template) {\r\n        this.$set(template, 'showActions', false)\r\n      }\r\n    },\r\n\r\n    // 创建新模板\r\n    createNewTemplate() {\r\n      this.createDialogVisible = true\r\n    },\r\n\r\n    // 刷新模板列表\r\n    refreshTemplates() {\r\n      this.$message.success('模板列表已刷新')\r\n      // TODO: 调用API刷新数据\r\n    },\r\n\r\n    // 编辑模板\r\n    editTemplate(template) {\r\n      this.currentTemplate = template\r\n      // 跳转到编辑器页面\r\n      this.$router.push({\r\n        path: '/storer/editor',\r\n        query: { templateId: template.id }\r\n      })\r\n    },\r\n\r\n    // 查看剪辑进度\r\n    viewProgress(template) {\r\n      this.currentTemplate = template\r\n      // 跳转到进度页面\r\n      this.$router.push({\r\n        path: '/storer/progress',\r\n        query: { templateId: template.id }\r\n      })\r\n    },\r\n\r\n    // 确认创建模板\r\n    confirmCreate() {\r\n      this.$refs.createForm.validate((valid) => {\r\n        if (valid) {\r\n          const newTemplate = {\r\n            id: 'tpl-' + Date.now(),\r\n            name: this.createForm.name,\r\n            description: this.createForm.description,\r\n            thumbnail: 'https://via.placeholder.com/300x200/9B59B6/FFFFFF?text=新模板',\r\n            duration: '00:00',\r\n            createTime: new Date().toLocaleString().slice(0, 16),\r\n            status: 'draft',\r\n            showActions: false,\r\n            resolution: this.createForm.resolution,\r\n            fps: this.createForm.fps\r\n          }\r\n\r\n          this.templates.unshift(newTemplate)\r\n          this.createDialogVisible = false\r\n          this.$message.success('模板创建成功！')\r\n\r\n          // 自动跳转到编辑器\r\n          this.editTemplate(newTemplate)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 重置创建表单\r\n    resetCreateForm() {\r\n      this.createForm = {\r\n        name: '',\r\n        description: '',\r\n        resolution: '1080x1920', // 默认抖音推荐尺寸\r\n        fps: '30'\r\n      }\r\n      if (this.$refs.createForm) {\r\n        this.$refs.createForm.resetFields()\r\n      }\r\n    },\r\n\r\n    // 开始批量剪辑\r\n    startBatchClip() {\r\n      this.$message.success(`开始生成 ${this.batchCount} 条视频`)\r\n      this.batchDialogVisible = false\r\n      // TODO: 调用批量剪辑API\r\n\r\n      // 跳转到进度页面查看剪辑状态\r\n      this.viewProgress(this.currentTemplate)\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        'active': '已发布',\r\n        'draft': '草稿',\r\n        'processing': '处理中'\r\n      }\r\n      return statusMap[status] || status\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.video-template-manager {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 头部工具栏 */\r\n.header-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.toolbar-left .page-title {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n}\r\n\r\n.toolbar-left .page-title i {\r\n  margin-right: 10px;\r\n  color: #409EFF;\r\n}\r\n\r\n.toolbar-left .page-subtitle {\r\n  margin: 0;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.toolbar-right {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n/* 模板网格 */\r\n.templates-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\r\n  gap: 24px;\r\n  margin-bottom: 40px;\r\n}\r\n\r\n/* 模板卡片 */\r\n.template-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.template-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* 模板缩略图 */\r\n.template-thumbnail {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 200px;\r\n  overflow: hidden;\r\n}\r\n\r\n.template-thumbnail img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.template-card:hover .template-thumbnail img {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.template-duration {\r\n  position: absolute;\r\n  bottom: 10px;\r\n  right: 10px;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  color: white;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 操作按钮 */\r\n.template-actions {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  display: flex;\r\n  gap: 10px;\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.template-actions.show {\r\n  opacity: 1;\r\n}\r\n\r\n.template-actions::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: -20px;\r\n  left: -20px;\r\n  right: -20px;\r\n  bottom: -20px;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  border-radius: 8px;\r\n  z-index: -1;\r\n}\r\n\r\n/* 模板信息 */\r\n.template-info {\r\n  padding: 20px;\r\n}\r\n\r\n.template-name {\r\n  margin: 0 0 8px 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n  line-height: 1.4;\r\n}\r\n\r\n.template-desc {\r\n  margin: 0 0 16px 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.template-meta {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.create-time {\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.template-status {\r\n  padding: 4px 12px;\r\n  border-radius: 20px;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n}\r\n\r\n.template-status.active {\r\n  background: #f0f9ff;\r\n  color: #409EFF;\r\n}\r\n\r\n.template-status.draft {\r\n  background: #fdf6ec;\r\n  color: #E6A23C;\r\n}\r\n\r\n.template-status.processing {\r\n  background: #f0f2f5;\r\n  color: #909399;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  grid-column: 1 / -1;\r\n  text-align: center;\r\n  padding: 80px 20px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.empty-state i {\r\n  font-size: 64px;\r\n  color: #C0C4CC;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.empty-state h3 {\r\n  margin: 0 0 12px 0;\r\n  color: #303133;\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n}\r\n\r\n.empty-state p {\r\n  margin: 0 0 24px 0;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 对话框样式 */\r\n.batch-config {\r\n  text-align: center;\r\n  padding: 20px 0;\r\n}\r\n\r\n.batch-config h4 {\r\n  margin: 0 0 20px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n}\r\n\r\n.batch-config p {\r\n  margin: 0 0 16px 0;\r\n  color: #606266;\r\n}\r\n\r\n.batch-tip {\r\n  margin-top: 12px !important;\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .video-template-manager {\r\n    padding: 15px;\r\n  }\r\n\r\n  .header-toolbar {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n    text-align: center;\r\n  }\r\n\r\n  .toolbar-right {\r\n    justify-content: center;\r\n  }\r\n\r\n  .templates-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 16px;\r\n  }\r\n\r\n  .template-actions {\r\n    flex-direction: column;\r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAsJA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,mBAAA;MACAC,kBAAA;MAEA;MACAC,eAAA;MAEA;MACAC,UAAA;MAEA;MACAC,UAAA;QACAN,IAAA;QACAO,WAAA;QACAC,UAAA;QAAA;QACAC,GAAA;MACA;MAEA;MACAC,WAAA;QACAV,IAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,WAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,UAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,GAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAG,SAAA,GACA;QACAC,EAAA;QACAjB,IAAA;QACAO,WAAA;QACAW,SAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;QACAC,WAAA;MACA,GACA;QACAL,EAAA;QACAjB,IAAA;QACAO,WAAA;QACAW,SAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;QACAC,WAAA;MACA,GACA;QACAL,EAAA;QACAjB,IAAA;QACAO,WAAA;QACAW,SAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;QACAC,WAAA;MACA;IAEA;EACA;EAEAC,OAAA;IACA;IACAD,WAAA,WAAAA,YAAAE,UAAA;MACA,IAAAC,QAAA,QAAAT,SAAA,CAAAU,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAV,EAAA,KAAAO,UAAA;MAAA;MACA,IAAAC,QAAA;QACA,KAAAG,IAAA,CAAAH,QAAA;MACA;IACA;IAEA;IACAI,WAAA,WAAAA,YAAAL,UAAA;MACA,IAAAC,QAAA,QAAAT,SAAA,CAAAU,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAV,EAAA,KAAAO,UAAA;MAAA;MACA,IAAAC,QAAA;QACA,KAAAG,IAAA,CAAAH,QAAA;MACA;IACA;IAEA;IACAK,iBAAA,WAAAA,kBAAA;MACA,KAAA5B,mBAAA;IACA;IAEA;IACA6B,gBAAA,WAAAA,iBAAA;MACA,KAAAC,QAAA,CAAAC,OAAA;MACA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAAT,QAAA;MACA,KAAArB,eAAA,GAAAqB,QAAA;MACA;MACA,KAAAU,OAAA,CAAAC,IAAA;QACAC,IAAA;QACAC,KAAA;UAAAd,UAAA,EAAAC,QAAA,CAAAR;QAAA;MACA;IACA;IAEA;IACAsB,YAAA,WAAAA,aAAAd,QAAA;MACA,KAAArB,eAAA,GAAAqB,QAAA;MACA;MACA,KAAAU,OAAA,CAAAC,IAAA;QACAC,IAAA;QACAC,KAAA;UAAAd,UAAA,EAAAC,QAAA,CAAAR;QAAA;MACA;IACA;IAEA;IACAuB,aAAA,WAAAA,cAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAApC,UAAA,CAAAqC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,WAAA;YACA5B,EAAA,WAAA6B,IAAA,CAAAC,GAAA;YACA/C,IAAA,EAAAyC,KAAA,CAAAnC,UAAA,CAAAN,IAAA;YACAO,WAAA,EAAAkC,KAAA,CAAAnC,UAAA,CAAAC,WAAA;YACAW,SAAA;YACAC,QAAA;YACAC,UAAA,MAAA0B,IAAA,GAAAE,cAAA,GAAAC,KAAA;YACA5B,MAAA;YACAC,WAAA;YACAd,UAAA,EAAAiC,KAAA,CAAAnC,UAAA,CAAAE,UAAA;YACAC,GAAA,EAAAgC,KAAA,CAAAnC,UAAA,CAAAG;UACA;UAEAgC,KAAA,CAAAzB,SAAA,CAAAkC,OAAA,CAAAL,WAAA;UACAJ,KAAA,CAAAvC,mBAAA;UACAuC,KAAA,CAAAT,QAAA,CAAAC,OAAA;;UAEA;UACAQ,KAAA,CAAAP,YAAA,CAAAW,WAAA;QACA;MACA;IACA;IAEA;IACAM,eAAA,WAAAA,gBAAA;MACA,KAAA7C,UAAA;QACAN,IAAA;QACAO,WAAA;QACAC,UAAA;QAAA;QACAC,GAAA;MACA;MACA,SAAAiC,KAAA,CAAApC,UAAA;QACA,KAAAoC,KAAA,CAAApC,UAAA,CAAA8C,WAAA;MACA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAA;MACA,KAAArB,QAAA,CAAAC,OAAA,6BAAAqB,MAAA,MAAAjD,UAAA;MACA,KAAAF,kBAAA;MACA;;MAEA;MACA,KAAAoC,YAAA,MAAAnC,eAAA;IACA;IAEA;IACAmD,aAAA,WAAAA,cAAAlC,MAAA;MACA,IAAAmC,SAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAnC,MAAA,KAAAA,MAAA;IACA;EACA;AACA", "ignoreList": []}]}