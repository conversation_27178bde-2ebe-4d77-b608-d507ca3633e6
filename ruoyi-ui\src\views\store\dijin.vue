<template>
  <div class="video-template-manager">
    <!-- 头部工具栏 -->
    <div class="header-toolbar">
      <div class="toolbar-left">
        <h2 class="page-title">
          <i class="el-icon-video-camera"></i>
          视频剪辑模板管理
        </h2>
        <p class="page-subtitle">基于阿里云智能媒体服务的视频剪辑模板系统</p>
      </div>
      <div class="toolbar-right">
        <el-button type="primary" icon="el-icon-plus" @click="createNewTemplate">
          新建模板
        </el-button>
        <el-button icon="el-icon-refresh" @click="refreshTemplates">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 模板网格展示区域 -->
    <div class="templates-grid">
      <div
        v-for="template in templates"
        :key="template.id"
        class="template-card"
        @mouseenter="showActions(template.id)"
        @mouseleave="hideActions(template.id)"
      >
        <!-- 模板缩略图 -->
        <div class="template-thumbnail">
          <img :src="template.thumbnail" :alt="template.name" />
          <div class="template-duration">{{ template.duration }}</div>

          <!-- 悬停时显示的操作按钮 -->
          <div
            class="template-actions"
            :class="{ 'show': template.showActions }"
          >
            <el-button
              type="primary"
              size="small"
              icon="el-icon-edit"
              @click="editTemplate(template)"
            >
              编辑模板
            </el-button>
            <el-button
              type="success"
              size="small"
              icon="el-icon-view"
              @click="viewProgress(template)"
            >
              查看进度
            </el-button>
          </div>
        </div>

        <!-- 模板信息 -->
        <div class="template-info">
          <h4 class="template-name">{{ template.name }}</h4>
          <p class="template-desc">{{ template.description }}</p>
          <div class="template-meta">
            <span class="create-time">{{ template.createTime }}</span>
            <span class="template-status" :class="template.status">
              {{ getStatusText(template.status) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="templates.length === 0" class="empty-state">
        <i class="el-icon-video-camera-solid"></i>
        <h3>暂无模板</h3>
        <p>点击"新建模板"开始创建您的第一个视频剪辑模板</p>
        <el-button type="primary" @click="createNewTemplate">
          立即创建
        </el-button>
      </div>
    </div>

    <!-- 新建模板对话框 -->
    <el-dialog
      title="新建剪辑模板"
      :visible.sync="createDialogVisible"
      width="500px"
      @close="resetCreateForm"
    >
      <el-form :model="createForm" :rules="createRules" ref="createForm" label-width="100px">
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="createForm.name" placeholder="请输入模板名称"></el-input>
        </el-form-item>
        <el-form-item label="模板描述" prop="description">
          <el-input
            type="textarea"
            v-model="createForm.description"
            placeholder="请输入模板描述"
            :rows="3"
          ></el-input>
        </el-form-item>
        <el-form-item label="视频尺寸" prop="resolution">
          <el-select v-model="createForm.resolution" placeholder="选择视频尺寸">
            <el-option label="1080x1920 (抖音推荐)" value="1080x1920"></el-option>
            <el-option label="720x1280 (抖音标准)" value="720x1280"></el-option>
            <el-option label="1920x1080 (横屏)" value="1920x1080"></el-option>
            <el-option label="1280x720 (横屏)" value="1280x720"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="帧率" prop="fps">
          <el-select v-model="createForm.fps" placeholder="选择帧率">
            <el-option label="24 FPS" value="24"></el-option>
            <el-option label="30 FPS" value="30"></el-option>
            <el-option label="60 FPS" value="60"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmCreate">确定创建</el-button>
      </div>
    </el-dialog>

    <!-- 开始剪辑对话框 -->
    <el-dialog
      title="开始批量剪辑"
      :visible.sync="batchDialogVisible"
      width="400px"
    >
      <div class="batch-config">
        <h4>{{ currentTemplate.name }}</h4>
        <p>请选择要生成的视频数量：</p>
        <el-input-number
          v-model="batchCount"
          :min="1"
          :max="50"
          label="生成数量"
        ></el-input-number>
        <p class="batch-tip">最多可生成50条视频</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="startBatchClip">开始剪辑</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'VideoTemplateManager',
  data() {
    return {
      // 对话框状态
      createDialogVisible: false,
      batchDialogVisible: false,

      // 当前操作的模板
      currentTemplate: {},

      // 批量剪辑数量
      batchCount: 1,

      // 新建模板表单
      createForm: {
        name: '',
        description: '',
        resolution: '1080x1920', // 默认抖音推荐尺寸
        fps: '30'
      },

      // 表单验证规则
      createRules: {
        name: [
          { required: true, message: '请输入模板名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入模板描述', trigger: 'blur' }
        ],
        resolution: [
          { required: true, message: '请选择视频尺寸', trigger: 'change' }
        ],
        fps: [
          { required: true, message: '请选择帧率', trigger: 'change' }
        ]
      },

      // 模板列表
      templates: [
        {
          id: 'tpl-001',
          name: '产品展示模板',
          description: '专业的产品展示视频模板，适合电商产品宣传',
          thumbnail: 'https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=Product',
          duration: '00:30',
          createTime: '2025-01-18 10:30',
          status: 'active',
          showActions: false
        },
        {
          id: 'tpl-002',
          name: '企业宣传模板',
          description: '现代化企业宣传片模板，展现公司实力',
          thumbnail: 'https://via.placeholder.com/300x200/50C878/FFFFFF?text=Company',
          duration: '01:20',
          createTime: '2025-01-17 15:20',
          status: 'active',
          showActions: false
        },
        {
          id: 'tpl-003',
          name: '教育培训模板',
          description: '在线教育课程介绍模板，清晰易懂',
          thumbnail: 'https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=Education',
          duration: '02:15',
          createTime: '2025-01-16 09:45',
          status: 'draft',
          showActions: false
        }
      ]
    }
  },

  methods: {
    // 显示操作按钮
    showActions(templateId) {
      const template = this.templates.find(t => t.id === templateId)
      if (template) {
        this.$set(template, 'showActions', true)
      }
    },

    // 隐藏操作按钮
    hideActions(templateId) {
      const template = this.templates.find(t => t.id === templateId)
      if (template) {
        this.$set(template, 'showActions', false)
      }
    },

    // 创建新模板
    createNewTemplate() {
      this.createDialogVisible = true
    },

    // 刷新模板列表
    refreshTemplates() {
      this.$message.success('模板列表已刷新')
      // TODO: 调用API刷新数据
    },

    // 编辑模板
    editTemplate(template) {
      this.currentTemplate = template
      // 跳转到编辑器页面
      this.$router.push({
        path: '/storer/editor',
        query: { templateId: template.id }
      })
    },

    // 查看剪辑进度
    viewProgress(template) {
      this.currentTemplate = template
      // 跳转到进度页面
      this.$router.push({
        path: '/storer/progress',
        query: { templateId: template.id }
      })
    },

    // 确认创建模板
    confirmCreate() {
      this.$refs.createForm.validate((valid) => {
        if (valid) {
          const newTemplate = {
            id: 'tpl-' + Date.now(),
            name: this.createForm.name,
            description: this.createForm.description,
            thumbnail: 'https://via.placeholder.com/300x200/9B59B6/FFFFFF?text=Template',
            duration: '00:00',
            createTime: new Date().toLocaleString().slice(0, 16),
            status: 'draft',
            showActions: false,
            resolution: this.createForm.resolution,
            fps: this.createForm.fps
          }

          this.templates.unshift(newTemplate)
          this.createDialogVisible = false
          this.$message.success('模板创建成功！')

          // 自动跳转到编辑器
          this.editTemplate(newTemplate)
        }
      })
    },

    // 重置创建表单
    resetCreateForm() {
      this.createForm = {
        name: '',
        description: '',
        resolution: '1080x1920', // 默认抖音推荐尺寸
        fps: '30'
      }
      if (this.$refs.createForm) {
        this.$refs.createForm.resetFields()
      }
    },

    // 开始批量剪辑
    startBatchClip() {
      this.$message.success(`开始生成 ${this.batchCount} 条视频`)
      this.batchDialogVisible = false
      // TODO: 调用批量剪辑API

      // 跳转到进度页面查看剪辑状态
      this.viewProgress(this.currentTemplate)
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'active': '已发布',
        'draft': '草稿',
        'processing': '处理中'
      }
      return statusMap[status] || status
    }
  }
}
</script>

<style scoped>
.video-template-manager {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 头部工具栏 */
.header-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.toolbar-left .page-title {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.toolbar-left .page-title i {
  margin-right: 10px;
  color: #409EFF;
}

.toolbar-left .page-subtitle {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

/* 模板网格 */
.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

/* 模板卡片 */
.template-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
}

.template-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 模板缩略图 */
.template-thumbnail {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.template-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.template-card:hover .template-thumbnail img {
  transform: scale(1.05);
}

.template-duration {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

/* 操作按钮 */
.template-actions {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.template-actions.show {
  opacity: 1;
}

.template-actions::before {
  content: '';
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  z-index: -1;
}

/* 模板信息 */
.template-info {
  padding: 20px;
}

.template-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.template-desc {
  margin: 0 0 16px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.create-time {
  color: #909399;
  font-size: 12px;
}

.template-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.template-status.active {
  background: #f0f9ff;
  color: #409EFF;
}

.template-status.draft {
  background: #fdf6ec;
  color: #E6A23C;
}

.template-status.processing {
  background: #f0f2f5;
  color: #909399;
}

/* 空状态 */
.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.empty-state i {
  font-size: 64px;
  color: #C0C4CC;
  margin-bottom: 20px;
}

.empty-state h3 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.empty-state p {
  margin: 0 0 24px 0;
  color: #909399;
  font-size: 14px;
}

/* 对话框样式 */
.batch-config {
  text-align: center;
  padding: 20px 0;
}

.batch-config h4 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 18px;
}

.batch-config p {
  margin: 0 0 16px 0;
  color: #606266;
}

.batch-tip {
  margin-top: 12px !important;
  color: #909399;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-template-manager {
    padding: 15px;
  }

  .header-toolbar {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .toolbar-right {
    justify-content: center;
  }

  .templates-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .template-actions {
    flex-direction: column;
  }
}
</style>