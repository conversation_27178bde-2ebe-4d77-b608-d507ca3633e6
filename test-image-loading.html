<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .image-item {
            text-align: center;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 4px;
        }
        .image-item img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
        }
        .image-item p {
            margin: 10px 0 0 0;
            font-size: 12px;
            color: #666;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>图片加载测试</h1>
        <p>测试修复后的占位图片是否能正常加载</p>

        <div class="test-section">
            <h3>修复前的图片（包含中文字符）</h3>
            <div class="image-grid">
                <div class="image-item">
                    <img src="https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=产品展示" alt="产品展示" onerror="this.nextElementSibling.innerHTML='<span class=\'status error\'>加载失败</span>'">
                    <p><span class="status error">包含中文</span></p>
                </div>
                <div class="image-item">
                    <img src="https://via.placeholder.com/300x200/50C878/FFFFFF?text=企业宣传" alt="企业宣传" onerror="this.nextElementSibling.innerHTML='<span class=\'status error\'>加载失败</span>'">
                    <p><span class="status error">包含中文</span></p>
                </div>
                <div class="image-item">
                    <img src="https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=教育培训" alt="教育培训" onerror="this.nextElementSibling.innerHTML='<span class=\'status error\'>加载失败</span>'">
                    <p><span class="status error">包含中文</span></p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>修复后的图片（英文字符）</h3>
            <div class="image-grid">
                <div class="image-item">
                    <img src="https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=Product" alt="Product" onload="this.nextElementSibling.innerHTML='<span class=\'status success\'>加载成功</span>'">
                    <p><span class="status success">英文字符</span></p>
                </div>
                <div class="image-item">
                    <img src="https://via.placeholder.com/300x200/50C878/FFFFFF?text=Company" alt="Company" onload="this.nextElementSibling.innerHTML='<span class=\'status success\'>加载成功</span>'">
                    <p><span class="status success">英文字符</span></p>
                </div>
                <div class="image-item">
                    <img src="https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=Education" alt="Education" onload="this.nextElementSibling.innerHTML='<span class=\'status success\'>加载成功</span>'">
                    <p><span class="status success">英文字符</span></p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>编辑器中的小图片</h3>
            <div class="image-grid">
                <div class="image-item">
                    <img src="https://via.placeholder.com/160x90/4A90E2/FFFFFF?text=Opening1" alt="Opening1" onload="this.nextElementSibling.innerHTML='<span class=\'status success\'>加载成功</span>'">
                    <p><span class="status success">开场视频1</span></p>
                </div>
                <div class="image-item">
                    <img src="https://via.placeholder.com/160x90/50C878/FFFFFF?text=Product1" alt="Product1" onload="this.nextElementSibling.innerHTML='<span class=\'status success\'>加载成功</span>'">
                    <p><span class="status success">产品视频1</span></p>
                </div>
                <div class="image-item">
                    <img src="https://via.placeholder.com/160x90/FF6B6B/FFFFFF?text=Feature1" alt="Feature1" onload="this.nextElementSibling.innerHTML='<span class=\'status success\'>加载成功</span>'">
                    <p><span class="status success">功能视频1</span></p>
                </div>
                <div class="image-item">
                    <img src="https://via.placeholder.com/160x90/999/FFFFFF?text=Video" alt="Video" onload="this.nextElementSibling.innerHTML='<span class=\'status success\'>加载成功</span>'">
                    <p><span class="status success">默认视频</span></p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>测试结果</h3>
            <p>✅ 修复后的图片应该能正常加载</p>
            <p>❌ 包含中文字符的图片会加载失败</p>
            <p>🔧 解决方案：将URL中的中文字符替换为英文字符</p>
        </div>
    </div>

    <script>
        // 检查图片加载状态
        window.addEventListener('load', function() {
            const images = document.querySelectorAll('img');
            let successCount = 0;
            let errorCount = 0;
            
            images.forEach(img => {
                if (img.complete && img.naturalHeight !== 0) {
                    successCount++;
                } else {
                    errorCount++;
                }
            });
            
            console.log(`图片加载统计: 成功 ${successCount} 个, 失败 ${errorCount} 个`);
        });
    </script>
</body>
</html>
