<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间轴布局测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .timeline-demo {
            width: 600px;
            height: 30px;
            background: #fafafa;
            border: 1px solid #e4e7ed;
            position: relative;
            margin: 20px 0;
            cursor: pointer;
        }
        .timeline-marks {
            position: relative;
            width: 100%;
            height: 100%;
        }
        .time-mark {
            position: absolute;
            top: 0;
            height: 100%;
            font-size: 11px;
            color: #606266;
            display: flex;
            align-items: center;
            padding-left: 6px;
            border-left: 2px solid #c0c4cc;
            font-weight: 500;
        }
        .time-tick {
            position: absolute;
            top: 22px;
            width: 1px;
            height: 8px;
            background: #f0f0f0;
        }
        .playhead {
            position: absolute;
            top: 0;
            width: 2px;
            height: 100%;
            background: #ff4757;
            z-index: 10;
            transition: left 0.2s ease;
        }
        .playhead::before {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            width: 10px;
            height: 10px;
            background: #ff4757;
            border-radius: 50%;
        }
        .comparison {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            flex: 1;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .comparison-item h4 {
            margin-top: 0;
            color: #333;
        }
        .old-timeline {
            width: 480px;
            height: 30px;
            background: #fafafa;
            border: 1px solid #e4e7ed;
            position: relative;
            margin: 10px 0;
        }
        .old-timeline .time-mark {
            font-size: 12px;
            color: #909399;
            padding-left: 4px;
            border-left: 1px solid #dcdfe6;
            font-weight: normal;
        }
        .info-box {
            background: #e7f3ff;
            border: 1px solid #b3d8ff;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .info-box h4 {
            margin-top: 0;
            color: #0066cc;
        }
        .current-time {
            position: absolute;
            top: -25px;
            left: 0;
            background: #ff4757;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>时间轴布局优化</h1>
        <p>调整时间轴刻度，让显示更稀疏，刚好显示到第60秒</p>

        <div class="info-box">
            <h4>优化目标</h4>
            <ul>
                <li>✅ 时间轴总宽度：600像素（每秒10像素）</li>
                <li>✅ 主刻度间隔：每10秒一个（00:00, 00:10, 00:20...01:00）</li>
                <li>✅ 小刻度间隔：每5秒一个（辅助定位）</li>
                <li>✅ 刚好显示到第60秒</li>
                <li>✅ 更清晰的视觉层次</li>
            </ul>
        </div>

        <h3>新的时间轴布局</h3>
        <div class="timeline-demo" onclick="updatePlayhead(event)">
            <div class="timeline-marks">
                <!-- 主刻度：每10秒 -->
                <div class="time-mark" style="left: 0px;">00:00</div>
                <div class="time-mark" style="left: 100px;">00:10</div>
                <div class="time-mark" style="left: 200px;">00:20</div>
                <div class="time-mark" style="left: 300px;">00:30</div>
                <div class="time-mark" style="left: 400px;">00:40</div>
                <div class="time-mark" style="left: 500px;">00:50</div>
                <div class="time-mark" style="left: 600px;">01:00</div>
                
                <!-- 小刻度：每5秒 -->
                <div class="time-tick" style="left: 50px;"></div>
                <div class="time-tick" style="left: 150px;"></div>
                <div class="time-tick" style="left: 250px;"></div>
                <div class="time-tick" style="left: 350px;"></div>
                <div class="time-tick" style="left: 450px;"></div>
                <div class="time-tick" style="left: 550px;"></div>
                
                <!-- 播放指针 -->
                <div class="playhead" id="playhead" style="left: 150px;">
                    <div class="current-time" id="currentTime">00:15</div>
                </div>
            </div>
        </div>
        <p><small>点击时间轴任意位置移动播放指针</small></p>

        <div class="comparison">
            <div class="comparison-item">
                <h4>修改前（密集）</h4>
                <div class="old-timeline">
                    <div class="timeline-marks">
                        <div class="time-mark" style="left: 0px;">00:00</div>
                        <div class="time-mark" style="left: 40px;">00:05</div>
                        <div class="time-mark" style="left: 80px;">00:10</div>
                        <div class="time-mark" style="left: 120px;">00:15</div>
                        <div class="time-mark" style="left: 160px;">00:20</div>
                        <div class="time-mark" style="left: 200px;">00:25</div>
                        <div class="time-mark" style="left: 240px;">00:30</div>
                        <div class="time-mark" style="left: 280px;">00:35</div>
                        <div class="time-mark" style="left: 320px;">00:40</div>
                        <div class="time-mark" style="left: 360px;">00:45</div>
                        <div class="time-mark" style="left: 400px;">00:50</div>
                        <div class="time-mark" style="left: 440px;">00:55</div>
                        <div class="time-mark" style="left: 480px;">01:00</div>
                    </div>
                </div>
                <ul>
                    <li>总宽度：480像素</li>
                    <li>每5秒一个刻度</li>
                    <li>刻度过密，视觉混乱</li>
                </ul>
            </div>
            
            <div class="comparison-item">
                <h4>修改后（稀疏）</h4>
                <div class="timeline-demo" style="width: 600px; margin: 10px 0;">
                    <div class="timeline-marks">
                        <div class="time-mark" style="left: 0px;">00:00</div>
                        <div class="time-mark" style="left: 100px;">00:10</div>
                        <div class="time-mark" style="left: 200px;">00:20</div>
                        <div class="time-mark" style="left: 300px;">00:30</div>
                        <div class="time-mark" style="left: 400px;">00:40</div>
                        <div class="time-mark" style="left: 500px;">00:50</div>
                        <div class="time-mark" style="left: 600px;">01:00</div>
                        <div class="time-tick" style="left: 50px;"></div>
                        <div class="time-tick" style="left: 150px;"></div>
                        <div class="time-tick" style="left: 250px;"></div>
                        <div class="time-tick" style="left: 350px;"></div>
                        <div class="time-tick" style="left: 450px;"></div>
                        <div class="time-tick" style="left: 550px;"></div>
                    </div>
                </div>
                <ul>
                    <li>总宽度：600像素</li>
                    <li>每10秒一个主刻度</li>
                    <li>每5秒一个小刻度</li>
                    <li>视觉清晰，层次分明</li>
                </ul>
            </div>
        </div>

        <div class="info-box">
            <h4>技术参数</h4>
            <ul>
                <li><strong>像素比例</strong>：每秒10像素（从8像素增加到10像素）</li>
                <li><strong>总宽度</strong>：600像素（60秒 × 10像素/秒）</li>
                <li><strong>主刻度</strong>：7个（0, 10, 20, 30, 40, 50, 60秒）</li>
                <li><strong>小刻度</strong>：6个（5, 15, 25, 35, 45, 55秒）</li>
                <li><strong>刻度样式</strong>：主刻度更粗更清晰，小刻度更细更淡</li>
            </ul>
        </div>

        <h3>使用效果</h3>
        <ul>
            <li>✅ <strong>更清晰</strong>：主要时间点一目了然</li>
            <li>✅ <strong>更精确</strong>：小刻度提供5秒精度的辅助定位</li>
            <li>✅ <strong>更美观</strong>：视觉层次分明，不会显得拥挤</li>
            <li>✅ <strong>更实用</strong>：刚好显示完整的60秒时间范围</li>
        </ul>
    </div>

    <script>
        function updatePlayhead(event) {
            const timeline = event.currentTarget;
            const rect = timeline.getBoundingClientRect();
            const clickX = event.clientX - rect.left;
            const maxWidth = 600; // 时间轴总宽度
            const clickTime = Math.round((clickX / maxWidth) * 60); // 计算点击的秒数
            
            // 限制在0-60秒范围内
            const time = Math.max(0, Math.min(60, clickTime));
            const position = (time / 60) * maxWidth;
            
            // 更新播放指针位置
            const playhead = document.getElementById('playhead');
            const currentTimeDisplay = document.getElementById('currentTime');
            
            playhead.style.left = position + 'px';
            
            // 格式化时间显示
            const minutes = Math.floor(time / 60);
            const seconds = time % 60;
            const timeStr = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            currentTimeDisplay.textContent = timeStr;
            
            console.log(`点击时间轴: ${timeStr} (${time}秒)`);
        }
        
        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('✅ 时间轴布局优化完成');
            console.log('📏 新布局：600像素宽度，每10秒一个主刻度');
            console.log('🎯 点击时间轴测试交互功能');
        });
    </script>
</body>
</html>
