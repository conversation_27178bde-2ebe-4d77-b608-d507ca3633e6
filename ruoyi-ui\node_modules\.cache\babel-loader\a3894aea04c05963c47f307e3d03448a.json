{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\progress.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\progress.vue", "mtime": 1754974569676}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\babel.config.js", "mtime": 1744968028000}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753759483709}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "templateInfo", "id", "batchDialogVisible", "playDialogVisible", "batchCount", "currentVideo", "statusFilter", "stats", "completed", "processing", "failed", "total", "videos", "status", "thumbnail", "url", "duration", "fileSize", "createTime", "progress", "computed", "filteredVideos", "_this", "filter", "video", "created", "templateId", "$route", "query", "loadTemplate", "startProgressPolling", "<PERSON><PERSON><PERSON><PERSON>", "pollingTimer", "clearInterval", "methods", "goBack", "$router", "go", "console", "log", "refreshProgress", "$message", "success", "startNewBatch", "confirmNewBatch", "concat", "i", "newVideo", "Date", "now", "length", "toLocaleString", "slice", "unshift", "updateStats", "playVideo", "stopVideo", "$refs", "videoPlayer", "pause", "onVideoLoaded", "downloadVideo", "shareVideo", "deleteVideo", "_this2", "$confirm", "type", "then", "index", "findIndex", "v", "splice", "catch", "retryVideo", "getStatusText", "statusMap", "_this3", "setInterval", "for<PERSON>ach", "Math", "random", "floor", "toFixed"], "sources": ["src/views/store/progress.vue"], "sourcesContent": ["<template>\n  <div class=\"video-progress\">\n    <!-- 头部工具栏 -->\n    <div class=\"header-toolbar\">\n      <div class=\"toolbar-left\">\n        <el-button icon=\"el-icon-arrow-left\" @click=\"goBack\">返回</el-button>\n        <h2 class=\"page-title\">\n          <i class=\"el-icon-video-camera\"></i>\n          剪辑进度 - {{ templateInfo.name }}\n        </h2>\n      </div>\n      <div class=\"toolbar-right\">\n        <el-button icon=\"el-icon-refresh\" @click=\"refreshProgress\">\n          刷新\n        </el-button>\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"startNewBatch\">\n          新建批次\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 统计信息 -->\n    <div class=\"stats-cards\">\n      <div class=\"stat-card\">\n        <div class=\"stat-icon completed\">\n          <i class=\"el-icon-check\"></i>\n        </div>\n        <div class=\"stat-info\">\n          <div class=\"stat-number\">{{ stats.completed }}</div>\n          <div class=\"stat-label\">已完成</div>\n        </div>\n      </div>\n      <div class=\"stat-card\">\n        <div class=\"stat-icon processing\">\n          <i class=\"el-icon-loading\"></i>\n        </div>\n        <div class=\"stat-info\">\n          <div class=\"stat-number\">{{ stats.processing }}</div>\n          <div class=\"stat-label\">处理中</div>\n        </div>\n      </div>\n      <div class=\"stat-card\">\n        <div class=\"stat-icon failed\">\n          <i class=\"el-icon-close\"></i>\n        </div>\n        <div class=\"stat-info\">\n          <div class=\"stat-number\">{{ stats.failed }}</div>\n          <div class=\"stat-label\">失败</div>\n        </div>\n      </div>\n      <div class=\"stat-card\">\n        <div class=\"stat-icon total\">\n          <i class=\"el-icon-document\"></i>\n        </div>\n        <div class=\"stat-info\">\n          <div class=\"stat-number\">{{ stats.total }}</div>\n          <div class=\"stat-label\">总计</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 视频列表 -->\n    <div class=\"videos-container\">\n      <div class=\"container-header\">\n        <h3>生成的视频</h3>\n        <div class=\"filter-controls\">\n          <el-select v-model=\"statusFilter\" placeholder=\"状态筛选\" size=\"small\" clearable>\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"已完成\" value=\"completed\"></el-option>\n            <el-option label=\"处理中\" value=\"processing\"></el-option>\n            <el-option label=\"失败\" value=\"failed\"></el-option>\n          </el-select>\n        </div>\n      </div>\n\n      <div class=\"videos-grid\">\n        <div \n          v-for=\"video in filteredVideos\" \n          :key=\"video.id\"\n          class=\"video-card\"\n          :class=\"video.status\"\n        >\n          <!-- 视频缩略图 -->\n          <div class=\"video-thumbnail\">\n            <img v-if=\"video.thumbnail\" :src=\"video.thumbnail\" :alt=\"video.name\" />\n            <div v-else class=\"thumbnail-placeholder\">\n              <i class=\"el-icon-video-camera\"></i>\n            </div>\n            \n            <!-- 状态覆盖层 -->\n            <div class=\"status-overlay\" v-if=\"video.status !== 'completed'\">\n              <div class=\"status-content\">\n                <i v-if=\"video.status === 'processing'\" class=\"el-icon-loading rotating\"></i>\n                <i v-else-if=\"video.status === 'failed'\" class=\"el-icon-warning-outline\"></i>\n                <p class=\"status-text\">{{ getStatusText(video.status) }}</p>\n                <div v-if=\"video.status === 'processing'\" class=\"progress-bar\">\n                  <div class=\"progress-fill\" :style=\"{ width: video.progress + '%' }\"></div>\n                </div>\n              </div>\n            </div>\n\n            <!-- 播放按钮 -->\n            <div v-if=\"video.status === 'completed'\" class=\"play-button\" @click=\"playVideo(video)\">\n              <i class=\"el-icon-video-play\"></i>\n            </div>\n\n            <!-- 视频时长 -->\n            <div v-if=\"video.duration\" class=\"video-duration\">{{ video.duration }}</div>\n          </div>\n\n          <!-- 视频信息 -->\n          <div class=\"video-info\">\n            <h4 class=\"video-name\">{{ video.name }}</h4>\n            <div class=\"video-meta\">\n              <span class=\"create-time\">{{ video.createTime }}</span>\n              <span class=\"video-size\" v-if=\"video.fileSize\">{{ video.fileSize }}</span>\n            </div>\n            \n            <!-- 操作按钮 -->\n            <div class=\"video-actions\" v-if=\"video.status === 'completed'\">\n              <el-button size=\"mini\" icon=\"el-icon-download\" @click=\"downloadVideo(video)\">\n                下载\n              </el-button>\n              <el-button size=\"mini\" icon=\"el-icon-share\" @click=\"shareVideo(video)\">\n                分享\n              </el-button>\n              <el-button size=\"mini\" icon=\"el-icon-delete\" type=\"danger\" @click=\"deleteVideo(video)\">\n                删除\n              </el-button>\n            </div>\n            \n            <!-- 重试按钮 -->\n            <div class=\"video-actions\" v-if=\"video.status === 'failed'\">\n              <el-button size=\"mini\" icon=\"el-icon-refresh\" type=\"primary\" @click=\"retryVideo(video)\">\n                重试\n              </el-button>\n              <el-button size=\"mini\" icon=\"el-icon-delete\" type=\"danger\" @click=\"deleteVideo(video)\">\n                删除\n              </el-button>\n            </div>\n          </div>\n        </div>\n\n        <!-- 空状态 -->\n        <div v-if=\"filteredVideos.length === 0\" class=\"empty-state\">\n          <i class=\"el-icon-video-camera-solid\"></i>\n          <h3>暂无视频</h3>\n          <p>还没有生成任何视频，点击\"新建批次\"开始剪辑</p>\n          <el-button type=\"primary\" @click=\"startNewBatch\">\n            新建批次\n          </el-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 新建批次对话框 -->\n    <el-dialog\n      title=\"新建剪辑批次\"\n      :visible.sync=\"batchDialogVisible\"\n      width=\"400px\"\n    >\n      <div class=\"batch-config\">\n        <h4>{{ templateInfo.name }}</h4>\n        <p>请选择要生成的视频数量：</p>\n        <el-input-number\n          v-model=\"batchCount\"\n          :min=\"1\"\n          :max=\"50\"\n          label=\"生成数量\"\n        ></el-input-number>\n        <p class=\"batch-tip\">最多可生成50条视频</p>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"batchDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmNewBatch\">开始剪辑</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 视频播放对话框 -->\n    <el-dialog\n      title=\"视频预览\"\n      :visible.sync=\"playDialogVisible\"\n      width=\"800px\"\n      @close=\"stopVideo\"\n    >\n      <div class=\"video-player\" v-if=\"currentVideo\">\n        <video \n          ref=\"videoPlayer\"\n          :src=\"currentVideo.url\" \n          controls \n          width=\"100%\"\n          @loadedmetadata=\"onVideoLoaded\"\n        ></video>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'VideoProgress',\n  data() {\n    return {\n      // 模板信息\n      templateInfo: {\n        id: '',\n        name: '视频模板'\n      },\n      \n      // 对话框状态\n      batchDialogVisible: false,\n      playDialogVisible: false,\n      \n      // 批量剪辑数量\n      batchCount: 1,\n      \n      // 当前播放视频\n      currentVideo: null,\n      \n      // 状态筛选\n      statusFilter: '',\n      \n      // 统计数据\n      stats: {\n        completed: 8,\n        processing: 2,\n        failed: 1,\n        total: 11\n      },\n      \n      // 视频列表\n      videos: [\n        {\n          id: 'v001',\n          name: '产品展示视频_001',\n          status: 'completed',\n          thumbnail: 'https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=Video+1',\n          url: 'https://example.com/video1.mp4',\n          duration: '00:32',\n          fileSize: '15.2MB',\n          createTime: '2025-01-18 14:30',\n          progress: 100\n        },\n        {\n          id: 'v002',\n          name: '产品展示视频_002',\n          status: 'completed',\n          thumbnail: 'https://via.placeholder.com/300x200/50C878/FFFFFF?text=Video+2',\n          url: 'https://example.com/video2.mp4',\n          duration: '00:28',\n          fileSize: '12.8MB',\n          createTime: '2025-01-18 14:32',\n          progress: 100\n        },\n        {\n          id: 'v003',\n          name: '产品展示视频_003',\n          status: 'processing',\n          thumbnail: null,\n          url: null,\n          duration: null,\n          fileSize: null,\n          createTime: '2025-01-18 14:35',\n          progress: 65\n        },\n        {\n          id: 'v004',\n          name: '产品展示视频_004',\n          status: 'failed',\n          thumbnail: null,\n          url: null,\n          duration: null,\n          fileSize: null,\n          createTime: '2025-01-18 14:36',\n          progress: 0\n        }\n      ]\n    }\n  },\n  \n  computed: {\n    // 筛选后的视频列表\n    filteredVideos() {\n      if (!this.statusFilter) {\n        return this.videos\n      }\n      return this.videos.filter(video => video.status === this.statusFilter)\n    }\n  },\n  \n  created() {\n    // 获取模板ID\n    const templateId = this.$route.query.templateId\n    if (templateId) {\n      this.loadTemplate(templateId)\n    }\n    \n    // 定时刷新处理中的视频状态\n    this.startProgressPolling()\n  },\n  \n  beforeDestroy() {\n    // 清理定时器\n    if (this.pollingTimer) {\n      clearInterval(this.pollingTimer)\n    }\n  },\n  \n  methods: {\n    // 返回上一页\n    goBack() {\n      this.$router.go(-1)\n    },\n    \n    // 加载模板信息\n    loadTemplate(templateId) {\n      this.templateInfo.id = templateId\n      // TODO: 从API加载模板数据\n      console.log('加载模板:', templateId)\n    },\n    \n    // 刷新进度\n    refreshProgress() {\n      this.$message.success('进度已刷新')\n      // TODO: 调用API刷新数据\n    },\n    \n    // 开始新批次\n    startNewBatch() {\n      this.batchDialogVisible = true\n    },\n    \n    // 确认新建批次\n    confirmNewBatch() {\n      this.$message.success(`开始生成 ${this.batchCount} 条新视频`)\n      this.batchDialogVisible = false\n      \n      // TODO: 调用批量剪辑API\n      // 模拟添加新的处理中视频\n      for (let i = 0; i < this.batchCount; i++) {\n        const newVideo = {\n          id: 'v' + Date.now() + i,\n          name: `产品展示视频_${this.videos.length + i + 1}`,\n          status: 'processing',\n          thumbnail: null,\n          url: null,\n          duration: null,\n          fileSize: null,\n          createTime: new Date().toLocaleString().slice(0, 16),\n          progress: 0\n        }\n        this.videos.unshift(newVideo)\n      }\n      \n      // 更新统计\n      this.updateStats()\n    },\n    \n    // 播放视频\n    playVideo(video) {\n      this.currentVideo = video\n      this.playDialogVisible = true\n    },\n    \n    // 停止视频\n    stopVideo() {\n      if (this.$refs.videoPlayer) {\n        this.$refs.videoPlayer.pause()\n      }\n      this.currentVideo = null\n    },\n    \n    // 视频加载完成\n    onVideoLoaded() {\n      // 视频元数据加载完成\n    },\n    \n    // 下载视频\n    downloadVideo(video) {\n      this.$message.success(`开始下载: ${video.name}`)\n      // TODO: 实现下载逻辑\n    },\n    \n    // 分享视频\n    shareVideo(video) {\n      this.$message.success(`分享链接已复制: ${video.name}`)\n      // TODO: 实现分享逻辑\n    },\n    \n    // 删除视频\n    deleteVideo(video) {\n      this.$confirm(`确定要删除视频 \"${video.name}\" 吗？`, '确认删除', {\n        type: 'warning'\n      }).then(() => {\n        const index = this.videos.findIndex(v => v.id === video.id)\n        if (index > -1) {\n          this.videos.splice(index, 1)\n          this.updateStats()\n          this.$message.success('删除成功')\n        }\n      }).catch(() => {})\n    },\n    \n    // 重试视频\n    retryVideo(video) {\n      video.status = 'processing'\n      video.progress = 0\n      this.updateStats()\n      this.$message.success(`重新开始处理: ${video.name}`)\n      // TODO: 调用重试API\n    },\n    \n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        'completed': '已完成',\n        'processing': '处理中',\n        'failed': '处理失败'\n      }\n      return statusMap[status] || status\n    },\n    \n    // 更新统计数据\n    updateStats() {\n      this.stats = {\n        completed: this.videos.filter(v => v.status === 'completed').length,\n        processing: this.videos.filter(v => v.status === 'processing').length,\n        failed: this.videos.filter(v => v.status === 'failed').length,\n        total: this.videos.length\n      }\n    },\n    \n    // 开始轮询进度\n    startProgressPolling() {\n      this.pollingTimer = setInterval(() => {\n        // 模拟进度更新\n        this.videos.forEach(video => {\n          if (video.status === 'processing' && video.progress < 100) {\n            video.progress += Math.random() * 10\n            if (video.progress >= 100) {\n              video.status = 'completed'\n              video.progress = 100\n              video.thumbnail = `https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=Video+${video.id}`\n              video.url = `https://example.com/${video.id}.mp4`\n              video.duration = '00:' + (25 + Math.floor(Math.random() * 20))\n              video.fileSize = (10 + Math.random() * 10).toFixed(1) + 'MB'\n              this.updateStats()\n            }\n          }\n        })\n      }, 3000)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.video-progress {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n}\n\n/* 头部工具栏 */\n.header-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  padding: 20px;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.toolbar-left {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.page-title {\n  margin: 0;\n  color: #303133;\n  font-size: 20px;\n  font-weight: 600;\n}\n\n.page-title i {\n  margin-right: 8px;\n  color: #409EFF;\n}\n\n.toolbar-right {\n  display: flex;\n  gap: 12px;\n}\n\n/* 统计卡片 */\n.stats-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  padding: 24px;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.stat-icon {\n  width: 48px;\n  height: 48px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20px;\n  color: white;\n}\n\n.stat-icon.completed {\n  background: #67C23A;\n}\n\n.stat-icon.processing {\n  background: #409EFF;\n}\n\n.stat-icon.failed {\n  background: #F56C6C;\n}\n\n.stat-icon.total {\n  background: #909399;\n}\n\n.stat-info {\n  flex: 1;\n}\n\n.stat-number {\n  font-size: 28px;\n  font-weight: 700;\n  color: #303133;\n  line-height: 1;\n  margin-bottom: 4px;\n}\n\n.stat-label {\n  color: #909399;\n  font-size: 14px;\n}\n\n/* 视频容器 */\n.videos-container {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n}\n\n.container-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.container-header h3 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #303133;\n}\n\n/* 视频网格 */\n.videos-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: 20px;\n  padding: 24px;\n}\n\n/* 视频卡片 */\n.video-card {\n  background: #f8f9fa;\n  border-radius: 8px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n.video-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n\n/* 视频缩略图 */\n.video-thumbnail {\n  position: relative;\n  width: 100%;\n  height: 160px;\n  background: #000;\n  overflow: hidden;\n}\n\n.video-thumbnail img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.thumbnail-placeholder {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #ddd;\n  color: #999;\n}\n\n.thumbnail-placeholder i {\n  font-size: 32px;\n}\n\n/* 状态覆盖层 */\n.status-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n}\n\n.status-content {\n  text-align: center;\n}\n\n.status-content i {\n  font-size: 32px;\n  margin-bottom: 12px;\n  display: block;\n}\n\n.status-text {\n  margin: 0 0 12px 0;\n  font-size: 14px;\n}\n\n.progress-bar {\n  width: 120px;\n  height: 4px;\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: 2px;\n  overflow: hidden;\n  margin: 0 auto;\n}\n\n.progress-fill {\n  height: 100%;\n  background: #409EFF;\n  transition: width 0.3s ease;\n}\n\n/* 播放按钮 */\n.play-button {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 48px;\n  height: 48px;\n  background: rgba(0, 0, 0, 0.7);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 20px;\n  cursor: pointer;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.video-card:hover .play-button {\n  opacity: 1;\n}\n\n.play-button:hover {\n  background: rgba(0, 0, 0, 0.9);\n}\n\n/* 视频时长 */\n.video-duration {\n  position: absolute;\n  bottom: 8px;\n  right: 8px;\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-size: 12px;\n}\n\n/* 视频信息 */\n.video-info {\n  padding: 16px;\n}\n\n.video-name {\n  margin: 0 0 8px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #303133;\n  line-height: 1.4;\n}\n\n.video-meta {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 12px;\n  font-size: 12px;\n  color: #909399;\n}\n\n.video-actions {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n/* 旋转动画 */\n.rotating {\n  animation: rotate 2s linear infinite;\n}\n\n@keyframes rotate {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 空状态 */\n.empty-state {\n  grid-column: 1 / -1;\n  text-align: center;\n  padding: 60px 20px;\n}\n\n.empty-state i {\n  font-size: 48px;\n  color: #C0C4CC;\n  margin-bottom: 16px;\n}\n\n.empty-state h3 {\n  margin: 0 0 8px 0;\n  color: #303133;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.empty-state p {\n  margin: 0 0 20px 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n/* 批量配置 */\n.batch-config {\n  text-align: center;\n  padding: 20px 0;\n}\n\n.batch-config h4 {\n  margin: 0 0 20px 0;\n  color: #303133;\n  font-size: 18px;\n}\n\n.batch-config p {\n  margin: 0 0 16px 0;\n  color: #606266;\n}\n\n.batch-tip {\n  margin-top: 12px !important;\n  color: #909399;\n  font-size: 12px;\n}\n\n/* 视频播放器 */\n.video-player {\n  text-align: center;\n}\n\n.video-player video {\n  max-width: 100%;\n  border-radius: 8px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .video-progress {\n    padding: 15px;\n  }\n\n  .header-toolbar {\n    flex-direction: column;\n    gap: 16px;\n    text-align: center;\n  }\n\n  .toolbar-right {\n    justify-content: center;\n  }\n\n  .stats-cards {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 16px;\n  }\n\n  .videos-grid {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAuMA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,YAAA;QACAC,EAAA;QACAH,IAAA;MACA;MAEA;MACAI,kBAAA;MACAC,iBAAA;MAEA;MACAC,UAAA;MAEA;MACAC,YAAA;MAEA;MACAC,YAAA;MAEA;MACAC,KAAA;QACAC,SAAA;QACAC,UAAA;QACAC,MAAA;QACAC,KAAA;MACA;MAEA;MACAC,MAAA,GACA;QACAX,EAAA;QACAH,IAAA;QACAe,MAAA;QACAC,SAAA;QACAC,GAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;MACA,GACA;QACAlB,EAAA;QACAH,IAAA;QACAe,MAAA;QACAC,SAAA;QACAC,GAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;MACA,GACA;QACAlB,EAAA;QACAH,IAAA;QACAe,MAAA;QACAC,SAAA;QACAC,GAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;MACA,GACA;QACAlB,EAAA;QACAH,IAAA;QACAe,MAAA;QACAC,SAAA;QACAC,GAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;MACA;IAEA;EACA;EAEAC,QAAA;IACA;IACAC,cAAA,WAAAA,eAAA;MAAA,IAAAC,KAAA;MACA,UAAAhB,YAAA;QACA,YAAAM,MAAA;MACA;MACA,YAAAA,MAAA,CAAAW,MAAA,WAAAC,KAAA;QAAA,OAAAA,KAAA,CAAAX,MAAA,KAAAS,KAAA,CAAAhB,YAAA;MAAA;IACA;EACA;EAEAmB,OAAA,WAAAA,QAAA;IACA;IACA,IAAAC,UAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,UAAA;IACA,IAAAA,UAAA;MACA,KAAAG,YAAA,CAAAH,UAAA;IACA;;IAEA;IACA,KAAAI,oBAAA;EACA;EAEAC,aAAA,WAAAA,cAAA;IACA;IACA,SAAAC,YAAA;MACAC,aAAA,MAAAD,YAAA;IACA;EACA;EAEAE,OAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IAEA;IACAR,YAAA,WAAAA,aAAAH,UAAA;MACA,KAAA1B,YAAA,CAAAC,EAAA,GAAAyB,UAAA;MACA;MACAY,OAAA,CAAAC,GAAA,UAAAb,UAAA;IACA;IAEA;IACAc,eAAA,WAAAA,gBAAA;MACA,KAAAC,QAAA,CAAAC,OAAA;MACA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAA;MACA,KAAAzC,kBAAA;IACA;IAEA;IACA0C,eAAA,WAAAA,gBAAA;MACA,KAAAH,QAAA,CAAAC,OAAA,6BAAAG,MAAA,MAAAzC,UAAA;MACA,KAAAF,kBAAA;;MAEA;MACA;MACA,SAAA4C,CAAA,MAAAA,CAAA,QAAA1C,UAAA,EAAA0C,CAAA;QACA,IAAAC,QAAA;UACA9C,EAAA,QAAA+C,IAAA,CAAAC,GAAA,KAAAH,CAAA;UACAhD,IAAA,0CAAA+C,MAAA,MAAAjC,MAAA,CAAAsC,MAAA,GAAAJ,CAAA;UACAjC,MAAA;UACAC,SAAA;UACAC,GAAA;UACAC,QAAA;UACAC,QAAA;UACAC,UAAA,MAAA8B,IAAA,GAAAG,cAAA,GAAAC,KAAA;UACAjC,QAAA;QACA;QACA,KAAAP,MAAA,CAAAyC,OAAA,CAAAN,QAAA;MACA;;MAEA;MACA,KAAAO,WAAA;IACA;IAEA;IACAC,SAAA,WAAAA,UAAA/B,KAAA;MACA,KAAAnB,YAAA,GAAAmB,KAAA;MACA,KAAArB,iBAAA;IACA;IAEA;IACAqD,SAAA,WAAAA,UAAA;MACA,SAAAC,KAAA,CAAAC,WAAA;QACA,KAAAD,KAAA,CAAAC,WAAA,CAAAC,KAAA;MACA;MACA,KAAAtD,YAAA;IACA;IAEA;IACAuD,aAAA,WAAAA,cAAA;MACA;IAAA,CACA;IAEA;IACAC,aAAA,WAAAA,cAAArC,KAAA;MACA,KAAAiB,QAAA,CAAAC,OAAA,8BAAAG,MAAA,CAAArB,KAAA,CAAA1B,IAAA;MACA;IACA;IAEA;IACAgE,UAAA,WAAAA,WAAAtC,KAAA;MACA,KAAAiB,QAAA,CAAAC,OAAA,gDAAAG,MAAA,CAAArB,KAAA,CAAA1B,IAAA;MACA;IACA;IAEA;IACAiE,WAAA,WAAAA,YAAAvC,KAAA;MAAA,IAAAwC,MAAA;MACA,KAAAC,QAAA,iDAAApB,MAAA,CAAArB,KAAA,CAAA1B,IAAA;QACAoE,IAAA;MACA,GAAAC,IAAA;QACA,IAAAC,KAAA,GAAAJ,MAAA,CAAApD,MAAA,CAAAyD,SAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAArE,EAAA,KAAAuB,KAAA,CAAAvB,EAAA;QAAA;QACA,IAAAmE,KAAA;UACAJ,MAAA,CAAApD,MAAA,CAAA2D,MAAA,CAAAH,KAAA;UACAJ,MAAA,CAAAV,WAAA;UACAU,MAAA,CAAAvB,QAAA,CAAAC,OAAA;QACA;MACA,GAAA8B,KAAA;IACA;IAEA;IACAC,UAAA,WAAAA,WAAAjD,KAAA;MACAA,KAAA,CAAAX,MAAA;MACAW,KAAA,CAAAL,QAAA;MACA,KAAAmC,WAAA;MACA,KAAAb,QAAA,CAAAC,OAAA,0CAAAG,MAAA,CAAArB,KAAA,CAAA1B,IAAA;MACA;IACA;IAEA;IACA4E,aAAA,WAAAA,cAAA7D,MAAA;MACA,IAAA8D,SAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAA9D,MAAA,KAAAA,MAAA;IACA;IAEA;IACAyC,WAAA,WAAAA,YAAA;MACA,KAAA/C,KAAA;QACAC,SAAA,OAAAI,MAAA,CAAAW,MAAA,WAAA+C,CAAA;UAAA,OAAAA,CAAA,CAAAzD,MAAA;QAAA,GAAAqC,MAAA;QACAzC,UAAA,OAAAG,MAAA,CAAAW,MAAA,WAAA+C,CAAA;UAAA,OAAAA,CAAA,CAAAzD,MAAA;QAAA,GAAAqC,MAAA;QACAxC,MAAA,OAAAE,MAAA,CAAAW,MAAA,WAAA+C,CAAA;UAAA,OAAAA,CAAA,CAAAzD,MAAA;QAAA,GAAAqC,MAAA;QACAvC,KAAA,OAAAC,MAAA,CAAAsC;MACA;IACA;IAEA;IACApB,oBAAA,WAAAA,qBAAA;MAAA,IAAA8C,MAAA;MACA,KAAA5C,YAAA,GAAA6C,WAAA;QACA;QACAD,MAAA,CAAAhE,MAAA,CAAAkE,OAAA,WAAAtD,KAAA;UACA,IAAAA,KAAA,CAAAX,MAAA,qBAAAW,KAAA,CAAAL,QAAA;YACAK,KAAA,CAAAL,QAAA,IAAA4D,IAAA,CAAAC,MAAA;YACA,IAAAxD,KAAA,CAAAL,QAAA;cACAK,KAAA,CAAAX,MAAA;cACAW,KAAA,CAAAL,QAAA;cACAK,KAAA,CAAAV,SAAA,mEAAA+B,MAAA,CAAArB,KAAA,CAAAvB,EAAA;cACAuB,KAAA,CAAAT,GAAA,0BAAA8B,MAAA,CAAArB,KAAA,CAAAvB,EAAA;cACAuB,KAAA,CAAAR,QAAA,iBAAA+D,IAAA,CAAAE,KAAA,CAAAF,IAAA,CAAAC,MAAA;cACAxD,KAAA,CAAAP,QAAA,SAAA8D,IAAA,CAAAC,MAAA,SAAAE,OAAA;cACAN,MAAA,CAAAtB,WAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}