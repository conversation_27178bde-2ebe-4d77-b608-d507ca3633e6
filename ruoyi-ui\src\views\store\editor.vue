<template>
  <div class="video-editor">
    <!-- 头部工具栏 -->
    <div class="editor-header">
      <div class="header-left">
        <el-button icon="el-icon-arrow-left" @click="goBack">返回</el-button>
        <h3 class="template-title">{{ templateInfo.name }}</h3>
      </div>
      <div class="header-right">
        <el-button @click="saveTemplate">保存</el-button>
        <el-button type="primary" @click="showBatchDialog">开始剪辑</el-button>
      </div>
    </div>

    <!-- 主编辑区域 -->
    <div class="editor-main">
      <!-- 预览面板 -->
      <div class="preview-panel">
        <div class="preview-container">
          <div class="video-preview">
            <div class="preview-frame">
              <!-- 显示选中场景的视频 -->
              <div v-if="selectedScene && selectedScene.currentVideo" class="scene-preview">
                <img :src="selectedScene.currentVideo.thumbnail" :alt="selectedScene.name" />
                <div class="scene-overlay">
                  <div class="scene-title">{{ selectedScene.name }}</div>
                  <div class="scene-video-name">{{ selectedScene.currentVideo.name }}</div>
                  <div class="scene-time-info">
                    <span>当前时间: {{ formatTime(currentTime) }}</span>
                    <span>场景时间: {{ formatTime(currentTime - selectedScene.startTime) }}</span>
                  </div>
                  <div class="scene-text-preview">{{ selectedScene.textSegment }}</div>
                </div>
              </div>

              <!-- 默认预览占位符 -->
              <div v-else class="preview-placeholder">
                <i class="el-icon-video-play"></i>
                <p>视频预览</p>
                <p class="preview-info">{{ getPreviewResolution() }} • {{ templateInfo.fps }}fps</p>
                <div class="current-time-display">
                  <p>当前时间: {{ formatTime(currentTime) }}</p>
                </div>
                <div class="preview-tips">
                  <p>9:16 竖屏比例</p>
                  <p>适配短视频平台</p>
                  <p>点击时间轴查看预览</p>
                </div>
              </div>
            </div>
          </div>
          <div class="preview-controls">
            <el-button-group>
              <el-button icon="el-icon-video-play" size="small">播放</el-button>
              <el-button icon="el-icon-video-pause" size="small">暂停</el-button>
              <el-button icon="el-icon-refresh-left" size="small">重置</el-button>
            </el-button-group>
            <div class="time-display">00:00 / 02:30</div>
          </div>
        </div>
      </div>

      <!-- 属性设置面板 -->
      <div class="properties-panel">
        <div class="panel-header">
          <h4>属性设置</h4>
        </div>
        <div class="panel-content">
          <el-collapse v-model="activeCollapse">
            <el-collapse-item title="视频设置" name="video">
              <el-form label-width="80px" size="small">
                <el-form-item label="背景色">
                  <el-color-picker v-model="videoSettings.backgroundColor"></el-color-picker>
                </el-form-item>
                <el-form-item label="亮度">
                  <el-slider v-model="videoSettings.brightness" :min="-100" :max="100"></el-slider>
                </el-form-item>
                <el-form-item label="对比度">
                  <el-slider v-model="videoSettings.contrast" :min="-100" :max="100"></el-slider>
                </el-form-item>
                <el-form-item label="饱和度">
                  <el-slider v-model="videoSettings.saturation" :min="-100" :max="100"></el-slider>
                </el-form-item>
              </el-form>
            </el-collapse-item>
            
            <el-collapse-item title="音频设置" name="audio">
              <el-form label-width="80px" size="small">
                <el-form-item label="主音量">
                  <el-slider v-model="audioSettings.masterVolume" :max="100"></el-slider>
                </el-form-item>
                <el-form-item label="背景音乐">
                  <el-slider v-model="audioSettings.bgmVolume" :max="100"></el-slider>
                </el-form-item>
                <el-form-item label="音效">
                  <el-slider v-model="audioSettings.sfxVolume" :max="100"></el-slider>
                </el-form-item>
              </el-form>
            </el-collapse-item>
            
            <el-collapse-item title="文字设置" name="text">
              <el-form label-width="80px" size="small">
                <el-form-item label="字体">
                  <el-select v-model="textSettings.fontFamily" size="small">
                    <el-option label="微软雅黑" value="Microsoft YaHei"></el-option>
                    <el-option label="宋体" value="SimSun"></el-option>
                    <el-option label="黑体" value="SimHei"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="字号">
                  <el-input-number v-model="textSettings.fontSize" :min="12" :max="72" size="small"></el-input-number>
                </el-form-item>
                <el-form-item label="颜色">
                  <el-color-picker v-model="textSettings.color"></el-color-picker>
                </el-form-item>
              </el-form>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>

    <!-- 时间轴区域 -->
    <div class="timeline-area">
      <div class="timeline-header">
        <h4>时间轴</h4>
        <div class="timeline-controls">
          <el-button-group size="small">
            <el-button icon="el-icon-zoom-in">放大</el-button>
            <el-button icon="el-icon-zoom-out">缩小</el-button>
          </el-button-group>
        </div>
      </div>
      
      <div class="timeline-container">
        <!-- 时间刻度 -->
        <div class="time-ruler-row">
          <div class="time-ruler-label"></div>
          <div class="time-ruler" @click="onTimelineClick" @mousedown="onTimelineDragStart">
            <div class="time-marks-container" :style="{ width: timelineWidth + 'px' }">
              <!-- 每15秒一个主刻度 -->
              <div class="time-mark" v-for="i in 5" :key="i" :style="{left: (i-1) * 150 + 'px'}">
                {{ formatTime((i-1) * 15) }}
              </div>
              <!-- 每5秒一个小刻度 -->
              <div class="time-tick" v-for="i in 11" :key="'tick-' + i" :style="{left: (i * 50 + 25) + 'px'}"></div>
              <!-- 播放指针 -->
              <div class="playhead" :style="{left: currentTime * pixelsPerSecond + 'px'}"></div>
            </div>
          </div>
        </div>
        
        <!-- 轨道区域 -->
        <div class="tracks-container">
          <!-- 视频场景轨道 -->
          <div class="track-row video">
            <div class="track-label">
              <i class="el-icon-video-camera"></i>
              <span>视频场景</span>
              <el-button
                size="mini"
                type="primary"
                @click="showAddSceneDialog"
                style="margin-left: 8px;"
              >
                <i class="el-icon-plus"></i>
              </el-button>
            </div>
            <div class="track-content" @drop="onDropScene($event)" @dragover.prevent">
              <div class="track-timeline" :style="{ width: timelineWidth + 'px' }">
                <div
                  v-for="scene in scenes"
                  :key="scene.id"
                  class="scene-clip"
                  :style="getSceneStyle(scene)"
                  @click="selectScene(scene)"
                  :class="{ active: selectedScene && selectedScene.id === scene.id }"
                  draggable="true"
                  @dragstart="onDragStartScene($event, scene)"
                >
                  <div class="scene-content">
                    <span class="scene-name">{{ scene.name }}</span>
                    <div class="scene-duration">
                      {{ formatTime(scene.duration) }}
                      <span v-if="scene.maxDuration" class="max-duration">
                        / {{ formatTime(scene.maxDuration) }}
                      </span>
                    </div>
                    <div class="scene-video-info" v-if="scene.currentVideo">
                      {{ scene.currentVideo.name }}
                    </div>
                  </div>
                  <!-- 调整手柄 -->
                  <div class="resize-handle left" @mousedown="startResizeScene($event, scene, 'left')"></div>
                  <div class="resize-handle right" @mousedown="startResizeScene($event, scene, 'right')"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 文案轨道 -->
          <div class="track-row text">
            <div class="track-label">
              <i class="el-icon-edit"></i>
              <span>文案</span>
              <el-button size="mini" type="success" @click="segmentText" style="margin-left: 8px;">
                分段
              </el-button>
            </div>
            <div class="track-content">
              <div class="track-timeline" :style="{ width: timelineWidth + 'px' }">
                <div
                  v-for="(scene, index) in scenes"
                  :key="'text-' + scene.id"
                  class="text-clip"
                  :style="getSceneStyle(scene)"
                  @click="selectTextSegment(scene, index)"
                  :class="{ active: selectedTextSegment && selectedTextSegment.id === scene.id }"
                >
                  <div class="text-content">
                    <span class="text-preview">{{ getTextPreview(scene.textSegment) }}</span>
                    <div class="text-duration">{{ formatTime(scene.duration) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 其他轨道 -->
          <div
            v-for="track in tracks.filter(t => t.type !== 'video' && t.type !== 'text')"
            :key="track.id"
            class="track-row"
            :class="track.type"
          >
            <div class="track-label">
              <i :class="track.icon"></i>
              <span>{{ track.name }}</span>
            </div>
            <div class="track-content" @drop="onDrop($event, track)" @dragover.prevent>
              <div class="track-timeline" :style="{ width: timelineWidth + 'px' }">
                <div
                  v-for="clip in track.clips"
                  :key="clip.id"
                  class="clip"
                  :style="getClipStyle(clip)"
                  @click="selectClip(clip)"
                  :class="{ active: selectedClip && selectedClip.id === clip.id }"
                  draggable="true"
                  @dragstart="onDragStart($event, clip)"
                >
                  <div class="clip-content">
                    <span class="clip-name">{{ clip.name }}</span>
                    <div class="clip-duration">
                      {{ formatTime(clip.duration) }}
                      <span v-if="clip.maxDuration || clip.originalDuration" class="max-duration">
                        / {{ formatTime(clip.maxDuration || clip.originalDuration) }}
                      </span>
                    </div>
                  </div>
                  <!-- 调整手柄 -->
                  <div class="resize-handle left" @mousedown="startResize($event, clip, 'left')"></div>
                  <div class="resize-handle right" @mousedown="startResize($event, clip, 'right')"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 对话框区域 -->
    <!-- 批量剪辑对话框 -->
    <el-dialog
      title="开始批量剪辑"
      :visible.sync="batchDialogVisible"
      width="400px"
    >
      <div class="batch-config">
        <h4>{{ templateInfo.name }}</h4>
        <p>请选择要生成的视频数量：</p>
        <el-input-number
          v-model="batchCount"
          :min="1"
          :max="50"
          label="生成数量"
        ></el-input-number>
        <p class="batch-tip">最多可生成50条视频</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="startBatchClip">开始剪辑</el-button>
      </div>
    </el-dialog>

    <!-- 添加场景对话框 -->
    <el-dialog
      title="添加视频场景"
      :visible.sync="addSceneDialogVisible"
      width="500px"
    >
      <el-form :model="sceneForm" label-width="100px">
        <el-form-item label="场景名称">
          <el-input v-model="sceneForm.name" placeholder="请输入场景名称"></el-input>
        </el-form-item>
        <el-form-item label="场景时长">
          <el-input-number v-model="sceneForm.duration" :min="1" :max="60" label="秒"></el-input-number>
          <span style="margin-left: 8px; color: #909399;">秒</span>
        </el-form-item>
        <el-form-item label="视频库">
          <el-select v-model="sceneForm.videoLibraryId" placeholder="选择视频库">
            <el-option
              v-for="lib in videoLibraries"
              :key="lib.id"
              :label="lib.name"
              :value="lib.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addSceneDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAddScene">确定</el-button>
      </div>
    </el-dialog>

    <!-- 视频库选择对话框 -->
    <el-dialog
      title="选择视频库"
      :visible.sync="videoLibraryDialogVisible"
      width="800px"
    >
      <div class="video-library-content" v-if="selectedVideoLibrary">
        <h4>{{ selectedVideoLibrary.name }}</h4>
        <div class="video-grid">
          <div
            v-for="video in selectedVideoLibrary.videos"
            :key="video.id"
            class="video-item"
            @click="selectVideoFromLibrary(video)"
          >
            <div class="video-thumbnail">
              <img :src="video.thumbnail" :alt="video.name" />
              <div class="video-duration-badge">{{ formatTime(video.duration) }}</div>
            </div>
            <div class="video-info">
              <p class="video-name">{{ video.name }}</p>
              <p class="video-duration-text">时长: {{ formatTime(video.duration) }}</p>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="videoLibraryDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'VideoEditor',
  data() {
    return {
      // 模板信息
      templateInfo: {
        id: '',
        name: '视频模板',
        resolution: '1080x1920',
        fps: '30'
      },
      
      // 批量剪辑
      batchDialogVisible: false,
      batchCount: 1,

      // 场景管理对话框
      addSceneDialogVisible: false,
      sceneForm: {
        name: '',
        duration: 10,
        videoLibraryId: ''
      },

      // 视频库选择对话框
      videoLibraryDialogVisible: false,
      selectedVideoLibrary: null,

      // 属性面板
      activeCollapse: ['video'],

      // 设置项
      videoSettings: {
        backgroundColor: '#000000',
        brightness: 0,
        contrast: 0,
        saturation: 0
      },

      audioSettings: {
        masterVolume: 80,
        bgmVolume: 60,
        sfxVolume: 70
      },

      textSettings: {
        fontFamily: 'Microsoft YaHei',
        fontSize: 24,
        color: '#FFFFFF'
      },

      // 选中的元素
      selectedClip: null,
      selectedScene: null,
      selectedTextSegment: null,

      // 时间轴设置
      timelineDuration: 60, // 时间轴总长度60秒
      pixelsPerSecond: 10, // 每秒10像素，60秒 = 600像素（稀疏显示）
      currentTime: 0, // 当前播放时间（秒）
      
      // 场景列表
      scenes: [
        {
          id: 'scene1',
          name: '开场场景',
          startTime: 0,
          duration: 8,
          maxDuration: 12, // 视频素材的最大时长
          videoLibraryId: 'lib_开场',
          textSegment: '欢迎来到我们的产品展示，今天为大家介绍一款革命性的产品。',
          currentVideo: null
        },
        {
          id: 'scene2',
          name: '产品介绍',
          startTime: 8,
          duration: 10,
          maxDuration: 15, // 视频素材的最大时长
          videoLibraryId: 'lib_产品介绍',
          textSegment: '这款产品具有独特的设计理念和先进的技术，能够为用户带来全新的体验。',
          currentVideo: null
        },
        {
          id: 'scene3',
          name: '功能展示',
          startTime: 18,
          duration: 9,
          maxDuration: 20, // 视频素材的最大时长
          videoLibraryId: 'lib_功能展示',
          textSegment: '让我们来看看它的核心功能和使用方法。',
          currentVideo: null
        }
      ],

      // 从up页面获取的素材库（视频库）
      sucaiList: [], // 从up页面的素材列表获取

      // 视频库分类（基于文件夹）
      videoLibraries: [],

      // AI文案库
      textLibrary: {
        id: 'text1',
        name: 'AI剪辑文案库',
        fullText: '欢迎来到我们的产品展示，今天为大家介绍一款革命性的产品。这款产品具有独特的设计理念和先进的技术，能够为用户带来全新的体验。让我们来看看它的核心功能和使用方法。通过简单的操作，您就能体验到前所未有的便利性。',
        segments: [] // 将通过阿里云接口分段
      },

      // 轨道数据（简化版）
      tracks: [
        {
          id: 'video',
          name: '视频场景',
          type: 'video',
          icon: 'el-icon-video-camera'
        },
        {
          id: 'text',
          name: '文案',
          type: 'text',
          icon: 'el-icon-edit'
        },
        {
          id: 'music',
          name: '背景音乐',
          type: 'audio',
          icon: 'el-icon-headset',
          clips: [
            {
              id: 'm1',
              name: '背景音乐.mp3',
              startTime: 0,
              duration: 45, // 当前使用时长
              maxDuration: 180, // 音频文件的实际时长（3分钟）
              originalDuration: 180, // 原始文件时长
              type: 'audio'
            }
          ]
        },
        {
          id: 'sound',
          name: '音效',
          type: 'audio',
          icon: 'el-icon-bell',
          clips: [
            {
              id: 's1',
              name: '点击音效.wav',
              startTime: 8,
              duration: 1,
              maxDuration: 2, // 音效文件时长
              originalDuration: 2,
              type: 'audio'
            },
            {
              id: 's2',
              name: '转场音效.wav',
              startTime: 18,
              duration: 2,
              maxDuration: 3,
              originalDuration: 3,
              type: 'audio'
            }
          ]
        }
      ]
    }
  },

  computed: {
    // 时间轴总宽度
    timelineWidth() {
      return this.timelineDuration * this.pixelsPerSecond
    }
  },

  created() {
    // 获取模板ID
    const templateId = this.$route.query.templateId
    if (templateId) {
      this.loadTemplate(templateId)
    }

    // 加载素材库
    this.loadMaterialsFromUpPage()

    // 初始化场景视频
    this.initializeSceneVideos()
  },
  
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },
    
    // 加载模板
    loadTemplate(templateId) {
      // TODO: 从API加载模板数据
      this.templateInfo.id = templateId
      console.log('加载模板:', templateId)
    },
    
    // 保存模板
    saveTemplate() {
      this.$message.success('模板保存成功')
      // TODO: 调用保存API
    },
    
    // 显示批量剪辑对话框
    showBatchDialog() {
      this.batchDialogVisible = true
    },
    
    // 开始批量剪辑
    startBatchClip() {
      this.$message.success(`开始生成 ${this.batchCount} 条视频`)
      this.batchDialogVisible = false
      
      // 跳转到进度页面
      this.$router.push({
        path: '/storer/progress',
        query: { templateId: this.templateInfo.id }
      })
    },
    
    // 选择片段
    selectClip(clip) {
      this.selectedClip = clip
    },
    
    // 格式化时间
    formatTime(seconds) {
      const mins = Math.floor(seconds / 60)
      const secs = seconds % 60
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    },
    
    // 获取片段样式
    getClipStyle(clip) {
      return {
        left: clip.startTime * this.pixelsPerSecond + 'px',
        width: clip.duration * this.pixelsPerSecond + 'px'
      }
    },
    
    // 拖拽开始
    onDragStart(event, clip) {
      event.dataTransfer.setData('text/plain', JSON.stringify(clip))
    },
    
    // 拖拽放置
    onDrop(event, track) {
      const clipData = JSON.parse(event.dataTransfer.getData('text/plain'))
      console.log('拖拽到轨道:', track.name, clipData)
      // TODO: 实现拖拽逻辑
    },
    
    // 开始调整大小
    startResize(event, clip, direction) {
      console.log('调整大小:', clip.name, direction)

      // 检查是否可以调整
      if (!clip.maxDuration && !clip.originalDuration) {
        this.$message.warning('该素材没有时长限制信息')
        return
      }

      const maxAllowedDuration = clip.maxDuration || clip.originalDuration

      if (direction === 'right') {
        // 向右拖拽（增加时长）
        if (clip.duration >= maxAllowedDuration) {
          this.$message.warning(`素材最大时长为 ${this.formatTime(maxAllowedDuration)}，无法继续延长`)
          return
        }
      }

      // TODO: 实现具体的拖拽调整逻辑
      this.startResizeWithLimit(event, clip, direction, maxAllowedDuration)
    },

    // 带时长限制的调整大小
    startResizeWithLimit(event, clip, direction, maxDuration) {
      const startX = event.clientX
      const startDuration = clip.duration
      const pixelsPerSecond = this.pixelsPerSecond

      const onMouseMove = (e) => {
        const deltaX = e.clientX - startX
        const deltaSeconds = deltaX / pixelsPerSecond

        let newDuration = startDuration

        if (direction === 'right') {
          newDuration = Math.max(1, Math.min(maxDuration, startDuration + deltaSeconds))
        } else if (direction === 'left') {
          newDuration = Math.max(1, Math.min(maxDuration, startDuration - deltaSeconds))
        }

        clip.duration = Math.round(newDuration)

        // 显示时长提示
        this.showDurationTip(clip, maxDuration)
      }

      const onMouseUp = () => {
        document.removeEventListener('mousemove', onMouseMove)
        document.removeEventListener('mouseup', onMouseUp)
        this.hideDurationTip()
      }

      document.addEventListener('mousemove', onMouseMove)
      document.addEventListener('mouseup', onMouseUp)
    },

    // 显示时长提示
    showDurationTip(clip, maxDuration) {
      const percentage = (clip.duration / maxDuration * 100).toFixed(1)
      console.log(`${clip.name}: ${this.formatTime(clip.duration)} / ${this.formatTime(maxDuration)} (${percentage}%)`)
    },

    // 隐藏时长提示
    hideDurationTip() {
      // 清除提示
    },

    // 获取预览分辨率显示文本
    getPreviewResolution() {
      // 根据模板分辨率返回对应的抖音尺寸
      const resolutionMap = {
        '1920x1080': '1080x1920',
        '1280x720': '720x1280',
        '1080x1920': '1080x1920',
        '720x1280': '720x1280'
      }
      return resolutionMap[this.templateInfo.resolution] || '1080x1920'
    },

    // 从up页面加载素材库
    async loadMaterialsFromUpPage() {
      try {
        // 模拟从up页面获取素材数据
        // 实际应用中，这里应该调用API或从localStorage获取
        const mockSucaiList = [
          {
            id: 'sucai1',
            name: '开场视频1.mp4',
            type: 'video',
            size: 1024000,
            uploadTime: '2024-01-01 12:00:00',
            duration: '00:12',
            url: 'https://www.w3schools.com/html/mov_bbb.mp4',
            folder: 'asucai/admin/开场',
            thumbnail: 'https://via.placeholder.com/160x90/4A90E2/FFFFFF?text=Opening1'
          },
          {
            id: 'sucai2',
            name: '开场视频2.mp4',
            type: 'video',
            size: 1024000,
            uploadTime: '2024-01-01 12:00:00',
            duration: '00:10',
            url: 'https://www.w3schools.com/html/mov_bbb.mp4',
            folder: 'asucai/admin/开场',
            thumbnail: 'https://via.placeholder.com/160x90/4A90E2/FFFFFF?text=Opening2'
          },
          {
            id: 'sucai3',
            name: '产品介绍1.mp4',
            type: 'video',
            size: 1024000,
            uploadTime: '2024-01-01 12:00:00',
            duration: '00:18',
            url: 'https://www.w3schools.com/html/mov_bbb.mp4',
            folder: 'asucai/admin/产品介绍',
            thumbnail: 'https://via.placeholder.com/160x90/50C878/FFFFFF?text=Product1'
          },
          {
            id: 'sucai4',
            name: '产品介绍2.mp4',
            type: 'video',
            size: 1024000,
            uploadTime: '2024-01-01 12:00:00',
            duration: '00:22',
            url: 'https://www.w3schools.com/html/mov_bbb.mp4',
            folder: 'asucai/admin/产品介绍',
            thumbnail: 'https://via.placeholder.com/160x90/50C878/FFFFFF?text=Product2'
          },
          {
            id: 'sucai5',
            name: '功能展示1.mp4',
            type: 'video',
            size: 1024000,
            uploadTime: '2024-01-01 12:00:00',
            duration: '00:25',
            url: 'https://www.w3schools.com/html/mov_bbb.mp4',
            folder: 'asucai/admin/功能展示',
            thumbnail: 'https://via.placeholder.com/160x90/FF6B6B/FFFFFF?text=Feature1'
          }
        ]

        this.sucaiList = mockSucaiList

        // 根据文件夹分组创建视频库
        this.createVideoLibrariesFromSucai()

        console.log('素材库加载完成:', this.videoLibraries)

      } catch (error) {
        console.error('加载素材库失败:', error)
        this.$message.error('加载素材库失败')
      }
    },

    // 根据素材列表创建视频库
    createVideoLibrariesFromSucai() {
      const folderMap = new Map()

      // 按文件夹分组
      this.sucaiList.forEach(item => {
        if (item.type === 'video') {
          // 提取文件夹名称
          const folderPath = item.folder || 'asucai/admin/总'
          const folderName = folderPath.split('/').pop() || '总'

          if (!folderMap.has(folderName)) {
            folderMap.set(folderName, {
              id: 'lib_' + folderName,
              name: folderName + '视频库',
              videos: []
            })
          }

          // 转换为视频库格式
          const video = {
            id: item.id,
            name: item.name,
            url: item.url,
            duration: this.parseDuration(item.duration),
            thumbnail: item.thumbnail || 'https://via.placeholder.com/160x90/999/FFFFFF?text=Video'
          }

          folderMap.get(folderName).videos.push(video)
        }
      })

      // 转换为数组
      this.videoLibraries = Array.from(folderMap.values())
    },

    // 解析时长字符串为秒数
    parseDuration(durationStr) {
      if (!durationStr) return 10

      const parts = durationStr.split(':')
      if (parts.length === 2) {
        const minutes = parseInt(parts[0]) || 0
        const seconds = parseInt(parts[1]) || 0
        return minutes * 60 + seconds
      }

      return 10 // 默认10秒
    },

    // 初始化场景视频（为每个场景随机选择一个视频）
    initializeSceneVideos() {
      this.scenes.forEach(scene => {
        const library = this.videoLibraries.find(lib => lib.id === scene.videoLibraryId)
        if (library && library.videos.length > 0) {
          // 随机选择一个视频
          const randomIndex = Math.floor(Math.random() * library.videos.length)
          const selectedVideo = library.videos[randomIndex]
          scene.currentVideo = selectedVideo

          // 设置场景的最大时长为视频时长
          scene.maxDuration = selectedVideo.duration

          // 如果当前场景时长超过视频时长，调整为视频时长
          if (scene.duration > selectedVideo.duration) {
            scene.duration = selectedVideo.duration
          }
        }
      })

      // 重新计算时间轴
      this.recalculateSceneTimeline()
    },

    // 显示添加场景对话框
    showAddSceneDialog() {
      this.sceneForm = {
        name: '',
        duration: 10,
        videoLibraryId: ''
      }
      this.addSceneDialogVisible = true
    },

    // 确认添加场景
    confirmAddScene() {
      if (!this.sceneForm.name || !this.sceneForm.videoLibraryId) {
        this.$message.error('请填写完整的场景信息')
        return
      }

      // 计算新场景的开始时间
      const lastScene = this.scenes[this.scenes.length - 1]
      const startTime = lastScene ? lastScene.startTime + lastScene.duration : 0

      const newScene = {
        id: 'scene' + Date.now(),
        name: this.sceneForm.name,
        startTime: startTime,
        duration: this.sceneForm.duration,
        videoLibraryId: this.sceneForm.videoLibraryId,
        textSegment: '新场景的文案内容...',
        currentVideo: null
      }

      // 为新场景随机选择视频
      const library = this.videoLibraries.find(lib => lib.id === this.sceneForm.videoLibraryId)
      if (library && library.videos.length > 0) {
        const randomIndex = Math.floor(Math.random() * library.videos.length)
        newScene.currentVideo = library.videos[randomIndex]
      }

      this.scenes.push(newScene)
      this.addSceneDialogVisible = false
      this.$message.success('场景添加成功')
    },

    // 选择场景
    selectScene(scene) {
      this.selectedScene = scene
      this.selectedClip = null
      this.selectedTextSegment = null

      // 显示视频库选择对话框
      const library = this.videoLibraries.find(lib => lib.id === scene.videoLibraryId)
      if (library) {
        this.selectedVideoLibrary = library
        this.videoLibraryDialogVisible = true
      }
    },

    // 从视频库选择视频
    selectVideoFromLibrary(video) {
      if (this.selectedScene) {
        this.selectedScene.currentVideo = video
        // 更新场景的最大时长限制
        this.selectedScene.maxDuration = video.duration

        // 如果当前时长超过新视频的时长，则调整为新视频的时长
        if (this.selectedScene.duration > video.duration) {
          this.selectedScene.duration = video.duration
          this.$message.warning(`场景时长已调整为视频时长: ${this.formatTime(video.duration)}`)
        }

        this.$message.success(`已为场景"${this.selectedScene.name}"选择视频: ${video.name} (${this.formatTime(video.duration)})`)
      }
      this.videoLibraryDialogVisible = false
    },

    // 选择文案片段
    selectTextSegment(scene, index) {
      this.selectedTextSegment = scene
      this.selectedClip = null
      this.selectedScene = null
    },

    // 获取场景样式
    getSceneStyle(scene) {
      return {
        left: scene.startTime * this.pixelsPerSecond + 'px',
        width: scene.duration * this.pixelsPerSecond + 'px'
      }
    },

    // 获取文案预览
    getTextPreview(text) {
      return text.length > 20 ? text.substring(0, 20) + '...' : text
    },

    // 文案分段（调用阿里云接口）
    async segmentText() {
      try {
        this.$message.info('正在调用阿里云接口进行文案分段...')

        // TODO: 调用阿里云文本分段接口
        // const segments = await this.callAliCloudTextSegmentation(this.textLibrary.fullText)

        // 模拟分段结果
        const mockSegments = [
          '欢迎来到我们的产品展示，今天为大家介绍一款革命性的产品。',
          '这款产品具有独特的设计理念和先进的技术，能够为用户带来全新的体验。',
          '让我们来看看它的核心功能和使用方法。',
          '通过简单的操作，您就能体验到前所未有的便利性。'
        ]

        // 更新场景的文案片段
        mockSegments.forEach((segment, index) => {
          if (this.scenes[index]) {
            this.scenes[index].textSegment = segment
          }
        })

        this.$message.success('文案分段完成')

      } catch (error) {
        console.error('文案分段失败:', error)
        this.$message.error('文案分段失败，请重试')
      }
    },

    // 场景拖拽开始
    onDragStartScene(event, scene) {
      event.dataTransfer.setData('text/plain', JSON.stringify(scene))
    },

    // 场景拖拽放置
    onDropScene(event) {
      const sceneData = JSON.parse(event.dataTransfer.getData('text/plain'))
      console.log('场景拖拽:', sceneData)
      // TODO: 实现场景拖拽逻辑
    },

    // 开始调整场景大小
    startResizeScene(event, scene, direction) {
      console.log('调整场景大小:', scene.name, direction)

      // 检查场景是否有视频和时长限制
      if (!scene.currentVideo || !scene.maxDuration) {
        this.$message.warning('请先为场景选择视频')
        return
      }

      const maxAllowedDuration = scene.maxDuration

      if (direction === 'right') {
        // 向右拖拽（增加时长）
        if (scene.duration >= maxAllowedDuration) {
          this.$message.warning(`场景最大时长为 ${this.formatTime(maxAllowedDuration)}（视频时长限制）`)
          return
        }
      }

      // 实现场景大小调整
      this.startResizeSceneWithLimit(event, scene, direction, maxAllowedDuration)
    },

    // 带时长限制的场景调整
    startResizeSceneWithLimit(event, scene, direction, maxDuration) {
      const startX = event.clientX
      const startDuration = scene.duration
      const pixelsPerSecond = this.pixelsPerSecond

      const onMouseMove = (e) => {
        const deltaX = e.clientX - startX
        const deltaSeconds = deltaX / pixelsPerSecond

        let newDuration = startDuration

        if (direction === 'right') {
          newDuration = Math.max(1, Math.min(maxDuration, startDuration + deltaSeconds))
        } else if (direction === 'left') {
          newDuration = Math.max(1, Math.min(maxDuration, startDuration - deltaSeconds))
        }

        scene.duration = Math.round(newDuration)

        // 显示场景时长提示
        this.showSceneDurationTip(scene, maxDuration)
      }

      const onMouseUp = () => {
        document.removeEventListener('mousemove', onMouseMove)
        document.removeEventListener('mouseup', onMouseUp)
        this.hideSceneDurationTip()

        // 调整完成后重新计算后续场景的开始时间
        this.recalculateSceneTimeline()
      }

      document.addEventListener('mousemove', onMouseMove)
      document.addEventListener('mouseup', onMouseUp)
    },

    // 显示场景时长提示
    showSceneDurationTip(scene, maxDuration) {
      const percentage = (scene.duration / maxDuration * 100).toFixed(1)
      console.log(`${scene.name}: ${this.formatTime(scene.duration)} / ${this.formatTime(maxDuration)} (${percentage}%)`)
    },

    // 隐藏场景时长提示
    hideSceneDurationTip() {
      // 清除提示
    },

    // 重新计算场景时间轴
    recalculateSceneTimeline() {
      let currentTime = 0
      this.scenes.forEach(scene => {
        scene.startTime = currentTime
        currentTime += scene.duration
      })
      console.log('场景时间轴已重新计算')
    },

    // 点击时间轴
    onTimelineClick(event) {
      const rect = event.currentTarget.getBoundingClientRect()
      const clickX = event.clientX - rect.left
      const clickedTime = Math.floor(clickX / this.pixelsPerSecond)

      // 限制在0-60秒范围内
      this.currentTime = Math.max(0, Math.min(60, clickedTime))

      console.log(`点击时间轴: ${this.currentTime}秒`)

      // 根据当前时间找到对应的场景并预览
      this.previewAtTime(this.currentTime)
    },

    // 根据时间预览对应场景
    previewAtTime(time) {
      // 找到当前时间对应的场景
      const currentScene = this.scenes.find(scene => {
        return time >= scene.startTime && time < (scene.startTime + scene.duration)
      })

      if (currentScene) {
        this.selectedScene = currentScene
        console.log(`预览场景: ${currentScene.name} (${time}秒)`)

        // 如果场景有视频，可以计算视频内的播放位置
        if (currentScene.currentVideo) {
          const sceneProgress = time - currentScene.startTime
          const videoProgress = (sceneProgress / currentScene.duration * 100).toFixed(1)
          console.log(`视频播放进度: ${videoProgress}%`)
        }
      } else {
        // 如果没有找到场景，清除选择
        this.selectedScene = null
        console.log('当前时间没有对应的场景')
      }
    },

    // 开始拖拽时间轴
    onTimelineDragStart(event) {
      event.preventDefault()

      const startX = event.clientX
      const rect = event.currentTarget.getBoundingClientRect()
      const startClickX = event.clientX - rect.left
      const startTime = Math.floor(startClickX / this.pixelsPerSecond)

      // 设置初始时间
      this.currentTime = Math.max(0, Math.min(60, startTime))
      this.previewAtTime(this.currentTime)

      const onMouseMove = (e) => {
        const currentX = e.clientX
        const deltaX = currentX - startX
        const newClickX = startClickX + deltaX
        const newTime = Math.floor(newClickX / this.pixelsPerSecond)

        // 限制在0-60秒范围内
        this.currentTime = Math.max(0, Math.min(60, newTime))
        this.previewAtTime(this.currentTime)
      }

      const onMouseUp = () => {
        document.removeEventListener('mousemove', onMouseMove)
        document.removeEventListener('mouseup', onMouseUp)
        console.log(`拖拽结束: ${this.currentTime}秒`)
      }

      document.addEventListener('mousemove', onMouseMove)
      document.addEventListener('mouseup', onMouseUp)
    }
  }
}
</script>

<style scoped>
.video-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

/* 头部工具栏 */
.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.template-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-right {
  display: flex;
  gap: 12px;
}

/* 主编辑区域 */
.editor-main {
  display: flex;
  flex: 1;
  min-height: 0;
}

/* 预览面板 */
.preview-panel {
  flex: 1;
  padding: 20px;
  background: white;
  border-right: 1px solid #e4e7ed;
}

.preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-preview {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  padding: 20px;
}

.preview-frame {
  width: 300px;
  height: 533px; /* 300 * 16/9 = 533.33 */
  background: #000;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  border: 1px solid #ddd;
  overflow: hidden;
}

/* 场景预览样式 */
.scene-preview {
  width: 100%;
  height: 100%;
  position: relative;
}

.scene-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.scene-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 20px 16px 16px;
}

.scene-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.scene-video-name {
  font-size: 12px;
  color: #ccc;
  margin-bottom: 4px;
}

.scene-time-info {
  font-size: 11px;
  color: #ffd700;
  margin-bottom: 8px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.scene-text-preview {
  font-size: 11px;
  line-height: 1.4;
  color: #ddd;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.preview-placeholder {
  text-align: center;
  color: #909399;
  padding: 40px 20px;
}

.preview-placeholder i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
  color: #666;
}

.preview-placeholder p {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: #909399;
}

.preview-info {
  font-size: 12px !important;
  margin: 12px 0 !important;
  color: #999 !important;
}

.preview-tips {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #333;
}

.preview-tips p {
  font-size: 11px !important;
  color: #666 !important;
  margin: 4px 0 !important;
}

.current-time-display {
  margin: 16px 0;
  padding: 8px 12px;
  background: rgba(255, 215, 0, 0.1);
  border: 1px solid #ffd700;
  border-radius: 4px;
}

.current-time-display p {
  margin: 0 !important;
  font-size: 14px !important;
  color: #ffd700 !important;
  font-weight: 600 !important;
}

.preview-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
}

.time-display {
  font-family: monospace;
  font-size: 14px;
  color: #606266;
}

/* 属性设置面板 */
.properties-panel {
  width: 300px;
  background: white;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* 时间轴区域 */
.timeline-area {
  height: 300px;
  background: white;
  border-top: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  min-width: 0; /* 允许收缩 */
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #e4e7ed;
}

.timeline-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.timeline-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* 时间刻度行 */
.time-ruler-row {
  display: flex;
  height: 30px;
  border-bottom: 1px solid #e4e7ed;
}

.time-ruler-label {
  width: 150px;
  background: #f8f9fa;
  border-right: 1px solid #e4e7ed;
  flex-shrink: 0;
}

/* 时间刻度 */
.time-ruler {
  flex: 1;
  height: 100%;
  background: #fafafa;
  position: relative;
  cursor: pointer;
  user-select: none;
  overflow: hidden;
}

.time-marks-container {
  position: relative;
  height: 100%;
  width: 100%;
}

.time-mark {
  position: absolute;
  top: 0;
  height: 100%;
  font-size: 11px;
  color: #606266;
  display: flex;
  align-items: center;
  padding-left: 6px;
  border-left: 2px solid #c0c4cc;
  pointer-events: none;
  font-weight: 500;
}

.time-tick {
  position: absolute;
  top: 22px;
  width: 1px;
  height: 8px;
  background: #f0f0f0;
  pointer-events: none;
}

.playhead {
  position: absolute;
  top: 0;
  width: 2px;
  height: 100%;
  background: #ff4757;
  z-index: 10;
  pointer-events: none;
}

.playhead::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  width: 10px;
  height: 10px;
  background: #ff4757;
  border-radius: 50%;
}

/* 轨道容器 */
.tracks-container {
  flex: 1;
}

.track-row {
  height: 40px;
  display: flex;
  border-bottom: 1px solid #e4e7ed;
}

.track-label {
  width: 150px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  background: #f8f9fa;
  border-right: 1px solid #e4e7ed;
  font-size: 14px;
  color: #606266;
  flex-shrink: 0;
}

.track-label i {
  margin-right: 8px;
  font-size: 16px;
}

.track-content {
  flex: 1;
  position: relative;
  background: white;
  overflow: hidden;
}

.track-timeline {
  position: relative;
  height: 100%;
  width: 100%;
}

/* 片段样式 */
.clip {
  position: absolute;
  top: 4px;
  height: 32px;
  background: #409EFF;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  padding: 0 8px;
  color: white;
  font-size: 12px;
  transition: all 0.2s ease;
}

.clip:hover {
  background: #337ecc;
  transform: translateY(-1px);
}

.clip.active {
  background: #E6A23C;
  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.3);
}

/* 场景片段样式 */
.scene-clip {
  position: absolute;
  top: 2px;
  height: 36px;
  background: #67C23A;
  border-radius: 6px;
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  padding: 0;
  color: white;
  font-size: 11px;
  transition: all 0.2s ease;
  overflow: hidden;
}

.scene-clip:hover {
  background: #5daf34;
  transform: translateY(-1px);
}

.scene-clip.active {
  background: #E6A23C;
  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.3);
}

.scene-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 4px 8px;
  overflow: hidden;
}

.scene-name {
  display: block;
  font-weight: 600;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  margin-bottom: 2px;
}

.scene-duration {
  font-size: 10px;
  opacity: 0.8;
  line-height: 1;
  margin-bottom: 1px;
}

.scene-duration .max-duration {
  color: rgba(255, 255, 255, 0.6);
  font-size: 9px;
}

.scene-video-info {
  font-size: 9px;
  opacity: 0.7;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 文案片段样式 */
.text-clip {
  position: absolute;
  top: 4px;
  height: 32px;
  background: #F56C6C;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  padding: 0 8px;
  color: white;
  font-size: 11px;
  transition: all 0.2s ease;
}

.text-clip:hover {
  background: #f45656;
  transform: translateY(-1px);
}

.text-clip.active {
  background: #E6A23C;
  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.3);
}

.text-content {
  flex: 1;
  overflow: hidden;
}

.text-preview {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  line-height: 1.2;
}

.text-duration {
  font-size: 10px;
  opacity: 0.8;
}

.clip-content {
  flex: 1;
  overflow: hidden;
}

.clip-name {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

.clip-duration {
  font-size: 10px;
  opacity: 0.8;
}

.max-duration {
  color: rgba(255, 255, 255, 0.6);
  font-size: 9px;
}

/* 调整手柄 */
.resize-handle {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 4px;
  background: rgba(255, 255, 255, 0.3);
  cursor: ew-resize;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.resize-handle.left {
  left: 0;
}

.resize-handle.right {
  right: 0;
}

.clip:hover .resize-handle {
  opacity: 1;
}

/* 轨道类型样式 */
.track-row.video .clip {
  background: #409EFF;
}

.track-row.audio .clip {
  background: #67C23A;
}

.track-row.text .clip {
  background: #E6A23C;
}

.track-row.effect .clip {
  background: #F56C6C;
}

/* 批量配置 */
.batch-config {
  text-align: center;
  padding: 20px 0;
}

.batch-config h4 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 18px;
}

.batch-config p {
  margin: 0 0 16px 0;
  color: #606266;
}

.batch-tip {
  margin-top: 12px !important;
  color: #909399;
  font-size: 12px;
}

/* 视频库对话框样式 */
.video-library-content h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.video-item {
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  background: white;
}

.video-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #409EFF;
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 90px;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.video-duration-badge {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
}

.video-info {
  padding: 8px;
  background: #f8f9fa;
}

.video-name {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #303133;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.video-duration-text {
  margin: 0;
  font-size: 11px;
  color: #909399;
}

/* 响应式设计 - 9:16 视频预览适配 */
@media (max-width: 1200px) {
  .preview-frame {
    width: 250px;
    height: 444px; /* 250 * 16/9 = 444.44 */
  }
}

@media (max-width: 768px) {
  .editor-main {
    flex-direction: column;
  }

  .preview-panel {
    border-right: none;
    border-bottom: 1px solid #e4e7ed;
  }

  .properties-panel {
    width: 100%;
    max-height: 300px;
  }

  .preview-frame {
    width: 200px;
    height: 356px; /* 200 * 16/9 = 355.56 */
  }

  .timeline-area {
    height: 250px;
  }
}

@media (max-width: 480px) {
  .preview-frame {
    width: 150px;
    height: 267px; /* 150 * 16/9 = 266.67 */
  }

  .preview-placeholder {
    padding: 20px 10px;
  }

  .preview-placeholder i {
    font-size: 32px;
  }

  .preview-placeholder p {
    font-size: 14px;
  }

  .preview-tips p {
    font-size: 10px !important;
  }
}
</style>
