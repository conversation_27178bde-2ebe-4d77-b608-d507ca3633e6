{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\progress.vue?vue&type=style&index=0&id=3d0766b5&scoped=true&lang=css", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\progress.vue", "mtime": 1754974569676}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753759480805}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753759474011}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753759476521}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi52aWRlby1wcm9ncmVzcyB7CiAgcGFkZGluZzogMjBweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOwogIG1pbi1oZWlnaHQ6IDEwMHZoOwp9CgovKiDlpLTpg6jlt6XlhbfmoI8gKi8KLmhlYWRlci10b29sYmFyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIG1hcmdpbi1ib3R0b206IDI0cHg7CiAgcGFkZGluZzogMjBweDsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICBib3JkZXItcmFkaXVzOiAxMnB4OwogIGJveC1zaGFkb3c6IDAgMnB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjEpOwp9CgoudG9vbGJhci1sZWZ0IHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZ2FwOiAxNnB4Owp9CgoucGFnZS10aXRsZSB7CiAgbWFyZ2luOiAwOwogIGNvbG9yOiAjMzAzMTMzOwogIGZvbnQtc2l6ZTogMjBweDsKICBmb250LXdlaWdodDogNjAwOwp9CgoucGFnZS10aXRsZSBpIHsKICBtYXJnaW4tcmlnaHQ6IDhweDsKICBjb2xvcjogIzQwOUVGRjsKfQoKLnRvb2xiYXItcmlnaHQgewogIGRpc3BsYXk6IGZsZXg7CiAgZ2FwOiAxMnB4Owp9CgovKiDnu5/orqHljaHniYcgKi8KLnN0YXRzLWNhcmRzIHsKICBkaXNwbGF5OiBncmlkOwogIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMjAwcHgsIDFmcikpOwogIGdhcDogMjBweDsKICBtYXJnaW4tYm90dG9tOiAzMHB4Owp9Cgouc3RhdC1jYXJkIHsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICBwYWRkaW5nOiAyNHB4OwogIGJvcmRlci1yYWRpdXM6IDEycHg7CiAgYm94LXNoYWRvdzogMCAycHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMDgpOwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBnYXA6IDE2cHg7Cn0KCi5zdGF0LWljb24gewogIHdpZHRoOiA0OHB4OwogIGhlaWdodDogNDhweDsKICBib3JkZXItcmFkaXVzOiAxMnB4OwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBmb250LXNpemU6IDIwcHg7CiAgY29sb3I6IHdoaXRlOwp9Cgouc3RhdC1pY29uLmNvbXBsZXRlZCB7CiAgYmFja2dyb3VuZDogIzY3QzIzQTsKfQoKLnN0YXQtaWNvbi5wcm9jZXNzaW5nIHsKICBiYWNrZ3JvdW5kOiAjNDA5RUZGOwp9Cgouc3RhdC1pY29uLmZhaWxlZCB7CiAgYmFja2dyb3VuZDogI0Y1NkM2QzsKfQoKLnN0YXQtaWNvbi50b3RhbCB7CiAgYmFja2dyb3VuZDogIzkwOTM5OTsKfQoKLnN0YXQtaW5mbyB7CiAgZmxleDogMTsKfQoKLnN0YXQtbnVtYmVyIHsKICBmb250LXNpemU6IDI4cHg7CiAgZm9udC13ZWlnaHQ6IDcwMDsKICBjb2xvcjogIzMwMzEzMzsKICBsaW5lLWhlaWdodDogMTsKICBtYXJnaW4tYm90dG9tOiA0cHg7Cn0KCi5zdGF0LWxhYmVsIHsKICBjb2xvcjogIzkwOTM5OTsKICBmb250LXNpemU6IDE0cHg7Cn0KCi8qIOinhumikeWuueWZqCAqLwoudmlkZW9zLWNvbnRhaW5lciB7CiAgYmFja2dyb3VuZDogd2hpdGU7CiAgYm9yZGVyLXJhZGl1czogMTJweDsKICBib3gtc2hhZG93OiAwIDJweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4wOCk7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKfQoKLmNvbnRhaW5lci1oZWFkZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgcGFkZGluZzogMjBweCAyNHB4OwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTRlN2VkOwp9CgouY29udGFpbmVyLWhlYWRlciBoMyB7CiAgbWFyZ2luOiAwOwogIGZvbnQtc2l6ZTogMThweDsKICBmb250LXdlaWdodDogNjAwOwogIGNvbG9yOiAjMzAzMTMzOwp9CgovKiDop4bpopHnvZHmoLwgKi8KLnZpZGVvcy1ncmlkIHsKICBkaXNwbGF5OiBncmlkOwogIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZmlsbCwgbWlubWF4KDI4MHB4LCAxZnIpKTsKICBnYXA6IDIwcHg7CiAgcGFkZGluZzogMjRweDsKfQoKLyog6KeG6aKR5Y2h54mHICovCi52aWRlby1jYXJkIHsKICBiYWNrZ3JvdW5kOiAjZjhmOWZhOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBvdmVyZmxvdzogaGlkZGVuOwogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7Cn0KCi52aWRlby1jYXJkOmhvdmVyIHsKICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7CiAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMSk7Cn0KCi8qIOinhumikee8qeeVpeWbviAqLwoudmlkZW8tdGh1bWJuYWlsIHsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiAxNjBweDsKICBiYWNrZ3JvdW5kOiAjMDAwOwogIG92ZXJmbG93OiBoaWRkZW47Cn0KCi52aWRlby10aHVtYm5haWwgaW1nIHsKICB3aWR0aDogMTAwJTsKICBoZWlnaHQ6IDEwMCU7CiAgb2JqZWN0LWZpdDogY292ZXI7Cn0KCi50aHVtYm5haWwtcGxhY2Vob2xkZXIgewogIHdpZHRoOiAxMDAlOwogIGhlaWdodDogMTAwJTsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgYmFja2dyb3VuZDogI2RkZDsKICBjb2xvcjogIzk5OTsKfQoKLnRodW1ibmFpbC1wbGFjZWhvbGRlciBpIHsKICBmb250LXNpemU6IDMycHg7Cn0KCi8qIOeKtuaAgeimhuebluWxgiAqLwouc3RhdHVzLW92ZXJsYXkgewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICB0b3A6IDA7CiAgbGVmdDogMDsKICByaWdodDogMDsKICBib3R0b206IDA7CiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjgpOwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBjb2xvcjogd2hpdGU7Cn0KCi5zdGF0dXMtY29udGVudCB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwp9Cgouc3RhdHVzLWNvbnRlbnQgaSB7CiAgZm9udC1zaXplOiAzMnB4OwogIG1hcmdpbi1ib3R0b206IDEycHg7CiAgZGlzcGxheTogYmxvY2s7Cn0KCi5zdGF0dXMtdGV4dCB7CiAgbWFyZ2luOiAwIDAgMTJweCAwOwogIGZvbnQtc2l6ZTogMTRweDsKfQoKLnByb2dyZXNzLWJhciB7CiAgd2lkdGg6IDEyMHB4OwogIGhlaWdodDogNHB4OwogIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKTsKICBib3JkZXItcmFkaXVzOiAycHg7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICBtYXJnaW46IDAgYXV0bzsKfQoKLnByb2dyZXNzLWZpbGwgewogIGhlaWdodDogMTAwJTsKICBiYWNrZ3JvdW5kOiAjNDA5RUZGOwogIHRyYW5zaXRpb246IHdpZHRoIDAuM3MgZWFzZTsKfQoKLyog5pKt5pS+5oyJ6ZKuICovCi5wbGF5LWJ1dHRvbiB7CiAgcG9zaXRpb246IGFic29sdXRlOwogIHRvcDogNTAlOwogIGxlZnQ6IDUwJTsKICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgtNTAlLCAtNTAlKTsKICB3aWR0aDogNDhweDsKICBoZWlnaHQ6IDQ4cHg7CiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjcpOwogIGJvcmRlci1yYWRpdXM6IDUwJTsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgY29sb3I6IHdoaXRlOwogIGZvbnQtc2l6ZTogMjBweDsKICBjdXJzb3I6IHBvaW50ZXI7CiAgb3BhY2l0eTogMDsKICB0cmFuc2l0aW9uOiBvcGFjaXR5IDAuM3MgZWFzZTsKfQoKLnZpZGVvLWNhcmQ6aG92ZXIgLnBsYXktYnV0dG9uIHsKICBvcGFjaXR5OiAxOwp9CgoucGxheS1idXR0b246aG92ZXIgewogIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC45KTsKfQoKLyog6KeG6aKR5pe26ZW/ICovCi52aWRlby1kdXJhdGlvbiB7CiAgcG9zaXRpb246IGFic29sdXRlOwogIGJvdHRvbTogOHB4OwogIHJpZ2h0OiA4cHg7CiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjcpOwogIGNvbG9yOiB3aGl0ZTsKICBwYWRkaW5nOiAycHggNnB4OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBmb250LXNpemU6IDEycHg7Cn0KCi8qIOinhumikeS/oeaBryAqLwoudmlkZW8taW5mbyB7CiAgcGFkZGluZzogMTZweDsKfQoKLnZpZGVvLW5hbWUgewogIG1hcmdpbjogMCAwIDhweCAwOwogIGZvbnQtc2l6ZTogMTRweDsKICBmb250LXdlaWdodDogNjAwOwogIGNvbG9yOiAjMzAzMTMzOwogIGxpbmUtaGVpZ2h0OiAxLjQ7Cn0KCi52aWRlby1tZXRhIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBtYXJnaW4tYm90dG9tOiAxMnB4OwogIGZvbnQtc2l6ZTogMTJweDsKICBjb2xvcjogIzkwOTM5OTsKfQoKLnZpZGVvLWFjdGlvbnMgewogIGRpc3BsYXk6IGZsZXg7CiAgZ2FwOiA4cHg7CiAgZmxleC13cmFwOiB3cmFwOwp9CgovKiDml4vovazliqjnlLsgKi8KLnJvdGF0aW5nIHsKICBhbmltYXRpb246IHJvdGF0ZSAycyBsaW5lYXIgaW5maW5pdGU7Cn0KCkBrZXlmcmFtZXMgcm90YXRlIHsKICBmcm9tIHsKICAgIHRyYW5zZm9ybTogcm90YXRlKDBkZWcpOwogIH0KICB0byB7CiAgICB0cmFuc2Zvcm06IHJvdGF0ZSgzNjBkZWcpOwogIH0KfQoKLyog56m654q25oCBICovCi5lbXB0eS1zdGF0ZSB7CiAgZ3JpZC1jb2x1bW46IDEgLyAtMTsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgcGFkZGluZzogNjBweCAyMHB4Owp9CgouZW1wdHktc3RhdGUgaSB7CiAgZm9udC1zaXplOiA0OHB4OwogIGNvbG9yOiAjQzBDNENDOwogIG1hcmdpbi1ib3R0b206IDE2cHg7Cn0KCi5lbXB0eS1zdGF0ZSBoMyB7CiAgbWFyZ2luOiAwIDAgOHB4IDA7CiAgY29sb3I6ICMzMDMxMzM7CiAgZm9udC1zaXplOiAxOHB4OwogIGZvbnQtd2VpZ2h0OiA2MDA7Cn0KCi5lbXB0eS1zdGF0ZSBwIHsKICBtYXJnaW46IDAgMCAyMHB4IDA7CiAgY29sb3I6ICM5MDkzOTk7CiAgZm9udC1zaXplOiAxNHB4Owp9CgovKiDmibnph4/phY3nva4gKi8KLmJhdGNoLWNvbmZpZyB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIHBhZGRpbmc6IDIwcHggMDsKfQoKLmJhdGNoLWNvbmZpZyBoNCB7CiAgbWFyZ2luOiAwIDAgMjBweCAwOwogIGNvbG9yOiAjMzAzMTMzOwogIGZvbnQtc2l6ZTogMThweDsKfQoKLmJhdGNoLWNvbmZpZyBwIHsKICBtYXJnaW46IDAgMCAxNnB4IDA7CiAgY29sb3I6ICM2MDYyNjY7Cn0KCi5iYXRjaC10aXAgewogIG1hcmdpbi10b3A6IDEycHggIWltcG9ydGFudDsKICBjb2xvcjogIzkwOTM5OTsKICBmb250LXNpemU6IDEycHg7Cn0KCi8qIOinhumikeaSreaUvuWZqCAqLwoudmlkZW8tcGxheWVyIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7Cn0KCi52aWRlby1wbGF5ZXIgdmlkZW8gewogIG1heC13aWR0aDogMTAwJTsKICBib3JkZXItcmFkaXVzOiA4cHg7Cn0KCi8qIOWTjeW6lOW8j+iuvuiuoSAqLwpAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHsKICAudmlkZW8tcHJvZ3Jlc3MgewogICAgcGFkZGluZzogMTVweDsKICB9CgogIC5oZWFkZXItdG9vbGJhciB7CiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgZ2FwOiAxNnB4OwogICAgdGV4dC1hbGlnbjogY2VudGVyOwogIH0KCiAgLnRvb2xiYXItcmlnaHQgewogICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgfQoKICAuc3RhdHMtY2FyZHMgewogICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMiwgMWZyKTsKICAgIGdhcDogMTZweDsKICB9CgogIC52aWRlb3MtZ3JpZCB7CiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjsKICAgIGdhcDogMTZweDsKICB9Cn0K"}, {"version": 3, "sources": ["progress.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAycA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "progress.vue", "sourceRoot": "src/views/store", "sourcesContent": ["<template>\n  <div class=\"video-progress\">\n    <!-- 头部工具栏 -->\n    <div class=\"header-toolbar\">\n      <div class=\"toolbar-left\">\n        <el-button icon=\"el-icon-arrow-left\" @click=\"goBack\">返回</el-button>\n        <h2 class=\"page-title\">\n          <i class=\"el-icon-video-camera\"></i>\n          剪辑进度 - {{ templateInfo.name }}\n        </h2>\n      </div>\n      <div class=\"toolbar-right\">\n        <el-button icon=\"el-icon-refresh\" @click=\"refreshProgress\">\n          刷新\n        </el-button>\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"startNewBatch\">\n          新建批次\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 统计信息 -->\n    <div class=\"stats-cards\">\n      <div class=\"stat-card\">\n        <div class=\"stat-icon completed\">\n          <i class=\"el-icon-check\"></i>\n        </div>\n        <div class=\"stat-info\">\n          <div class=\"stat-number\">{{ stats.completed }}</div>\n          <div class=\"stat-label\">已完成</div>\n        </div>\n      </div>\n      <div class=\"stat-card\">\n        <div class=\"stat-icon processing\">\n          <i class=\"el-icon-loading\"></i>\n        </div>\n        <div class=\"stat-info\">\n          <div class=\"stat-number\">{{ stats.processing }}</div>\n          <div class=\"stat-label\">处理中</div>\n        </div>\n      </div>\n      <div class=\"stat-card\">\n        <div class=\"stat-icon failed\">\n          <i class=\"el-icon-close\"></i>\n        </div>\n        <div class=\"stat-info\">\n          <div class=\"stat-number\">{{ stats.failed }}</div>\n          <div class=\"stat-label\">失败</div>\n        </div>\n      </div>\n      <div class=\"stat-card\">\n        <div class=\"stat-icon total\">\n          <i class=\"el-icon-document\"></i>\n        </div>\n        <div class=\"stat-info\">\n          <div class=\"stat-number\">{{ stats.total }}</div>\n          <div class=\"stat-label\">总计</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 视频列表 -->\n    <div class=\"videos-container\">\n      <div class=\"container-header\">\n        <h3>生成的视频</h3>\n        <div class=\"filter-controls\">\n          <el-select v-model=\"statusFilter\" placeholder=\"状态筛选\" size=\"small\" clearable>\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"已完成\" value=\"completed\"></el-option>\n            <el-option label=\"处理中\" value=\"processing\"></el-option>\n            <el-option label=\"失败\" value=\"failed\"></el-option>\n          </el-select>\n        </div>\n      </div>\n\n      <div class=\"videos-grid\">\n        <div \n          v-for=\"video in filteredVideos\" \n          :key=\"video.id\"\n          class=\"video-card\"\n          :class=\"video.status\"\n        >\n          <!-- 视频缩略图 -->\n          <div class=\"video-thumbnail\">\n            <img v-if=\"video.thumbnail\" :src=\"video.thumbnail\" :alt=\"video.name\" />\n            <div v-else class=\"thumbnail-placeholder\">\n              <i class=\"el-icon-video-camera\"></i>\n            </div>\n            \n            <!-- 状态覆盖层 -->\n            <div class=\"status-overlay\" v-if=\"video.status !== 'completed'\">\n              <div class=\"status-content\">\n                <i v-if=\"video.status === 'processing'\" class=\"el-icon-loading rotating\"></i>\n                <i v-else-if=\"video.status === 'failed'\" class=\"el-icon-warning-outline\"></i>\n                <p class=\"status-text\">{{ getStatusText(video.status) }}</p>\n                <div v-if=\"video.status === 'processing'\" class=\"progress-bar\">\n                  <div class=\"progress-fill\" :style=\"{ width: video.progress + '%' }\"></div>\n                </div>\n              </div>\n            </div>\n\n            <!-- 播放按钮 -->\n            <div v-if=\"video.status === 'completed'\" class=\"play-button\" @click=\"playVideo(video)\">\n              <i class=\"el-icon-video-play\"></i>\n            </div>\n\n            <!-- 视频时长 -->\n            <div v-if=\"video.duration\" class=\"video-duration\">{{ video.duration }}</div>\n          </div>\n\n          <!-- 视频信息 -->\n          <div class=\"video-info\">\n            <h4 class=\"video-name\">{{ video.name }}</h4>\n            <div class=\"video-meta\">\n              <span class=\"create-time\">{{ video.createTime }}</span>\n              <span class=\"video-size\" v-if=\"video.fileSize\">{{ video.fileSize }}</span>\n            </div>\n            \n            <!-- 操作按钮 -->\n            <div class=\"video-actions\" v-if=\"video.status === 'completed'\">\n              <el-button size=\"mini\" icon=\"el-icon-download\" @click=\"downloadVideo(video)\">\n                下载\n              </el-button>\n              <el-button size=\"mini\" icon=\"el-icon-share\" @click=\"shareVideo(video)\">\n                分享\n              </el-button>\n              <el-button size=\"mini\" icon=\"el-icon-delete\" type=\"danger\" @click=\"deleteVideo(video)\">\n                删除\n              </el-button>\n            </div>\n            \n            <!-- 重试按钮 -->\n            <div class=\"video-actions\" v-if=\"video.status === 'failed'\">\n              <el-button size=\"mini\" icon=\"el-icon-refresh\" type=\"primary\" @click=\"retryVideo(video)\">\n                重试\n              </el-button>\n              <el-button size=\"mini\" icon=\"el-icon-delete\" type=\"danger\" @click=\"deleteVideo(video)\">\n                删除\n              </el-button>\n            </div>\n          </div>\n        </div>\n\n        <!-- 空状态 -->\n        <div v-if=\"filteredVideos.length === 0\" class=\"empty-state\">\n          <i class=\"el-icon-video-camera-solid\"></i>\n          <h3>暂无视频</h3>\n          <p>还没有生成任何视频，点击\"新建批次\"开始剪辑</p>\n          <el-button type=\"primary\" @click=\"startNewBatch\">\n            新建批次\n          </el-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 新建批次对话框 -->\n    <el-dialog\n      title=\"新建剪辑批次\"\n      :visible.sync=\"batchDialogVisible\"\n      width=\"400px\"\n    >\n      <div class=\"batch-config\">\n        <h4>{{ templateInfo.name }}</h4>\n        <p>请选择要生成的视频数量：</p>\n        <el-input-number\n          v-model=\"batchCount\"\n          :min=\"1\"\n          :max=\"50\"\n          label=\"生成数量\"\n        ></el-input-number>\n        <p class=\"batch-tip\">最多可生成50条视频</p>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"batchDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmNewBatch\">开始剪辑</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 视频播放对话框 -->\n    <el-dialog\n      title=\"视频预览\"\n      :visible.sync=\"playDialogVisible\"\n      width=\"800px\"\n      @close=\"stopVideo\"\n    >\n      <div class=\"video-player\" v-if=\"currentVideo\">\n        <video \n          ref=\"videoPlayer\"\n          :src=\"currentVideo.url\" \n          controls \n          width=\"100%\"\n          @loadedmetadata=\"onVideoLoaded\"\n        ></video>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'VideoProgress',\n  data() {\n    return {\n      // 模板信息\n      templateInfo: {\n        id: '',\n        name: '视频模板'\n      },\n      \n      // 对话框状态\n      batchDialogVisible: false,\n      playDialogVisible: false,\n      \n      // 批量剪辑数量\n      batchCount: 1,\n      \n      // 当前播放视频\n      currentVideo: null,\n      \n      // 状态筛选\n      statusFilter: '',\n      \n      // 统计数据\n      stats: {\n        completed: 8,\n        processing: 2,\n        failed: 1,\n        total: 11\n      },\n      \n      // 视频列表\n      videos: [\n        {\n          id: 'v001',\n          name: '产品展示视频_001',\n          status: 'completed',\n          thumbnail: 'https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=Video+1',\n          url: 'https://example.com/video1.mp4',\n          duration: '00:32',\n          fileSize: '15.2MB',\n          createTime: '2025-01-18 14:30',\n          progress: 100\n        },\n        {\n          id: 'v002',\n          name: '产品展示视频_002',\n          status: 'completed',\n          thumbnail: 'https://via.placeholder.com/300x200/50C878/FFFFFF?text=Video+2',\n          url: 'https://example.com/video2.mp4',\n          duration: '00:28',\n          fileSize: '12.8MB',\n          createTime: '2025-01-18 14:32',\n          progress: 100\n        },\n        {\n          id: 'v003',\n          name: '产品展示视频_003',\n          status: 'processing',\n          thumbnail: null,\n          url: null,\n          duration: null,\n          fileSize: null,\n          createTime: '2025-01-18 14:35',\n          progress: 65\n        },\n        {\n          id: 'v004',\n          name: '产品展示视频_004',\n          status: 'failed',\n          thumbnail: null,\n          url: null,\n          duration: null,\n          fileSize: null,\n          createTime: '2025-01-18 14:36',\n          progress: 0\n        }\n      ]\n    }\n  },\n  \n  computed: {\n    // 筛选后的视频列表\n    filteredVideos() {\n      if (!this.statusFilter) {\n        return this.videos\n      }\n      return this.videos.filter(video => video.status === this.statusFilter)\n    }\n  },\n  \n  created() {\n    // 获取模板ID\n    const templateId = this.$route.query.templateId\n    if (templateId) {\n      this.loadTemplate(templateId)\n    }\n    \n    // 定时刷新处理中的视频状态\n    this.startProgressPolling()\n  },\n  \n  beforeDestroy() {\n    // 清理定时器\n    if (this.pollingTimer) {\n      clearInterval(this.pollingTimer)\n    }\n  },\n  \n  methods: {\n    // 返回上一页\n    goBack() {\n      this.$router.go(-1)\n    },\n    \n    // 加载模板信息\n    loadTemplate(templateId) {\n      this.templateInfo.id = templateId\n      // TODO: 从API加载模板数据\n      console.log('加载模板:', templateId)\n    },\n    \n    // 刷新进度\n    refreshProgress() {\n      this.$message.success('进度已刷新')\n      // TODO: 调用API刷新数据\n    },\n    \n    // 开始新批次\n    startNewBatch() {\n      this.batchDialogVisible = true\n    },\n    \n    // 确认新建批次\n    confirmNewBatch() {\n      this.$message.success(`开始生成 ${this.batchCount} 条新视频`)\n      this.batchDialogVisible = false\n      \n      // TODO: 调用批量剪辑API\n      // 模拟添加新的处理中视频\n      for (let i = 0; i < this.batchCount; i++) {\n        const newVideo = {\n          id: 'v' + Date.now() + i,\n          name: `产品展示视频_${this.videos.length + i + 1}`,\n          status: 'processing',\n          thumbnail: null,\n          url: null,\n          duration: null,\n          fileSize: null,\n          createTime: new Date().toLocaleString().slice(0, 16),\n          progress: 0\n        }\n        this.videos.unshift(newVideo)\n      }\n      \n      // 更新统计\n      this.updateStats()\n    },\n    \n    // 播放视频\n    playVideo(video) {\n      this.currentVideo = video\n      this.playDialogVisible = true\n    },\n    \n    // 停止视频\n    stopVideo() {\n      if (this.$refs.videoPlayer) {\n        this.$refs.videoPlayer.pause()\n      }\n      this.currentVideo = null\n    },\n    \n    // 视频加载完成\n    onVideoLoaded() {\n      // 视频元数据加载完成\n    },\n    \n    // 下载视频\n    downloadVideo(video) {\n      this.$message.success(`开始下载: ${video.name}`)\n      // TODO: 实现下载逻辑\n    },\n    \n    // 分享视频\n    shareVideo(video) {\n      this.$message.success(`分享链接已复制: ${video.name}`)\n      // TODO: 实现分享逻辑\n    },\n    \n    // 删除视频\n    deleteVideo(video) {\n      this.$confirm(`确定要删除视频 \"${video.name}\" 吗？`, '确认删除', {\n        type: 'warning'\n      }).then(() => {\n        const index = this.videos.findIndex(v => v.id === video.id)\n        if (index > -1) {\n          this.videos.splice(index, 1)\n          this.updateStats()\n          this.$message.success('删除成功')\n        }\n      }).catch(() => {})\n    },\n    \n    // 重试视频\n    retryVideo(video) {\n      video.status = 'processing'\n      video.progress = 0\n      this.updateStats()\n      this.$message.success(`重新开始处理: ${video.name}`)\n      // TODO: 调用重试API\n    },\n    \n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        'completed': '已完成',\n        'processing': '处理中',\n        'failed': '处理失败'\n      }\n      return statusMap[status] || status\n    },\n    \n    // 更新统计数据\n    updateStats() {\n      this.stats = {\n        completed: this.videos.filter(v => v.status === 'completed').length,\n        processing: this.videos.filter(v => v.status === 'processing').length,\n        failed: this.videos.filter(v => v.status === 'failed').length,\n        total: this.videos.length\n      }\n    },\n    \n    // 开始轮询进度\n    startProgressPolling() {\n      this.pollingTimer = setInterval(() => {\n        // 模拟进度更新\n        this.videos.forEach(video => {\n          if (video.status === 'processing' && video.progress < 100) {\n            video.progress += Math.random() * 10\n            if (video.progress >= 100) {\n              video.status = 'completed'\n              video.progress = 100\n              video.thumbnail = `https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=Video+${video.id}`\n              video.url = `https://example.com/${video.id}.mp4`\n              video.duration = '00:' + (25 + Math.floor(Math.random() * 20))\n              video.fileSize = (10 + Math.random() * 10).toFixed(1) + 'MB'\n              this.updateStats()\n            }\n          }\n        })\n      }, 3000)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.video-progress {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n}\n\n/* 头部工具栏 */\n.header-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  padding: 20px;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.toolbar-left {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.page-title {\n  margin: 0;\n  color: #303133;\n  font-size: 20px;\n  font-weight: 600;\n}\n\n.page-title i {\n  margin-right: 8px;\n  color: #409EFF;\n}\n\n.toolbar-right {\n  display: flex;\n  gap: 12px;\n}\n\n/* 统计卡片 */\n.stats-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  padding: 24px;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.stat-icon {\n  width: 48px;\n  height: 48px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20px;\n  color: white;\n}\n\n.stat-icon.completed {\n  background: #67C23A;\n}\n\n.stat-icon.processing {\n  background: #409EFF;\n}\n\n.stat-icon.failed {\n  background: #F56C6C;\n}\n\n.stat-icon.total {\n  background: #909399;\n}\n\n.stat-info {\n  flex: 1;\n}\n\n.stat-number {\n  font-size: 28px;\n  font-weight: 700;\n  color: #303133;\n  line-height: 1;\n  margin-bottom: 4px;\n}\n\n.stat-label {\n  color: #909399;\n  font-size: 14px;\n}\n\n/* 视频容器 */\n.videos-container {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n}\n\n.container-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.container-header h3 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #303133;\n}\n\n/* 视频网格 */\n.videos-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: 20px;\n  padding: 24px;\n}\n\n/* 视频卡片 */\n.video-card {\n  background: #f8f9fa;\n  border-radius: 8px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n.video-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n\n/* 视频缩略图 */\n.video-thumbnail {\n  position: relative;\n  width: 100%;\n  height: 160px;\n  background: #000;\n  overflow: hidden;\n}\n\n.video-thumbnail img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.thumbnail-placeholder {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #ddd;\n  color: #999;\n}\n\n.thumbnail-placeholder i {\n  font-size: 32px;\n}\n\n/* 状态覆盖层 */\n.status-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n}\n\n.status-content {\n  text-align: center;\n}\n\n.status-content i {\n  font-size: 32px;\n  margin-bottom: 12px;\n  display: block;\n}\n\n.status-text {\n  margin: 0 0 12px 0;\n  font-size: 14px;\n}\n\n.progress-bar {\n  width: 120px;\n  height: 4px;\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: 2px;\n  overflow: hidden;\n  margin: 0 auto;\n}\n\n.progress-fill {\n  height: 100%;\n  background: #409EFF;\n  transition: width 0.3s ease;\n}\n\n/* 播放按钮 */\n.play-button {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 48px;\n  height: 48px;\n  background: rgba(0, 0, 0, 0.7);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 20px;\n  cursor: pointer;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.video-card:hover .play-button {\n  opacity: 1;\n}\n\n.play-button:hover {\n  background: rgba(0, 0, 0, 0.9);\n}\n\n/* 视频时长 */\n.video-duration {\n  position: absolute;\n  bottom: 8px;\n  right: 8px;\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-size: 12px;\n}\n\n/* 视频信息 */\n.video-info {\n  padding: 16px;\n}\n\n.video-name {\n  margin: 0 0 8px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #303133;\n  line-height: 1.4;\n}\n\n.video-meta {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 12px;\n  font-size: 12px;\n  color: #909399;\n}\n\n.video-actions {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n/* 旋转动画 */\n.rotating {\n  animation: rotate 2s linear infinite;\n}\n\n@keyframes rotate {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 空状态 */\n.empty-state {\n  grid-column: 1 / -1;\n  text-align: center;\n  padding: 60px 20px;\n}\n\n.empty-state i {\n  font-size: 48px;\n  color: #C0C4CC;\n  margin-bottom: 16px;\n}\n\n.empty-state h3 {\n  margin: 0 0 8px 0;\n  color: #303133;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.empty-state p {\n  margin: 0 0 20px 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n/* 批量配置 */\n.batch-config {\n  text-align: center;\n  padding: 20px 0;\n}\n\n.batch-config h4 {\n  margin: 0 0 20px 0;\n  color: #303133;\n  font-size: 18px;\n}\n\n.batch-config p {\n  margin: 0 0 16px 0;\n  color: #606266;\n}\n\n.batch-tip {\n  margin-top: 12px !important;\n  color: #909399;\n  font-size: 12px;\n}\n\n/* 视频播放器 */\n.video-player {\n  text-align: center;\n}\n\n.video-player video {\n  max-width: 100%;\n  border-radius: 8px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .video-progress {\n    padding: 15px;\n  }\n\n  .header-toolbar {\n    flex-direction: column;\n    gap: 16px;\n    text-align: center;\n  }\n\n  .toolbar-right {\n    justify-content: center;\n  }\n\n  .stats-cards {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 16px;\n  }\n\n  .videos-grid {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n}\n</style>\n"]}]}