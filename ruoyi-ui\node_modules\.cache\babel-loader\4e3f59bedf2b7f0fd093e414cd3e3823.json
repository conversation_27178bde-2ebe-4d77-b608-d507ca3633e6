{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\up.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\up.vue", "mtime": 1754971237942}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\babel.config.js", "mtime": 1744968028000}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753759483709}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ossUpload", "require", "_auth", "_store", "_interopRequireDefault", "name", "data", "_ref", "materialTab", "selectedFolder", "selectAll", "selectedFiles", "currentPage", "pageSize", "bgmFolderTree", "sucaiFolderTree", "uploadDialogVisible", "uploading", "fileList", "uploadForm", "uploadProgress", "storageType", "ossConfigVisible", "testingOSS", "ossConfig", "bucket", "accessKeyId", "accessKeySecret", "endpoint", "status", "ossInitialized", "currentFolder", "folderDialogVisible", "newFolderName", "renameFolderName", "_defineProperty2", "default", "id", "type", "size", "uploadTime", "duration", "url", "computed", "currentMaterialList", "bgmList", "sucaiList", "currentFolderTree", "filteredMaterialList", "list", "paginatedMaterials", "start", "end", "slice", "totalPages", "Math", "ceil", "length", "uploadDialogTitle", "uploadTipText", "created", "_this", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "w", "_context", "n", "loadOSSConfig", "loadMaterialList", "a", "methods", "openBGMDownloadSite", "window", "open", "$message", "success", "_this2", "_callee2", "_context2", "console", "log", "loadFilesFromOSS", "updateCurrentTabFolderCounts", "_this3", "_callee3", "client", "user", "_t", "_context3", "p", "getOSSClient", "getCurrentUser", "getCurrentFolder", "loadFilesFromFolder", "concat", "v", "error", "baseFolder", "folder", "listType", "_this4", "_callee4", "prefix", "result", "files", "_this4$bgmList", "_this4$sucaiList", "_t2", "_context4", "objects", "filter", "obj", "endsWith", "map", "index", "fileName", "split", "pop", "fileExtension", "toLowerCase", "audioExts", "videoExts", "fileType", "includes", "Date", "now", "random", "lastModified", "toLocaleString", "resolution", "undefined", "bitrate", "replace", "ossFileName", "push", "apply", "_toConsumableArray2", "savedConfig", "localStorage", "getItem", "_objectSpread2", "JSON", "parse", "initializeOSS", "saveOSSConfig", "_this5", "_callee5", "hostname", "match", "_match", "region", "_t3", "_t4", "_context5", "startsWith", "URL", "_slicedToArray2", "setItem", "stringify", "message", "f", "_this6", "_callee6", "_t5", "_context6", "initOSSClient", "loadUserFoldersFromOSS", "openOSSConfig", "showCreateFolderDialog", "createFolderForm", "createFolderVisible", "createFolder", "_this7", "_callee7", "folderName", "currentFolders", "newFolder", "_context7", "trim", "some", "count", "createOSSFolderAsync", "_this8", "_callee8", "_t6", "_context8", "createOSSFolderForCurrentTab", "warn", "createOSSFolder", "_this9", "_callee9", "baseFolders", "_i", "_baseFolders", "folderPath", "_t7", "_context9", "put", "<PERSON><PERSON><PERSON>", "from", "_this0", "_callee0", "_t8", "_context0", "Error", "handleFileDoubleClick", "file", "previewFile", "toggleVideoPlay", "videoRef", "$refs", "video", "paused", "pauseAllVideos", "muted", "play", "$set", "startControlTimer", "pause", "clearControlTimer", "toggleVideoPlayWithScale", "_this1", "pauseAllVideosAndResetScale", "handleLoadedMetadata", "videoWidth", "videoHeight", "max<PERSON><PERSON><PERSON>", "min", "innerWidth", "maxHeight", "innerHeight", "aspectRatio", "displayWidth", "displayHeight", "round", "removeEventListener", "readyState", "addEventListener", "isScaled", "addBackdrop", "$nextTick", "removeBackdrop", "_this10", "for<PERSON>ach", "isPlaying", "_this11", "_this12", "backdrop", "document", "createElement", "className", "body", "append<PERSON><PERSON><PERSON>", "getElementById", "remove", "getVideoProgress", "currentTime", "getCurrentTime", "getDuration", "formatTime", "seconds", "isNaN", "minutes", "floor", "remainingSeconds", "toString", "padStart", "seekVideo", "event", "progressBar", "currentTarget", "querySelector", "rect", "getBoundingClientRect", "clickX", "clientX", "left", "percentage", "width", "newTime", "onVideoTimeUpdate", "$forceUpdate", "onVideoLoadedData", "toggleAudioPlay", "audioRef", "audio", "pauseAllMedia", "pauseAllAudios", "_this13", "currentPreviewFile", "previewVisible", "getFileUrl", "stopAllMedia", "previewVideo", "previewAudio", "onVideoLoaded", "target", "onVideoEnded", "onVideoPaused", "_this14", "hoveredFile", "syncPortalVideo", "onVideoPlayed", "_this15", "onAudioEnded", "onAudioPaused", "onAudioPlayed", "onImageError", "style", "display", "parentNode", "innerHTML", "onVideoMouseEnter", "onVideoMouseLeave", "onVideoMouseMove", "onAudioMouseEnter", "onAudioMouseLeave", "onAudioMouseMove", "onImageMouseEnter", "onImageMouseLeave", "_this16", "controlTimers", "setTimeout", "fileId", "clearTimeout", "formatFileSize", "bytes", "k", "sizes", "i", "parseFloat", "pow", "toFixed", "downloadFile", "link", "href", "download", "click", "<PERSON><PERSON><PERSON><PERSON>", "_this17", "_callee1", "_t9", "_context1", "loadFoldersForType", "treeProperty", "_this18", "_callee10", "folderSet", "folders", "defaultFolder", "_t0", "_context10", "Set", "delimiter", "prefixes", "prefixInfo", "fullPrefix", "add", "object<PERSON>ey", "relativePath", "Array", "has", "unshift", "updateFolderFileCounts", "_this19", "_callee11", "_i2", "_arr", "totalCount", "_i3", "_baseFolders2", "fileCount", "_t1", "_t10", "_context11", "simpleFolderTree", "showFolderContextMenu", "contextMenuVisible", "contextMenuX", "contextMenuY", "clientY", "contextMenuFolder", "hideFolderContextMenu", "renameFolderAction", "_this20", "_callee13", "_context13", "$prompt", "confirmButtonText", "cancelButtonText", "inputValue", "then", "_ref3", "_callee12", "_ref2", "value", "oldFolderName", "_t11", "_context12", "renameOSSFolder", "_typeof2", "_x", "arguments", "deleteFolderAction", "_this21", "_callee15", "_context15", "warning", "$confirm", "_callee14", "targetFolderName", "_t12", "_t13", "_context14", "deleteOSSFolderForCurrentTab", "removeFilesFromFolder", "$store", "state", "buildOSSPath", "copyAndDeleteOSSFile", "old<PERSON><PERSON>", "newPath", "_callee16", "_t14", "_context16", "copy", "delete", "_this22", "_callee17", "_i4", "_baseFolders3", "oldPrefix", "newPrefix", "_iterator", "_step", "new<PERSON>ey", "_iterator2", "_step2", "_obj", "_t15", "_t16", "_t17", "_context17", "_createForOfIteratorHelper2", "s", "done", "e", "deleteOSSFolder", "_this23", "_callee18", "totalDeletedFiles", "_i5", "_baseFolders4", "_iterator3", "_step3", "_t18", "_t19", "_context18", "_this24", "_callee19", "deletedFiles", "_iterator4", "_step4", "_t20", "_t21", "_context19", "fileFolderName", "treeIndex", "findIndex", "splice", "selectFolder", "_this25", "_callee20", "_context20", "currentMaterials", "handleSelectAll", "checked", "handlePreview", "_this26", "find", "toggleFileSelection", "indexOf", "getSimpleFileIcon", "iconMap", "switchTab", "tab", "_this27", "tabNames", "handleRename", "_this28", "_callee22", "_context22", "_ref6", "_callee21", "_ref5", "newFileName", "old<PERSON>s<PERSON>ile<PERSON><PERSON>", "_baseFolderName", "_fullPath", "baseFolderName", "fullPath", "newOssFilePath", "_t22", "_t23", "_context21", "_x2", "handleDelete", "_this29", "_callee25", "_context25", "_callee24", "currentList", "filesToDelete", "deletePromises", "deleteResults", "failedDeletes", "_t25", "_context24", "_ref8", "_callee23", "ossFile<PERSON>ath", "_t24", "_context23", "deleteFileFromOSS", "_x3", "Promise", "all", "showUploadDialog", "beforeUpload", "currentTab", "maxSize", "isValidSize", "extension", "imageExts", "join", "allExts", "isValidFileType", "videoTypes", "videoExtensions", "audioTypes", "audioExtensions", "handleUploadSuccess", "response", "newFile", "videoList", "musicList", "handleUploadError", "err", "handleUploadProgress", "progress", "loaded", "total", "handleFileChange", "handleRemove", "submitUpload", "_this30", "_callee26", "actualFiles", "uploadResults", "_t26", "_t27", "_context26", "fileItem", "raw", "File", "uploadFilesToOSS", "originalName", "actualFileType", "r", "progressInterval", "setInterval", "currentProgress", "clearInterval", "createObjectURL"], "sources": ["src/views/store/up.vue"], "sourcesContent": ["<template>\r\n  <div class=\"up-container\">\r\n    <!-- 顶部标签页 -->\r\n    <div class=\"materials-tabs\">\r\n      <div class=\"tab-buttons\">\r\n        <el-button\r\n          :type=\"materialTab === 'sucai' ? 'primary' : 'default'\"\r\n          @click=\"switchTab('sucai')\"\r\n          class=\"tab-button\">\r\n          素材上传\r\n        </el-button>\r\n        <el-button\r\n          :type=\"materialTab === 'bgm' ? 'primary' : 'default'\"\r\n          @click=\"switchTab('bgm')\"\r\n          class=\"tab-button\">\r\n          BGM上传\r\n        </el-button>\r\n\r\n        <!-- BGM免费下载按钮 -->\r\n        <el-button\r\n          v-if=\"materialTab === 'bgm'\"\r\n          type=\"success\"\r\n          size=\"small\"\r\n          icon=\"el-icon-download\"\r\n          @click=\"openBGMDownloadSite\"\r\n          class=\"bgm-download-button\">\r\n          免费在线下载\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <div class=\"materials-main\">\r\n      <!-- 左侧文件夹树 -->\r\n      <div class=\"folder-sidebar\">\r\n        <div class=\"folder-header\">\r\n          <div class=\"folder-actions\">\r\n            <el-button\r\n              type=\"primary\"\r\n              size=\"small\"\r\n              icon=\"el-icon-upload2\"\r\n              @click=\"showUploadDialog\"\r\n              style=\"height: 32px !important; width: 100% !important; max-width: 120px !important;\">\r\n              上传\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              size=\"small\"\r\n              icon=\"el-icon-folder-add\"\r\n              @click=\"showCreateFolderDialog\"\r\n              style=\"height: 32px !important; width: 100% !important; max-width: 120px !important;\">\r\n              新建文件夹\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"folder-list\">\r\n          <!-- 只有OSS配置成功后才显示文件夹 -->\r\n          <div v-if=\"ossInitialized\">\r\n            <div\r\n              v-for=\"folder in currentFolderTree\"\r\n              :key=\"folder.id\"\r\n              class=\"folder-item\"\r\n              :class=\"{ active: selectedFolder === folder.id }\"\r\n              @click=\"selectFolder(folder)\"\r\n              @contextmenu.prevent=\"showFolderContextMenu($event, folder)\">\r\n              <i class=\"folder-icon\">📁</i>\r\n              <span class=\"folder-name\">{{ folder.name }}</span>\r\n              <span class=\"folder-count\">{{ folder.count }}</span>\r\n            </div>\r\n          </div>\r\n\r\n\r\n          <!-- OSS未配置时的提示 -->\r\n          <div v-else class=\"no-oss-tip\">\r\n            <div class=\"tip-icon\">⚙️</div>\r\n            <div class=\"tip-text\">请先配置OSS存储</div>\r\n            <el-button type=\"primary\" size=\"small\" @click=\"openOSSConfig\">配置OSS</el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 文件夹右键菜单 -->\r\n        <div\r\n          v-show=\"contextMenuVisible\"\r\n          class=\"context-menu\"\r\n          :style=\"{ left: contextMenuX + 'px', top: contextMenuY + 'px' }\"\r\n          @click.stop>\r\n          <div class=\"menu-item\" @click=\"renameFolderAction\">\r\n            <i class=\"el-icon-edit\"></i>\r\n            重命名文件夹\r\n          </div>\r\n          <div class=\"menu-item\" @click=\"deleteFolderAction\" v-if=\"contextMenuFolder && (contextMenuFolder.name || contextMenuFolder) !== '总'\">\r\n            <i class=\"el-icon-delete\"></i>\r\n            删除文件夹\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧文件区域 -->\r\n      <div class=\"files-area\">\r\n        <!-- OSS配置成功后显示文件管理界面 -->\r\n        <div v-if=\"ossInitialized\" class=\"files-toolbar\">\r\n          <div class=\"toolbar-left\">\r\n            <el-checkbox v-model=\"selectAll\" @change=\"handleSelectAll\">全选</el-checkbox>\r\n            <span class=\"file-actions\">\r\n              <span class=\"action-text\" @click=\"handlePreview\" :disabled=\"selectedFiles.length !== 1\">预览</span>\r\n              <span class=\"action-text\" @click=\"handleRename\">重命名</span>\r\n              <span class=\"action-text\" @click=\"handleDelete\">删除</span>\r\n            </span>\r\n          </div>\r\n          <div class=\"toolbar-right\">\r\n            <span class=\"file-count\">共 {{ filteredMaterialList.length }} 项</span>\r\n            <div class=\"pagination-info\">\r\n              <span>{{ currentPage }}</span>\r\n              <span>/</span>\r\n              <span>{{ totalPages }}</span>\r\n              <span>页</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 文件列表 -->\r\n        <div class=\"files-content\">\r\n          <!-- OSS未配置时的提示 -->\r\n          <div v-if=\"!ossInitialized\" class=\"no-oss-files-tip\">\r\n            <div class=\"tip-content\">\r\n              <div class=\"tip-icon\">☁️</div>\r\n              <div class=\"tip-title\">请先配置OSS存储</div>\r\n              <div class=\"tip-description\">配置OSS后即可开始上传和管理文件</div>\r\n              <el-button type=\"primary\" @click=\"openOSSConfig\">配置OSS存储</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- OSS已配置时显示文件列表 -->\r\n          <div v-else-if=\"filteredMaterialList.length === 0\" class=\"empty-state\">\r\n            <div class=\"empty-icon\">📁</div>\r\n            <div class=\"empty-text\">暂无文件</div>\r\n          </div>\r\n          <div v-else>\r\n            <!-- 文件操作工具栏 -->\r\n            <div class=\"file-toolbar\">\r\n              <div class=\"toolbar-left\">\r\n                <el-checkbox v-model=\"selectAll\" @change=\"handleSelectAll\">全选</el-checkbox>\r\n                <span class=\"selected-count\" v-if=\"selectedFiles.length > 0\">\r\n                  已选择 {{ selectedFiles.length }} 个文件\r\n                </span>\r\n              </div>\r\n              <div class=\"toolbar-right\">\r\n                <el-button\r\n                  v-if=\"selectedFiles.length > 0\"\r\n                  type=\"danger\"\r\n                  size=\"small\"\r\n                  @click=\"handleDelete\">\r\n                  删除选中\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"file-grid\">\r\n            <div\r\n              v-for=\"file in paginatedMaterials\"\r\n              :key=\"file.id\"\r\n              class=\"file-card\"\r\n              :class=\"{\r\n                selected: selectedFiles.includes(file.id),\r\n                hovered: file.isHovered,\r\n                enlarged: file.isPlaying,\r\n                'scale-enlarged': file.isScaled\r\n              }\"\r\n              :style=\"file.isScaled && file.displayWidth && file.displayHeight ? {\r\n                width: file.displayWidth + 'px',\r\n                height: file.displayHeight + 'px'\r\n              } : {}\"\r\n              @click=\"toggleFileSelection(file.id)\"\r\n              @dblclick=\"handleFileDoubleClick(file)\">\r\n\r\n              <!-- 文件选择框 -->\r\n              <div class=\"file-checkbox\">\r\n                <el-checkbox :value=\"selectedFiles.includes(file.id)\" @change=\"toggleFileSelection(file.id)\"></el-checkbox>\r\n              </div>\r\n\r\n              <!-- 文件缩略图 -->\r\n              <div class=\"file-thumbnail\">\r\n                <!-- 视频缩略图 -->\r\n                <div v-if=\"file.type === 'video'\" class=\"video-thumbnail\"\r\n                     :class=\"{ playing: file.isPlaying }\"\r\n                     @mouseenter=\"onVideoMouseEnter(file)\"\r\n                     @mouseleave=\"onVideoMouseLeave(file)\"\r\n                     @mousemove=\"onVideoMouseMove(file)\"\r\n                     @click.stop=\"toggleVideoPlayWithScale(file)\">\r\n                  <video\r\n                    :ref=\"`video-${file.id}`\"\r\n                    :src=\"getFileUrl(file)\"\r\n                    preload=\"metadata\"\r\n                    :muted=\"!file.isPlaying\"\r\n                    class=\"thumbnail-video\"\r\n                    @loadedmetadata=\"onVideoLoaded\"\r\n                    @ended=\"onVideoEnded(file)\"\r\n                    @pause=\"onVideoPaused(file)\"\r\n                    @play=\"onVideoPlayed(file)\"\r\n                    @timeupdate=\"onVideoTimeUpdate(file)\"\r\n                    @loadeddata=\"onVideoLoadedData(file)\">\r\n                  </video>\r\n\r\n                  <!-- 小视频的简洁播放按钮 -->\r\n                  <div v-if=\"!file.isScaled\" class=\"simple-play-overlay\">\r\n                    <div class=\"play-button\">\r\n                      <i :class=\"file.isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'\"></i>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- 放大视频的进度条 - 悬浮在视频底部 -->\r\n                  <div v-if=\"file.isScaled\" class=\"video-controls-overlay\">\r\n                    <div class=\"progress-container\" @click.stop=\"seekVideo($event, file)\">\r\n                      <div class=\"progress-track\">\r\n                        <div class=\"progress-fill\" :style=\"{ width: getVideoProgress(file) + '%' }\"></div>\r\n                        <div class=\"progress-thumb\" :style=\"{ left: getVideoProgress(file) + '%' }\"></div>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"controls-bottom\">\r\n                      <div class=\"play-pause-btn\" @click.stop=\"toggleVideoPlayWithScale(file)\">\r\n                        <i :class=\"file.isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'\"></i>\r\n                      </div>\r\n                      <div class=\"time-display\">\r\n                        <span class=\"current-time\">{{ formatTime(getCurrentTime(file)) }}</span>\r\n                        <span class=\"separator\">/</span>\r\n                        <span class=\"total-time\">{{ formatTime(getDuration(file)) }}</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div class=\"duration-badge\" v-if=\"file.duration && !file.isScaled\">{{ file.duration }}</div>\r\n                </div>\r\n\r\n                <!-- 音频缩略图 -->\r\n                <div v-else-if=\"file.type === 'audio'\" class=\"audio-thumbnail\"\r\n                     :class=\"{ playing: file.isPlaying }\"\r\n                     @mouseenter=\"onAudioMouseEnter(file)\"\r\n                     @mouseleave=\"onAudioMouseLeave(file)\"\r\n                     @mousemove=\"onAudioMouseMove(file)\">\r\n                  <audio\r\n                    :ref=\"`audio-${file.id}`\"\r\n                    :src=\"getFileUrl(file)\"\r\n                    preload=\"metadata\"\r\n                    @ended=\"onAudioEnded(file)\"\r\n                    @pause=\"onAudioPaused(file)\"\r\n                    @play=\"onAudioPlayed(file)\">\r\n                  </audio>\r\n                  <div class=\"audio-icon\">🎵</div>\r\n                  <div class=\"audio-waveform\">\r\n                    <div class=\"wave-bar\" v-for=\"i in 12\" :key=\"i\"></div>\r\n                  </div>\r\n                  <div class=\"play-overlay-audio\"\r\n                       @click.stop=\"toggleAudioPlay(file)\"\r\n                       :class=\"{\r\n                         playing: file.isPlaying,\r\n                         visible: file.showControls || !file.isPlaying,\r\n                         hidden: file.isPlaying && !file.showControls\r\n                       }\">\r\n                    <i :class=\"file.isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'\"></i>\r\n                  </div>\r\n                  <div class=\"duration-badge\" v-if=\"file.duration\">{{ file.duration }}</div>\r\n                </div>\r\n\r\n                <!-- 图片缩略图 -->\r\n                <div v-else-if=\"file.type === 'image'\" class=\"image-thumbnail\"\r\n                     @click.stop=\"previewFile(file)\"\r\n                     @mouseenter=\"onImageMouseEnter(file)\"\r\n                     @mouseleave=\"onImageMouseLeave(file)\">\r\n                  <img\r\n                    :src=\"getFileUrl(file)\"\r\n                    :alt=\"file.name\"\r\n                    class=\"thumbnail-image\"\r\n                    @error=\"onImageError\">\r\n                  <div class=\"image-overlay\">\r\n                    <i class=\"el-icon-zoom-in\"></i>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 其他文件类型 -->\r\n                <div v-else class=\"file-icon-thumbnail\">\r\n                  <i :class=\"getSimpleFileIcon(file.type)\"></i>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 文件信息 -->\r\n              <div class=\"file-info\">\r\n                <div class=\"file-name\" :title=\"file.name\">{{ file.name }}</div>\r\n                <div class=\"file-meta\">\r\n                  <span class=\"file-size\">{{ formatFileSize(file.size) }}</span>\r\n                  <span class=\"file-time\">{{ file.uploadTime }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- OSS配置对话框 -->\r\n    <el-dialog\r\n      title=\"设置存储\"\r\n      :visible.sync=\"ossConfigVisible\"\r\n      width=\"600px\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"oss-config-dialog\"\r\n    >\r\n      <div class=\"oss-config-content\">\r\n        <!-- 存储方式选择 -->\r\n        <div class=\"storage-type-section\">\r\n          <div class=\"section-label\">存储方式</div>\r\n          <div class=\"storage-options\">\r\n            <el-radio v-model=\"ossConfig.storageType\" label=\"oss\" class=\"storage-radio\">\r\n              <span class=\"radio-text\">阿里云OSS</span>\r\n            </el-radio>\r\n          </div>\r\n          <div class=\"storage-description\">\r\n            切换阿里云OSS后，素材库需要重新上传至阿里云OSS\r\n          </div>\r\n        </div>\r\n\r\n        <!-- OSS配置表单 -->\r\n        <div class=\"oss-form-section\">\r\n          <div class=\"form-row\">\r\n            <div class=\"form-label required\">存储空间名称</div>\r\n            <el-input\r\n              v-model=\"ossConfig.bucket\"\r\n              placeholder=\"jkhghfgddedb\"\r\n              class=\"form-input\"\r\n            />\r\n          </div>\r\n\r\n          <div class=\"form-row\">\r\n            <div class=\"form-label required\">ACCESS_KEY</div>\r\n            <el-input\r\n              v-model=\"ossConfig.accessKeyId\"\r\n              placeholder=\"LTAI5tSgfoZwykU9M1qvThgq\"\r\n              class=\"form-input\"\r\n            />\r\n          </div>\r\n\r\n          <div class=\"form-row\">\r\n            <div class=\"form-label required\">SECRET_KEY</div>\r\n            <el-input\r\n              v-model=\"ossConfig.accessKeySecret\"\r\n              placeholder=\"******************************\"\r\n              class=\"form-input\"\r\n              show-password\r\n            />\r\n          </div>\r\n\r\n          <div class=\"form-row\">\r\n            <div class=\"form-label required\">空间域名</div>\r\n            <el-input\r\n              v-model=\"ossConfig.endpoint\"\r\n              placeholder=\"https://jkhghfgddedb.oss-cn-shanghai.aliyuncs.com\"\r\n              class=\"form-input\"\r\n            />\r\n            <div class=\"form-hint\">\r\n              请补全http://或https://，例如https://static.cloud.com\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 状态选择 -->\r\n          <div class=\"form-row\">\r\n            <div class=\"form-label\">状态</div>\r\n            <div class=\"status-options\">\r\n              <el-radio v-model=\"ossConfig.status\" label=\"disabled\" class=\"status-radio\">\r\n                <span class=\"radio-text\">关闭</span>\r\n              </el-radio>\r\n              <el-radio v-model=\"ossConfig.status\" label=\"enabled\" class=\"status-radio\">\r\n                <span class=\"radio-text\">开启</span>\r\n              </el-radio>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"ossConfigVisible = false\" class=\"cancel-btn\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveOSSConfig\" :loading=\"testingOSS\" class=\"confirm-btn\">\r\n          {{ testingOSS ? '测试连接中...' : '确定' }}\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 新建文件夹对话框 -->\r\n    <el-dialog\r\n      title=\"新建文件夹\"\r\n      :visible.sync=\"createFolderVisible\"\r\n      width=\"400px\"\r\n      :close-on-click-modal=\"false\">\r\n      <el-form :model=\"createFolderForm\" label-width=\"80px\">\r\n        <el-form-item label=\"文件夹名\">\r\n          <el-input\r\n            v-model=\"createFolderForm.name\"\r\n            placeholder=\"请输入文件夹名称\"\r\n            @keyup.enter.native=\"createFolder\">\r\n          </el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"createFolderVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"createFolder\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 文件预览对话框 -->\r\n    <el-dialog\r\n      title=\"文件预览\"\r\n      :visible.sync=\"previewVisible\"\r\n      width=\"80%\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"preview-dialog\"\r\n      @close=\"stopAllMedia\">\r\n      <div class=\"preview-content\" v-if=\"previewFile\">\r\n        <div class=\"preview-header\">\r\n          <h3>{{ previewFile.name }}</h3>\r\n          <div class=\"file-info\">\r\n            <span>大小: {{ formatFileSize(previewFile.size) }}</span>\r\n            <span>上传时间: {{ previewFile.uploadTime }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 视频预览 -->\r\n        <div v-if=\"currentPreviewFile && currentPreviewFile.type === 'video'\" class=\"video-preview\">\r\n          <video\r\n            ref=\"previewVideo\"\r\n            :src=\"currentPreviewFile.url\"\r\n            controls\r\n            preload=\"metadata\"\r\n            style=\"width: 100%; max-height: 500px;\">\r\n            您的浏览器不支持视频播放\r\n          </video>\r\n        </div>\r\n\r\n        <!-- 音频预览 -->\r\n        <div v-else-if=\"currentPreviewFile && currentPreviewFile.type === 'audio'\" class=\"audio-preview\">\r\n          <div class=\"audio-player\">\r\n            <audio\r\n              ref=\"previewAudio\"\r\n              :src=\"currentPreviewFile.url\"\r\n              controls\r\n              preload=\"metadata\"\r\n              style=\"width: 100%;\">\r\n              您的浏览器不支持音频播放\r\n            </audio>\r\n          </div>\r\n          <div class=\"audio-info\">\r\n            <div class=\"audio-icon\">🎵</div>\r\n            <div class=\"audio-details\">\r\n              <p><strong>{{ currentPreviewFile.name }}</strong></p>\r\n              <p v-if=\"currentPreviewFile.duration\">时长: {{ currentPreviewFile.duration }}</p>\r\n              <p v-if=\"currentPreviewFile.bitrate\">比特率: {{ currentPreviewFile.bitrate }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 图片预览 -->\r\n        <div v-else-if=\"currentPreviewFile && currentPreviewFile.type === 'image'\" class=\"image-preview\">\r\n          <img\r\n            :src=\"currentPreviewFile.url\"\r\n            :alt=\"currentPreviewFile.name\"\r\n            style=\"max-width: 100%; max-height: 500px;\">\r\n        </div>\r\n\r\n        <!-- 不支持预览的文件 -->\r\n        <div v-else class=\"unsupported-preview\">\r\n          <div class=\"unsupported-icon\">📄</div>\r\n          <p>此文件类型不支持预览</p>\r\n          <el-button type=\"primary\" @click=\"downloadFile(previewFile)\">下载文件</el-button>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 上传对话框 -->\r\n    <el-dialog :title=\"uploadDialogTitle\" :visible.sync=\"uploadDialogVisible\" width=\"600px\">\r\n      <div class=\"upload-content\">\r\n        <!-- 存储方式选择 -->\r\n        <div class=\"storage-selector\" style=\"margin-bottom: 20px;\">\r\n          <span style=\"margin-right: 10px;\">存储方式：</span>\r\n          <el-radio-group v-model=\"storageType\" size=\"small\">\r\n            <el-radio label=\"local\">本地存储</el-radio>\r\n            <el-radio label=\"oss\">阿里云OSS</el-radio>\r\n          </el-radio-group>\r\n          <el-button\r\n            v-if=\"storageType === 'oss'\"\r\n            type=\"text\"\r\n            size=\"small\"\r\n            @click=\"openOSSConfig\"\r\n            style=\"margin-left: 10px;\"\r\n          >\r\n            <i class=\"el-icon-setting\"></i> 配置OSS\r\n          </el-button>\r\n          <span\r\n            v-if=\"storageType === 'oss'\"\r\n            :class=\"ossInitialized ? 'text-success' : 'text-danger'\"\r\n            style=\"margin-left: 10px; font-size: 12px;\"\r\n          >\r\n            {{ ossInitialized ? '✓ 已配置' : '✗ 未配置' }}\r\n          </span>\r\n        </div>\r\n\r\n        <el-upload\r\n          class=\"upload-dragger\"\r\n          ref=\"upload\"\r\n          action=\"#\"\r\n          :multiple=\"true\"\r\n          :file-list=\"fileList\"\r\n          :before-upload=\"beforeUpload\"\r\n          :on-success=\"handleUploadSuccess\"\r\n          :on-error=\"handleUploadError\"\r\n          :on-progress=\"handleUploadProgress\"\r\n          :on-remove=\"handleRemove\"\r\n          :on-change=\"handleFileChange\"\r\n          :auto-upload=\"false\"\r\n          drag\r\n        >\r\n          <i class=\"el-icon-upload\"></i>\r\n          <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n          <div class=\"el-upload__tip\" slot=\"tip\">\r\n            {{ uploadTipText }}\r\n          </div>\r\n        </el-upload>\r\n\r\n        <!-- 上传进度显示 -->\r\n        <div v-if=\"uploading && Object.keys(uploadProgress).length > 0\" class=\"upload-progress\" style=\"margin-top: 20px;\">\r\n          <h4>上传进度</h4>\r\n          <div v-for=\"(progress, fileName) in uploadProgress\" :key=\"fileName\" class=\"progress-item\">\r\n            <div class=\"progress-info\">\r\n              <span class=\"file-name\">{{ fileName }}</span>\r\n              <span class=\"progress-text\">{{ progress }}%</span>\r\n            </div>\r\n            <el-progress :percentage=\"progress\" :show-text=\"false\"></el-progress>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"uploadDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitUpload\" :loading=\"uploading\">\r\n          {{ uploading ? '上传中...' : '开始上传' }}\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { initOSSClient, uploadFilesToOSS, getOSSFileList, deleteFileFromOSS, getOSSClient } from '@/utils/ossUpload'\r\nimport { getToken } from '@/utils/auth'\r\nimport store from '@/store'\r\n\r\nexport default {\r\n  name: 'StorerUp',\r\n  data() {\r\n    return {\r\n      // 界面控制\r\n      materialTab: 'sucai', // sucai, bgm\r\n      selectedFolder: 1,\r\n      selectAll: false,\r\n      selectedFiles: [],\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n\r\n      // 分别为BGM和素材维护文件夹结构\r\n      bgmFolderTree: [],\r\n      sucaiFolderTree: [],\r\n\r\n      // 上传相关\r\n      uploadDialogVisible: false,\r\n      uploading: false,\r\n      fileList: [],\r\n      uploadForm: {},\r\n      uploadProgress: {}, // 上传进度\r\n      storageType: 'oss', // 存储方式: local | oss\r\n\r\n      // OSS配置相关\r\n      ossConfigVisible: false,\r\n      testingOSS: false,\r\n      ossConfig: {\r\n        storageType: 'oss', // 存储方式\r\n        bucket: '', // 存储空间名称\r\n        accessKeyId: '', // ACCESS_KEY\r\n        accessKeySecret: '', // SECRET_KEY\r\n        endpoint: '', // 空间域名\r\n        status: 'enabled' // 状态：enabled/disabled\r\n      },\r\n      ossInitialized: false, // OSS是否已初始化\r\n\r\n      // 文件夹管理\r\n      currentFolder: '总', // 当前选中的文件夹\r\n      folderDialogVisible: false, // 文件夹管理对话框\r\n      newFolderName: '', // 新文件夹名称\r\n      renameFolderName: '', // 重命名文件夹名称\r\n      selectedFolder: '', // 选中的文件夹\r\n\r\n      // 右键菜单\r\n      contextMenuVisible: false,\r\n      contextMenuX: 0,\r\n      contextMenuY: 0,\r\n      contextMenuFolder: null,\r\n\r\n      // 新建文件夹\r\n      createFolderVisible: false,\r\n      createFolderForm: {\r\n        name: ''\r\n      },\r\n\r\n      // 文件预览\r\n      previewVisible: false,\r\n      currentPreviewFile: null,\r\n\r\n      // 控制按钮隐藏定时器\r\n      controlTimers: {},\r\n\r\n\r\n\r\n      // BGM文件数据 (abgm/admin/总/ 文件夹) - 添加测试数据\r\n      bgmList: [\r\n        {\r\n          id: 'bgm1',\r\n          name: '测试音频.mp3',\r\n          type: 'audio',\r\n          size: 512000,\r\n          uploadTime: '2024-01-01 12:00:00',\r\n          duration: '02:30',\r\n          url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'\r\n        }\r\n      ],\r\n\r\n      // 素材文件数据 (asucai/admin/总/ 文件夹) - 添加测试数据\r\n      sucaiList: [\r\n        {\r\n          id: 'sucai1',\r\n          name: '测试视频.mp4',\r\n          type: 'video',\r\n          size: 1024000,\r\n          uploadTime: '2024-01-01 12:00:00',\r\n          duration: '00:30',\r\n          url: 'https://www.w3schools.com/html/mov_bbb.mp4'\r\n        },\r\n        {\r\n          id: 'sucai2',\r\n          name: '测试图片.jpg',\r\n          type: 'image',\r\n          size: 256000,\r\n          uploadTime: '2024-01-01 12:00:00',\r\n          url: 'https://picsum.photos/400/300'\r\n        },\r\n        {\r\n          id: 'sucai3',\r\n          name: '测试音频.mp3',\r\n          type: 'audio',\r\n          size: 512000,\r\n          uploadTime: '2024-01-01 12:00:00',\r\n          duration: '01:45',\r\n          url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    // 根据当前标签页返回对应的文件列表\r\n    currentMaterialList() {\r\n      switch (this.materialTab) {\r\n        case 'bgm':\r\n          return this.bgmList\r\n        case 'sucai':\r\n          return this.sucaiList\r\n        default:\r\n          return this.sucaiList\r\n      }\r\n    },\r\n\r\n    // 根据当前标签页返回对应的文件夹列表\r\n    currentFolderTree() {\r\n      switch (this.materialTab) {\r\n        case 'bgm':\r\n          return this.bgmFolderTree\r\n        case 'sucai':\r\n          return this.sucaiFolderTree\r\n        default:\r\n          return this.sucaiFolderTree\r\n      }\r\n    },\r\n\r\n    filteredMaterialList() {\r\n      let list = this.currentMaterialList\r\n\r\n      // 按文件夹过滤（简化版本）\r\n      if (this.selectedFolder !== 1) {\r\n        // 这里可以根据实际需求添加过滤逻辑\r\n        // 目前显示所有文件\r\n      }\r\n\r\n      return list\r\n    },\r\n\r\n    paginatedMaterials() {\r\n      const start = (this.currentPage - 1) * this.pageSize\r\n      const end = start + this.pageSize\r\n      return this.filteredMaterialList.slice(start, end)\r\n    },\r\n\r\n    totalPages() {\r\n      return Math.ceil(this.filteredMaterialList.length / this.pageSize)\r\n    },\r\n\r\n    // 上传对话框相关计算属性\r\n    uploadDialogTitle() {\r\n      switch (this.materialTab) {\r\n        case 'bgm':\r\n          return '上传BGM文件'\r\n        case 'sucai':\r\n          return '上传素材文件'\r\n        default:\r\n          return '上传文件'\r\n      }\r\n    },\r\n\r\n    uploadTipText() {\r\n      switch (this.materialTab) {\r\n        case 'bgm':\r\n          return '支持 MP3、WAV、FLAC、AAC、M4A、OGG、WMA 等音频格式，单个文件不超过100MB'\r\n        case 'sucai':\r\n          return '支持各种视频、音频、图片格式，单个文件不超过500MB'\r\n        default:\r\n          return '请选择要上传的文件'\r\n      }\r\n    },\r\n\r\n\r\n  },\r\n  async created() {\r\n    // 先加载OSS配置\r\n    this.loadOSSConfig()\r\n\r\n    // 如果OSS已配置，则加载文件列表\r\n    if (this.ossInitialized) {\r\n      await this.loadMaterialList()\r\n    }\r\n  },\r\n  methods: {\r\n    // BGM免费下载\r\n    openBGMDownloadSite() {\r\n      window.open('https://www.buguyy.top/', '_blank')\r\n      this.$message.success('正在打开免费BGM下载网站...')\r\n    },\r\n\r\n    // 基础方法\r\n    async loadMaterialList() {\r\n      console.log('加载素材列表')\r\n\r\n      // 如果OSS已初始化，从OSS加载文件列表\r\n      if (this.ossInitialized) {\r\n        await this.loadFilesFromOSS()\r\n      } else {\r\n        console.log('OSS未初始化，跳过文件加载')\r\n      }\r\n\r\n      // 更新文件夹计数\r\n      this.updateCurrentTabFolderCounts()\r\n    },\r\n\r\n    // 从OSS加载文件列表\r\n    async loadFilesFromOSS() {\r\n      try {\r\n        const client = getOSSClient()\r\n        const user = this.getCurrentUser()\r\n        const currentFolder = this.getCurrentFolder()\r\n\r\n        // 清空现有列表\r\n        this.bgmList = []\r\n        this.sucaiList = []\r\n\r\n        // 加载BGM文件\r\n        await this.loadFilesFromFolder('abgm', user, currentFolder, 'bgm')\r\n\r\n        // 加载素材文件\r\n        await this.loadFilesFromFolder('asucai', user, currentFolder, 'sucai')\r\n\r\n        console.log(`从OSS加载文件完成 - BGM: ${this.bgmList.length}个, 素材: ${this.sucaiList.length}个`)\r\n\r\n      } catch (error) {\r\n        console.error('从OSS加载文件列表失败:', error)\r\n      }\r\n    },\r\n\r\n    // 从指定文件夹加载文件\r\n    async loadFilesFromFolder(baseFolder, user, folder, listType) {\r\n      try {\r\n        const client = getOSSClient()\r\n        const prefix = `${baseFolder}/${user}/${folder}/`\r\n\r\n        const result = await client.list({\r\n          prefix: prefix,\r\n          'max-keys': 1000\r\n        })\r\n\r\n        if (result.objects) {\r\n          const files = result.objects\r\n            .filter(obj => !obj.name.endsWith('.keep') && !obj.name.endsWith('/'))\r\n            .map((obj, index) => {\r\n              const fileName = obj.name.split('/').pop()\r\n              const fileExtension = fileName.toLowerCase().split('.').pop()\r\n\r\n              // 判断文件类型\r\n              const audioExts = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']\r\n              const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v']\r\n\r\n              let fileType = 'unknown'\r\n              if (audioExts.includes(fileExtension)) {\r\n                fileType = 'audio'\r\n              } else if (videoExts.includes(fileExtension)) {\r\n                fileType = 'video'\r\n              }\r\n\r\n              return {\r\n                id: Date.now() + Math.random() + index,\r\n                name: fileName,\r\n                type: fileType,\r\n                size: obj.size,\r\n                uploadTime: new Date(obj.lastModified).toLocaleString().slice(0, 16),\r\n                duration: fileType === 'video' ? '00:02:30' : '00:03:45', // 默认值，实际应该从文件元数据获取\r\n                resolution: fileType === 'video' ? '1920x1080' : undefined,\r\n                bitrate: fileType === 'audio' ? '128kbps' : undefined,\r\n                url: `https://${this.ossConfig.bucket}.${this.ossConfig.endpoint.replace('https://', '').replace(this.ossConfig.bucket + '.', '')}/${obj.name}`,\r\n                ossFileName: obj.name,\r\n                folder: `${baseFolder}/${user}/${folder}`\r\n              }\r\n            })\r\n\r\n          // 添加到对应的列表\r\n          if (listType === 'bgm') {\r\n            this.bgmList.push(...files)\r\n          } else if (listType === 'sucai') {\r\n            this.sucaiList.push(...files)\r\n          }\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error(`从文件夹 ${baseFolder}/${user}/${folder} 加载文件失败:`, error)\r\n      }\r\n    },\r\n\r\n    // OSS配置相关方法\r\n    loadOSSConfig() {\r\n      // 从localStorage加载OSS配置\r\n      const savedConfig = localStorage.getItem('ossConfig')\r\n      if (savedConfig) {\r\n        try {\r\n          this.ossConfig = { ...this.ossConfig, ...JSON.parse(savedConfig) }\r\n          if (this.ossConfig.accessKeyId && this.ossConfig.accessKeySecret) {\r\n            this.initializeOSS()\r\n          }\r\n        } catch (error) {\r\n          console.error('加载OSS配置失败:', error)\r\n        }\r\n      }\r\n    },\r\n\r\n    async saveOSSConfig() {\r\n      // 验证必填字段\r\n      if (!this.ossConfig.bucket) {\r\n        this.$message.error('请输入存储空间名称')\r\n        return\r\n      }\r\n      if (!this.ossConfig.accessKeyId) {\r\n        this.$message.error('请输入ACCESS_KEY')\r\n        return\r\n      }\r\n      if (!this.ossConfig.accessKeySecret) {\r\n        this.$message.error('请输入SECRET_KEY')\r\n        return\r\n      }\r\n      if (!this.ossConfig.endpoint) {\r\n        this.$message.error('请输入空间域名')\r\n        return\r\n      }\r\n\r\n      // 验证域名格式\r\n      if (!this.ossConfig.endpoint.startsWith('http://') && !this.ossConfig.endpoint.startsWith('https://')) {\r\n        this.$message.error('空间域名必须以http://或https://开头')\r\n        return\r\n      }\r\n\r\n      // 验证是否为标准的OSS域名格式\r\n      try {\r\n        const url = new URL(this.ossConfig.endpoint)\r\n        const hostname = url.hostname\r\n        const match = hostname.match(/^([^.]+)\\.oss-([^.]+)\\.aliyuncs\\.com$/)\r\n\r\n        if (!match) {\r\n          this.$message.error('请输入标准的OSS域名格式，如：https://bucket.oss-region.aliyuncs.com')\r\n          return\r\n        }\r\n\r\n        const [, bucket, region] = match\r\n        if (bucket !== this.ossConfig.bucket) {\r\n          this.$message.error(`域名中的存储桶名称(${bucket})与配置的存储桶名称(${this.ossConfig.bucket})不匹配`)\r\n          return\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('域名格式不正确')\r\n        return\r\n      }\r\n\r\n      this.testingOSS = true\r\n\r\n      try {\r\n        // 直接使用配置初始化OSS客户端\r\n        await this.initializeOSS()\r\n\r\n        // 保存配置到localStorage\r\n        localStorage.setItem('ossConfig', JSON.stringify(this.ossConfig))\r\n\r\n        this.ossConfigVisible = false\r\n        this.$message.success('OSS配置保存成功！')\r\n\r\n      } catch (error) {\r\n        console.error('OSS配置测试失败:', error)\r\n        this.$message.error(`OSS配置失败: ${error.message}`)\r\n      } finally {\r\n        this.testingOSS = false\r\n      }\r\n    },\r\n\r\n    async initializeOSS() {\r\n      try {\r\n        const client = initOSSClient(this.ossConfig)\r\n        this.ossInitialized = true\r\n\r\n        // OSS初始化成功后，获取用户的文件夹列表\r\n        await this.loadUserFoldersFromOSS()\r\n\r\n        console.log('OSS客户端初始化成功')\r\n        return client\r\n      } catch (error) {\r\n        this.ossInitialized = false\r\n        console.error('OSS客户端初始化失败:', error)\r\n        throw error\r\n      }\r\n    },\r\n\r\n    openOSSConfig() {\r\n      this.ossConfigVisible = true\r\n    },\r\n\r\n    // 显示新建文件夹对话框\r\n    showCreateFolderDialog() {\r\n      this.createFolderForm.name = ''\r\n      this.createFolderVisible = true\r\n    },\r\n\r\n    // 创建文件夹\r\n    async createFolder() {\r\n      console.log('🚀 开始创建文件夹')\r\n\r\n      if (!this.createFolderForm.name || this.createFolderForm.name.trim() === '') {\r\n        this.$message.error('文件夹名称不能为空')\r\n        return\r\n      }\r\n\r\n      const folderName = this.createFolderForm.name.trim()\r\n      console.log('📁 文件夹名称:', folderName)\r\n      console.log('📋 当前标签页:', this.materialTab)\r\n\r\n      // 检查当前标签页的文件夹是否已存在\r\n      const currentFolders = this.currentFolderTree\r\n      console.log('📂 当前文件夹列表:', currentFolders.map(f => f.name))\r\n\r\n      if (currentFolders.some(folder => folder.name === folderName)) {\r\n        this.$message.error(`${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹已存在`)\r\n        return\r\n      }\r\n\r\n      try {\r\n        console.log('✅ 开始创建本地文件夹记录')\r\n\r\n        // 先创建本地文件夹记录\r\n        const newFolder = {\r\n          id: Date.now() + Math.random(), // 使用时间戳避免ID冲突\r\n          name: folderName,\r\n          count: 0\r\n        }\r\n\r\n        if (this.materialTab === 'bgm') {\r\n          this.bgmFolderTree.push(newFolder)\r\n          console.log('📁 已添加到BGM文件夹树')\r\n        } else {\r\n          this.sucaiFolderTree.push(newFolder)\r\n          console.log('📁 已添加到素材文件夹树')\r\n        }\r\n\r\n        console.log('✅ 本地文件夹创建成功')\r\n        this.createFolderVisible = false\r\n        this.$message.success(`${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹创建成功`)\r\n\r\n        // 异步处理OSS创建，不阻塞UI\r\n        if (this.storageType === 'oss' && this.ossInitialized) {\r\n          this.createOSSFolderAsync(folderName)\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('❌ 创建文件夹失败:', error)\r\n        this.$message.error(`创建失败: ${error.message}`)\r\n      }\r\n    },\r\n\r\n    // 异步创建OSS文件夹，不阻塞UI\r\n    async createOSSFolderAsync(folderName) {\r\n      try {\r\n        console.log('🌐 开始异步创建OSS文件夹:', folderName)\r\n        await this.createOSSFolderForCurrentTab(folderName)\r\n        console.log('✅ OSS文件夹创建成功')\r\n      } catch (ossError) {\r\n        console.warn('⚠️ OSS文件夹创建失败:', ossError.message)\r\n      }\r\n    },\r\n\r\n    // OSS文件夹创建（旧方法，为两种类型都创建）\r\n    async createOSSFolder(folderName) {\r\n      try {\r\n        const client = getOSSClient()\r\n        const user = this.getCurrentUser()\r\n\r\n        // 为两种类型创建空文件夹（通过上传空文件实现）\r\n        const baseFolders = ['abgm', 'asucai']\r\n\r\n        for (const baseFolder of baseFolders) {\r\n          const folderPath = `${baseFolder}/${user}/${folderName}/.keep`\r\n\r\n          // 上传一个空文件来创建文件夹结构\r\n          await client.put(folderPath, Buffer.from(''))\r\n          console.log(`OSS文件夹创建: ${folderPath}`)\r\n        }\r\n\r\n        return true\r\n      } catch (error) {\r\n        console.error('OSS文件夹创建失败:', error)\r\n        throw error\r\n      }\r\n    },\r\n\r\n    // 只为当前标签页创建OSS文件夹\r\n    async createOSSFolderForCurrentTab(folderName) {\r\n      try {\r\n        console.log(`开始创建OSS文件夹: ${folderName} (${this.materialTab})`)\r\n\r\n        if (!folderName || !folderName.trim()) {\r\n          throw new Error('文件夹名称不能为空')\r\n        }\r\n\r\n        const client = getOSSClient()\r\n        if (!client) {\r\n          throw new Error('OSS客户端未初始化')\r\n        }\r\n\r\n        const user = this.getCurrentUser()\r\n        if (!user) {\r\n          throw new Error('无法获取当前用户信息')\r\n        }\r\n\r\n        // 根据当前标签页确定基础文件夹\r\n        const baseFolder = this.materialTab === 'bgm' ? 'abgm' : 'asucai'\r\n        const folderPath = `${baseFolder}/${user}/${folderName}/.keep`\r\n\r\n        console.log(`OSS文件夹路径: ${folderPath}`)\r\n\r\n        // 上传一个空文件来创建文件夹结构\r\n        await client.put(folderPath, Buffer.from(''))\r\n        console.log(`OSS文件夹创建成功 (${this.materialTab}): ${folderPath}`)\r\n\r\n        return true\r\n      } catch (error) {\r\n        console.error(`OSS文件夹创建失败 (${this.materialTab}):`, error)\r\n        throw new Error(`OSS文件夹创建失败: ${error.message}`)\r\n      }\r\n    },\r\n\r\n    // 处理文件双击事件\r\n    handleFileDoubleClick(file) {\r\n      // 只有视频、音频、图片类型才支持预览\r\n      if (['video', 'audio', 'image'].includes(file.type)) {\r\n        this.previewFile(file)\r\n      }\r\n    },\r\n\r\n    // 切换视频播放/暂停\r\n    toggleVideoPlay(file) {\r\n      const videoRef = this.$refs[`video-${file.id}`]\r\n      if (videoRef && videoRef.length > 0) {\r\n        const video = videoRef[0]\r\n\r\n        if (video.paused) {\r\n          // 暂停所有其他视频\r\n          this.pauseAllVideos()\r\n\r\n          // 取消静音并播放当前视频\r\n          video.muted = false\r\n          video.play()\r\n          this.$set(file, 'isPlaying', true)\r\n          this.$set(file, 'showControls', true)\r\n\r\n          // 开始3秒隐藏定时器\r\n          this.startControlTimer(file)\r\n        } else {\r\n          // 暂停当前视频并静音\r\n          video.pause()\r\n          video.muted = true\r\n          this.$set(file, 'isPlaying', false)\r\n          this.$set(file, 'showControls', true)\r\n\r\n          // 清除隐藏定时器\r\n          this.clearControlTimer(file.id)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 切换视频播放/暂停并缩放\r\n    toggleVideoPlayWithScale(file) {\r\n      const videoRef = this.$refs[`video-${file.id}`]\r\n      if (videoRef && videoRef.length > 0) {\r\n        const video = videoRef[0]\r\n\r\n        if (video.paused) {\r\n          // 暂停所有其他视频并取消缩放\r\n          this.pauseAllVideosAndResetScale()\r\n\r\n          // 等待视频元数据加载完成\r\n          const handleLoadedMetadata = () => {\r\n            // 获取视频的真实尺寸\r\n            const videoWidth = video.videoWidth\r\n            const videoHeight = video.videoHeight\r\n\r\n            if (videoWidth && videoHeight) {\r\n              // 计算合适的显示尺寸（最大宽度800px，最大高度600px）\r\n              const maxWidth = Math.min(800, window.innerWidth * 0.8)\r\n              const maxHeight = Math.min(600, window.innerHeight * 0.8)\r\n\r\n              const aspectRatio = videoWidth / videoHeight\r\n              let displayWidth, displayHeight\r\n\r\n              if (aspectRatio > maxWidth / maxHeight) {\r\n                // 视频比较宽，以宽度为准\r\n                displayWidth = maxWidth\r\n                displayHeight = maxWidth / aspectRatio\r\n              } else {\r\n                // 视频比较高，以高度为准\r\n                displayHeight = maxHeight\r\n                displayWidth = maxHeight * aspectRatio\r\n              }\r\n\r\n              // 设置视频的显示尺寸\r\n              this.$set(file, 'displayWidth', Math.round(displayWidth))\r\n              this.$set(file, 'displayHeight', Math.round(displayHeight))\r\n\r\n              console.log(`视频 ${file.name} 真实尺寸: ${videoWidth}x${videoHeight}, 显示尺寸: ${displayWidth}x${displayHeight}`)\r\n            }\r\n\r\n            // 移除事件监听器\r\n            video.removeEventListener('loadedmetadata', handleLoadedMetadata)\r\n          }\r\n\r\n          // 如果元数据已经加载，直接处理；否则等待加载\r\n          if (video.readyState >= 1) {\r\n            handleLoadedMetadata()\r\n          } else {\r\n            video.addEventListener('loadedmetadata', handleLoadedMetadata)\r\n          }\r\n\r\n          // 播放当前视频并放大\r\n          video.muted = false\r\n          video.play()\r\n          this.$set(file, 'isPlaying', true)\r\n          this.$set(file, 'isScaled', true) // 设置放大状态\r\n          this.$set(file, 'showControls', true)\r\n\r\n          console.log(`视频 ${file.name} 开始播放并放大，isScaled: ${file.isScaled}`)\r\n\r\n          // 添加背景遮罩\r\n          this.addBackdrop()\r\n\r\n          // 强制更新DOM以显示进度条\r\n          this.$nextTick(() => {\r\n            console.log('DOM更新完成，进度条应该显示')\r\n          })\r\n\r\n          // 开始3秒隐藏定时器\r\n          this.startControlTimer(file)\r\n        } else {\r\n          // 暂停视频并恢复大小\r\n          video.pause()\r\n          video.muted = true\r\n          this.$set(file, 'isPlaying', false)\r\n          this.$set(file, 'isScaled', false) // 取消放大状态\r\n          this.$set(file, 'displayWidth', null) // 清除显示宽度\r\n          this.$set(file, 'displayHeight', null) // 清除显示高度\r\n          this.$set(file, 'showControls', true)\r\n\r\n          console.log(`视频 ${file.name} 已暂停并恢复大小`)\r\n\r\n          // 移除背景遮罩\r\n          this.removeBackdrop()\r\n\r\n          // 清除隐藏定时器\r\n          this.clearControlTimer(file.id)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 暂停所有视频\r\n    pauseAllVideos() {\r\n      // 遍历所有文件，暂停正在播放的视频\r\n      this.paginatedMaterials.forEach(file => {\r\n        if (file.type === 'video' && file.isPlaying) {\r\n          const videoRef = this.$refs[`video-${file.id}`]\r\n          if (videoRef && videoRef.length > 0) {\r\n            const video = videoRef[0]\r\n            video.pause()\r\n            video.muted = true\r\n            this.$set(file, 'isPlaying', false)\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 暂停所有视频并重置缩放\r\n    pauseAllVideosAndResetScale() {\r\n      // 遍历所有文件，暂停正在播放的视频并重置缩放\r\n      this.paginatedMaterials.forEach(file => {\r\n        if (file.type === 'video') {\r\n          if (file.isPlaying) {\r\n            const videoRef = this.$refs[`video-${file.id}`]\r\n            if (videoRef && videoRef.length > 0) {\r\n              const video = videoRef[0]\r\n              video.pause()\r\n              video.muted = true\r\n              this.$set(file, 'isPlaying', false)\r\n            }\r\n          }\r\n          // 重置缩放状态和显示尺寸\r\n          this.$set(file, 'isScaled', false)\r\n          this.$set(file, 'displayWidth', null)\r\n          this.$set(file, 'displayHeight', null)\r\n        }\r\n      })\r\n\r\n      // 移除背景遮罩\r\n      this.removeBackdrop()\r\n    },\r\n\r\n    // 添加背景遮罩\r\n    addBackdrop() {\r\n      // 移除已存在的遮罩\r\n      this.removeBackdrop()\r\n\r\n      const backdrop = document.createElement('div')\r\n      backdrop.className = 'scale-enlarged-backdrop'\r\n      backdrop.id = 'video-scale-backdrop'\r\n      document.body.appendChild(backdrop)\r\n\r\n      // 点击遮罩关闭放大\r\n      backdrop.addEventListener('click', () => {\r\n        this.pauseAllVideosAndResetScale()\r\n      })\r\n    },\r\n\r\n    // 移除背景遮罩\r\n    removeBackdrop() {\r\n      const backdrop = document.getElementById('video-scale-backdrop')\r\n      if (backdrop) {\r\n        backdrop.remove()\r\n      }\r\n    },\r\n\r\n    // 获取视频播放进度百分比\r\n    getVideoProgress(file) {\r\n      const videoRef = this.$refs[`video-${file.id}`]\r\n      if (videoRef && videoRef.length > 0) {\r\n        const video = videoRef[0]\r\n        if (video.duration && video.currentTime) {\r\n          return (video.currentTime / video.duration) * 100\r\n        }\r\n      }\r\n      return 0\r\n    },\r\n\r\n    // 获取当前播放时间\r\n    getCurrentTime(file) {\r\n      const videoRef = this.$refs[`video-${file.id}`]\r\n      if (videoRef && videoRef.length > 0) {\r\n        const video = videoRef[0]\r\n        return video.currentTime || 0\r\n      }\r\n      return 0\r\n    },\r\n\r\n    // 获取视频总时长\r\n    getDuration(file) {\r\n      const videoRef = this.$refs[`video-${file.id}`]\r\n      if (videoRef && videoRef.length > 0) {\r\n        const video = videoRef[0]\r\n        return video.duration || 0\r\n      }\r\n      return 0\r\n    },\r\n\r\n    // 格式化时间显示\r\n    formatTime(seconds) {\r\n      if (!seconds || isNaN(seconds)) return '0:00'\r\n\r\n      const minutes = Math.floor(seconds / 60)\r\n      const remainingSeconds = Math.floor(seconds % 60)\r\n      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`\r\n    },\r\n\r\n    // 点击进度条跳转\r\n    seekVideo(event, file) {\r\n      const videoRef = this.$refs[`video-${file.id}`]\r\n      if (videoRef && videoRef.length > 0) {\r\n        const video = videoRef[0]\r\n        const progressBar = event.currentTarget.querySelector('.progress-track')\r\n        const rect = progressBar.getBoundingClientRect()\r\n        const clickX = event.clientX - rect.left\r\n        const percentage = clickX / rect.width\r\n        const newTime = percentage * video.duration\r\n\r\n        if (newTime >= 0 && newTime <= video.duration) {\r\n          video.currentTime = newTime\r\n        }\r\n      }\r\n    },\r\n\r\n    // 视频时间更新事件\r\n    onVideoTimeUpdate(file) {\r\n      // 强制更新进度条显示\r\n      this.$forceUpdate()\r\n    },\r\n\r\n    // 视频数据加载完成事件\r\n    onVideoLoadedData(file) {\r\n      // 视频数据加载完成，可以获取时长等信息\r\n      console.log(`视频 ${file.name} 数据加载完成`)\r\n    },\r\n\r\n    // 切换音频播放/暂停\r\n    toggleAudioPlay(file) {\r\n      const audioRef = this.$refs[`audio-${file.id}`]\r\n      if (audioRef && audioRef.length > 0) {\r\n        const audio = audioRef[0]\r\n\r\n        if (audio.paused) {\r\n          // 暂停所有其他音频和视频\r\n          this.pauseAllMedia()\r\n\r\n          // 播放当前音频\r\n          audio.play()\r\n          this.$set(file, 'isPlaying', true)\r\n          this.$set(file, 'showControls', true)\r\n\r\n          // 开始3秒隐藏定时器\r\n          this.startControlTimer(file)\r\n        } else {\r\n          // 暂停当前音频\r\n          audio.pause()\r\n          this.$set(file, 'isPlaying', false)\r\n          this.$set(file, 'showControls', true)\r\n\r\n          // 清除隐藏定时器\r\n          this.clearControlTimer(file.id)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 暂停所有媒体（音频和视频）\r\n    pauseAllMedia() {\r\n      this.pauseAllVideos()\r\n      this.pauseAllAudios()\r\n    },\r\n\r\n    // 暂停所有音频\r\n    pauseAllAudios() {\r\n      this.paginatedMaterials.forEach(file => {\r\n        if (file.type === 'audio' && file.isPlaying) {\r\n          const audioRef = this.$refs[`audio-${file.id}`]\r\n          if (audioRef && audioRef.length > 0) {\r\n            audioRef[0].pause()\r\n            this.$set(file, 'isPlaying', false)\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 预览文件\r\n    previewFile(file) {\r\n      // 构建文件URL\r\n      if (!file.url && this.ossInitialized) {\r\n        // 如果没有URL，构建OSS URL\r\n        const endpoint = this.ossConfig.endpoint.replace('https://', '').replace('http://', '')\r\n        file.url = `https://${endpoint}/${file.ossFileName}`\r\n      }\r\n\r\n      this.currentPreviewFile = file\r\n      this.previewVisible = true\r\n    },\r\n\r\n    // 获取文件URL\r\n    getFileUrl(file) {\r\n      if (file.url) {\r\n        return file.url\r\n      }\r\n\r\n      if (this.ossInitialized && file.ossFileName) {\r\n        const endpoint = this.ossConfig.endpoint.replace('https://', '').replace('http://', '')\r\n        return `https://${endpoint}/${file.ossFileName}`\r\n      }\r\n\r\n      return ''\r\n    },\r\n\r\n    // 停止所有媒体播放\r\n    stopAllMedia() {\r\n      // 停止预览对话框中的视频播放\r\n      if (this.$refs.previewVideo) {\r\n        this.$refs.previewVideo.pause()\r\n        this.$refs.previewVideo.currentTime = 0\r\n      }\r\n\r\n      // 停止预览对话框中的音频播放\r\n      if (this.$refs.previewAudio) {\r\n        this.$refs.previewAudio.pause()\r\n        this.$refs.previewAudio.currentTime = 0\r\n      }\r\n\r\n      // 停止缩略图中的所有媒体播放\r\n      this.pauseAllMedia()\r\n\r\n      // 清空预览文件\r\n      this.currentPreviewFile = null\r\n    },\r\n\r\n    // 视频加载完成\r\n    onVideoLoaded(event) {\r\n      // 可以在这里获取视频的第一帧作为缩略图\r\n      const video = event.target\r\n      video.currentTime = 1 // 跳到第1秒获取缩略图\r\n    },\r\n\r\n    // 视频播放结束\r\n    onVideoEnded(file) {\r\n      this.$set(file, 'isPlaying', false)\r\n      this.$set(file, 'isScaled', false)\r\n      this.$set(file, 'displayWidth', null)\r\n      this.$set(file, 'displayHeight', null)\r\n      this.removeBackdrop()\r\n    },\r\n\r\n    // 视频暂停\r\n    onVideoPaused(file) {\r\n      this.$set(file, 'isPlaying', false)\r\n      // 如果是放大状态，也要清理\r\n      if (file.isScaled) {\r\n        this.$set(file, 'isScaled', false)\r\n        this.$set(file, 'displayWidth', null)\r\n        this.$set(file, 'displayHeight', null)\r\n        this.removeBackdrop()\r\n      }\r\n\r\n      // 同步Portal视频\r\n      if (this.hoveredFile && this.hoveredFile.id === file.id) {\r\n        this.$nextTick(() => {\r\n          this.syncPortalVideo()\r\n        })\r\n      }\r\n    },\r\n\r\n    // 视频开始播放\r\n    onVideoPlayed(file) {\r\n      this.$set(file, 'isPlaying', true)\r\n\r\n      // 同步Portal视频\r\n      if (this.hoveredFile && this.hoveredFile.id === file.id) {\r\n        this.$nextTick(() => {\r\n          this.syncPortalVideo()\r\n        })\r\n      }\r\n    },\r\n\r\n    // 音频播放结束\r\n    onAudioEnded(file) {\r\n      this.$set(file, 'isPlaying', false)\r\n    },\r\n\r\n    // 音频暂停\r\n    onAudioPaused(file) {\r\n      this.$set(file, 'isPlaying', false)\r\n    },\r\n\r\n    // 音频开始播放\r\n    onAudioPlayed(file) {\r\n      this.$set(file, 'isPlaying', true)\r\n    },\r\n\r\n    // 图片加载错误\r\n    onImageError(event) {\r\n      // 图片加载失败时显示默认图标\r\n      event.target.style.display = 'none'\r\n      event.target.parentNode.innerHTML = '<i class=\"el-icon-picture-outline\"></i>'\r\n    },\r\n\r\n    // 视频鼠标进入事件\r\n    onVideoMouseEnter(file) {\r\n      console.log('视频鼠标进入:', file.name, '显示控制按钮')\r\n      this.$set(file, 'showControls', true)\r\n      this.clearControlTimer(file.id)\r\n    },\r\n\r\n    // 视频鼠标离开事件\r\n    onVideoMouseLeave(file) {\r\n      if (file.isPlaying) {\r\n        this.startControlTimer(file)\r\n      } else {\r\n        this.$set(file, 'showControls', false)\r\n      }\r\n    },\r\n\r\n    // 视频鼠标移动事件\r\n    onVideoMouseMove(file) {\r\n      this.$set(file, 'showControls', true)\r\n      if (file.isPlaying) {\r\n        this.clearControlTimer(file.id)\r\n        this.startControlTimer(file)\r\n      }\r\n    },\r\n\r\n    // 音频鼠标进入事件\r\n    onAudioMouseEnter(file) {\r\n      console.log('音频鼠标进入:', file.name)\r\n      this.$set(file, 'showControls', true)\r\n      this.clearControlTimer(file.id)\r\n    },\r\n\r\n    // 音频鼠标离开事件\r\n    onAudioMouseLeave(file) {\r\n      if (file.isPlaying) {\r\n        this.startControlTimer(file)\r\n      } else {\r\n        this.$set(file, 'showControls', false)\r\n      }\r\n    },\r\n\r\n    // 音频鼠标移动事件\r\n    onAudioMouseMove(file) {\r\n      this.$set(file, 'showControls', true)\r\n      if (file.isPlaying) {\r\n        this.clearControlTimer(file.id)\r\n        this.startControlTimer(file)\r\n      }\r\n    },\r\n\r\n    // 图片鼠标进入事件\r\n    onImageMouseEnter(file) {\r\n      console.log('图片鼠标进入:', file.name)\r\n    },\r\n\r\n    // 图片鼠标离开事件\r\n    onImageMouseLeave(file) {\r\n      console.log('图片鼠标离开:', file.name)\r\n    },\r\n\r\n    // 开始控制按钮隐藏定时器\r\n    startControlTimer(file) {\r\n      this.clearControlTimer(file.id)\r\n      this.controlTimers[file.id] = setTimeout(() => {\r\n        this.$set(file, 'showControls', false)\r\n      }, 3000) // 3秒后隐藏\r\n    },\r\n\r\n    // 清除控制按钮隐藏定时器\r\n    clearControlTimer(fileId) {\r\n      if (this.controlTimers[fileId]) {\r\n        clearTimeout(this.controlTimers[fileId])\r\n        delete this.controlTimers[fileId]\r\n      }\r\n    },\r\n\r\n\r\n\r\n\r\n\r\n    // 格式化文件大小\r\n    formatFileSize(bytes) {\r\n      if (bytes === 0) return '0 B'\r\n      const k = 1024\r\n      const sizes = ['B', 'KB', 'MB', 'GB']\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\r\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(file) {\r\n      if (file.url) {\r\n        const link = document.createElement('a')\r\n        link.href = file.url\r\n        link.download = file.name\r\n        link.target = '_blank'\r\n        document.body.appendChild(link)\r\n        link.click()\r\n        document.body.removeChild(link)\r\n      } else {\r\n        this.$message.error('文件链接不可用')\r\n      }\r\n    },\r\n\r\n    // 从OSS获取用户文件夹列表（分别为BGM和素材）\r\n    async loadUserFoldersFromOSS() {\r\n      try {\r\n        const client = getOSSClient()\r\n        const user = this.getCurrentUser()\r\n\r\n        // 分别获取BGM和素材的文件夹\r\n        await this.loadFoldersForType('abgm', 'bgmFolderTree')\r\n        await this.loadFoldersForType('asucai', 'sucaiFolderTree')\r\n\r\n        console.log('BGM文件夹:', this.bgmFolderTree.map(f => f.name))\r\n        console.log('素材文件夹:', this.sucaiFolderTree.map(f => f.name))\r\n\r\n      } catch (error) {\r\n        console.error('获取OSS文件夹列表失败:', error)\r\n        // 如果获取失败，至少显示默认的\"总\"文件夹\r\n        this.bgmFolderTree = [{ id: 1, name: '总', count: 0 }]\r\n        this.sucaiFolderTree = [{ id: 1, name: '总', count: 0 }]\r\n      }\r\n    },\r\n\r\n    // 为指定类型加载文件夹\r\n    async loadFoldersForType(baseFolder, treeProperty) {\r\n      try {\r\n        const client = getOSSClient()\r\n        const user = this.getCurrentUser()\r\n        const folderSet = new Set()\r\n        const prefix = `${baseFolder}/${user}/`\r\n\r\n        // 列出该前缀下的所有对象\r\n        const result = await client.list({\r\n          prefix: prefix,\r\n          delimiter: '/',\r\n          'max-keys': 1000\r\n        })\r\n\r\n        // 从commonPrefixes中提取文件夹名称\r\n        if (result.prefixes) {\r\n          result.prefixes.forEach(prefixInfo => {\r\n            const fullPrefix = prefixInfo.name || prefixInfo\r\n            // 提取文件夹名称：abgm/admin/总/ -> 总\r\n            if (fullPrefix && typeof fullPrefix === 'string') {\r\n              const folderName = fullPrefix.replace(prefix, '').replace('/', '')\r\n              if (folderName) {\r\n                folderSet.add(folderName)\r\n              }\r\n            }\r\n          })\r\n        }\r\n\r\n        // 如果有objects，也从中提取文件夹名称\r\n        if (result.objects) {\r\n          result.objects.forEach(obj => {\r\n            const objectKey = obj.name\r\n            if (objectKey.startsWith(prefix)) {\r\n              const relativePath = objectKey.replace(prefix, '')\r\n              const folderName = relativePath.split('/')[0]\r\n              if (folderName && folderName !== '.keep') {\r\n                folderSet.add(folderName)\r\n              }\r\n            }\r\n          })\r\n        }\r\n\r\n        // 转换为文件夹树格式\r\n        const folders = Array.from(folderSet).map((folderName, index) => ({\r\n          id: Date.now() + index, // 使用时间戳避免ID冲突\r\n          name: folderName,\r\n          count: 0 // 稍后可以计算实际文件数量\r\n        }))\r\n\r\n        // 确保\"总\"文件夹存在\r\n        if (!folderSet.has('总')) {\r\n          folders.unshift({ id: Date.now(), name: '总', count: 0 })\r\n        }\r\n\r\n        // 更新对应的文件夹树\r\n        if (baseFolder === 'abgm') {\r\n          this.bgmFolderTree = folders\r\n        } else {\r\n          this.sucaiFolderTree = folders\r\n        }\r\n\r\n        console.log(`${baseFolder} 文件夹加载完成:`, folders.map(f => f.name))\r\n\r\n      } catch (error) {\r\n        console.error(`获取 ${baseFolder} 文件夹列表失败:`, error)\r\n        // 如果获取失败，至少显示默认的\"总\"文件夹\r\n        const defaultFolder = [{ id: Date.now(), name: '总', count: 0 }]\r\n        if (baseFolder === 'abgm') {\r\n          this.bgmFolderTree = defaultFolder\r\n        } else {\r\n          this.sucaiFolderTree = defaultFolder\r\n        }\r\n      }\r\n    },\r\n\r\n    // 更新文件夹的文件数量\r\n    async updateFolderFileCounts() {\r\n      try {\r\n        const client = getOSSClient()\r\n        const user = this.getCurrentUser()\r\n\r\n        // 由于现在使用分离的文件夹树，这个方法暂时禁用\r\n        // 可以根据需要为每个标签页单独更新文件数量\r\n        console.log('文件夹数量更新已改为分离模式，请使用具体的标签页更新方法')\r\n        return\r\n\r\n        for (let folder of []) { // 临时禁用\r\n          let totalCount = 0\r\n\r\n          // 检查两种类型的文件夹中的文件数量\r\n          const baseFolders = ['abgm', 'asucai']\r\n\r\n          for (const baseFolder of baseFolders) {\r\n            const prefix = `${baseFolder}/${user}/${folder.name}/`\r\n\r\n            try {\r\n              const result = await client.list({\r\n                prefix: prefix,\r\n                'max-keys': 1000\r\n              })\r\n\r\n              if (result.objects) {\r\n                // 过滤掉.keep文件\r\n                const fileCount = result.objects.filter(obj =>\r\n                  !obj.name.endsWith('.keep') && !obj.name.endsWith('/')\r\n                ).length\r\n                totalCount += fileCount\r\n              }\r\n            } catch (error) {\r\n              console.warn(`获取文件夹 ${folder.name} 在 ${baseFolder} 中的文件数量失败:`, error)\r\n            }\r\n          }\r\n\r\n          folder.count = totalCount\r\n        }\r\n\r\n        console.log('文件夹文件数量更新完成:', this.simpleFolderTree)\r\n\r\n      } catch (error) {\r\n        console.error('更新文件夹文件数量失败:', error)\r\n      }\r\n    },\r\n\r\n    // 文件夹右键菜单相关方法\r\n    showFolderContextMenu(event, folder) {\r\n      this.contextMenuVisible = true\r\n      this.contextMenuX = event.clientX\r\n      this.contextMenuY = event.clientY\r\n      this.contextMenuFolder = folder\r\n\r\n      // 点击其他地方隐藏菜单\r\n      document.addEventListener('click', this.hideFolderContextMenu)\r\n    },\r\n\r\n    hideFolderContextMenu() {\r\n      this.contextMenuVisible = false\r\n      this.contextMenuFolder = null\r\n      document.removeEventListener('click', this.hideFolderContextMenu)\r\n    },\r\n\r\n    // 重命名文件夹\r\n    async renameFolderAction() {\r\n      if (!this.contextMenuFolder) {\r\n        this.$message.error('未选择文件夹')\r\n        return\r\n      }\r\n\r\n      const folderName = this.contextMenuFolder.name || this.contextMenuFolder\r\n\r\n      this.$prompt('请输入新的文件夹名称', '重命名文件夹', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        inputValue: folderName\r\n      }).then(async ({ value }) => {\r\n        try {\r\n          if (!value || value.trim() === '') {\r\n            this.$message.error('文件夹名称不能为空')\r\n            return\r\n          }\r\n\r\n          const newFolderName = value.trim()\r\n          const oldFolderName = this.contextMenuFolder.name || this.contextMenuFolder\r\n\r\n          if (newFolderName === oldFolderName) {\r\n            this.hideFolderContextMenu()\r\n            return\r\n          }\r\n\r\n          // 如果使用OSS存储，需要重命名OSS中的文件夹\r\n          if (this.storageType === 'oss' && this.ossInitialized) {\r\n            await this.renameOSSFolder(oldFolderName, newFolderName)\r\n          }\r\n\r\n          // 更新前端文件夹名称\r\n          if (typeof this.contextMenuFolder === 'object') {\r\n            this.contextMenuFolder.name = newFolderName\r\n          }\r\n\r\n          // 如果当前选中的是被重命名的文件夹，更新当前文件夹\r\n          if (this.currentFolder === oldFolderName) {\r\n            this.currentFolder = newFolderName\r\n          }\r\n\r\n          this.$message.success('文件夹重命名成功')\r\n          this.hideFolderContextMenu()\r\n\r\n        } catch (error) {\r\n          console.error('重命名文件夹失败:', error)\r\n          this.$message.error(`重命名失败: ${error.message}`)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 删除文件夹\r\n    async deleteFolderAction() {\r\n      console.log('删除文件夹操作开始，contextMenuFolder:', this.contextMenuFolder)\r\n\r\n      if (!this.contextMenuFolder) {\r\n        this.$message.error('未选择文件夹')\r\n        return\r\n      }\r\n\r\n      // 安全地获取文件夹名称\r\n      let folderName\r\n      if (typeof this.contextMenuFolder === 'string') {\r\n        folderName = this.contextMenuFolder\r\n      } else if (this.contextMenuFolder && this.contextMenuFolder.name) {\r\n        folderName = this.contextMenuFolder.name\r\n      } else {\r\n        console.error('无法获取文件夹名称，contextMenuFolder:', this.contextMenuFolder)\r\n        this.$message.error('无法获取文件夹名称')\r\n        return\r\n      }\r\n\r\n      console.log('准备删除文件夹:', folderName)\r\n\r\n      if (folderName === '总') {\r\n        this.$message.warning('不能删除\"总\"文件夹')\r\n        return\r\n      }\r\n\r\n      this.$confirm(`确定要删除文件夹\"${folderName}\"吗？文件夹内的所有文件也会被删除！`, '删除文件夹', {\r\n        confirmButtonText: '确定删除',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          // 使用之前获取的folderName，避免在异步操作中contextMenuFolder变为null\r\n          const targetFolderName = folderName\r\n\r\n          // 如果使用OSS存储，只删除当前标签页的OSS文件夹\r\n          if (this.storageType === 'oss' && this.ossInitialized) {\r\n            try {\r\n              await this.deleteOSSFolderForCurrentTab(targetFolderName)\r\n              console.log(`${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹删除成功`)\r\n            } catch (ossError) {\r\n              console.warn('OSS文件夹删除失败，但继续删除前端记录:', ossError.message)\r\n              // 即使OSS删除失败，也继续删除前端记录（可能是空文件夹）\r\n            }\r\n          }\r\n\r\n          // 从前端移除文件夹相关的文件\r\n          this.removeFilesFromFolder(targetFolderName)\r\n\r\n          // 如果当前选中的是被删除的文件夹，切换到\"总\"文件夹\r\n          if (this.currentFolder === targetFolderName) {\r\n            this.currentFolder = '总'\r\n          }\r\n\r\n          this.$message.success('文件夹删除成功')\r\n          this.hideFolderContextMenu()\r\n\r\n        } catch (error) {\r\n          console.error('删除文件夹失败:', error)\r\n          this.$message.error(`删除失败: ${error.message}`)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 获取当前用户名\r\n    getCurrentUser() {\r\n      // 从store中获取用户信息\r\n      return this.$store.state.user.name || 'admin'\r\n    },\r\n\r\n    // 获取当前文件夹名称\r\n    getCurrentFolder() {\r\n      return this.currentFolder || '总'\r\n    },\r\n\r\n    // 构建OSS上传路径\r\n    buildOSSPath(baseFolder) {\r\n      const user = this.getCurrentUser()\r\n      const folder = this.getCurrentFolder()\r\n      return `${baseFolder}/${user}/${folder}`\r\n    },\r\n\r\n    // OSS文件复制和删除（用于重命名）\r\n    async copyAndDeleteOSSFile(oldPath, newPath) {\r\n      try {\r\n        const client = getOSSClient()\r\n\r\n        // 复制文件到新路径\r\n        await client.copy(newPath, oldPath)\r\n        console.log(`OSS文件复制成功: ${oldPath} -> ${newPath}`)\r\n\r\n        // 删除原文件\r\n        await client.delete(oldPath)\r\n        console.log(`OSS原文件删除成功: ${oldPath}`)\r\n\r\n        return true\r\n      } catch (error) {\r\n        console.error('OSS文件复制删除失败:', error)\r\n        throw error\r\n      }\r\n    },\r\n\r\n    // OSS文件夹重命名\r\n    async renameOSSFolder(oldFolderName, newFolderName) {\r\n      try {\r\n        const client = getOSSClient()\r\n        const user = this.getCurrentUser()\r\n\r\n        // 获取两种类型文件夹的路径\r\n        const baseFolders = ['abgm', 'asucai']\r\n\r\n        for (const baseFolder of baseFolders) {\r\n          const oldPrefix = `${baseFolder}/${user}/${oldFolderName}/`\r\n          const newPrefix = `${baseFolder}/${user}/${newFolderName}/`\r\n\r\n          // 列出文件夹中的所有文件\r\n          const result = await client.list({\r\n            prefix: oldPrefix,\r\n            'max-keys': 1000\r\n          })\r\n\r\n          if (result.objects && result.objects.length > 0) {\r\n            // 复制所有文件到新路径\r\n            for (const obj of result.objects) {\r\n              const newKey = obj.name.replace(oldPrefix, newPrefix)\r\n              await client.copy(newKey, obj.name)\r\n              console.log(`OSS文件夹重命名: ${obj.name} -> ${newKey}`)\r\n            }\r\n\r\n            // 删除原文件\r\n            for (const obj of result.objects) {\r\n              await client.delete(obj.name)\r\n            }\r\n          }\r\n        }\r\n\r\n        return true\r\n      } catch (error) {\r\n        console.error('OSS文件夹重命名失败:', error)\r\n        throw error\r\n      }\r\n    },\r\n\r\n    // OSS文件夹删除\r\n    async deleteOSSFolder(folderName) {\r\n      try {\r\n        console.log('开始删除OSS文件夹:', folderName)\r\n\r\n        if (!folderName) {\r\n          throw new Error('文件夹名称不能为空')\r\n        }\r\n\r\n        const client = getOSSClient()\r\n        if (!client) {\r\n          throw new Error('OSS客户端未初始化')\r\n        }\r\n\r\n        const user = this.getCurrentUser()\r\n        console.log('当前用户:', user)\r\n\r\n        // 获取两种类型文件夹的路径\r\n        const baseFolders = ['abgm', 'asucai']\r\n        let totalDeletedFiles = 0\r\n\r\n        for (const baseFolder of baseFolders) {\r\n          const prefix = `${baseFolder}/${user}/${folderName}/`\r\n          console.log('检查OSS路径:', prefix)\r\n\r\n          // 列出文件夹中的所有文件\r\n          const result = await client.list({\r\n            prefix: prefix,\r\n            'max-keys': 1000\r\n          })\r\n\r\n          console.log(`路径 ${prefix} 下找到 ${result.objects ? result.objects.length : 0} 个文件`)\r\n\r\n          if (result.objects && result.objects.length > 0) {\r\n            // 删除所有文件\r\n            for (const obj of result.objects) {\r\n              await client.delete(obj.name)\r\n              console.log(`OSS文件删除: ${obj.name}`)\r\n              totalDeletedFiles++\r\n            }\r\n          }\r\n        }\r\n\r\n        console.log(`OSS文件夹删除完成，共删除 ${totalDeletedFiles} 个文件`)\r\n        return true\r\n      } catch (error) {\r\n        console.error('OSS文件夹删除失败:', error)\r\n        throw new Error(`删除OSS文件夹失败: ${error.message}`)\r\n      }\r\n    },\r\n\r\n    // 只删除当前标签页的OSS文件夹\r\n    async deleteOSSFolderForCurrentTab(folderName) {\r\n      try {\r\n        console.log(`开始删除${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹:`, folderName)\r\n\r\n        if (!folderName) {\r\n          throw new Error('文件夹名称不能为空')\r\n        }\r\n\r\n        const client = getOSSClient()\r\n        if (!client) {\r\n          throw new Error('OSS客户端未初始化')\r\n        }\r\n\r\n        const user = this.getCurrentUser()\r\n        const baseFolder = this.materialTab === 'bgm' ? 'abgm' : 'asucai'\r\n        const prefix = `${baseFolder}/${user}/${folderName}/`\r\n\r\n        console.log('删除OSS路径:', prefix)\r\n\r\n        // 列出文件夹中的所有文件\r\n        const result = await client.list({\r\n          prefix: prefix,\r\n          'max-keys': 1000\r\n        })\r\n\r\n        let deletedFiles = 0\r\n        if (result.objects && result.objects.length > 0) {\r\n          // 删除所有文件\r\n          for (const obj of result.objects) {\r\n            await client.delete(obj.name)\r\n            console.log(`OSS文件删除: ${obj.name}`)\r\n            deletedFiles++\r\n          }\r\n        }\r\n\r\n        console.log(`${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹删除完成，共删除 ${deletedFiles} 个文件`)\r\n        return true\r\n      } catch (error) {\r\n        console.error(`删除${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹失败:`, error)\r\n        throw new Error(`删除${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹失败: ${error.message}`)\r\n      }\r\n    },\r\n\r\n    // 从前端移除文件夹相关的文件（只删除当前标签页的）\r\n    removeFilesFromFolder(folderName) {\r\n      if (this.materialTab === 'bgm') {\r\n        // 只移除BGM列表中该文件夹的文件\r\n        this.bgmList = this.bgmList.filter(file => {\r\n          const fileFolderName = file.folder ? file.folder.split('/').pop() : '总'\r\n          return fileFolderName !== folderName\r\n        })\r\n\r\n        // 从BGM文件夹树中移除\r\n        const treeIndex = this.bgmFolderTree.findIndex(folder => folder.name === folderName)\r\n        if (treeIndex > -1) {\r\n          this.bgmFolderTree.splice(treeIndex, 1)\r\n          console.log(`从BGM文件夹树中移除: ${folderName}`)\r\n        }\r\n      } else {\r\n        // 只移除素材列表中该文件夹的文件\r\n        this.sucaiList = this.sucaiList.filter(file => {\r\n          const fileFolderName = file.folder ? file.folder.split('/').pop() : '总'\r\n          return fileFolderName !== folderName\r\n        })\r\n\r\n        // 从素材文件夹树中移除\r\n        const treeIndex = this.sucaiFolderTree.findIndex(folder => folder.name === folderName)\r\n        if (treeIndex > -1) {\r\n          this.sucaiFolderTree.splice(treeIndex, 1)\r\n          console.log(`从素材文件夹树中移除: ${folderName}`)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 文件夹选择\r\n    async selectFolder(folder) {\r\n      this.selectedFolder = folder.id\r\n      this.currentFolder = folder.name\r\n      console.log('选择文件夹:', folder.name)\r\n\r\n      // 切换文件夹时重新加载文件列表\r\n      if (this.ossInitialized) {\r\n        await this.loadMaterialList()\r\n      }\r\n\r\n      // 更新文件夹计数\r\n      this.updateCurrentTabFolderCounts()\r\n    },\r\n\r\n    // 更新当前标签页的文件夹计数\r\n    updateCurrentTabFolderCounts() {\r\n      const currentMaterials = this.currentMaterialList\r\n      const currentFolders = this.currentFolderTree\r\n\r\n      // 为每个文件夹计算文件数量\r\n      currentFolders.forEach(folder => {\r\n        if (folder.name === '总') {\r\n          // \"总\"文件夹显示所有文件数量\r\n          folder.count = currentMaterials.length\r\n        } else {\r\n          // 其他文件夹只计算属于该文件夹的文件\r\n          folder.count = currentMaterials.filter(file => {\r\n            const fileFolderName = file.folder ? file.folder.split('/').pop() : '总'\r\n            return fileFolderName === folder.name\r\n          }).length\r\n        }\r\n      })\r\n\r\n      console.log(`${this.materialTab} 文件夹计数更新:`, currentFolders.map(f => `${f.name}(${f.count})`))\r\n    },\r\n\r\n    // 文件选择相关方法\r\n    handleSelectAll(checked) {\r\n      if (checked) {\r\n        this.selectedFiles = this.paginatedMaterials.map(f => f.id)\r\n      } else {\r\n        this.selectedFiles = []\r\n      }\r\n    },\r\n\r\n    // 预览文件\r\n    handlePreview() {\r\n      if (this.selectedFiles.length !== 1) {\r\n        this.$message.warning('请选择一个文件进行预览')\r\n        return\r\n      }\r\n\r\n      const file = this.currentMaterialList.find(f => f.id === this.selectedFiles[0])\r\n      if (!file) {\r\n        this.$message.error('文件不存在')\r\n        return\r\n      }\r\n\r\n      // 构建文件URL\r\n      if (!file.url && this.ossInitialized) {\r\n        // 如果没有URL，构建OSS URL\r\n        const endpoint = this.ossConfig.endpoint.replace('https://', '').replace('http://', '')\r\n        file.url = `https://${endpoint}/${file.ossFileName}`\r\n      }\r\n\r\n      this.currentPreviewFile = file\r\n      this.previewVisible = true\r\n    },\r\n\r\n    toggleFileSelection(fileId) {\r\n      const index = this.selectedFiles.indexOf(fileId)\r\n      if (index > -1) {\r\n        this.selectedFiles.splice(index, 1)\r\n      } else {\r\n        this.selectedFiles.push(fileId)\r\n      }\r\n\r\n      // 更新全选状态\r\n      this.selectAll = this.selectedFiles.length === this.paginatedMaterials.length\r\n    },\r\n\r\n    getSimpleFileIcon(type) {\r\n      const iconMap = {\r\n        'video': '🎬',\r\n        'image': '🖼️',\r\n        'audio': '🎵'\r\n      }\r\n      return iconMap[type] || '📄'\r\n    },\r\n\r\n    // 标签页切换\r\n    switchTab(tab) {\r\n      this.materialTab = tab\r\n      this.currentPage = 1 // 重置页码\r\n      this.selectedFiles = [] // 清空选择\r\n      this.selectAll = false\r\n\r\n      const tabNames = {\r\n        'bgm': 'BGM',\r\n        'sucai': '素材'\r\n      }\r\n      this.$message.success(`切换到${tabNames[tab] || tab}上传页面`)\r\n\r\n      // 切换标签页后更新文件夹计数\r\n      this.$nextTick(() => {\r\n        this.updateCurrentTabFolderCounts()\r\n      })\r\n    },\r\n\r\n    // 文件操作\r\n    async handleRename() {\r\n      if (this.selectedFiles.length === 0) {\r\n        this.$message.warning('请先选择要重命名的文件')\r\n        return\r\n      }\r\n      if (this.selectedFiles.length > 1) {\r\n        this.$message.warning('一次只能重命名一个文件')\r\n        return\r\n      }\r\n\r\n      const file = this.currentMaterialList.find(f => f.id === this.selectedFiles[0])\r\n      this.$prompt('请输入新的文件名', '重命名文件', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        inputValue: file.name\r\n      }).then(async ({ value }) => {\r\n        try {\r\n          // 验证新文件名\r\n          if (!value || value.trim() === '') {\r\n            this.$message.error('文件名不能为空')\r\n            return\r\n          }\r\n\r\n          const newFileName = value.trim()\r\n\r\n          // 如果使用OSS存储，需要重命名OSS中的文件\r\n          if (this.storageType === 'oss' && this.ossInitialized) {\r\n            // 构建原文件路径\r\n            let oldOssFilePath = ''\r\n            if (file.ossFileName) {\r\n              oldOssFilePath = file.ossFileName\r\n            } else {\r\n              const baseFolderName = this.materialTab === 'bgm' ? 'abgm' : 'asucai'\r\n              const fullPath = this.buildOSSPath(baseFolderName)\r\n              oldOssFilePath = `${fullPath}/${file.name}`\r\n            }\r\n\r\n            // 构建新文件路径\r\n            const baseFolderName = this.materialTab === 'bgm' ? 'abgm' : 'asucai'\r\n            const fullPath = this.buildOSSPath(baseFolderName)\r\n            const newOssFilePath = `${fullPath}/${newFileName}`\r\n\r\n            console.log(`OSS重命名: ${oldOssFilePath} -> ${newOssFilePath}`)\r\n\r\n            try {\r\n              // OSS不支持直接重命名，需要复制后删除\r\n              await this.copyAndDeleteOSSFile(oldOssFilePath, newOssFilePath)\r\n\r\n              // 更新文件信息\r\n              file.name = newFileName\r\n              file.ossFileName = newOssFilePath\r\n\r\n              this.$message.success('重命名成功')\r\n            } catch (error) {\r\n              console.error('OSS重命名失败:', error)\r\n              this.$message.error(`重命名失败: ${error.message}`)\r\n            }\r\n          } else {\r\n            // 本地存储，直接更新文件名\r\n            file.name = newFileName\r\n            this.$message.success('重命名成功')\r\n          }\r\n\r\n        } catch (error) {\r\n          console.error('重命名失败:', error)\r\n          this.$message.error(`重命名失败: ${error.message}`)\r\n        }\r\n      })\r\n    },\r\n\r\n    async handleDelete() {\r\n      if (this.selectedFiles.length === 0) {\r\n        this.$message.warning('请先选择要删除的文件')\r\n        return\r\n      }\r\n\r\n      this.$confirm(`确定要删除选中的 ${this.selectedFiles.length} 个文件吗？删除后无法恢复！`, '删除文件', {\r\n        confirmButtonText: '确定删除',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          const currentList = this.materialTab === 'bgm' ? this.bgmList : this.sucaiList\r\n\r\n          // 获取要删除的文件信息\r\n          const filesToDelete = this.selectedFiles.map(fileId => {\r\n            return currentList.find(f => f.id === fileId)\r\n          }).filter(file => file) // 过滤掉找不到的文件\r\n\r\n          console.log('准备删除的文件:', filesToDelete)\r\n\r\n          // 如果使用OSS存储，删除OSS中的文件\r\n          if (this.storageType === 'oss' && this.ossInitialized) {\r\n            const deletePromises = filesToDelete.map(async (file) => {\r\n              try {\r\n                // 构建OSS文件路径\r\n                let ossFilePath = ''\r\n                if (file.ossFileName) {\r\n                  // 如果有OSS文件名，直接使用\r\n                  ossFilePath = file.ossFileName\r\n                } else {\r\n                  // 否则根据文件夹和文件名构建路径\r\n                  const baseFolderName = this.materialTab === 'bgm' ? 'abgm' : 'asucai'\r\n                  const fullPath = this.buildOSSPath(baseFolderName)\r\n                  ossFilePath = `${fullPath}/${file.name}`\r\n                }\r\n\r\n                console.log(`删除OSS文件: ${ossFilePath}`)\r\n                await deleteFileFromOSS(ossFilePath)\r\n                return { success: true, file: file.name }\r\n              } catch (error) {\r\n                console.error(`删除OSS文件失败: ${file.name}`, error)\r\n                return { success: false, file: file.name, error: error.message }\r\n              }\r\n            })\r\n\r\n            const deleteResults = await Promise.all(deletePromises)\r\n\r\n            // 检查删除结果\r\n            const failedDeletes = deleteResults.filter(result => !result.success)\r\n            if (failedDeletes.length > 0) {\r\n              console.warn('部分文件删除失败:', failedDeletes)\r\n              this.$message.warning(`${failedDeletes.length} 个文件删除失败，但已从列表中移除`)\r\n            }\r\n          }\r\n\r\n          // 从前端列表中删除文件\r\n          this.selectedFiles.forEach(fileId => {\r\n            const index = currentList.findIndex(f => f.id === fileId)\r\n            if (index > -1) {\r\n              currentList.splice(index, 1)\r\n            }\r\n          })\r\n\r\n          this.selectedFiles = []\r\n          this.selectAll = false\r\n          this.$message.success('删除成功')\r\n\r\n        } catch (error) {\r\n          console.error('删除失败:', error)\r\n          this.$message.error(`删除失败: ${error.message}`)\r\n        }\r\n      })\r\n    },\r\n    showUploadDialog() {\r\n      this.uploadDialogVisible = true\r\n      this.fileList = []\r\n      this.uploadForm = {}\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      // 添加调试信息\r\n      console.log('上传文件信息:', {\r\n        name: file.name,\r\n        type: file.type,\r\n        size: file.size,\r\n        currentTab: this.materialTab\r\n      })\r\n\r\n      // 根据标签页检查文件大小\r\n      let maxSize = 500 // 默认500MB\r\n      if (this.materialTab === 'bgm') {\r\n        maxSize = 100 // BGM文件100MB\r\n      }\r\n\r\n      const isValidSize = file.size / 1024 / 1024 < maxSize\r\n      if (!isValidSize) {\r\n        this.$message.error(`文件大小不能超过 ${maxSize}MB！`)\r\n        return false\r\n      }\r\n\r\n      // 根据标签页检查文件格式\r\n      const extension = file.name.toLowerCase().split('.').pop()\r\n      const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v']\r\n      const audioExts = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']\r\n      const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']\r\n\r\n      if (this.materialTab === 'bgm' && !audioExts.includes(extension)) {\r\n        this.$message.error(`BGM只支持音频文件！支持格式：${audioExts.join(', ')}`)\r\n        return false\r\n      }\r\n\r\n      if (this.materialTab === 'sucai') {\r\n        const allExts = [...videoExts, ...audioExts, ...imageExts]\r\n        if (!allExts.includes(extension)) {\r\n          this.$message.error(`素材支持视频、音频、图片文件！支持格式：${allExts.join(', ')}`)\r\n          return false\r\n        }\r\n      }\r\n\r\n      return true\r\n    },\r\n\r\n    isValidFileType(type, fileName) {\r\n      // 获取文件扩展名作为备用验证\r\n      const extension = fileName.toLowerCase().split('.').pop()\r\n\r\n      if (this.materialTab === 'video') {\r\n        const videoTypes = [\r\n          'video/mp4', 'video/mpeg', 'video/quicktime', 'video/x-msvideo',\r\n          'video/x-ms-wmv', 'video/x-flv', 'video/webm', 'video/3gpp',\r\n          'video/mp2t', 'video/x-m4v'\r\n        ]\r\n        const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v', 'ts']\r\n\r\n        return videoTypes.includes(type) || videoExtensions.includes(extension)\r\n      } else {\r\n        const audioTypes = [\r\n          'audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/x-wav',\r\n          'audio/flac', 'audio/aac', 'audio/mp4', 'audio/x-m4a',\r\n          'audio/ogg', 'audio/webm'\r\n        ]\r\n        const audioExtensions = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']\r\n\r\n        return audioTypes.includes(type) || audioExtensions.includes(extension)\r\n      }\r\n    },\r\n    handleUploadSuccess(response, file) {\r\n      // 创建新的文件对象\r\n      const newFile = {\r\n        id: Date.now() + Math.random(),\r\n        name: file.name,\r\n        type: this.materialTab === 'video' ? 'video' : 'audio',\r\n        size: file.size,\r\n        uploadTime: new Date().toLocaleString().slice(0, 16),\r\n        duration: '00:00:00', // 实际应用中应该从服务器返回\r\n        resolution: this.materialTab === 'video' ? '1920x1080' : undefined,\r\n        bitrate: this.materialTab === 'music' ? '128kbps' : undefined\r\n      }\r\n\r\n      // 添加到对应的列表\r\n      if (this.materialTab === 'video') {\r\n        this.videoList.unshift(newFile)\r\n      } else {\r\n        this.musicList.unshift(newFile)\r\n      }\r\n\r\n      this.$message.success(`${file.name} 上传成功！`)\r\n    },\r\n\r\n    handleUploadError(err, file) {\r\n      this.$message.error(`${file.name} 上传失败！`)\r\n    },\r\n\r\n    handleUploadProgress(event, file) {\r\n      // 计算上传进度百分比\r\n      const progress = Math.round((event.loaded / event.total) * 100)\r\n\r\n      // 更新进度条显示\r\n      this.$set(this.uploadProgress, file.name, progress)\r\n\r\n      console.log(`文件 ${file.name} 上传进度: ${progress}%`)\r\n    },\r\n\r\n    handleFileChange(file, fileList) {\r\n      console.log('文件变化:', file)\r\n      console.log('当前文件列表:', fileList)\r\n      this.fileList = fileList\r\n    },\r\n\r\n    handleRemove(file, fileList) {\r\n      console.log('移除文件:', file)\r\n      console.log('更新后的文件列表:', fileList)\r\n      this.fileList = fileList\r\n    },\r\n\r\n    async submitUpload() {\r\n      console.log('当前文件列表:', this.fileList)\r\n      console.log('文件列表长度:', this.fileList.length)\r\n\r\n      if (!this.fileList || this.fileList.length === 0) {\r\n        this.$message.warning('请先选择要上传的文件')\r\n        return\r\n      }\r\n\r\n      this.uploading = true\r\n      const fileCount = this.fileList.length\r\n\r\n      // 根据标签页确定文件夹和类型\r\n      let baseFolderName = ''\r\n      let fileType = ''\r\n\r\n      switch (this.materialTab) {\r\n        case 'bgm':\r\n          baseFolderName = 'abgm'\r\n          fileType = 'audio'\r\n          break\r\n        case 'sucai':\r\n          baseFolderName = 'asucai'\r\n          fileType = 'mixed' // 混合类型\r\n          break\r\n        default:\r\n          baseFolderName = 'abgm'\r\n          fileType = 'audio'\r\n      }\r\n\r\n      // 构建完整的OSS路径：baseFolderName/用户名/文件夹名\r\n      const folderName = this.buildOSSPath(baseFolderName)\r\n\r\n      console.log('当前标签页:', this.materialTab)\r\n      console.log('基础文件夹:', baseFolderName)\r\n      console.log('完整上传路径:', folderName)\r\n      console.log('文件类型:', fileType)\r\n\r\n      // 获取实际的文件对象\r\n      const actualFiles = this.fileList.map(fileItem => {\r\n        // Element UI 上传组件的文件对象可能有不同的结构\r\n        return fileItem.raw || fileItem.file || fileItem\r\n      }).filter(file => file instanceof File)\r\n\r\n      console.log('实际文件对象:', actualFiles)\r\n\r\n      if (actualFiles.length === 0) {\r\n        this.$message.warning('没有找到有效的文件对象')\r\n        return\r\n      }\r\n\r\n      try {\r\n        // 初始化所有文件的进度条\r\n        actualFiles.forEach(file => {\r\n          this.$set(this.uploadProgress, file.name, 0)\r\n        })\r\n\r\n        if (this.storageType === 'oss') {\r\n          // OSS上传\r\n          if (!this.ossInitialized) {\r\n            this.$message.error('OSS未配置，请先配置OSS')\r\n            this.openOSSConfig()\r\n            this.uploading = false\r\n            return\r\n          }\r\n\r\n          // 使用OSS上传\r\n          const uploadResults = await uploadFilesToOSS(\r\n            actualFiles,\r\n            fileType,\r\n            folderName, // 使用对应的文件夹\r\n            (index, progress, fileName, result, error) => {\r\n              if (error) {\r\n                this.$set(this.uploadProgress, fileName, -1) // 表示失败\r\n              } else {\r\n                this.$set(this.uploadProgress, fileName, progress)\r\n              }\r\n            }\r\n          )\r\n\r\n          // 处理上传结果\r\n          uploadResults.forEach((result, index) => {\r\n            if (result.success) {\r\n              // 根据文件扩展名确定文件类型\r\n              const fileExtension = result.originalName.toLowerCase().split('.').pop()\r\n              const audioExts = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']\r\n              const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v']\r\n              const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']\r\n\r\n              let actualFileType = 'unknown'\r\n              if (audioExts.includes(fileExtension)) {\r\n                actualFileType = 'audio'\r\n              } else if (videoExts.includes(fileExtension)) {\r\n                actualFileType = 'video'\r\n              } else if (imageExts.includes(fileExtension)) {\r\n                actualFileType = 'image'\r\n              }\r\n\r\n              const newFile = {\r\n                id: Date.now() + Math.random() + index,\r\n                name: result.originalName,\r\n                type: actualFileType,\r\n                size: result.size,\r\n                uploadTime: new Date().toLocaleString().slice(0, 16),\r\n                duration: actualFileType === 'video' ? '00:02:30' : (actualFileType === 'audio' ? '00:03:45' : undefined),\r\n                resolution: actualFileType === 'video' ? '1920x1080' : undefined,\r\n                bitrate: actualFileType === 'audio' ? '128kbps' : undefined,\r\n                url: result.url,\r\n                ossFileName: result.fileName,\r\n                folder: folderName\r\n              }\r\n\r\n              // 根据标签页添加到对应列表\r\n              switch (this.materialTab) {\r\n                case 'bgm':\r\n                  this.bgmList.unshift(newFile)\r\n                  break\r\n                case 'sucai':\r\n                  this.sucaiList.unshift(newFile)\r\n                  break\r\n              }\r\n            }\r\n          })\r\n\r\n          this.$message.success(`成功上传 ${uploadResults.filter(r => r.success).length} 个文件到阿里云OSS！`)\r\n\r\n        } else {\r\n          // 本地模拟上传\r\n          actualFiles.forEach((file) => {\r\n            // 进度已在上面初始化，这里直接开始模拟进度更新\r\n\r\n            // 模拟进度更新\r\n            const progressInterval = setInterval(() => {\r\n              const currentProgress = this.uploadProgress[file.name] || 0\r\n              if (currentProgress < 100) {\r\n                this.$set(this.uploadProgress, file.name, Math.min(currentProgress + Math.random() * 30, 100))\r\n              } else {\r\n                clearInterval(progressInterval)\r\n              }\r\n            }, 200)\r\n          })\r\n\r\n          // 模拟上传完成\r\n          setTimeout(() => {\r\n            // 为每个文件创建新记录\r\n            actualFiles.forEach((file, index) => {\r\n              // 根据文件扩展名确定文件类型\r\n              const fileExtension = file.name.toLowerCase().split('.').pop()\r\n              const audioExts = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']\r\n              const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v']\r\n              const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']\r\n\r\n              let actualFileType = 'unknown'\r\n              if (audioExts.includes(fileExtension)) {\r\n                actualFileType = 'audio'\r\n              } else if (videoExts.includes(fileExtension)) {\r\n                actualFileType = 'video'\r\n              } else if (imageExts.includes(fileExtension)) {\r\n                actualFileType = 'image'\r\n              }\r\n\r\n              const newFile = {\r\n                id: Date.now() + Math.random() + index,\r\n                name: file.name,\r\n                type: actualFileType,\r\n                size: file.size,\r\n                uploadTime: new Date().toLocaleString().slice(0, 16),\r\n                duration: actualFileType === 'video' ? '00:02:30' : (actualFileType === 'audio' ? '00:03:45' : undefined),\r\n                resolution: actualFileType === 'video' ? '1920x1080' : undefined,\r\n                bitrate: actualFileType === 'audio' ? '128kbps' : undefined,\r\n                folder: folderName,\r\n                url: URL.createObjectURL(file) // 为本地文件创建预览URL\r\n              }\r\n\r\n              // 根据标签页添加到对应列表\r\n              switch (this.materialTab) {\r\n                case 'bgm':\r\n                  this.bgmList.unshift(newFile)\r\n                  break\r\n                case 'sucai':\r\n                  this.sucaiList.unshift(newFile)\r\n                  break\r\n              }\r\n            })\r\n\r\n            this.$message.success(`成功上传 ${fileCount} 个文件！`)\r\n\r\n            // 上传完成后更新文件夹计数\r\n            this.updateCurrentTabFolderCounts()\r\n          }, 2000)\r\n        }\r\n\r\n        this.uploading = false\r\n        this.uploadDialogVisible = false\r\n        this.fileList = []\r\n        this.uploadProgress = {}\r\n\r\n      } catch (error) {\r\n        this.uploading = false\r\n        this.$message.error(`上传失败：${error.message}`)\r\n        console.error('上传错误:', error)\r\n      }\r\n    },\r\n\r\n    formatFileSize(bytes) {\r\n      if (bytes === 0) return '0 Bytes'\r\n      const k = 1024\r\n      const sizes = ['Bytes', 'KB', 'MB', 'GB']\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\r\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 素材管理界面 - 完全按照图片设计 */\r\n.up-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: #f8f9fa;\r\n  margin: 0;\r\n  height: 100vh;\r\n}\r\n\r\n/* 顶部标签页 */\r\n.materials-tabs {\r\n  background: white;\r\n  padding: 0;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.tab-buttons {\r\n  display: flex;\r\n  gap: 0;\r\n  align-items: center;\r\n}\r\n\r\n.tab-button {\r\n  border-radius: 20px !important;\r\n  margin: 10px 5px;\r\n  padding: 8px 20px;\r\n  border: none;\r\n  font-size: 14px;\r\n}\r\n\r\n.tab-button.el-button--primary {\r\n  background: #4a90e2;\r\n  color: white;\r\n}\r\n\r\n.tab-button.el-button--default {\r\n  background: #e8e8e8;\r\n  color: #666;\r\n}\r\n\r\n/* BGM免费下载按钮样式 */\r\n.bgm-download-button {\r\n  margin-left: 20px !important;\r\n  border-radius: 15px !important;\r\n  padding: 6px 16px !important;\r\n  font-size: 13px !important;\r\n  background: linear-gradient(135deg, #67b26f 0%, #4ca2cd 100%) !important;\r\n  border: none !important;\r\n  color: white !important;\r\n  box-shadow: 0 2px 8px rgba(76, 162, 205, 0.3) !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.bgm-download-button:hover {\r\n  transform: translateY(-1px) !important;\r\n  box-shadow: 0 4px 12px rgba(76, 162, 205, 0.4) !important;\r\n  background: linear-gradient(135deg, #5a9f63 0%, #3d8bb8 100%) !important;\r\n}\r\n\r\n.bgm-download-button:active {\r\n  transform: translateY(0) !important;\r\n}\r\n\r\n/* 主要内容区域 */\r\n.materials-main {\r\n  flex: 1;\r\n  display: flex;\r\n  background: white;\r\n}\r\n\r\n/* 左侧文件夹树 - 按照图片样式 */\r\n.folder-sidebar {\r\n  width: 200px;\r\n  background: white;\r\n  border-right: 1px solid #e8e8e8;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.folder-header {\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.folder-actions {\r\n  display: flex !important;\r\n  flex-direction: column !important; /* 垂直排列 */\r\n  align-items: flex-start !important; /* 左对齐 */\r\n  gap: 8px; /* 上下间距 */\r\n  padding: 8px 0;\r\n}\r\n\r\n/* 使用更强的选择器覆盖Element UI样式 */\r\n.folder-actions .el-button,\r\n.folder-actions .el-button.el-button--small,\r\n.folder-actions .el-button.el-button--primary,\r\n.folder-actions .el-button.el-button--success {\r\n  border-radius: 6px !important;\r\n  font-weight: 500 !important;\r\n  font-size: 13px !important;\r\n  height: 32px !important;\r\n  width: 100% !important; /* 垂直排列时占满宽度 */\r\n  max-width: 120px !important; /* 限制最大宽度 */\r\n  min-height: 32px !important;\r\n  max-height: 32px !important;\r\n  line-height: 1 !important;\r\n  display: inline-flex !important;\r\n  align-items: center !important;\r\n  justify-content: center !important;\r\n  padding: 0 16px !important;\r\n  margin: 0 !important;\r\n  transition: all 0.3s ease;\r\n  flex-shrink: 0;\r\n  vertical-align: baseline !important;\r\n  box-sizing: border-box !important;\r\n  border-width: 1px !important;\r\n}\r\n\r\n.folder-actions .el-button:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 图标样式 */\r\n.folder-actions .el-button i {\r\n  margin-right: 4px !important;\r\n  line-height: 1 !important;\r\n  vertical-align: middle !important;\r\n}\r\n\r\n/* 按钮文字样式 */\r\n.folder-actions .el-button span {\r\n  line-height: 1 !important;\r\n  vertical-align: middle !important;\r\n}\r\n\r\n.add-folder-icon {\r\n  font-size: 16px;\r\n  color: #666;\r\n  cursor: pointer;\r\n}\r\n\r\n.folder-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  cursor: pointer;\r\n}\r\n\r\n.folder-list {\r\n  flex: 1;\r\n  padding: 10px 0;\r\n}\r\n\r\n.folder-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 15px;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  gap: 8px;\r\n}\r\n\r\n.folder-item:hover {\r\n  background: #f5f5f5;\r\n}\r\n\r\n.folder-item.active {\r\n  background: #e6f3ff;\r\n}\r\n\r\n.folder-icon {\r\n  font-size: 16px;\r\n}\r\n\r\n.folder-name {\r\n  flex: 1;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.folder-count {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n/* 右侧文件区域 */\r\n.files-area {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: white;\r\n}\r\n\r\n/* 文件操作栏 */\r\n.files-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 20px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  background: #fafafa;\r\n}\r\n\r\n.toolbar-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 15px;\r\n}\r\n\r\n.action-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  cursor: pointer;\r\n}\r\n\r\n.action-text:hover {\r\n  color: #4a90e2;\r\n}\r\n\r\n.toolbar-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.pagination-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 2px;\r\n}\r\n\r\n/* 文件内容区域 */\r\n.files-content {\r\n  flex: 1;\r\n  overflow: auto;\r\n}\r\n\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 300px;\r\n  color: #999;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 48px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件表格 */\r\n.files-table {\r\n  width: 100%;\r\n}\r\n\r\n.table-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 20px;\r\n  background: #fafafa;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  font-size: 14px;\r\n  color: #666;\r\n  font-weight: 500;\r\n}\r\n\r\n.table-body {\r\n  background: white;\r\n}\r\n\r\n.table-row {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 20px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.table-row:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.table-row.selected {\r\n  background: #e6f3ff;\r\n}\r\n\r\n.header-cell,\r\n.cell {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.checkbox-cell {\r\n  width: 50px;\r\n  justify-content: center;\r\n}\r\n\r\n.name-cell {\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.size-cell {\r\n  width: 100px;\r\n}\r\n\r\n.time-cell {\r\n  width: 150px;\r\n}\r\n\r\n.file-icon {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n/* 上传进度样式 */\r\n.upload-progress {\r\n  .progress-item {\r\n    margin-bottom: 15px;\r\n\r\n    .progress-info {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 5px;\r\n\r\n      .file-name {\r\n        font-size: 14px;\r\n        color: #333;\r\n        flex: 1;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .progress-text {\r\n        font-size: 12px;\r\n        color: #666;\r\n        margin-left: 10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.storage-selector {\r\n  padding: 15px;\r\n  background: #f8f9fa;\r\n  border-radius: 4px;\r\n  border: 1px solid #e8e8e8;\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n}\r\n\r\n.text-success {\r\n  color: #67c23a;\r\n  font-weight: 500;\r\n}\r\n\r\n.text-danger {\r\n  color: #f56c6c;\r\n  font-weight: 500;\r\n}\r\n\r\n/* OSS配置对话框样式 */\r\n.oss-config-dialog {\r\n  .el-dialog__header {\r\n    padding: 20px 20px 0;\r\n    border-bottom: 1px solid #f0f0f0;\r\n  }\r\n\r\n  .el-dialog__body {\r\n    padding: 0;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    padding: 20px;\r\n    border-top: 1px solid #f0f0f0;\r\n  }\r\n}\r\n\r\n.oss-config-content {\r\n  padding: 20px;\r\n}\r\n\r\n.storage-type-section {\r\n  margin-bottom: 30px;\r\n\r\n  .section-label {\r\n    font-size: 14px;\r\n    color: #333;\r\n    margin-bottom: 15px;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .storage-options {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .storage-radio {\r\n    .radio-text {\r\n      font-size: 14px;\r\n      color: #333;\r\n    }\r\n  }\r\n\r\n  .storage-description {\r\n    font-size: 12px;\r\n    color: #999;\r\n    line-height: 1.5;\r\n  }\r\n}\r\n\r\n.oss-form-section {\r\n  .form-row {\r\n    margin-bottom: 20px;\r\n\r\n    .form-label {\r\n      font-size: 14px;\r\n      color: #333;\r\n      margin-bottom: 8px;\r\n\r\n      &.required::before {\r\n        content: '*';\r\n        color: #f56c6c;\r\n        margin-right: 4px;\r\n      }\r\n    }\r\n\r\n    .form-input {\r\n      width: 100%;\r\n\r\n      .el-input__inner {\r\n        border-radius: 4px;\r\n        border: 1px solid #dcdfe6;\r\n        padding: 0 15px;\r\n        height: 40px;\r\n        line-height: 40px;\r\n\r\n        &:focus {\r\n          border-color: #409eff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .form-hint {\r\n      font-size: 12px;\r\n      color: #999;\r\n      margin-top: 5px;\r\n      line-height: 1.4;\r\n    }\r\n  }\r\n\r\n  .status-options {\r\n    display: flex;\r\n    gap: 20px;\r\n\r\n    .status-radio {\r\n      .radio-text {\r\n        font-size: 14px;\r\n        color: #333;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.dialog-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n\r\n  .cancel-btn {\r\n    padding: 8px 20px;\r\n    border-radius: 4px;\r\n    border: 1px solid #dcdfe6;\r\n    background: #fff;\r\n    color: #606266;\r\n\r\n    &:hover {\r\n      color: #409eff;\r\n      border-color: #c6e2ff;\r\n      background-color: #ecf5ff;\r\n    }\r\n  }\r\n\r\n  .confirm-btn {\r\n    padding: 8px 20px;\r\n    border-radius: 4px;\r\n    background: #409eff;\r\n    border-color: #409eff;\r\n    color: #fff;\r\n\r\n    &:hover {\r\n      background: #66b1ff;\r\n      border-color: #66b1ff;\r\n    }\r\n  }\r\n}\r\n\r\n/* 右键菜单样式 */\r\n.context-menu {\r\n  position: fixed;\r\n  background: #fff;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  z-index: 9999;\r\n  min-width: 120px;\r\n  padding: 4px 0;\r\n\r\n  .menu-item {\r\n    padding: 8px 16px;\r\n    cursor: pointer;\r\n    display: flex;\r\n    align-items: center;\r\n    font-size: 14px;\r\n    color: #606266;\r\n    transition: all 0.3s;\r\n\r\n    i {\r\n      margin-right: 8px;\r\n      font-size: 14px;\r\n    }\r\n\r\n    &:hover {\r\n      background-color: #f5f7fa;\r\n      color: #409eff;\r\n    }\r\n\r\n    &:active {\r\n      background-color: #e6f7ff;\r\n    }\r\n  }\r\n}\r\n\r\n/* 文件夹项样式增强 */\r\n.folder-item {\r\n  position: relative;\r\n  user-select: none;\r\n\r\n  &:hover {\r\n    background-color: #f5f7fa;\r\n  }\r\n\r\n  &.active {\r\n    background-color: #e6f7ff;\r\n    color: #409eff;\r\n  }\r\n}\r\n\r\n/* 文件夹操作按钮样式已在上面定义，这里删除重复定义 */\r\n\r\n/* OSS未配置提示样式 */\r\n.no-oss-tip {\r\n  padding: 20px;\r\n  text-align: center;\r\n  color: #909399;\r\n\r\n  .tip-icon {\r\n    font-size: 32px;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .tip-text {\r\n    margin-bottom: 15px;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n.no-oss-files-tip {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 400px;\r\n\r\n  .tip-content {\r\n    text-align: center;\r\n\r\n    .tip-icon {\r\n      font-size: 64px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .tip-title {\r\n      font-size: 18px;\r\n      font-weight: 500;\r\n      color: #303133;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .tip-description {\r\n      font-size: 14px;\r\n      color: #909399;\r\n      margin-bottom: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n/* 文件工具栏样式 */\r\n.file-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 16px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n\r\n  .toolbar-left {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 16px;\r\n\r\n    .selected-count {\r\n      color: #409eff;\r\n      font-size: 14px;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n\r\n  .toolbar-right {\r\n    display: flex;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n/* 文件网格样式 */\r\n.file-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));\r\n  gap: 16px;\r\n  padding: 16px;\r\n}\r\n\r\n.file-card {\r\n  position: relative;\r\n  background: #fff;\r\n  border: 2px solid transparent;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  cursor: pointer;\r\n  transition: all 0.4s ease;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  z-index: 1;\r\n\r\n  &:hover {\r\n    border-color: #409eff;\r\n    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);\r\n    transform: translateY(-2px);\r\n  }\r\n\r\n  &.selected {\r\n    border-color: #409eff;\r\n    background-color: #f0f8ff;\r\n  }\r\n\r\n  // 播放时放大效果\r\n  &.enlarged {\r\n    transform: scale(1.15) translateY(-8px);\r\n    z-index: 10;\r\n    box-shadow: 0 8px 32px rgba(64, 158, 255, 0.4);\r\n    border-color: #409eff;\r\n\r\n    .file-thumbnail {\r\n      border-radius: 8px;\r\n    }\r\n  }\r\n\r\n\r\n}\r\n\r\n// 视频放大样式 - 现代播放器设计\r\n.file-card.scale-enlarged {\r\n  position: fixed !important;\r\n  top: 50vh !important;\r\n  left: 50vw !important;\r\n  transform: translate(-50%, -50%) !important;\r\n  z-index: 9999999 !important;\r\n  border-radius: 12px !important;\r\n  overflow: hidden !important;\r\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4),\r\n              0 0 0 2px rgba(255, 255, 255, 0.1) !important;\r\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;\r\n\r\n  // 隐藏文件信息\r\n  .file-info {\r\n    display: none !important;\r\n  }\r\n\r\n  // 视频区域\r\n  .file-thumbnail {\r\n    height: 100% !important;\r\n    border-radius: 12px !important;\r\n    overflow: hidden !important;\r\n    position: relative !important;\r\n\r\n    .video-thumbnail {\r\n      border-radius: 12px !important;\r\n      position: relative !important;\r\n\r\n      .thumbnail-video {\r\n        border-radius: 12px !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 背景遮罩\r\n.scale-enlarged-backdrop {\r\n  position: fixed !important;\r\n  top: 0 !important;\r\n  left: 0 !important;\r\n  width: 100vw !important;\r\n  height: 100vh !important;\r\n  background: rgba(0, 0, 0, 0.7) !important;\r\n  backdrop-filter: blur(8px) !important;\r\n  z-index: 9999998 !important;\r\n  animation: backdropFadeIn 0.3s ease !important;\r\n}\r\n\r\n// 发光边框动画\r\n@keyframes borderGlow {\r\n  0%, 100% {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    opacity: 0.8;\r\n    transform: scale(1.02);\r\n  }\r\n}\r\n\r\n// 背景淡入动画\r\n@keyframes backdropFadeIn {\r\n  from {\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n// 小视频简洁播放按钮\r\n.simple-play-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border-radius: 12px;\r\n  transition: all 0.3s ease;\r\n\r\n  .play-button {\r\n    width: 40px;\r\n    height: 40px;\r\n    background: rgba(255, 255, 255, 0.9);\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\r\n    transition: all 0.3s ease;\r\n\r\n    i {\r\n      font-size: 16px;\r\n      color: #333;\r\n      margin-left: 2px; // 播放图标视觉居中\r\n    }\r\n\r\n    &:hover {\r\n      transform: scale(1.1);\r\n      background: white;\r\n    }\r\n  }\r\n}\r\n\r\n// 放大视频的现代控件\r\n.video-controls-overlay {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));\r\n  padding: 20px 16px 12px;\r\n  border-radius: 0 0 12px 12px;\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n\r\n  .progress-container {\r\n    margin-bottom: 12px;\r\n    cursor: pointer;\r\n\r\n    .progress-track {\r\n      position: relative;\r\n      height: 3px;\r\n      background: rgba(255, 255, 255, 0.3);\r\n      border-radius: 2px;\r\n\r\n      .progress-fill {\r\n        height: 100%;\r\n        background: #fff;\r\n        border-radius: 2px;\r\n        transition: width 0.1s ease;\r\n      }\r\n\r\n      .progress-thumb {\r\n        position: absolute;\r\n        top: 50%;\r\n        width: 10px;\r\n        height: 10px;\r\n        background: white;\r\n        border-radius: 50%;\r\n        transform: translate(-50%, -50%);\r\n        opacity: 0;\r\n        transition: all 0.2s ease;\r\n      }\r\n    }\r\n\r\n    &:hover .progress-thumb {\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .controls-bottom {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .play-pause-btn {\r\n      width: 32px;\r\n      height: 32px;\r\n      background: rgba(255, 255, 255, 0.2);\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      cursor: pointer;\r\n      transition: all 0.2s ease;\r\n\r\n      i {\r\n        font-size: 14px;\r\n        color: white;\r\n        margin-left: 1px;\r\n      }\r\n\r\n      &:hover {\r\n        background: rgba(255, 255, 255, 0.3);\r\n      }\r\n    }\r\n\r\n    .time-display {\r\n      font-size: 12px;\r\n      color: white;\r\n      font-family: 'SF Mono', Monaco, monospace;\r\n\r\n      .separator {\r\n        margin: 0 4px;\r\n        opacity: 0.7;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 鼠标悬停时显示控件\r\n.video-thumbnail:hover .video-controls-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n// Portal悬停放大层样式\r\n.hover-portal-layer {\r\n  position: fixed !important;\r\n  z-index: 999999 !important;\r\n  pointer-events: auto !important;\r\n  transition: all 0.3s ease !important;\r\n\r\n  .hover-file-card {\r\n    width: 100% !important;\r\n    height: 100% !important;\r\n    border-radius: 12px !important;\r\n    overflow: hidden !important;\r\n    box-shadow: 0 20px 60px rgba(64, 158, 255, 0.8) !important;\r\n    border: 3px solid #409eff !important;\r\n    background: #ffffff !important;\r\n    transform: scale(1) !important;\r\n    animation: portalFadeIn 0.3s ease !important;\r\n\r\n    // 确保可见性\r\n    opacity: 1 !important;\r\n    display: block !important;\r\n\r\n    // 视频样式\r\n    .video-thumbnail {\r\n      width: 100% !important;\r\n      height: calc(100% - 60px) !important;\r\n      position: relative !important;\r\n      background: #000 !important;\r\n      border-radius: 8px 8px 0 0 !important;\r\n\r\n      .thumbnail-video {\r\n        width: 100% !important;\r\n        height: 100% !important;\r\n        object-fit: cover !important;\r\n        border-radius: 8px 8px 0 0 !important;\r\n      }\r\n\r\n      .duration-badge {\r\n        position: absolute !important;\r\n        bottom: 8px !important;\r\n        right: 8px !important;\r\n        background: rgba(0, 0, 0, 0.8) !important;\r\n        color: white !important;\r\n        padding: 4px 8px !important;\r\n        border-radius: 4px !important;\r\n        font-size: 12px !important;\r\n      }\r\n\r\n      // Portal视频控制按钮\r\n      .portal-video-controls {\r\n        position: absolute !important;\r\n        bottom: 0 !important;\r\n        left: 0 !important;\r\n        right: 0 !important;\r\n        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8)) !important;\r\n        padding: 20px 16px 12px !important;\r\n        opacity: 0 !important;\r\n        transform: translateY(10px) !important;\r\n        transition: all 0.3s ease !important;\r\n        pointer-events: auto !important;\r\n\r\n        &.visible {\r\n          opacity: 1 !important;\r\n          transform: translateY(0) !important;\r\n        }\r\n\r\n        .portal-play-btn {\r\n          position: absolute !important;\r\n          top: -30px !important;\r\n          left: 50% !important;\r\n          transform: translateX(-50%) !important;\r\n          width: 48px !important;\r\n          height: 48px !important;\r\n          background: rgba(64, 158, 255, 0.9) !important;\r\n          border: none !important;\r\n          border-radius: 50% !important;\r\n          display: flex !important;\r\n          align-items: center !important;\r\n          justify-content: center !important;\r\n          cursor: pointer !important;\r\n          transition: all 0.3s ease !important;\r\n\r\n          &:hover {\r\n            background: #409eff !important;\r\n            transform: translateX(-50%) scale(1.1) !important;\r\n          }\r\n\r\n          i {\r\n            font-size: 20px !important;\r\n            color: white !important;\r\n          }\r\n        }\r\n\r\n        .portal-info {\r\n          color: white !important;\r\n          font-size: 12px !important;\r\n          text-align: center !important;\r\n          background: rgba(0, 0, 0, 0.5) !important;\r\n          padding: 4px 8px !important;\r\n          border-radius: 4px !important;\r\n          margin-top: 8px !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 音频样式\r\n    .audio-thumbnail {\r\n      width: 100% !important;\r\n      height: calc(100% - 60px) !important;\r\n      position: relative !important;\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n      border-radius: 8px 8px 0 0 !important;\r\n      display: flex !important;\r\n      flex-direction: column !important;\r\n      align-items: center !important;\r\n      justify-content: center !important;\r\n\r\n      .audio-icon {\r\n        font-size: 48px !important;\r\n        margin-bottom: 16px !important;\r\n        color: white !important;\r\n      }\r\n\r\n      .audio-waveform {\r\n        display: flex !important;\r\n        align-items: center !important;\r\n        gap: 3px !important;\r\n\r\n        .wave-bar {\r\n          width: 4px !important;\r\n          background: rgba(255, 255, 255, 0.8) !important;\r\n          border-radius: 2px !important;\r\n          animation: audioWave 1.5s ease-in-out infinite !important;\r\n\r\n          &:nth-child(1) { height: 20px !important; animation-delay: 0s !important; }\r\n          &:nth-child(2) { height: 35px !important; animation-delay: 0.1s !important; }\r\n          &:nth-child(3) { height: 25px !important; animation-delay: 0.2s !important; }\r\n          &:nth-child(4) { height: 40px !important; animation-delay: 0.3s !important; }\r\n          &:nth-child(5) { height: 30px !important; animation-delay: 0.4s !important; }\r\n          &:nth-child(6) { height: 45px !important; animation-delay: 0.5s !important; }\r\n          &:nth-child(7) { height: 35px !important; animation-delay: 0.6s !important; }\r\n          &:nth-child(8) { height: 25px !important; animation-delay: 0.7s !important; }\r\n          &:nth-child(9) { height: 40px !important; animation-delay: 0.8s !important; }\r\n          &:nth-child(10) { height: 30px !important; animation-delay: 0.9s !important; }\r\n          &:nth-child(11) { height: 35px !important; animation-delay: 1.0s !important; }\r\n          &:nth-child(12) { height: 20px !important; animation-delay: 1.1s !important; }\r\n        }\r\n      }\r\n\r\n      .duration-badge {\r\n        position: absolute !important;\r\n        bottom: 8px !important;\r\n        right: 8px !important;\r\n        background: rgba(0, 0, 0, 0.8) !important;\r\n        color: white !important;\r\n        padding: 4px 8px !important;\r\n        border-radius: 4px !important;\r\n        font-size: 12px !important;\r\n      }\r\n    }\r\n\r\n    // 图片样式\r\n    .image-thumbnail {\r\n      width: 100% !important;\r\n      height: calc(100% - 60px) !important;\r\n      position: relative !important;\r\n      border-radius: 8px 8px 0 0 !important;\r\n      overflow: hidden !important;\r\n\r\n      .thumbnail-image {\r\n        width: 100% !important;\r\n        height: 100% !important;\r\n        object-fit: cover !important;\r\n        border-radius: 8px 8px 0 0 !important;\r\n      }\r\n    }\r\n\r\n    // 文件信息\r\n    .file-info {\r\n      height: 60px !important;\r\n      padding: 12px 16px !important;\r\n      background: #f8f9fa !important;\r\n      border-radius: 0 0 8px 8px !important;\r\n\r\n      .file-name {\r\n        font-size: 14px !important;\r\n        font-weight: 600 !important;\r\n        color: #333 !important;\r\n        margin-bottom: 4px !important;\r\n        white-space: nowrap !important;\r\n        overflow: hidden !important;\r\n        text-overflow: ellipsis !important;\r\n      }\r\n\r\n      .file-meta {\r\n        display: flex !important;\r\n        gap: 12px !important;\r\n        font-size: 12px !important;\r\n        color: #666 !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// Portal动画\r\n@keyframes portalFadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: scale(0.8);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n@keyframes audioWave {\r\n  0%, 100% {\r\n    transform: scaleY(0.5);\r\n    opacity: 0.7;\r\n  }\r\n  50% {\r\n    transform: scaleY(1);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.file-checkbox {\r\n  position: absolute;\r\n  top: 8px;\r\n  left: 8px;\r\n  z-index: 10;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  border-radius: 4px;\r\n  padding: 2px;\r\n}\r\n\r\n.file-thumbnail {\r\n  width: 100%;\r\n  height: 120px;\r\n  position: relative;\r\n  overflow: hidden;\r\n  background: #f5f7fa;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n\r\n}\r\n\r\n/* 视频缩略图样式 */\r\n.video-thumbnail {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .thumbnail-video {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n  }\r\n\r\n\r\n\r\n  .duration-badge {\r\n    position: absolute;\r\n    bottom: 8px;\r\n    right: 8px;\r\n    background: rgba(0, 0, 0, 0.7);\r\n    color: white;\r\n    padding: 2px 6px;\r\n    border-radius: 4px;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n/* 音频缩略图样式 */\r\n.audio-thumbnail {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n  transition: all 0.4s ease;\r\n\r\n  &:hover {\r\n    transform: scale(1.05);\r\n  }\r\n\r\n  &.playing {\r\n    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\r\n  }\r\n\r\n  .audio-icon {\r\n    font-size: 32px;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .audio-waveform {\r\n    display: flex;\r\n    align-items: flex-end;\r\n    gap: 2px;\r\n    height: 20px;\r\n\r\n    .wave-bar {\r\n      width: 3px;\r\n      background: rgba(255, 255, 255, 0.8);\r\n      border-radius: 2px;\r\n      animation: wave 1.5s ease-in-out infinite;\r\n\r\n      &:nth-child(1) { height: 8px; animation-delay: 0s; }\r\n      &:nth-child(2) { height: 12px; animation-delay: 0.1s; }\r\n      &:nth-child(3) { height: 16px; animation-delay: 0.2s; }\r\n      &:nth-child(4) { height: 10px; animation-delay: 0.3s; }\r\n      &:nth-child(5) { height: 14px; animation-delay: 0.4s; }\r\n      &:nth-child(6) { height: 18px; animation-delay: 0.5s; }\r\n      &:nth-child(7) { height: 12px; animation-delay: 0.6s; }\r\n      &:nth-child(8) { height: 8px; animation-delay: 0.7s; }\r\n      &:nth-child(9) { height: 15px; animation-delay: 0.8s; }\r\n      &:nth-child(10) { height: 11px; animation-delay: 0.9s; }\r\n      &:nth-child(11) { height: 9px; animation-delay: 1s; }\r\n      &:nth-child(12) { height: 13px; animation-delay: 1.1s; }\r\n    }\r\n  }\r\n\r\n  .play-overlay-audio {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    background: rgba(0, 0, 0, 0.7);\r\n    border-radius: 50%;\r\n    width: 40px;\r\n    height: 40px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: white;\r\n    font-size: 16px;\r\n    opacity: 1;\r\n    transition: all 0.3s ease;\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      background: rgba(0, 0, 0, 0.9);\r\n      transform: translate(-50%, -50%) scale(1.1);\r\n    }\r\n\r\n    &.playing {\r\n      background: rgba(255, 0, 0, 0.8);\r\n\r\n      &:hover {\r\n        background: rgba(255, 0, 0, 1);\r\n      }\r\n    }\r\n\r\n    &.visible {\r\n      opacity: 1;\r\n      visibility: visible;\r\n    }\r\n\r\n    &.hidden {\r\n      opacity: 0;\r\n      visibility: hidden;\r\n    }\r\n  }\r\n\r\n  .duration-badge {\r\n    position: absolute;\r\n    bottom: 8px;\r\n    right: 8px;\r\n    background: rgba(0, 0, 0, 0.7);\r\n    color: white;\r\n    padding: 2px 6px;\r\n    border-radius: 4px;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n@keyframes wave {\r\n  0%, 100% { transform: scaleY(0.5); }\r\n  50% { transform: scaleY(1); }\r\n}\r\n\r\n/* 图片缩略图样式 */\r\n.image-thumbnail {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  cursor: pointer;\r\n  overflow: hidden;\r\n\r\n  &:hover {\r\n    .thumbnail-image {\r\n      transform: scale(1.05);\r\n    }\r\n\r\n    .image-overlay {\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .thumbnail-image {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n    transition: transform 0.3s ease;\r\n  }\r\n\r\n  .image-overlay {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.4);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    opacity: 0;\r\n    transition: opacity 0.3s ease;\r\n\r\n    i {\r\n      color: white;\r\n      font-size: 24px;\r\n    }\r\n  }\r\n}\r\n\r\n/* 文件图标缩略图样式 */\r\n.file-icon-thumbnail {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 48px;\r\n  color: #909399;\r\n}\r\n\r\n\r\n\r\n/* 文件信息样式 */\r\n.file-card .file-info {\r\n  padding: 12px;\r\n\r\n  .file-name {\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    color: #303133;\r\n    margin-bottom: 4px;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n  }\r\n\r\n  .file-meta {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    font-size: 12px;\r\n    color: #909399;\r\n\r\n    .file-size {\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n/* 文件预览对话框样式 */\r\n.preview-dialog {\r\n  .el-dialog__body {\r\n    padding: 20px;\r\n  }\r\n}\r\n\r\n.preview-content {\r\n  .preview-header {\r\n    margin-bottom: 20px;\r\n\r\n    h3 {\r\n      margin: 0 0 10px 0;\r\n      color: #303133;\r\n      font-size: 18px;\r\n    }\r\n\r\n    .file-info {\r\n      display: flex;\r\n      gap: 20px;\r\n      color: #909399;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  .video-preview {\r\n    text-align: center;\r\n\r\n    video {\r\n      border-radius: 8px;\r\n      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    }\r\n  }\r\n\r\n  .audio-preview {\r\n    .audio-player {\r\n      margin-bottom: 20px;\r\n      text-align: center;\r\n    }\r\n\r\n    .audio-info {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 20px;\r\n      background-color: #f8f9fa;\r\n      border-radius: 8px;\r\n\r\n      .audio-icon {\r\n        font-size: 48px;\r\n        margin-right: 20px;\r\n      }\r\n\r\n      .audio-details {\r\n        p {\r\n          margin: 5px 0;\r\n          color: #606266;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .image-preview {\r\n    text-align: center;\r\n\r\n    img {\r\n      border-radius: 8px;\r\n      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    }\r\n  }\r\n\r\n  .unsupported-preview {\r\n    text-align: center;\r\n    padding: 40px;\r\n\r\n    .unsupported-icon {\r\n      font-size: 64px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    p {\r\n      color: #909399;\r\n      margin-bottom: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n/* OSS未配置提示样式 */\r\n.no-oss-tip {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40px 20px;\r\n  text-align: center;\r\n  color: #909399;\r\n\r\n  .tip-icon {\r\n    font-size: 48px;\r\n    margin-bottom: 16px;\r\n  }\r\n\r\n  .tip-text {\r\n    font-size: 14px;\r\n    margin-bottom: 16px;\r\n    color: #606266;\r\n  }\r\n}\r\n\r\n.no-oss-files-tip {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100%;\r\n  min-height: 400px;\r\n\r\n  .tip-content {\r\n    text-align: center;\r\n\r\n    .tip-icon {\r\n      font-size: 64px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .tip-title {\r\n      font-size: 18px;\r\n      font-weight: 500;\r\n      color: #303133;\r\n      margin-bottom: 12px;\r\n    }\r\n\r\n    .tip-description {\r\n      font-size: 14px;\r\n      color: #909399;\r\n      margin-bottom: 24px;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .folder-sidebar {\r\n    width: 150px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .materials-main {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .folder-sidebar {\r\n    width: 100%;\r\n    height: 200px;\r\n    border-right: none;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .files-toolbar {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 10px;\r\n  }\r\n\r\n  .toolbar-right {\r\n    align-self: flex-end;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwiBA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAC,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,IAAA;IACA,OAAAA,IAAA;MACA;MACAC,WAAA;MAAA;MACAC,cAAA;MACAC,SAAA;MACAC,aAAA;MACAC,WAAA;MACAC,QAAA;MAEA;MACAC,aAAA;MACAC,eAAA;MAEA;MACAC,mBAAA;MACAC,SAAA;MACAC,QAAA;MACAC,UAAA;MACAC,cAAA;MAAA;MACAC,WAAA;MAAA;;MAEA;MACAC,gBAAA;MACAC,UAAA;MACAC,SAAA;QACAH,WAAA;QAAA;QACAI,MAAA;QAAA;QACAC,WAAA;QAAA;QACAC,eAAA;QAAA;QACAC,QAAA;QAAA;QACAC,MAAA;MACA;MACAC,cAAA;MAAA;;MAEA;MACAC,aAAA;MAAA;MACAC,mBAAA;MAAA;MACAC,aAAA;MAAA;MACAC,gBAAA;IAAA,OAAAC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAA7B,IAAA,oBACA,2BAGA,wBACA,oBACA,yBACA,8BAGA,4BACA;MACAF,IAAA;IACA,sBAGA,8BACA,wBAGA,SAAA8B,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAA7B,IAAA,aAKA,CACA;MACA8B,EAAA;MACAhC,IAAA;MACAiC,IAAA;MACAC,IAAA;MACAC,UAAA;MACAC,QAAA;MACAC,GAAA;IACA,EACA,gBAGA,CACA;MACAL,EAAA;MACAhC,IAAA;MACAiC,IAAA;MACAC,IAAA;MACAC,UAAA;MACAC,QAAA;MACAC,GAAA;IACA,GACA;MACAL,EAAA;MACAhC,IAAA;MACAiC,IAAA;MACAC,IAAA;MACAC,UAAA;MACAE,GAAA;IACA,GACA;MACAL,EAAA;MACAhC,IAAA;MACAiC,IAAA;MACAC,IAAA;MACAC,UAAA;MACAC,QAAA;MACAC,GAAA;IACA,EACA;EAEA;EACAC,QAAA;IACA;IACAC,mBAAA,WAAAA,oBAAA;MACA,aAAApC,WAAA;QACA;UACA,YAAAqC,OAAA;QACA;UACA,YAAAC,SAAA;QACA;UACA,YAAAA,SAAA;MACA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAA;MACA,aAAAvC,WAAA;QACA;UACA,YAAAM,aAAA;QACA;UACA,YAAAC,eAAA;QACA;UACA,YAAAA,eAAA;MACA;IACA;IAEAiC,oBAAA,WAAAA,qBAAA;MACA,IAAAC,IAAA,QAAAL,mBAAA;;MAEA;MACA,SAAAnC,cAAA;QACA;QACA;MAAA;MAGA,OAAAwC,IAAA;IACA;IAEAC,kBAAA,WAAAA,mBAAA;MACA,IAAAC,KAAA,SAAAvC,WAAA,aAAAC,QAAA;MACA,IAAAuC,GAAA,GAAAD,KAAA,QAAAtC,QAAA;MACA,YAAAmC,oBAAA,CAAAK,KAAA,CAAAF,KAAA,EAAAC,GAAA;IACA;IAEAE,UAAA,WAAAA,WAAA;MACA,OAAAC,IAAA,CAAAC,IAAA,MAAAR,oBAAA,CAAAS,MAAA,QAAA5C,QAAA;IACA;IAEA;IACA6C,iBAAA,WAAAA,kBAAA;MACA,aAAAlD,WAAA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IAEAmD,aAAA,WAAAA,cAAA;MACA,aAAAnD,WAAA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;EAGA;EACAoD,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,WAAAC,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAAC,QAAA;MAAA,WAAAF,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,CAAA;UAAA;YACA;YACAP,KAAA,CAAAQ,aAAA;;YAEA;YAAA,KACAR,KAAA,CAAA/B,cAAA;cAAAqC,QAAA,CAAAC,CAAA;cAAA;YAAA;YAAAD,QAAA,CAAAC,CAAA;YAAA,OACAP,KAAA,CAAAS,gBAAA;UAAA;YAAA,OAAAH,QAAA,CAAAI,CAAA;QAAA;MAAA,GAAAN,OAAA;IAAA;EAEA;EACAO,OAAA,MAAArC,gBAAA,CAAAC,OAAA;IACA;IACAqC,mBAAA,WAAAA,oBAAA;MACAC,MAAA,CAAAC,IAAA;MACA,KAAAC,QAAA,CAAAC,OAAA;IACA;IAEA;IACAP,gBAAA,WAAAA,iBAAA;MAAA,IAAAQ,MAAA;MAAA,WAAAhB,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAAe,SAAA;QAAA,WAAAhB,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAAc,SAAA;UAAA,kBAAAA,SAAA,CAAAZ,CAAA;YAAA;cACAa,OAAA,CAAAC,GAAA;;cAEA;cAAA,KACAJ,MAAA,CAAAhD,cAAA;gBAAAkD,SAAA,CAAAZ,CAAA;gBAAA;cAAA;cAAAY,SAAA,CAAAZ,CAAA;cAAA,OACAU,MAAA,CAAAK,gBAAA;YAAA;cAAAH,SAAA,CAAAZ,CAAA;cAAA;YAAA;cAEAa,OAAA,CAAAC,GAAA;YAAA;cAGA;cACAJ,MAAA,CAAAM,4BAAA;YAAA;cAAA,OAAAJ,SAAA,CAAAT,CAAA;UAAA;QAAA,GAAAQ,QAAA;MAAA;IACA;IAEA;IACAI,gBAAA,WAAAA,iBAAA;MAAA,IAAAE,MAAA;MAAA,WAAAvB,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAAsB,SAAA;QAAA,IAAAC,MAAA,EAAAC,IAAA,EAAAzD,aAAA,EAAA0D,EAAA;QAAA,WAAA1B,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAAwB,SAAA;UAAA,kBAAAA,SAAA,CAAAC,CAAA,GAAAD,SAAA,CAAAtB,CAAA;YAAA;cAAAsB,SAAA,CAAAC,CAAA;cAEAJ,MAAA,OAAAK,uBAAA;cACAJ,IAAA,GAAAH,MAAA,CAAAQ,cAAA;cACA9D,aAAA,GAAAsD,MAAA,CAAAS,gBAAA,IAEA;cACAT,MAAA,CAAAxC,OAAA;cACAwC,MAAA,CAAAvC,SAAA;;cAEA;cAAA4C,SAAA,CAAAtB,CAAA;cAAA,OACAiB,MAAA,CAAAU,mBAAA,SAAAP,IAAA,EAAAzD,aAAA;YAAA;cAAA2D,SAAA,CAAAtB,CAAA;cAAA,OAGAiB,MAAA,CAAAU,mBAAA,WAAAP,IAAA,EAAAzD,aAAA;YAAA;cAEAkD,OAAA,CAAAC,GAAA,yDAAAc,MAAA,CAAAX,MAAA,CAAAxC,OAAA,CAAAY,MAAA,4BAAAuC,MAAA,CAAAX,MAAA,CAAAvC,SAAA,CAAAW,MAAA;cAAAiC,SAAA,CAAAtB,CAAA;cAAA;YAAA;cAAAsB,SAAA,CAAAC,CAAA;cAAAF,EAAA,GAAAC,SAAA,CAAAO,CAAA;cAGAhB,OAAA,CAAAiB,KAAA,kBAAAT,EAAA;YAAA;cAAA,OAAAC,SAAA,CAAAnB,CAAA;UAAA;QAAA,GAAAe,QAAA;MAAA;IAEA;IAEA;IACAS,mBAAA,WAAAA,oBAAAI,UAAA,EAAAX,IAAA,EAAAY,MAAA,EAAAC,QAAA;MAAA,IAAAC,MAAA;MAAA,WAAAxC,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAAuC,SAAA;QAAA,IAAAhB,MAAA,EAAAiB,MAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,cAAA,EAAAC,gBAAA,EAAAC,GAAA;QAAA,WAAA9C,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAA4C,SAAA;UAAA,kBAAAA,SAAA,CAAAnB,CAAA,GAAAmB,SAAA,CAAA1C,CAAA;YAAA;cAAA0C,SAAA,CAAAnB,CAAA;cAEAJ,MAAA,OAAAK,uBAAA;cACAY,MAAA,MAAAR,MAAA,CAAAG,UAAA,OAAAH,MAAA,CAAAR,IAAA,OAAAQ,MAAA,CAAAI,MAAA;cAAAU,SAAA,CAAA1C,CAAA;cAAA,OAEAmB,MAAA,CAAAtC,IAAA;gBACAuD,MAAA,EAAAA,MAAA;gBACA;cACA;YAAA;cAHAC,MAAA,GAAAK,SAAA,CAAAb,CAAA;cAKA,IAAAQ,MAAA,CAAAM,OAAA;gBACAL,KAAA,GAAAD,MAAA,CAAAM,OAAA,CACAC,MAAA,WAAAC,GAAA;kBAAA,QAAAA,GAAA,CAAA5G,IAAA,CAAA6G,QAAA,cAAAD,GAAA,CAAA5G,IAAA,CAAA6G,QAAA;gBAAA,GACAC,GAAA,WAAAF,GAAA,EAAAG,KAAA;kBACA,IAAAC,QAAA,GAAAJ,GAAA,CAAA5G,IAAA,CAAAiH,KAAA,MAAAC,GAAA;kBACA,IAAAC,aAAA,GAAAH,QAAA,CAAAI,WAAA,GAAAH,KAAA,MAAAC,GAAA;;kBAEA;kBACA,IAAAG,SAAA;kBACA,IAAAC,SAAA;kBAEA,IAAAC,QAAA;kBACA,IAAAF,SAAA,CAAAG,QAAA,CAAAL,aAAA;oBACAI,QAAA;kBACA,WAAAD,SAAA,CAAAE,QAAA,CAAAL,aAAA;oBACAI,QAAA;kBACA;kBAEA;oBACAvF,EAAA,EAAAyF,IAAA,CAAAC,GAAA,KAAAxE,IAAA,CAAAyE,MAAA,KAAAZ,KAAA;oBACA/G,IAAA,EAAAgH,QAAA;oBACA/E,IAAA,EAAAsF,QAAA;oBACArF,IAAA,EAAA0E,GAAA,CAAA1E,IAAA;oBACAC,UAAA,MAAAsF,IAAA,CAAAb,GAAA,CAAAgB,YAAA,EAAAC,cAAA,GAAA7E,KAAA;oBACAZ,QAAA,EAAAmF,QAAA;oBAAA;oBACAO,UAAA,EAAAP,QAAA,6BAAAQ,SAAA;oBACAC,OAAA,EAAAT,QAAA,2BAAAQ,SAAA;oBACA1F,GAAA,aAAAsD,MAAA,CAAAM,MAAA,CAAA9E,SAAA,CAAAC,MAAA,OAAAuE,MAAA,CAAAM,MAAA,CAAA9E,SAAA,CAAAI,QAAA,CAAA0G,OAAA,iBAAAA,OAAA,CAAAhC,MAAA,CAAA9E,SAAA,CAAAC,MAAA,kBAAAuE,MAAA,CAAAiB,GAAA,CAAA5G,IAAA;oBACAkI,WAAA,EAAAtB,GAAA,CAAA5G,IAAA;oBACA+F,MAAA,KAAAJ,MAAA,CAAAG,UAAA,OAAAH,MAAA,CAAAR,IAAA,OAAAQ,MAAA,CAAAI,MAAA;kBACA;gBACA,IAEA;gBACA,IAAAC,QAAA;kBACA,CAAAM,cAAA,GAAAL,MAAA,CAAAzD,OAAA,EAAA2F,IAAA,CAAAC,KAAA,CAAA9B,cAAA,MAAA+B,mBAAA,CAAAtG,OAAA,EAAAsE,KAAA;gBACA,WAAAL,QAAA;kBACA,CAAAO,gBAAA,GAAAN,MAAA,CAAAxD,SAAA,EAAA0F,IAAA,CAAAC,KAAA,CAAA7B,gBAAA,MAAA8B,mBAAA,CAAAtG,OAAA,EAAAsE,KAAA;gBACA;cACA;cAAAI,SAAA,CAAA1C,CAAA;cAAA;YAAA;cAAA0C,SAAA,CAAAnB,CAAA;cAAAkB,GAAA,GAAAC,SAAA,CAAAb,CAAA;cAGAhB,OAAA,CAAAiB,KAAA,6BAAAF,MAAA,CAAAG,UAAA,OAAAH,MAAA,CAAAR,IAAA,OAAAQ,MAAA,CAAAI,MAAA,6CAAAS,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAvC,CAAA;UAAA;QAAA,GAAAgC,QAAA;MAAA;IAEA;IAEA;IACAlC,aAAA,WAAAA,cAAA;MACA;MACA,IAAAsE,WAAA,GAAAC,YAAA,CAAAC,OAAA;MACA,IAAAF,WAAA;QACA;UACA,KAAAnH,SAAA,OAAAsH,cAAA,CAAA1G,OAAA,MAAA0G,cAAA,CAAA1G,OAAA,WAAAZ,SAAA,GAAAuH,IAAA,CAAAC,KAAA,CAAAL,WAAA;UACA,SAAAnH,SAAA,CAAAE,WAAA,SAAAF,SAAA,CAAAG,eAAA;YACA,KAAAsH,aAAA;UACA;QACA,SAAA/C,KAAA;UACAjB,OAAA,CAAAiB,KAAA,eAAAA,KAAA;QACA;MACA;IACA;IAEAgD,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MAAA,WAAArF,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAAoF,SAAA;QAAA,IAAA1G,GAAA,EAAA2G,QAAA,EAAAC,KAAA,EAAAC,MAAA,EAAA9H,MAAA,EAAA+H,MAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,WAAA3F,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAAyF,SAAA;UAAA,kBAAAA,SAAA,CAAAhE,CAAA,GAAAgE,SAAA,CAAAvF,CAAA;YAAA;cAAA,IAEA+E,MAAA,CAAA3H,SAAA,CAAAC,MAAA;gBAAAkI,SAAA,CAAAvF,CAAA;gBAAA;cAAA;cACA+E,MAAA,CAAAvE,QAAA,CAAAsB,KAAA;cAAA,OAAAyD,SAAA,CAAApF,CAAA;YAAA;cAAA,IAGA4E,MAAA,CAAA3H,SAAA,CAAAE,WAAA;gBAAAiI,SAAA,CAAAvF,CAAA;gBAAA;cAAA;cACA+E,MAAA,CAAAvE,QAAA,CAAAsB,KAAA;cAAA,OAAAyD,SAAA,CAAApF,CAAA;YAAA;cAAA,IAGA4E,MAAA,CAAA3H,SAAA,CAAAG,eAAA;gBAAAgI,SAAA,CAAAvF,CAAA;gBAAA;cAAA;cACA+E,MAAA,CAAAvE,QAAA,CAAAsB,KAAA;cAAA,OAAAyD,SAAA,CAAApF,CAAA;YAAA;cAAA,IAGA4E,MAAA,CAAA3H,SAAA,CAAAI,QAAA;gBAAA+H,SAAA,CAAAvF,CAAA;gBAAA;cAAA;cACA+E,MAAA,CAAAvE,QAAA,CAAAsB,KAAA;cAAA,OAAAyD,SAAA,CAAApF,CAAA;YAAA;cAAA,MAKA,CAAA4E,MAAA,CAAA3H,SAAA,CAAAI,QAAA,CAAAgI,UAAA,gBAAAT,MAAA,CAAA3H,SAAA,CAAAI,QAAA,CAAAgI,UAAA;gBAAAD,SAAA,CAAAvF,CAAA;gBAAA;cAAA;cACA+E,MAAA,CAAAvE,QAAA,CAAAsB,KAAA;cAAA,OAAAyD,SAAA,CAAApF,CAAA;YAAA;cAAAoF,SAAA,CAAAhE,CAAA;cAMAjD,GAAA,OAAAmH,GAAA,CAAAV,MAAA,CAAA3H,SAAA,CAAAI,QAAA;cACAyH,QAAA,GAAA3G,GAAA,CAAA2G,QAAA;cACAC,KAAA,GAAAD,QAAA,CAAAC,KAAA;cAAA,IAEAA,KAAA;gBAAAK,SAAA,CAAAvF,CAAA;gBAAA;cAAA;cACA+E,MAAA,CAAAvE,QAAA,CAAAsB,KAAA;cAAA,OAAAyD,SAAA,CAAApF,CAAA;YAAA;cAAAgF,MAAA,OAAAO,eAAA,CAAA1H,OAAA,EAIAkH,KAAA,MAAA7H,MAAA,GAAA8H,MAAA,KAAAC,MAAA,GAAAD,MAAA;cAAA,MACA9H,MAAA,KAAA0H,MAAA,CAAA3H,SAAA,CAAAC,MAAA;gBAAAkI,SAAA,CAAAvF,CAAA;gBAAA;cAAA;cACA+E,MAAA,CAAAvE,QAAA,CAAAsB,KAAA,2DAAAF,MAAA,CAAAvE,MAAA,8DAAAuE,MAAA,CAAAmD,MAAA,CAAA3H,SAAA,CAAAC,MAAA;cAAA,OAAAkI,SAAA,CAAApF,CAAA;YAAA;cAAAoF,SAAA,CAAAvF,CAAA;cAAA;YAAA;cAAAuF,SAAA,CAAAhE,CAAA;cAAA8D,GAAA,GAAAE,SAAA,CAAA1D,CAAA;cAIAkD,MAAA,CAAAvE,QAAA,CAAAsB,KAAA;cAAA,OAAAyD,SAAA,CAAApF,CAAA;YAAA;cAIA4E,MAAA,CAAA5H,UAAA;cAAAoI,SAAA,CAAAhE,CAAA;cAAAgE,SAAA,CAAAvF,CAAA;cAAA,OAIA+E,MAAA,CAAAF,aAAA;YAAA;cAEA;cACAL,YAAA,CAAAmB,OAAA,cAAAhB,IAAA,CAAAiB,SAAA,CAAAb,MAAA,CAAA3H,SAAA;cAEA2H,MAAA,CAAA7H,gBAAA;cACA6H,MAAA,CAAAvE,QAAA,CAAAC,OAAA;cAAA8E,SAAA,CAAAvF,CAAA;cAAA;YAAA;cAAAuF,SAAA,CAAAhE,CAAA;cAAA+D,GAAA,GAAAC,SAAA,CAAA1D,CAAA;cAGAhB,OAAA,CAAAiB,KAAA,eAAAwD,GAAA;cACAP,MAAA,CAAAvE,QAAA,CAAAsB,KAAA,iCAAAF,MAAA,CAAA0D,GAAA,CAAAO,OAAA;YAAA;cAAAN,SAAA,CAAAhE,CAAA;cAEAwD,MAAA,CAAA5H,UAAA;cAAA,OAAAoI,SAAA,CAAAO,CAAA;YAAA;cAAA,OAAAP,SAAA,CAAApF,CAAA;UAAA;QAAA,GAAA6E,QAAA;MAAA;IAEA;IAEAH,aAAA,WAAAA,cAAA;MAAA,IAAAkB,MAAA;MAAA,WAAArG,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAAoG,SAAA;QAAA,IAAA7E,MAAA,EAAA8E,GAAA;QAAA,WAAAtG,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAAoG,SAAA;UAAA,kBAAAA,SAAA,CAAA3E,CAAA,GAAA2E,SAAA,CAAAlG,CAAA;YAAA;cAAAkG,SAAA,CAAA3E,CAAA;cAEAJ,MAAA,OAAAgF,wBAAA,EAAAJ,MAAA,CAAA3I,SAAA;cACA2I,MAAA,CAAArI,cAAA;;cAEA;cAAAwI,SAAA,CAAAlG,CAAA;cAAA,OACA+F,MAAA,CAAAK,sBAAA;YAAA;cAEAvF,OAAA,CAAAC,GAAA;cAAA,OAAAoF,SAAA,CAAA/F,CAAA,IACAgB,MAAA;YAAA;cAAA+E,SAAA,CAAA3E,CAAA;cAAA0E,GAAA,GAAAC,SAAA,CAAArE,CAAA;cAEAkE,MAAA,CAAArI,cAAA;cACAmD,OAAA,CAAAiB,KAAA,iBAAAmE,GAAA;cAAA,MAAAA,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAA/F,CAAA;UAAA;QAAA,GAAA6F,QAAA;MAAA;IAGA;IAEAK,aAAA,WAAAA,cAAA;MACA,KAAAnJ,gBAAA;IACA;IAEA;IACAoJ,sBAAA,WAAAA,uBAAA;MACA,KAAAC,gBAAA,CAAAtK,IAAA;MACA,KAAAuK,mBAAA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MAAA,WAAAhH,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAA+G,SAAA;QAAA,IAAAC,UAAA,EAAAC,cAAA,EAAAC,SAAA;QAAA,WAAAnH,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAAiH,SAAA;UAAA,kBAAAA,SAAA,CAAA/G,CAAA;YAAA;cACAa,OAAA,CAAAC,GAAA;cAAA,MAEA,CAAA4F,MAAA,CAAAH,gBAAA,CAAAtK,IAAA,IAAAyK,MAAA,CAAAH,gBAAA,CAAAtK,IAAA,CAAA+K,IAAA;gBAAAD,SAAA,CAAA/G,CAAA;gBAAA;cAAA;cACA0G,MAAA,CAAAlG,QAAA,CAAAsB,KAAA;cAAA,OAAAiF,SAAA,CAAA5G,CAAA;YAAA;cAIAyG,UAAA,GAAAF,MAAA,CAAAH,gBAAA,CAAAtK,IAAA,CAAA+K,IAAA;cACAnG,OAAA,CAAAC,GAAA,cAAA8F,UAAA;cACA/F,OAAA,CAAAC,GAAA,cAAA4F,MAAA,CAAAtK,WAAA;;cAEA;cACAyK,cAAA,GAAAH,MAAA,CAAA/H,iBAAA;cACAkC,OAAA,CAAAC,GAAA,gBAAA+F,cAAA,CAAA9D,GAAA,WAAA+C,CAAA;gBAAA,OAAAA,CAAA,CAAA7J,IAAA;cAAA;cAAA,KAEA4K,cAAA,CAAAI,IAAA,WAAAjF,MAAA;gBAAA,OAAAA,MAAA,CAAA/F,IAAA,KAAA2K,UAAA;cAAA;gBAAAG,SAAA,CAAA/G,CAAA;gBAAA;cAAA;cACA0G,MAAA,CAAAlG,QAAA,CAAAsB,KAAA,IAAAF,MAAA,CAAA8E,MAAA,CAAAtK,WAAA;cAAA,OAAA2K,SAAA,CAAA5G,CAAA;YAAA;cAIA;gBACAU,OAAA,CAAAC,GAAA;;gBAEA;gBACAgG,SAAA;kBACA7I,EAAA,EAAAyF,IAAA,CAAAC,GAAA,KAAAxE,IAAA,CAAAyE,MAAA;kBAAA;kBACA3H,IAAA,EAAA2K,UAAA;kBACAM,KAAA;gBACA;gBAEA,IAAAR,MAAA,CAAAtK,WAAA;kBACAsK,MAAA,CAAAhK,aAAA,CAAA0H,IAAA,CAAA0C,SAAA;kBACAjG,OAAA,CAAAC,GAAA;gBACA;kBACA4F,MAAA,CAAA/J,eAAA,CAAAyH,IAAA,CAAA0C,SAAA;kBACAjG,OAAA,CAAAC,GAAA;gBACA;gBAEAD,OAAA,CAAAC,GAAA;gBACA4F,MAAA,CAAAF,mBAAA;gBACAE,MAAA,CAAAlG,QAAA,CAAAC,OAAA,IAAAmB,MAAA,CAAA8E,MAAA,CAAAtK,WAAA;;gBAEA;gBACA,IAAAsK,MAAA,CAAAzJ,WAAA,cAAAyJ,MAAA,CAAAhJ,cAAA;kBACAgJ,MAAA,CAAAS,oBAAA,CAAAP,UAAA;gBACA;cAEA,SAAA9E,KAAA;gBACAjB,OAAA,CAAAiB,KAAA,eAAAA,KAAA;gBACA4E,MAAA,CAAAlG,QAAA,CAAAsB,KAAA,8BAAAF,MAAA,CAAAE,KAAA,CAAA+D,OAAA;cACA;YAAA;cAAA,OAAAkB,SAAA,CAAA5G,CAAA;UAAA;QAAA,GAAAwG,QAAA;MAAA;IACA;IAEA;IACAQ,oBAAA,WAAAA,qBAAAP,UAAA;MAAA,IAAAQ,MAAA;MAAA,WAAA1H,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAAyH,SAAA;QAAA,IAAAC,GAAA;QAAA,WAAA3H,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAAyH,SAAA;UAAA,kBAAAA,SAAA,CAAAhG,CAAA,GAAAgG,SAAA,CAAAvH,CAAA;YAAA;cAAAuH,SAAA,CAAAhG,CAAA;cAEAV,OAAA,CAAAC,GAAA,qBAAA8F,UAAA;cAAAW,SAAA,CAAAvH,CAAA;cAAA,OACAoH,MAAA,CAAAI,4BAAA,CAAAZ,UAAA;YAAA;cACA/F,OAAA,CAAAC,GAAA;cAAAyG,SAAA,CAAAvH,CAAA;cAAA;YAAA;cAAAuH,SAAA,CAAAhG,CAAA;cAAA+F,GAAA,GAAAC,SAAA,CAAA1F,CAAA;cAEAhB,OAAA,CAAA4G,IAAA,mBAAAH,GAAA,CAAAzB,OAAA;YAAA;cAAA,OAAA0B,SAAA,CAAApH,CAAA;UAAA;QAAA,GAAAkH,QAAA;MAAA;IAEA;IAEA;IACAK,eAAA,WAAAA,gBAAAd,UAAA;MAAA,IAAAe,MAAA;MAAA,WAAAjI,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAAgI,SAAA;QAAA,IAAAzG,MAAA,EAAAC,IAAA,EAAAyG,WAAA,EAAAC,EAAA,EAAAC,YAAA,EAAAhG,UAAA,EAAAiG,UAAA,EAAAC,GAAA;QAAA,WAAAtI,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAAoI,SAAA;UAAA,kBAAAA,SAAA,CAAA3G,CAAA,GAAA2G,SAAA,CAAAlI,CAAA;YAAA;cAAAkI,SAAA,CAAA3G,CAAA;cAEAJ,MAAA,OAAAK,uBAAA;cACAJ,IAAA,GAAAuG,MAAA,CAAAlG,cAAA,IAEA;cACAoG,WAAA;cAAAC,EAAA,MAAAC,YAAA,GAEAF,WAAA;YAAA;cAAA,MAAAC,EAAA,GAAAC,YAAA,CAAA1I,MAAA;gBAAA6I,SAAA,CAAAlI,CAAA;gBAAA;cAAA;cAAA+B,UAAA,GAAAgG,YAAA,CAAAD,EAAA;cACAE,UAAA,MAAApG,MAAA,CAAAG,UAAA,OAAAH,MAAA,CAAAR,IAAA,OAAAQ,MAAA,CAAAgF,UAAA,aAEA;cAAAsB,SAAA,CAAAlI,CAAA;cAAA,OACAmB,MAAA,CAAAgH,GAAA,CAAAH,UAAA,EAAAI,MAAA,CAAAC,IAAA;YAAA;cACAxH,OAAA,CAAAC,GAAA,uCAAAc,MAAA,CAAAoG,UAAA;YAAA;cAAAF,EAAA;cAAAI,SAAA,CAAAlI,CAAA;cAAA;YAAA;cAAA,OAAAkI,SAAA,CAAA/H,CAAA,IAGA;YAAA;cAAA+H,SAAA,CAAA3G,CAAA;cAAA0G,GAAA,GAAAC,SAAA,CAAArG,CAAA;cAEAhB,OAAA,CAAAiB,KAAA,gBAAAmG,GAAA;cAAA,MAAAA,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAA/H,CAAA;UAAA;QAAA,GAAAyH,QAAA;MAAA;IAGA;IAEA;IACAJ,4BAAA,WAAAA,6BAAAZ,UAAA;MAAA,IAAA0B,MAAA;MAAA,WAAA5I,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAA2I,SAAA;QAAA,IAAApH,MAAA,EAAAC,IAAA,EAAAW,UAAA,EAAAiG,UAAA,EAAAQ,GAAA;QAAA,WAAA7I,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAA2I,SAAA;UAAA,kBAAAA,SAAA,CAAAlH,CAAA,GAAAkH,SAAA,CAAAzI,CAAA;YAAA;cAAAyI,SAAA,CAAAlH,CAAA;cAEAV,OAAA,CAAAC,GAAA,mDAAAc,MAAA,CAAAgF,UAAA,QAAAhF,MAAA,CAAA0G,MAAA,CAAAlM,WAAA;cAAA,MAEA,CAAAwK,UAAA,KAAAA,UAAA,CAAAI,IAAA;gBAAAyB,SAAA,CAAAzI,CAAA;gBAAA;cAAA;cAAA,MACA,IAAA0I,KAAA;YAAA;cAGAvH,MAAA,OAAAK,uBAAA;cAAA,IACAL,MAAA;gBAAAsH,SAAA,CAAAzI,CAAA;gBAAA;cAAA;cAAA,MACA,IAAA0I,KAAA;YAAA;cAGAtH,IAAA,GAAAkH,MAAA,CAAA7G,cAAA;cAAA,IACAL,IAAA;gBAAAqH,SAAA,CAAAzI,CAAA;gBAAA;cAAA;cAAA,MACA,IAAA0I,KAAA;YAAA;cAGA;cACA3G,UAAA,GAAAuG,MAAA,CAAAlM,WAAA;cACA4L,UAAA,MAAApG,MAAA,CAAAG,UAAA,OAAAH,MAAA,CAAAR,IAAA,OAAAQ,MAAA,CAAAgF,UAAA;cAEA/F,OAAA,CAAAC,GAAA,uCAAAc,MAAA,CAAAoG,UAAA;;cAEA;cAAAS,SAAA,CAAAzI,CAAA;cAAA,OACAmB,MAAA,CAAAgH,GAAA,CAAAH,UAAA,EAAAI,MAAA,CAAAC,IAAA;YAAA;cACAxH,OAAA,CAAAC,GAAA,mDAAAc,MAAA,CAAA0G,MAAA,CAAAlM,WAAA,SAAAwF,MAAA,CAAAoG,UAAA;cAAA,OAAAS,SAAA,CAAAtI,CAAA,IAEA;YAAA;cAAAsI,SAAA,CAAAlH,CAAA;cAAAiH,GAAA,GAAAC,SAAA,CAAA5G,CAAA;cAEAhB,OAAA,CAAAiB,KAAA,mDAAAF,MAAA,CAAA0G,MAAA,CAAAlM,WAAA,SAAAoM,GAAA;cAAA,MACA,IAAAE,KAAA,mDAAA9G,MAAA,CAAA4G,GAAA,CAAA3C,OAAA;YAAA;cAAA,OAAA4C,SAAA,CAAAtI,CAAA;UAAA;QAAA,GAAAoI,QAAA;MAAA;IAEA;IAEA;IACAI,qBAAA,WAAAA,sBAAAC,IAAA;MACA;MACA,gCAAAnF,QAAA,CAAAmF,IAAA,CAAA1K,IAAA;QACA,KAAA2K,WAAA,CAAAD,IAAA;MACA;IACA;IAEA;IACAE,eAAA,WAAAA,gBAAAF,IAAA;MACA,IAAAG,QAAA,QAAAC,KAAA,UAAApH,MAAA,CAAAgH,IAAA,CAAA3K,EAAA;MACA,IAAA8K,QAAA,IAAAA,QAAA,CAAA1J,MAAA;QACA,IAAA4J,KAAA,GAAAF,QAAA;QAEA,IAAAE,KAAA,CAAAC,MAAA;UACA;UACA,KAAAC,cAAA;;UAEA;UACAF,KAAA,CAAAG,KAAA;UACAH,KAAA,CAAAI,IAAA;UACA,KAAAC,IAAA,CAAAV,IAAA;UACA,KAAAU,IAAA,CAAAV,IAAA;;UAEA;UACA,KAAAW,iBAAA,CAAAX,IAAA;QACA;UACA;UACAK,KAAA,CAAAO,KAAA;UACAP,KAAA,CAAAG,KAAA;UACA,KAAAE,IAAA,CAAAV,IAAA;UACA,KAAAU,IAAA,CAAAV,IAAA;;UAEA;UACA,KAAAa,iBAAA,CAAAb,IAAA,CAAA3K,EAAA;QACA;MACA;IACA;IAEA;IACAyL,wBAAA,WAAAA,yBAAAd,IAAA;MAAA,IAAAe,MAAA;MACA,IAAAZ,QAAA,QAAAC,KAAA,UAAApH,MAAA,CAAAgH,IAAA,CAAA3K,EAAA;MACA,IAAA8K,QAAA,IAAAA,QAAA,CAAA1J,MAAA;QACA,IAAA4J,KAAA,GAAAF,QAAA;QAEA,IAAAE,KAAA,CAAAC,MAAA;UACA;UACA,KAAAU,2BAAA;;UAEA;UACA,IAAAC,qBAAA,YAAAA,qBAAA;YACA;YACA,IAAAC,UAAA,GAAAb,KAAA,CAAAa,UAAA;YACA,IAAAC,WAAA,GAAAd,KAAA,CAAAc,WAAA;YAEA,IAAAD,UAAA,IAAAC,WAAA;cACA;cACA,IAAAC,QAAA,GAAA7K,IAAA,CAAA8K,GAAA,MAAA3J,MAAA,CAAA4J,UAAA;cACA,IAAAC,SAAA,GAAAhL,IAAA,CAAA8K,GAAA,MAAA3J,MAAA,CAAA8J,WAAA;cAEA,IAAAC,WAAA,GAAAP,UAAA,GAAAC,WAAA;cACA,IAAAO,YAAA,EAAAC,aAAA;cAEA,IAAAF,WAAA,GAAAL,QAAA,GAAAG,SAAA;gBACA;gBACAG,YAAA,GAAAN,QAAA;gBACAO,aAAA,GAAAP,QAAA,GAAAK,WAAA;cACA;gBACA;gBACAE,aAAA,GAAAJ,SAAA;gBACAG,YAAA,GAAAH,SAAA,GAAAE,WAAA;cACA;;cAEA;cACAV,MAAA,CAAAL,IAAA,CAAAV,IAAA,kBAAAzJ,IAAA,CAAAqL,KAAA,CAAAF,YAAA;cACAX,MAAA,CAAAL,IAAA,CAAAV,IAAA,mBAAAzJ,IAAA,CAAAqL,KAAA,CAAAD,aAAA;cAEA1J,OAAA,CAAAC,GAAA,iBAAAc,MAAA,CAAAgH,IAAA,CAAA3M,IAAA,iCAAA2F,MAAA,CAAAkI,UAAA,OAAAlI,MAAA,CAAAmI,WAAA,kCAAAnI,MAAA,CAAA0I,YAAA,OAAA1I,MAAA,CAAA2I,aAAA;YACA;;YAEA;YACAtB,KAAA,CAAAwB,mBAAA,mBAAAZ,qBAAA;UACA;;UAEA;UACA,IAAAZ,KAAA,CAAAyB,UAAA;YACAb,qBAAA;UACA;YACAZ,KAAA,CAAA0B,gBAAA,mBAAAd,qBAAA;UACA;;UAEA;UACAZ,KAAA,CAAAG,KAAA;UACAH,KAAA,CAAAI,IAAA;UACA,KAAAC,IAAA,CAAAV,IAAA;UACA,KAAAU,IAAA,CAAAV,IAAA;UACA,KAAAU,IAAA,CAAAV,IAAA;UAEA/H,OAAA,CAAAC,GAAA,iBAAAc,MAAA,CAAAgH,IAAA,CAAA3M,IAAA,iEAAA2F,MAAA,CAAAgH,IAAA,CAAAgC,QAAA;;UAEA;UACA,KAAAC,WAAA;;UAEA;UACA,KAAAC,SAAA;YACAjK,OAAA,CAAAC,GAAA;UACA;;UAEA;UACA,KAAAyI,iBAAA,CAAAX,IAAA;QACA;UACA;UACAK,KAAA,CAAAO,KAAA;UACAP,KAAA,CAAAG,KAAA;UACA,KAAAE,IAAA,CAAAV,IAAA;UACA,KAAAU,IAAA,CAAAV,IAAA;UACA,KAAAU,IAAA,CAAAV,IAAA;UACA,KAAAU,IAAA,CAAAV,IAAA;UACA,KAAAU,IAAA,CAAAV,IAAA;UAEA/H,OAAA,CAAAC,GAAA,iBAAAc,MAAA,CAAAgH,IAAA,CAAA3M,IAAA;;UAEA;UACA,KAAA8O,cAAA;;UAEA;UACA,KAAAtB,iBAAA,CAAAb,IAAA,CAAA3K,EAAA;QACA;MACA;IACA;IAEA;IACAkL,cAAA,WAAAA,eAAA;MAAA,IAAA6B,OAAA;MACA;MACA,KAAAlM,kBAAA,CAAAmM,OAAA,WAAArC,IAAA;QACA,IAAAA,IAAA,CAAA1K,IAAA,gBAAA0K,IAAA,CAAAsC,SAAA;UACA,IAAAnC,QAAA,GAAAiC,OAAA,CAAAhC,KAAA,UAAApH,MAAA,CAAAgH,IAAA,CAAA3K,EAAA;UACA,IAAA8K,QAAA,IAAAA,QAAA,CAAA1J,MAAA;YACA,IAAA4J,KAAA,GAAAF,QAAA;YACAE,KAAA,CAAAO,KAAA;YACAP,KAAA,CAAAG,KAAA;YACA4B,OAAA,CAAA1B,IAAA,CAAAV,IAAA;UACA;QACA;MACA;IACA;IAEA;IACAgB,2BAAA,WAAAA,4BAAA;MAAA,IAAAuB,OAAA;MACA;MACA,KAAArM,kBAAA,CAAAmM,OAAA,WAAArC,IAAA;QACA,IAAAA,IAAA,CAAA1K,IAAA;UACA,IAAA0K,IAAA,CAAAsC,SAAA;YACA,IAAAnC,QAAA,GAAAoC,OAAA,CAAAnC,KAAA,UAAApH,MAAA,CAAAgH,IAAA,CAAA3K,EAAA;YACA,IAAA8K,QAAA,IAAAA,QAAA,CAAA1J,MAAA;cACA,IAAA4J,KAAA,GAAAF,QAAA;cACAE,KAAA,CAAAO,KAAA;cACAP,KAAA,CAAAG,KAAA;cACA+B,OAAA,CAAA7B,IAAA,CAAAV,IAAA;YACA;UACA;UACA;UACAuC,OAAA,CAAA7B,IAAA,CAAAV,IAAA;UACAuC,OAAA,CAAA7B,IAAA,CAAAV,IAAA;UACAuC,OAAA,CAAA7B,IAAA,CAAAV,IAAA;QACA;MACA;;MAEA;MACA,KAAAmC,cAAA;IACA;IAEA;IACAF,WAAA,WAAAA,YAAA;MAAA,IAAAO,OAAA;MACA;MACA,KAAAL,cAAA;MAEA,IAAAM,QAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,QAAA,CAAAG,SAAA;MACAH,QAAA,CAAApN,EAAA;MACAqN,QAAA,CAAAG,IAAA,CAAAC,WAAA,CAAAL,QAAA;;MAEA;MACAA,QAAA,CAAAV,gBAAA;QACAS,OAAA,CAAAxB,2BAAA;MACA;IACA;IAEA;IACAmB,cAAA,WAAAA,eAAA;MACA,IAAAM,QAAA,GAAAC,QAAA,CAAAK,cAAA;MACA,IAAAN,QAAA;QACAA,QAAA,CAAAO,MAAA;MACA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAAjD,IAAA;MACA,IAAAG,QAAA,QAAAC,KAAA,UAAApH,MAAA,CAAAgH,IAAA,CAAA3K,EAAA;MACA,IAAA8K,QAAA,IAAAA,QAAA,CAAA1J,MAAA;QACA,IAAA4J,KAAA,GAAAF,QAAA;QACA,IAAAE,KAAA,CAAA5K,QAAA,IAAA4K,KAAA,CAAA6C,WAAA;UACA,OAAA7C,KAAA,CAAA6C,WAAA,GAAA7C,KAAA,CAAA5K,QAAA;QACA;MACA;MACA;IACA;IAEA;IACA0N,cAAA,WAAAA,eAAAnD,IAAA;MACA,IAAAG,QAAA,QAAAC,KAAA,UAAApH,MAAA,CAAAgH,IAAA,CAAA3K,EAAA;MACA,IAAA8K,QAAA,IAAAA,QAAA,CAAA1J,MAAA;QACA,IAAA4J,KAAA,GAAAF,QAAA;QACA,OAAAE,KAAA,CAAA6C,WAAA;MACA;MACA;IACA;IAEA;IACAE,WAAA,WAAAA,YAAApD,IAAA;MACA,IAAAG,QAAA,QAAAC,KAAA,UAAApH,MAAA,CAAAgH,IAAA,CAAA3K,EAAA;MACA,IAAA8K,QAAA,IAAAA,QAAA,CAAA1J,MAAA;QACA,IAAA4J,KAAA,GAAAF,QAAA;QACA,OAAAE,KAAA,CAAA5K,QAAA;MACA;MACA;IACA;IAEA;IACA4N,UAAA,WAAAA,WAAAC,OAAA;MACA,KAAAA,OAAA,IAAAC,KAAA,CAAAD,OAAA;MAEA,IAAAE,OAAA,GAAAjN,IAAA,CAAAkN,KAAA,CAAAH,OAAA;MACA,IAAAI,gBAAA,GAAAnN,IAAA,CAAAkN,KAAA,CAAAH,OAAA;MACA,UAAAtK,MAAA,CAAAwK,OAAA,OAAAxK,MAAA,CAAA0K,gBAAA,CAAAC,QAAA,GAAAC,QAAA;IACA;IAEA;IACAC,SAAA,WAAAA,UAAAC,KAAA,EAAA9D,IAAA;MACA,IAAAG,QAAA,QAAAC,KAAA,UAAApH,MAAA,CAAAgH,IAAA,CAAA3K,EAAA;MACA,IAAA8K,QAAA,IAAAA,QAAA,CAAA1J,MAAA;QACA,IAAA4J,KAAA,GAAAF,QAAA;QACA,IAAA4D,WAAA,GAAAD,KAAA,CAAAE,aAAA,CAAAC,aAAA;QACA,IAAAC,IAAA,GAAAH,WAAA,CAAAI,qBAAA;QACA,IAAAC,MAAA,GAAAN,KAAA,CAAAO,OAAA,GAAAH,IAAA,CAAAI,IAAA;QACA,IAAAC,UAAA,GAAAH,MAAA,GAAAF,IAAA,CAAAM,KAAA;QACA,IAAAC,OAAA,GAAAF,UAAA,GAAAlE,KAAA,CAAA5K,QAAA;QAEA,IAAAgP,OAAA,SAAAA,OAAA,IAAApE,KAAA,CAAA5K,QAAA;UACA4K,KAAA,CAAA6C,WAAA,GAAAuB,OAAA;QACA;MACA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAA1E,IAAA;MACA;MACA,KAAA2E,YAAA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAA5E,IAAA;MACA;MACA/H,OAAA,CAAAC,GAAA,iBAAAc,MAAA,CAAAgH,IAAA,CAAA3M,IAAA;IACA;IAEA;IACAwR,eAAA,WAAAA,gBAAA7E,IAAA;MACA,IAAA8E,QAAA,QAAA1E,KAAA,UAAApH,MAAA,CAAAgH,IAAA,CAAA3K,EAAA;MACA,IAAAyP,QAAA,IAAAA,QAAA,CAAArO,MAAA;QACA,IAAAsO,KAAA,GAAAD,QAAA;QAEA,IAAAC,KAAA,CAAAzE,MAAA;UACA;UACA,KAAA0E,aAAA;;UAEA;UACAD,KAAA,CAAAtE,IAAA;UACA,KAAAC,IAAA,CAAAV,IAAA;UACA,KAAAU,IAAA,CAAAV,IAAA;;UAEA;UACA,KAAAW,iBAAA,CAAAX,IAAA;QACA;UACA;UACA+E,KAAA,CAAAnE,KAAA;UACA,KAAAF,IAAA,CAAAV,IAAA;UACA,KAAAU,IAAA,CAAAV,IAAA;;UAEA;UACA,KAAAa,iBAAA,CAAAb,IAAA,CAAA3K,EAAA;QACA;MACA;IACA;IAEA;IACA2P,aAAA,WAAAA,cAAA;MACA,KAAAzE,cAAA;MACA,KAAA0E,cAAA;IACA;IAEA;IACAA,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MACA,KAAAhP,kBAAA,CAAAmM,OAAA,WAAArC,IAAA;QACA,IAAAA,IAAA,CAAA1K,IAAA,gBAAA0K,IAAA,CAAAsC,SAAA;UACA,IAAAwC,QAAA,GAAAI,OAAA,CAAA9E,KAAA,UAAApH,MAAA,CAAAgH,IAAA,CAAA3K,EAAA;UACA,IAAAyP,QAAA,IAAAA,QAAA,CAAArO,MAAA;YACAqO,QAAA,IAAAlE,KAAA;YACAsE,OAAA,CAAAxE,IAAA,CAAAV,IAAA;UACA;QACA;MACA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAAD,IAAA;MACA;MACA,KAAAA,IAAA,CAAAtK,GAAA,SAAAZ,cAAA;QACA;QACA,IAAAF,QAAA,QAAAJ,SAAA,CAAAI,QAAA,CAAA0G,OAAA,iBAAAA,OAAA;QACA0E,IAAA,CAAAtK,GAAA,cAAAsD,MAAA,CAAApE,QAAA,OAAAoE,MAAA,CAAAgH,IAAA,CAAAzE,WAAA;MACA;MAEA,KAAA4J,kBAAA,GAAAnF,IAAA;MACA,KAAAoF,cAAA;IACA;IAEA;IACAC,UAAA,WAAAA,WAAArF,IAAA;MACA,IAAAA,IAAA,CAAAtK,GAAA;QACA,OAAAsK,IAAA,CAAAtK,GAAA;MACA;MAEA,SAAAZ,cAAA,IAAAkL,IAAA,CAAAzE,WAAA;QACA,IAAA3G,QAAA,QAAAJ,SAAA,CAAAI,QAAA,CAAA0G,OAAA,iBAAAA,OAAA;QACA,kBAAAtC,MAAA,CAAApE,QAAA,OAAAoE,MAAA,CAAAgH,IAAA,CAAAzE,WAAA;MACA;MAEA;IACA;IAEA;IACA+J,YAAA,WAAAA,aAAA;MACA;MACA,SAAAlF,KAAA,CAAAmF,YAAA;QACA,KAAAnF,KAAA,CAAAmF,YAAA,CAAA3E,KAAA;QACA,KAAAR,KAAA,CAAAmF,YAAA,CAAArC,WAAA;MACA;;MAEA;MACA,SAAA9C,KAAA,CAAAoF,YAAA;QACA,KAAApF,KAAA,CAAAoF,YAAA,CAAA5E,KAAA;QACA,KAAAR,KAAA,CAAAoF,YAAA,CAAAtC,WAAA;MACA;;MAEA;MACA,KAAA8B,aAAA;;MAEA;MACA,KAAAG,kBAAA;IACA;IAEA;IACAM,aAAA,WAAAA,cAAA3B,KAAA;MACA;MACA,IAAAzD,KAAA,GAAAyD,KAAA,CAAA4B,MAAA;MACArF,KAAA,CAAA6C,WAAA;IACA;IAEA;IACAyC,YAAA,WAAAA,aAAA3F,IAAA;MACA,KAAAU,IAAA,CAAAV,IAAA;MACA,KAAAU,IAAA,CAAAV,IAAA;MACA,KAAAU,IAAA,CAAAV,IAAA;MACA,KAAAU,IAAA,CAAAV,IAAA;MACA,KAAAmC,cAAA;IACA;IAEA;IACAyD,aAAA,WAAAA,cAAA5F,IAAA;MAAA,IAAA6F,OAAA;MACA,KAAAnF,IAAA,CAAAV,IAAA;MACA;MACA,IAAAA,IAAA,CAAAgC,QAAA;QACA,KAAAtB,IAAA,CAAAV,IAAA;QACA,KAAAU,IAAA,CAAAV,IAAA;QACA,KAAAU,IAAA,CAAAV,IAAA;QACA,KAAAmC,cAAA;MACA;;MAEA;MACA,SAAA2D,WAAA,SAAAA,WAAA,CAAAzQ,EAAA,KAAA2K,IAAA,CAAA3K,EAAA;QACA,KAAA6M,SAAA;UACA2D,OAAA,CAAAE,eAAA;QACA;MACA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAAhG,IAAA;MAAA,IAAAiG,OAAA;MACA,KAAAvF,IAAA,CAAAV,IAAA;;MAEA;MACA,SAAA8F,WAAA,SAAAA,WAAA,CAAAzQ,EAAA,KAAA2K,IAAA,CAAA3K,EAAA;QACA,KAAA6M,SAAA;UACA+D,OAAA,CAAAF,eAAA;QACA;MACA;IACA;IAEA;IACAG,YAAA,WAAAA,aAAAlG,IAAA;MACA,KAAAU,IAAA,CAAAV,IAAA;IACA;IAEA;IACAmG,aAAA,WAAAA,cAAAnG,IAAA;MACA,KAAAU,IAAA,CAAAV,IAAA;IACA;IAEA;IACAoG,aAAA,WAAAA,cAAApG,IAAA;MACA,KAAAU,IAAA,CAAAV,IAAA;IACA;IAEA;IACAqG,YAAA,WAAAA,aAAAvC,KAAA;MACA;MACAA,KAAA,CAAA4B,MAAA,CAAAY,KAAA,CAAAC,OAAA;MACAzC,KAAA,CAAA4B,MAAA,CAAAc,UAAA,CAAAC,SAAA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAA1G,IAAA;MACA/H,OAAA,CAAAC,GAAA,YAAA8H,IAAA,CAAA3M,IAAA;MACA,KAAAqN,IAAA,CAAAV,IAAA;MACA,KAAAa,iBAAA,CAAAb,IAAA,CAAA3K,EAAA;IACA;IAEA;IACAsR,iBAAA,WAAAA,kBAAA3G,IAAA;MACA,IAAAA,IAAA,CAAAsC,SAAA;QACA,KAAA3B,iBAAA,CAAAX,IAAA;MACA;QACA,KAAAU,IAAA,CAAAV,IAAA;MACA;IACA;IAEA;IACA4G,gBAAA,WAAAA,iBAAA5G,IAAA;MACA,KAAAU,IAAA,CAAAV,IAAA;MACA,IAAAA,IAAA,CAAAsC,SAAA;QACA,KAAAzB,iBAAA,CAAAb,IAAA,CAAA3K,EAAA;QACA,KAAAsL,iBAAA,CAAAX,IAAA;MACA;IACA;IAEA;IACA6G,iBAAA,WAAAA,kBAAA7G,IAAA;MACA/H,OAAA,CAAAC,GAAA,YAAA8H,IAAA,CAAA3M,IAAA;MACA,KAAAqN,IAAA,CAAAV,IAAA;MACA,KAAAa,iBAAA,CAAAb,IAAA,CAAA3K,EAAA;IACA;IAEA;IACAyR,iBAAA,WAAAA,kBAAA9G,IAAA;MACA,IAAAA,IAAA,CAAAsC,SAAA;QACA,KAAA3B,iBAAA,CAAAX,IAAA;MACA;QACA,KAAAU,IAAA,CAAAV,IAAA;MACA;IACA;IAEA;IACA+G,gBAAA,WAAAA,iBAAA/G,IAAA;MACA,KAAAU,IAAA,CAAAV,IAAA;MACA,IAAAA,IAAA,CAAAsC,SAAA;QACA,KAAAzB,iBAAA,CAAAb,IAAA,CAAA3K,EAAA;QACA,KAAAsL,iBAAA,CAAAX,IAAA;MACA;IACA;IAEA;IACAgH,iBAAA,WAAAA,kBAAAhH,IAAA;MACA/H,OAAA,CAAAC,GAAA,YAAA8H,IAAA,CAAA3M,IAAA;IACA;IAEA;IACA4T,iBAAA,WAAAA,kBAAAjH,IAAA;MACA/H,OAAA,CAAAC,GAAA,YAAA8H,IAAA,CAAA3M,IAAA;IACA;IAEA;IACAsN,iBAAA,WAAAA,kBAAAX,IAAA;MAAA,IAAAkH,OAAA;MACA,KAAArG,iBAAA,CAAAb,IAAA,CAAA3K,EAAA;MACA,KAAA8R,aAAA,CAAAnH,IAAA,CAAA3K,EAAA,IAAA+R,UAAA;QACAF,OAAA,CAAAxG,IAAA,CAAAV,IAAA;MACA;IACA;IAEA;IACAa,iBAAA,WAAAA,kBAAAwG,MAAA;MACA,SAAAF,aAAA,CAAAE,MAAA;QACAC,YAAA,MAAAH,aAAA,CAAAE,MAAA;QACA,YAAAF,aAAA,CAAAE,MAAA;MACA;IACA;IAMA;IACAE,cAAA,WAAAA,eAAAC,KAAA;MACA,IAAAA,KAAA;MACA,IAAAC,CAAA;MACA,IAAAC,KAAA;MACA,IAAAC,CAAA,GAAApR,IAAA,CAAAkN,KAAA,CAAAlN,IAAA,CAAA2B,GAAA,CAAAsP,KAAA,IAAAjR,IAAA,CAAA2B,GAAA,CAAAuP,CAAA;MACA,OAAAG,UAAA,EAAAJ,KAAA,GAAAjR,IAAA,CAAAsR,GAAA,CAAAJ,CAAA,EAAAE,CAAA,GAAAG,OAAA,aAAAJ,KAAA,CAAAC,CAAA;IACA;IAEA;IACAI,YAAA,WAAAA,aAAA/H,IAAA;MACA,IAAAA,IAAA,CAAAtK,GAAA;QACA,IAAAsS,IAAA,GAAAtF,QAAA,CAAAC,aAAA;QACAqF,IAAA,CAAAC,IAAA,GAAAjI,IAAA,CAAAtK,GAAA;QACAsS,IAAA,CAAAE,QAAA,GAAAlI,IAAA,CAAA3M,IAAA;QACA2U,IAAA,CAAAtC,MAAA;QACAhD,QAAA,CAAAG,IAAA,CAAAC,WAAA,CAAAkF,IAAA;QACAA,IAAA,CAAAG,KAAA;QACAzF,QAAA,CAAAG,IAAA,CAAAuF,WAAA,CAAAJ,IAAA;MACA;QACA,KAAApQ,QAAA,CAAAsB,KAAA;MACA;IACA;IAEA;IACAsE,sBAAA,WAAAA,uBAAA;MAAA,IAAA6K,OAAA;MAAA,WAAAvR,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAAsR,SAAA;QAAA,IAAA/P,MAAA,EAAAC,IAAA,EAAA+P,GAAA;QAAA,WAAAxR,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAAsR,SAAA;UAAA,kBAAAA,SAAA,CAAA7P,CAAA,GAAA6P,SAAA,CAAApR,CAAA;YAAA;cAAAoR,SAAA,CAAA7P,CAAA;cAEAJ,MAAA,OAAAK,uBAAA;cACAJ,IAAA,GAAA6P,OAAA,CAAAxP,cAAA,IAEA;cAAA2P,SAAA,CAAApR,CAAA;cAAA,OACAiR,OAAA,CAAAI,kBAAA;YAAA;cAAAD,SAAA,CAAApR,CAAA;cAAA,OACAiR,OAAA,CAAAI,kBAAA;YAAA;cAEAxQ,OAAA,CAAAC,GAAA,YAAAmQ,OAAA,CAAAvU,aAAA,CAAAqG,GAAA,WAAA+C,CAAA;gBAAA,OAAAA,CAAA,CAAA7J,IAAA;cAAA;cACA4E,OAAA,CAAAC,GAAA,WAAAmQ,OAAA,CAAAtU,eAAA,CAAAoG,GAAA,WAAA+C,CAAA;gBAAA,OAAAA,CAAA,CAAA7J,IAAA;cAAA;cAAAmV,SAAA,CAAApR,CAAA;cAAA;YAAA;cAAAoR,SAAA,CAAA7P,CAAA;cAAA4P,GAAA,GAAAC,SAAA,CAAAvP,CAAA;cAGAhB,OAAA,CAAAiB,KAAA,kBAAAqP,GAAA;cACA;cACAF,OAAA,CAAAvU,aAAA;gBAAAuB,EAAA;gBAAAhC,IAAA;gBAAAiL,KAAA;cAAA;cACA+J,OAAA,CAAAtU,eAAA;gBAAAsB,EAAA;gBAAAhC,IAAA;gBAAAiL,KAAA;cAAA;YAAA;cAAA,OAAAkK,SAAA,CAAAjR,CAAA;UAAA;QAAA,GAAA+Q,QAAA;MAAA;IAEA;IAEA;IACAG,kBAAA,WAAAA,mBAAAtP,UAAA,EAAAuP,YAAA;MAAA,IAAAC,OAAA;MAAA,WAAA7R,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAA4R,UAAA;QAAA,IAAArQ,MAAA,EAAAC,IAAA,EAAAqQ,SAAA,EAAArP,MAAA,EAAAC,MAAA,EAAAqP,OAAA,EAAAC,aAAA,EAAAC,GAAA;QAAA,WAAAjS,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAA+R,UAAA;UAAA,kBAAAA,UAAA,CAAAtQ,CAAA,GAAAsQ,UAAA,CAAA7R,CAAA;YAAA;cAAA6R,UAAA,CAAAtQ,CAAA;cAEAJ,MAAA,OAAAK,uBAAA;cACAJ,IAAA,GAAAmQ,OAAA,CAAA9P,cAAA;cACAgQ,SAAA,OAAAK,GAAA;cACA1P,MAAA,MAAAR,MAAA,CAAAG,UAAA,OAAAH,MAAA,CAAAR,IAAA,QAEA;cAAAyQ,UAAA,CAAA7R,CAAA;cAAA,OACAmB,MAAA,CAAAtC,IAAA;gBACAuD,MAAA,EAAAA,MAAA;gBACA2P,SAAA;gBACA;cACA;YAAA;cAJA1P,MAAA,GAAAwP,UAAA,CAAAhQ,CAAA;cAMA;cACA,IAAAQ,MAAA,CAAA2P,QAAA;gBACA3P,MAAA,CAAA2P,QAAA,CAAA/G,OAAA,WAAAgH,UAAA;kBACA,IAAAC,UAAA,GAAAD,UAAA,CAAAhW,IAAA,IAAAgW,UAAA;kBACA;kBACA,IAAAC,UAAA,WAAAA,UAAA;oBACA,IAAAtL,UAAA,GAAAsL,UAAA,CAAAhO,OAAA,CAAA9B,MAAA,MAAA8B,OAAA;oBACA,IAAA0C,UAAA;sBACA6K,SAAA,CAAAU,GAAA,CAAAvL,UAAA;oBACA;kBACA;gBACA;cACA;;cAEA;cACA,IAAAvE,MAAA,CAAAM,OAAA;gBACAN,MAAA,CAAAM,OAAA,CAAAsI,OAAA,WAAApI,GAAA;kBACA,IAAAuP,SAAA,GAAAvP,GAAA,CAAA5G,IAAA;kBACA,IAAAmW,SAAA,CAAA5M,UAAA,CAAApD,MAAA;oBACA,IAAAiQ,YAAA,GAAAD,SAAA,CAAAlO,OAAA,CAAA9B,MAAA;oBACA,IAAAwE,UAAA,GAAAyL,YAAA,CAAAnP,KAAA;oBACA,IAAA0D,UAAA,IAAAA,UAAA;sBACA6K,SAAA,CAAAU,GAAA,CAAAvL,UAAA;oBACA;kBACA;gBACA;cACA;;cAEA;cACA8K,OAAA,GAAAY,KAAA,CAAAjK,IAAA,CAAAoJ,SAAA,EAAA1O,GAAA,WAAA6D,UAAA,EAAA5D,KAAA;gBAAA;kBACA/E,EAAA,EAAAyF,IAAA,CAAAC,GAAA,KAAAX,KAAA;kBAAA;kBACA/G,IAAA,EAAA2K,UAAA;kBACAM,KAAA;gBACA;cAAA,IAEA;cACA,KAAAuK,SAAA,CAAAc,GAAA;gBACAb,OAAA,CAAAc,OAAA;kBAAAvU,EAAA,EAAAyF,IAAA,CAAAC,GAAA;kBAAA1H,IAAA;kBAAAiL,KAAA;gBAAA;cACA;;cAEA;cACA,IAAAnF,UAAA;gBACAwP,OAAA,CAAA7U,aAAA,GAAAgV,OAAA;cACA;gBACAH,OAAA,CAAA5U,eAAA,GAAA+U,OAAA;cACA;cAEA7Q,OAAA,CAAAC,GAAA,IAAAc,MAAA,CAAAG,UAAA,mDAAA2P,OAAA,CAAA3O,GAAA,WAAA+C,CAAA;gBAAA,OAAAA,CAAA,CAAA7J,IAAA;cAAA;cAAA4V,UAAA,CAAA7R,CAAA;cAAA;YAAA;cAAA6R,UAAA,CAAAtQ,CAAA;cAAAqQ,GAAA,GAAAC,UAAA,CAAAhQ,CAAA;cAGAhB,OAAA,CAAAiB,KAAA,iBAAAF,MAAA,CAAAG,UAAA,mDAAA6P,GAAA;cACA;cACAD,aAAA;gBAAA1T,EAAA,EAAAyF,IAAA,CAAAC,GAAA;gBAAA1H,IAAA;gBAAAiL,KAAA;cAAA;cACA,IAAAnF,UAAA;gBACAwP,OAAA,CAAA7U,aAAA,GAAAiV,aAAA;cACA;gBACAJ,OAAA,CAAA5U,eAAA,GAAAgV,aAAA;cACA;YAAA;cAAA,OAAAE,UAAA,CAAA1R,CAAA;UAAA;QAAA,GAAAqR,SAAA;MAAA;IAEA;IAEA;IACAiB,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAhT,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAA+S,UAAA;QAAA,IAAAxR,MAAA,EAAAC,IAAA,EAAAwR,GAAA,EAAAC,IAAA,EAAA7Q,MAAA,EAAA8Q,UAAA,EAAAjL,WAAA,EAAAkL,GAAA,EAAAC,aAAA,EAAAjR,UAAA,EAAAK,MAAA,EAAAC,MAAA,EAAA4Q,SAAA,EAAAC,GAAA,EAAAC,IAAA;QAAA,WAAAxT,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAAsT,UAAA;UAAA,kBAAAA,UAAA,CAAA7R,CAAA,GAAA6R,UAAA,CAAApT,CAAA;YAAA;cAAAoT,UAAA,CAAA7R,CAAA;cAEAJ,MAAA,OAAAK,uBAAA;cACAJ,IAAA,GAAAsR,OAAA,CAAAjR,cAAA,IAEA;cACA;cACAZ,OAAA,CAAAC,GAAA;cAAA,OAAAsS,UAAA,CAAAjT,CAAA;YAAA;cAAA,MAAAyS,GAAA,GAAAC,IAAA,CAAAxT,MAAA;gBAAA+T,UAAA,CAAApT,CAAA;gBAAA;cAAA;cAGAgC,MAAA,GAAA6Q,IAAA,CAAAD,GAAA;cAAA;cACAE,UAAA,MAEA;cACAjL,WAAA;cAAAkL,GAAA,MAAAC,aAAA,GAEAnL,WAAA;YAAA;cAAA,MAAAkL,GAAA,GAAAC,aAAA,CAAA3T,MAAA;gBAAA+T,UAAA,CAAApT,CAAA;gBAAA;cAAA;cAAA+B,UAAA,GAAAiR,aAAA,CAAAD,GAAA;cACA3Q,MAAA,MAAAR,MAAA,CAAAG,UAAA,OAAAH,MAAA,CAAAR,IAAA,OAAAQ,MAAA,CAAAI,MAAA,CAAA/F,IAAA;cAAAmX,UAAA,CAAA7R,CAAA;cAAA6R,UAAA,CAAApT,CAAA;cAAA,OAGAmB,MAAA,CAAAtC,IAAA;gBACAuD,MAAA,EAAAA,MAAA;gBACA;cACA;YAAA;cAHAC,MAAA,GAAA+Q,UAAA,CAAAvR,CAAA;cAKA,IAAAQ,MAAA,CAAAM,OAAA;gBACA;gBACAsQ,SAAA,GAAA5Q,MAAA,CAAAM,OAAA,CAAAC,MAAA,WAAAC,GAAA;kBAAA,OACA,CAAAA,GAAA,CAAA5G,IAAA,CAAA6G,QAAA,cAAAD,GAAA,CAAA5G,IAAA,CAAA6G,QAAA;gBAAA,CACA,EAAAzD,MAAA;gBACAyT,UAAA,IAAAG,SAAA;cACA;cAAAG,UAAA,CAAApT,CAAA;cAAA;YAAA;cAAAoT,UAAA,CAAA7R,CAAA;cAAA2R,GAAA,GAAAE,UAAA,CAAAvR,CAAA;cAEAhB,OAAA,CAAA4G,IAAA,mCAAA7F,MAAA,CAAAI,MAAA,CAAA/F,IAAA,cAAA2F,MAAA,CAAAG,UAAA,yDAAAmR,GAAA;YAAA;cAAAH,GAAA;cAAAK,UAAA,CAAApT,CAAA;cAAA;YAAA;cAIAgC,MAAA,CAAAkF,KAAA,GAAA4L,UAAA;YAAA;cAAAF,GAAA;cAAAQ,UAAA,CAAApT,CAAA;cAAA;YAAA;cAGAa,OAAA,CAAAC,GAAA,iBAAA4R,OAAA,CAAAW,gBAAA;cAAAD,UAAA,CAAApT,CAAA;cAAA;YAAA;cAAAoT,UAAA,CAAA7R,CAAA;cAAA4R,IAAA,GAAAC,UAAA,CAAAvR,CAAA;cAGAhB,OAAA,CAAAiB,KAAA,iBAAAqR,IAAA;YAAA;cAAA,OAAAC,UAAA,CAAAjT,CAAA;UAAA;QAAA,GAAAwS,SAAA;MAAA;IAEA;IAEA;IACAW,qBAAA,WAAAA,sBAAA5G,KAAA,EAAA1K,MAAA;MACA,KAAAuR,kBAAA;MACA,KAAAC,YAAA,GAAA9G,KAAA,CAAAO,OAAA;MACA,KAAAwG,YAAA,GAAA/G,KAAA,CAAAgH,OAAA;MACA,KAAAC,iBAAA,GAAA3R,MAAA;;MAEA;MACAsJ,QAAA,CAAAX,gBAAA,eAAAiJ,qBAAA;IACA;IAEAA,qBAAA,WAAAA,sBAAA;MACA,KAAAL,kBAAA;MACA,KAAAI,iBAAA;MACArI,QAAA,CAAAb,mBAAA,eAAAmJ,qBAAA;IACA;IAEA;IACAC,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MAAA,WAAApU,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAAmU,UAAA;QAAA,IAAAnN,UAAA;QAAA,WAAAjH,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAAkU,UAAA;UAAA,kBAAAA,UAAA,CAAAhU,CAAA;YAAA;cAAA,IACA8T,OAAA,CAAAH,iBAAA;gBAAAK,UAAA,CAAAhU,CAAA;gBAAA;cAAA;cACA8T,OAAA,CAAAtT,QAAA,CAAAsB,KAAA;cAAA,OAAAkS,UAAA,CAAA7T,CAAA;YAAA;cAIAyG,UAAA,GAAAkN,OAAA,CAAAH,iBAAA,CAAA1X,IAAA,IAAA6X,OAAA,CAAAH,iBAAA;cAEAG,OAAA,CAAAG,OAAA;gBACAC,iBAAA;gBACAC,gBAAA;gBACAC,UAAA,EAAAxN;cACA,GAAAyN,IAAA;gBAAA,IAAAC,KAAA,OAAA5U,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAA2U,UAAAC,KAAA;kBAAA,IAAAC,KAAA,EAAA5W,aAAA,EAAA6W,aAAA,EAAAC,IAAA;kBAAA,WAAAhV,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAA8U,UAAA;oBAAA,kBAAAA,UAAA,CAAArT,CAAA,GAAAqT,UAAA,CAAA5U,CAAA;sBAAA;wBAAAyU,KAAA,GAAAD,KAAA,CAAAC,KAAA;wBAAAG,UAAA,CAAArT,CAAA;wBAAA,MAEA,CAAAkT,KAAA,IAAAA,KAAA,CAAAzN,IAAA;0BAAA4N,UAAA,CAAA5U,CAAA;0BAAA;wBAAA;wBACA8T,OAAA,CAAAtT,QAAA,CAAAsB,KAAA;wBAAA,OAAA8S,UAAA,CAAAzU,CAAA;sBAAA;wBAIAtC,aAAA,GAAA4W,KAAA,CAAAzN,IAAA;wBACA0N,aAAA,GAAAZ,OAAA,CAAAH,iBAAA,CAAA1X,IAAA,IAAA6X,OAAA,CAAAH,iBAAA;wBAAA,MAEA9V,aAAA,KAAA6W,aAAA;0BAAAE,UAAA,CAAA5U,CAAA;0BAAA;wBAAA;wBACA8T,OAAA,CAAAF,qBAAA;wBAAA,OAAAgB,UAAA,CAAAzU,CAAA;sBAAA;wBAAA,MAKA2T,OAAA,CAAA7W,WAAA,cAAA6W,OAAA,CAAApW,cAAA;0BAAAkX,UAAA,CAAA5U,CAAA;0BAAA;wBAAA;wBAAA4U,UAAA,CAAA5U,CAAA;wBAAA,OACA8T,OAAA,CAAAe,eAAA,CAAAH,aAAA,EAAA7W,aAAA;sBAAA;wBAGA;wBACA,QAAAiX,QAAA,CAAA9W,OAAA,EAAA8V,OAAA,CAAAH,iBAAA;0BACAG,OAAA,CAAAH,iBAAA,CAAA1X,IAAA,GAAA4B,aAAA;wBACA;;wBAEA;wBACA,IAAAiW,OAAA,CAAAnW,aAAA,KAAA+W,aAAA;0BACAZ,OAAA,CAAAnW,aAAA,GAAAE,aAAA;wBACA;wBAEAiW,OAAA,CAAAtT,QAAA,CAAAC,OAAA;wBACAqT,OAAA,CAAAF,qBAAA;wBAAAgB,UAAA,CAAA5U,CAAA;wBAAA;sBAAA;wBAAA4U,UAAA,CAAArT,CAAA;wBAAAoT,IAAA,GAAAC,UAAA,CAAA/S,CAAA;wBAGAhB,OAAA,CAAAiB,KAAA,cAAA6S,IAAA;wBACAb,OAAA,CAAAtT,QAAA,CAAAsB,KAAA,oCAAAF,MAAA,CAAA+S,IAAA,CAAA9O,OAAA;sBAAA;wBAAA,OAAA+O,UAAA,CAAAzU,CAAA;oBAAA;kBAAA,GAAAoU,SAAA;gBAAA,CAEA;gBAAA,iBAAAQ,EAAA;kBAAA,OAAAT,KAAA,CAAAjQ,KAAA,OAAA2Q,SAAA;gBAAA;cAAA;YAAA;cAAA,OAAAhB,UAAA,CAAA7T,CAAA;UAAA;QAAA,GAAA4T,SAAA;MAAA;IACA;IAEA;IACAkB,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAxV,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAAuV,UAAA;QAAA,IAAAvO,UAAA;QAAA,WAAAjH,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAAsV,UAAA;UAAA,kBAAAA,UAAA,CAAApV,CAAA;YAAA;cACAa,OAAA,CAAAC,GAAA,iCAAAoU,OAAA,CAAAvB,iBAAA;cAAA,IAEAuB,OAAA,CAAAvB,iBAAA;gBAAAyB,UAAA,CAAApV,CAAA;gBAAA;cAAA;cACAkV,OAAA,CAAA1U,QAAA,CAAAsB,KAAA;cAAA,OAAAsT,UAAA,CAAAjV,CAAA;YAAA;cAAA,MAMA,OAAA+U,OAAA,CAAAvB,iBAAA;gBAAAyB,UAAA,CAAApV,CAAA;gBAAA;cAAA;cACA4G,UAAA,GAAAsO,OAAA,CAAAvB,iBAAA;cAAAyB,UAAA,CAAApV,CAAA;cAAA;YAAA;cAAA,MACAkV,OAAA,CAAAvB,iBAAA,IAAAuB,OAAA,CAAAvB,iBAAA,CAAA1X,IAAA;gBAAAmZ,UAAA,CAAApV,CAAA;gBAAA;cAAA;cACA4G,UAAA,GAAAsO,OAAA,CAAAvB,iBAAA,CAAA1X,IAAA;cAAAmZ,UAAA,CAAApV,CAAA;cAAA;YAAA;cAEAa,OAAA,CAAAiB,KAAA,iCAAAoT,OAAA,CAAAvB,iBAAA;cACAuB,OAAA,CAAA1U,QAAA,CAAAsB,KAAA;cAAA,OAAAsT,UAAA,CAAAjV,CAAA;YAAA;cAIAU,OAAA,CAAAC,GAAA,aAAA8F,UAAA;cAAA,MAEAA,UAAA;gBAAAwO,UAAA,CAAApV,CAAA;gBAAA;cAAA;cACAkV,OAAA,CAAA1U,QAAA,CAAA6U,OAAA;cAAA,OAAAD,UAAA,CAAAjV,CAAA;YAAA;cAIA+U,OAAA,CAAAI,QAAA,sDAAA1T,MAAA,CAAAgF,UAAA;gBACAsN,iBAAA;gBACAC,gBAAA;gBACAjW,IAAA;cACA,GAAAmW,IAAA,kBAAA3U,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAA2V,UAAA;gBAAA,IAAAC,gBAAA,EAAAC,IAAA,EAAAC,IAAA;gBAAA,WAAA/V,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAA6V,UAAA;kBAAA,kBAAAA,UAAA,CAAApU,CAAA,GAAAoU,UAAA,CAAA3V,CAAA;oBAAA;sBAAA2V,UAAA,CAAApU,CAAA;sBAEA;sBACAiU,gBAAA,GAAA5O,UAAA,EAEA;sBAAA,MACAsO,OAAA,CAAAjY,WAAA,cAAAiY,OAAA,CAAAxX,cAAA;wBAAAiY,UAAA,CAAA3V,CAAA;wBAAA;sBAAA;sBAAA2V,UAAA,CAAApU,CAAA;sBAAAoU,UAAA,CAAA3V,CAAA;sBAAA,OAEAkV,OAAA,CAAAU,4BAAA,CAAAJ,gBAAA;oBAAA;sBACA3U,OAAA,CAAAC,GAAA,IAAAc,MAAA,CAAAsT,OAAA,CAAA9Y,WAAA;sBAAAuZ,UAAA,CAAA3V,CAAA;sBAAA;oBAAA;sBAAA2V,UAAA,CAAApU,CAAA;sBAAAkU,IAAA,GAAAE,UAAA,CAAA9T,CAAA;sBAEAhB,OAAA,CAAA4G,IAAA,0BAAAgO,IAAA,CAAA5P,OAAA;sBACA;oBAAA;sBAIA;sBACAqP,OAAA,CAAAW,qBAAA,CAAAL,gBAAA;;sBAEA;sBACA,IAAAN,OAAA,CAAAvX,aAAA,KAAA6X,gBAAA;wBACAN,OAAA,CAAAvX,aAAA;sBACA;sBAEAuX,OAAA,CAAA1U,QAAA,CAAAC,OAAA;sBACAyU,OAAA,CAAAtB,qBAAA;sBAAA+B,UAAA,CAAA3V,CAAA;sBAAA;oBAAA;sBAAA2V,UAAA,CAAApU,CAAA;sBAAAmU,IAAA,GAAAC,UAAA,CAAA9T,CAAA;sBAGAhB,OAAA,CAAAiB,KAAA,aAAA4T,IAAA;sBACAR,OAAA,CAAA1U,QAAA,CAAAsB,KAAA,8BAAAF,MAAA,CAAA8T,IAAA,CAAA7P,OAAA;oBAAA;sBAAA,OAAA8P,UAAA,CAAAxV,CAAA;kBAAA;gBAAA,GAAAoV,SAAA;cAAA,CAEA;YAAA;cAAA,OAAAH,UAAA,CAAAjV,CAAA;UAAA;QAAA,GAAAgV,SAAA;MAAA;IACA;IAEA;IACA1T,cAAA,WAAAA,eAAA;MACA;MACA,YAAAqU,MAAA,CAAAC,KAAA,CAAA3U,IAAA,CAAAnF,IAAA;IACA;IAEA;IACAyF,gBAAA,WAAAA,iBAAA;MACA,YAAA/D,aAAA;IACA;IAEA;IACAqY,YAAA,WAAAA,aAAAjU,UAAA;MACA,IAAAX,IAAA,QAAAK,cAAA;MACA,IAAAO,MAAA,QAAAN,gBAAA;MACA,UAAAE,MAAA,CAAAG,UAAA,OAAAH,MAAA,CAAAR,IAAA,OAAAQ,MAAA,CAAAI,MAAA;IACA;IAEA;IACAiU,oBAAA,WAAAA,qBAAAC,OAAA,EAAAC,OAAA;MAAA,WAAAzW,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAAwW,UAAA;QAAA,IAAAjV,MAAA,EAAAkV,IAAA;QAAA,WAAA1W,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAAwW,UAAA;UAAA,kBAAAA,UAAA,CAAA/U,CAAA,GAAA+U,UAAA,CAAAtW,CAAA;YAAA;cAAAsW,UAAA,CAAA/U,CAAA;cAEAJ,MAAA,OAAAK,uBAAA,KAEA;cAAA8U,UAAA,CAAAtW,CAAA;cAAA,OACAmB,MAAA,CAAAoV,IAAA,CAAAJ,OAAA,EAAAD,OAAA;YAAA;cACArV,OAAA,CAAAC,GAAA,6CAAAc,MAAA,CAAAsU,OAAA,UAAAtU,MAAA,CAAAuU,OAAA;;cAEA;cAAAG,UAAA,CAAAtW,CAAA;cAAA,OACAmB,MAAA,CAAAqV,MAAA,CAAAN,OAAA;YAAA;cACArV,OAAA,CAAAC,GAAA,mDAAAc,MAAA,CAAAsU,OAAA;cAAA,OAAAI,UAAA,CAAAnW,CAAA,IAEA;YAAA;cAAAmW,UAAA,CAAA/U,CAAA;cAAA8U,IAAA,GAAAC,UAAA,CAAAzU,CAAA;cAEAhB,OAAA,CAAAiB,KAAA,iBAAAuU,IAAA;cAAA,MAAAA,IAAA;YAAA;cAAA,OAAAC,UAAA,CAAAnW,CAAA;UAAA;QAAA,GAAAiW,SAAA;MAAA;IAGA;IAEA;IACAvB,eAAA,WAAAA,gBAAAH,aAAA,EAAA7W,aAAA;MAAA,IAAA4Y,OAAA;MAAA,WAAA/W,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAA8W,UAAA;QAAA,IAAAvV,MAAA,EAAAC,IAAA,EAAAyG,WAAA,EAAA8O,GAAA,EAAAC,aAAA,EAAA7U,UAAA,EAAA8U,SAAA,EAAAC,SAAA,EAAAzU,MAAA,EAAA0U,SAAA,EAAAC,KAAA,EAAAnU,GAAA,EAAAoU,MAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;QAAA,WAAA5X,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAA0X,UAAA;UAAA,kBAAAA,UAAA,CAAAjW,CAAA,GAAAiW,UAAA,CAAAxX,CAAA;YAAA;cAAAwX,UAAA,CAAAjW,CAAA;cAEAJ,MAAA,OAAAK,uBAAA;cACAJ,IAAA,GAAAqV,OAAA,CAAAhV,cAAA,IAEA;cACAoG,WAAA;cAAA8O,GAAA,MAAAC,aAAA,GAEA/O,WAAA;YAAA;cAAA,MAAA8O,GAAA,GAAAC,aAAA,CAAAvX,MAAA;gBAAAmY,UAAA,CAAAxX,CAAA;gBAAA;cAAA;cAAA+B,UAAA,GAAA6U,aAAA,CAAAD,GAAA;cACAE,SAAA,MAAAjV,MAAA,CAAAG,UAAA,OAAAH,MAAA,CAAAR,IAAA,OAAAQ,MAAA,CAAA8S,aAAA;cACAoC,SAAA,MAAAlV,MAAA,CAAAG,UAAA,OAAAH,MAAA,CAAAR,IAAA,OAAAQ,MAAA,CAAA/D,aAAA,QAEA;cAAA2Z,UAAA,CAAAxX,CAAA;cAAA,OACAmB,MAAA,CAAAtC,IAAA;gBACAuD,MAAA,EAAAyU,SAAA;gBACA;cACA;YAAA;cAHAxU,MAAA,GAAAmV,UAAA,CAAA3V,CAAA;cAAA,MAKAQ,MAAA,CAAAM,OAAA,IAAAN,MAAA,CAAAM,OAAA,CAAAtD,MAAA;gBAAAmY,UAAA,CAAAxX,CAAA;gBAAA;cAAA;cACA;cAAA+W,SAAA,OAAAU,2BAAA,CAAAzZ,OAAA,EACAqE,MAAA,CAAAM,OAAA;cAAA6U,UAAA,CAAAjW,CAAA;cAAAwV,SAAA,CAAAW,CAAA;YAAA;cAAA,KAAAV,KAAA,GAAAD,SAAA,CAAA/W,CAAA,IAAA2X,IAAA;gBAAAH,UAAA,CAAAxX,CAAA;gBAAA;cAAA;cAAA6C,GAAA,GAAAmU,KAAA,CAAAvC,KAAA;cACAwC,MAAA,GAAApU,GAAA,CAAA5G,IAAA,CAAAiI,OAAA,CAAA2S,SAAA,EAAAC,SAAA;cAAAU,UAAA,CAAAxX,CAAA;cAAA,OACAmB,MAAA,CAAAoV,IAAA,CAAAU,MAAA,EAAApU,GAAA,CAAA5G,IAAA;YAAA;cACA4E,OAAA,CAAAC,GAAA,6CAAAc,MAAA,CAAAiB,GAAA,CAAA5G,IAAA,UAAA2F,MAAA,CAAAqV,MAAA;YAAA;cAAAO,UAAA,CAAAxX,CAAA;cAAA;YAAA;cAAAwX,UAAA,CAAAxX,CAAA;cAAA;YAAA;cAAAwX,UAAA,CAAAjW,CAAA;cAAA8V,IAAA,GAAAG,UAAA,CAAA3V,CAAA;cAAAkV,SAAA,CAAAa,CAAA,CAAAP,IAAA;YAAA;cAAAG,UAAA,CAAAjW,CAAA;cAAAwV,SAAA,CAAAjR,CAAA;cAAA,OAAA0R,UAAA,CAAA1R,CAAA;YAAA;cAGA;cAAAoR,UAAA,OAAAO,2BAAA,CAAAzZ,OAAA,EACAqE,MAAA,CAAAM,OAAA;cAAA6U,UAAA,CAAAjW,CAAA;cAAA2V,UAAA,CAAAQ,CAAA;YAAA;cAAA,KAAAP,MAAA,GAAAD,UAAA,CAAAlX,CAAA,IAAA2X,IAAA;gBAAAH,UAAA,CAAAxX,CAAA;gBAAA;cAAA;cAAA6C,IAAA,GAAAsU,MAAA,CAAA1C,KAAA;cAAA+C,UAAA,CAAAxX,CAAA;cAAA,OACAmB,MAAA,CAAAqV,MAAA,CAAA3T,IAAA,CAAA5G,IAAA;YAAA;cAAAub,UAAA,CAAAxX,CAAA;cAAA;YAAA;cAAAwX,UAAA,CAAAxX,CAAA;cAAA;YAAA;cAAAwX,UAAA,CAAAjW,CAAA;cAAA+V,IAAA,GAAAE,UAAA,CAAA3V,CAAA;cAAAqV,UAAA,CAAAU,CAAA,CAAAN,IAAA;YAAA;cAAAE,UAAA,CAAAjW,CAAA;cAAA2V,UAAA,CAAApR,CAAA;cAAA,OAAA0R,UAAA,CAAA1R,CAAA;YAAA;cAAA6Q,GAAA;cAAAa,UAAA,CAAAxX,CAAA;cAAA;YAAA;cAAA,OAAAwX,UAAA,CAAArX,CAAA,IAKA;YAAA;cAAAqX,UAAA,CAAAjW,CAAA;cAAAgW,IAAA,GAAAC,UAAA,CAAA3V,CAAA;cAEAhB,OAAA,CAAAiB,KAAA,iBAAAyV,IAAA;cAAA,MAAAA,IAAA;YAAA;cAAA,OAAAC,UAAA,CAAArX,CAAA;UAAA;QAAA,GAAAuW,SAAA;MAAA;IAGA;IAEA;IACAmB,eAAA,WAAAA,gBAAAjR,UAAA;MAAA,IAAAkR,OAAA;MAAA,WAAApY,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAAmY,UAAA;QAAA,IAAA5W,MAAA,EAAAC,IAAA,EAAAyG,WAAA,EAAAmQ,iBAAA,EAAAC,GAAA,EAAAC,aAAA,EAAAnW,UAAA,EAAAK,MAAA,EAAAC,MAAA,EAAA8V,UAAA,EAAAC,MAAA,EAAAvV,GAAA,EAAAwV,IAAA,EAAAC,IAAA;QAAA,WAAA3Y,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAAyY,UAAA;UAAA,kBAAAA,UAAA,CAAAhX,CAAA,GAAAgX,UAAA,CAAAvY,CAAA;YAAA;cAAAuY,UAAA,CAAAhX,CAAA;cAEAV,OAAA,CAAAC,GAAA,gBAAA8F,UAAA;cAAA,IAEAA,UAAA;gBAAA2R,UAAA,CAAAvY,CAAA;gBAAA;cAAA;cAAA,MACA,IAAA0I,KAAA;YAAA;cAGAvH,MAAA,OAAAK,uBAAA;cAAA,IACAL,MAAA;gBAAAoX,UAAA,CAAAvY,CAAA;gBAAA;cAAA;cAAA,MACA,IAAA0I,KAAA;YAAA;cAGAtH,IAAA,GAAA0W,OAAA,CAAArW,cAAA;cACAZ,OAAA,CAAAC,GAAA,UAAAM,IAAA;;cAEA;cACAyG,WAAA;cACAmQ,iBAAA;cAAAC,GAAA,MAAAC,aAAA,GAEArQ,WAAA;YAAA;cAAA,MAAAoQ,GAAA,GAAAC,aAAA,CAAA7Y,MAAA;gBAAAkZ,UAAA,CAAAvY,CAAA;gBAAA;cAAA;cAAA+B,UAAA,GAAAmW,aAAA,CAAAD,GAAA;cACA7V,MAAA,MAAAR,MAAA,CAAAG,UAAA,OAAAH,MAAA,CAAAR,IAAA,OAAAQ,MAAA,CAAAgF,UAAA;cACA/F,OAAA,CAAAC,GAAA,aAAAsB,MAAA;;cAEA;cAAAmW,UAAA,CAAAvY,CAAA;cAAA,OACAmB,MAAA,CAAAtC,IAAA;gBACAuD,MAAA,EAAAA,MAAA;gBACA;cACA;YAAA;cAHAC,MAAA,GAAAkW,UAAA,CAAA1W,CAAA;cAKAhB,OAAA,CAAAC,GAAA,iBAAAc,MAAA,CAAAQ,MAAA,0BAAAR,MAAA,CAAAS,MAAA,CAAAM,OAAA,GAAAN,MAAA,CAAAM,OAAA,CAAAtD,MAAA;cAAA,MAEAgD,MAAA,CAAAM,OAAA,IAAAN,MAAA,CAAAM,OAAA,CAAAtD,MAAA;gBAAAkZ,UAAA,CAAAvY,CAAA;gBAAA;cAAA;cACA;cAAAmY,UAAA,OAAAV,2BAAA,CAAAzZ,OAAA,EACAqE,MAAA,CAAAM,OAAA;cAAA4V,UAAA,CAAAhX,CAAA;cAAA4W,UAAA,CAAAT,CAAA;YAAA;cAAA,KAAAU,MAAA,GAAAD,UAAA,CAAAnY,CAAA,IAAA2X,IAAA;gBAAAY,UAAA,CAAAvY,CAAA;gBAAA;cAAA;cAAA6C,GAAA,GAAAuV,MAAA,CAAA3D,KAAA;cAAA8D,UAAA,CAAAvY,CAAA;cAAA,OACAmB,MAAA,CAAAqV,MAAA,CAAA3T,GAAA,CAAA5G,IAAA;YAAA;cACA4E,OAAA,CAAAC,GAAA,iCAAAc,MAAA,CAAAiB,GAAA,CAAA5G,IAAA;cACA+b,iBAAA;YAAA;cAAAO,UAAA,CAAAvY,CAAA;cAAA;YAAA;cAAAuY,UAAA,CAAAvY,CAAA;cAAA;YAAA;cAAAuY,UAAA,CAAAhX,CAAA;cAAA8W,IAAA,GAAAE,UAAA,CAAA1W,CAAA;cAAAsW,UAAA,CAAAP,CAAA,CAAAS,IAAA;YAAA;cAAAE,UAAA,CAAAhX,CAAA;cAAA4W,UAAA,CAAArS,CAAA;cAAA,OAAAyS,UAAA,CAAAzS,CAAA;YAAA;cAAAmS,GAAA;cAAAM,UAAA,CAAAvY,CAAA;cAAA;YAAA;cAKAa,OAAA,CAAAC,GAAA,0EAAAc,MAAA,CAAAoW,iBAAA;cAAA,OAAAO,UAAA,CAAApY,CAAA,IACA;YAAA;cAAAoY,UAAA,CAAAhX,CAAA;cAAA+W,IAAA,GAAAC,UAAA,CAAA1W,CAAA;cAEAhB,OAAA,CAAAiB,KAAA,gBAAAwW,IAAA;cAAA,MACA,IAAA5P,KAAA,mDAAA9G,MAAA,CAAA0W,IAAA,CAAAzS,OAAA;YAAA;cAAA,OAAA0S,UAAA,CAAApY,CAAA;UAAA;QAAA,GAAA4X,SAAA;MAAA;IAEA;IAEA;IACAnC,4BAAA,WAAAA,6BAAAhP,UAAA;MAAA,IAAA4R,OAAA;MAAA,WAAA9Y,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAA6Y,UAAA;QAAA,IAAAtX,MAAA,EAAAC,IAAA,EAAAW,UAAA,EAAAK,MAAA,EAAAC,MAAA,EAAAqW,YAAA,EAAAC,UAAA,EAAAC,MAAA,EAAA/V,GAAA,EAAAgW,IAAA,EAAAC,IAAA;QAAA,WAAAnZ,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAAiZ,UAAA;UAAA,kBAAAA,UAAA,CAAAxX,CAAA,GAAAwX,UAAA,CAAA/Y,CAAA;YAAA;cAAA+Y,UAAA,CAAAxX,CAAA;cAEAV,OAAA,CAAAC,GAAA,4BAAAc,MAAA,CAAA4W,OAAA,CAAApc,WAAA,mDAAAwK,UAAA;cAAA,IAEAA,UAAA;gBAAAmS,UAAA,CAAA/Y,CAAA;gBAAA;cAAA;cAAA,MACA,IAAA0I,KAAA;YAAA;cAGAvH,MAAA,OAAAK,uBAAA;cAAA,IACAL,MAAA;gBAAA4X,UAAA,CAAA/Y,CAAA;gBAAA;cAAA;cAAA,MACA,IAAA0I,KAAA;YAAA;cAGAtH,IAAA,GAAAoX,OAAA,CAAA/W,cAAA;cACAM,UAAA,GAAAyW,OAAA,CAAApc,WAAA;cACAgG,MAAA,MAAAR,MAAA,CAAAG,UAAA,OAAAH,MAAA,CAAAR,IAAA,OAAAQ,MAAA,CAAAgF,UAAA;cAEA/F,OAAA,CAAAC,GAAA,aAAAsB,MAAA;;cAEA;cAAA2W,UAAA,CAAA/Y,CAAA;cAAA,OACAmB,MAAA,CAAAtC,IAAA;gBACAuD,MAAA,EAAAA,MAAA;gBACA;cACA;YAAA;cAHAC,MAAA,GAAA0W,UAAA,CAAAlX,CAAA;cAKA6W,YAAA;cAAA,MACArW,MAAA,CAAAM,OAAA,IAAAN,MAAA,CAAAM,OAAA,CAAAtD,MAAA;gBAAA0Z,UAAA,CAAA/Y,CAAA;gBAAA;cAAA;cACA;cAAA2Y,UAAA,OAAAlB,2BAAA,CAAAzZ,OAAA,EACAqE,MAAA,CAAAM,OAAA;cAAAoW,UAAA,CAAAxX,CAAA;cAAAoX,UAAA,CAAAjB,CAAA;YAAA;cAAA,KAAAkB,MAAA,GAAAD,UAAA,CAAA3Y,CAAA,IAAA2X,IAAA;gBAAAoB,UAAA,CAAA/Y,CAAA;gBAAA;cAAA;cAAA6C,GAAA,GAAA+V,MAAA,CAAAnE,KAAA;cAAAsE,UAAA,CAAA/Y,CAAA;cAAA,OACAmB,MAAA,CAAAqV,MAAA,CAAA3T,GAAA,CAAA5G,IAAA;YAAA;cACA4E,OAAA,CAAAC,GAAA,iCAAAc,MAAA,CAAAiB,GAAA,CAAA5G,IAAA;cACAyc,YAAA;YAAA;cAAAK,UAAA,CAAA/Y,CAAA;cAAA;YAAA;cAAA+Y,UAAA,CAAA/Y,CAAA;cAAA;YAAA;cAAA+Y,UAAA,CAAAxX,CAAA;cAAAsX,IAAA,GAAAE,UAAA,CAAAlX,CAAA;cAAA8W,UAAA,CAAAf,CAAA,CAAAiB,IAAA;YAAA;cAAAE,UAAA,CAAAxX,CAAA;cAAAoX,UAAA,CAAA7S,CAAA;cAAA,OAAAiT,UAAA,CAAAjT,CAAA;YAAA;cAIAjF,OAAA,CAAAC,GAAA,IAAAc,MAAA,CAAA4W,OAAA,CAAApc,WAAA,kGAAAwF,MAAA,CAAA8W,YAAA;cAAA,OAAAK,UAAA,CAAA5Y,CAAA,IACA;YAAA;cAAA4Y,UAAA,CAAAxX,CAAA;cAAAuX,IAAA,GAAAC,UAAA,CAAAlX,CAAA;cAEAhB,OAAA,CAAAiB,KAAA,gBAAAF,MAAA,CAAA4W,OAAA,CAAApc,WAAA,+DAAA0c,IAAA;cAAA,MACA,IAAApQ,KAAA,gBAAA9G,MAAA,CAAA4W,OAAA,CAAApc,WAAA,+DAAAwF,MAAA,CAAAkX,IAAA,CAAAjT,OAAA;YAAA;cAAA,OAAAkT,UAAA,CAAA5Y,CAAA;UAAA;QAAA,GAAAsY,SAAA;MAAA;IAEA;IAEA;IACA5C,qBAAA,WAAAA,sBAAAjP,UAAA;MACA,SAAAxK,WAAA;QACA;QACA,KAAAqC,OAAA,QAAAA,OAAA,CAAAmE,MAAA,WAAAgG,IAAA;UACA,IAAAoQ,cAAA,GAAApQ,IAAA,CAAA5G,MAAA,GAAA4G,IAAA,CAAA5G,MAAA,CAAAkB,KAAA,MAAAC,GAAA;UACA,OAAA6V,cAAA,KAAApS,UAAA;QACA;;QAEA;QACA,IAAAqS,SAAA,QAAAvc,aAAA,CAAAwc,SAAA,WAAAlX,MAAA;UAAA,OAAAA,MAAA,CAAA/F,IAAA,KAAA2K,UAAA;QAAA;QACA,IAAAqS,SAAA;UACA,KAAAvc,aAAA,CAAAyc,MAAA,CAAAF,SAAA;UACApY,OAAA,CAAAC,GAAA,yDAAAc,MAAA,CAAAgF,UAAA;QACA;MACA;QACA;QACA,KAAAlI,SAAA,QAAAA,SAAA,CAAAkE,MAAA,WAAAgG,IAAA;UACA,IAAAoQ,cAAA,GAAApQ,IAAA,CAAA5G,MAAA,GAAA4G,IAAA,CAAA5G,MAAA,CAAAkB,KAAA,MAAAC,GAAA;UACA,OAAA6V,cAAA,KAAApS,UAAA;QACA;;QAEA;QACA,IAAAqS,UAAA,QAAAtc,eAAA,CAAAuc,SAAA,WAAAlX,MAAA;UAAA,OAAAA,MAAA,CAAA/F,IAAA,KAAA2K,UAAA;QAAA;QACA,IAAAqS,UAAA;UACA,KAAAtc,eAAA,CAAAwc,MAAA,CAAAF,UAAA;UACApY,OAAA,CAAAC,GAAA,kEAAAc,MAAA,CAAAgF,UAAA;QACA;MACA;IACA;IAEA;IACAwS,YAAA,WAAAA,aAAApX,MAAA;MAAA,IAAAqX,OAAA;MAAA,WAAA3Z,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAA0Z,UAAA;QAAA,WAAA3Z,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAAyZ,UAAA;UAAA,kBAAAA,UAAA,CAAAvZ,CAAA;YAAA;cACAqZ,OAAA,CAAAhd,cAAA,GAAA2F,MAAA,CAAA/D,EAAA;cACAob,OAAA,CAAA1b,aAAA,GAAAqE,MAAA,CAAA/F,IAAA;cACA4E,OAAA,CAAAC,GAAA,WAAAkB,MAAA,CAAA/F,IAAA;;cAEA;cAAA,KACAod,OAAA,CAAA3b,cAAA;gBAAA6b,UAAA,CAAAvZ,CAAA;gBAAA;cAAA;cAAAuZ,UAAA,CAAAvZ,CAAA;cAAA,OACAqZ,OAAA,CAAAnZ,gBAAA;YAAA;cAGA;cACAmZ,OAAA,CAAArY,4BAAA;YAAA;cAAA,OAAAuY,UAAA,CAAApZ,CAAA;UAAA;QAAA,GAAAmZ,SAAA;MAAA;IACA;IAEA;IACAtY,4BAAA,WAAAA,6BAAA;MACA,IAAAwY,gBAAA,QAAAhb,mBAAA;MACA,IAAAqI,cAAA,QAAAlI,iBAAA;;MAEA;MACAkI,cAAA,CAAAoE,OAAA,WAAAjJ,MAAA;QACA,IAAAA,MAAA,CAAA/F,IAAA;UACA;UACA+F,MAAA,CAAAkF,KAAA,GAAAsS,gBAAA,CAAAna,MAAA;QACA;UACA;UACA2C,MAAA,CAAAkF,KAAA,GAAAsS,gBAAA,CAAA5W,MAAA,WAAAgG,IAAA;YACA,IAAAoQ,cAAA,GAAApQ,IAAA,CAAA5G,MAAA,GAAA4G,IAAA,CAAA5G,MAAA,CAAAkB,KAAA,MAAAC,GAAA;YACA,OAAA6V,cAAA,KAAAhX,MAAA,CAAA/F,IAAA;UACA,GAAAoD,MAAA;QACA;MACA;MAEAwB,OAAA,CAAAC,GAAA,IAAAc,MAAA,MAAAxF,WAAA,mDAAAyK,cAAA,CAAA9D,GAAA,WAAA+C,CAAA;QAAA,UAAAlE,MAAA,CAAAkE,CAAA,CAAA7J,IAAA,OAAA2F,MAAA,CAAAkE,CAAA,CAAAoB,KAAA;MAAA;IACA;IAEA;IACAuS,eAAA,WAAAA,gBAAAC,OAAA;MACA,IAAAA,OAAA;QACA,KAAAnd,aAAA,QAAAuC,kBAAA,CAAAiE,GAAA,WAAA+C,CAAA;UAAA,OAAAA,CAAA,CAAA7H,EAAA;QAAA;MACA;QACA,KAAA1B,aAAA;MACA;IACA;IAEA;IACAod,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA,SAAArd,aAAA,CAAA8C,MAAA;QACA,KAAAmB,QAAA,CAAA6U,OAAA;QACA;MACA;MAEA,IAAAzM,IAAA,QAAApK,mBAAA,CAAAqb,IAAA,WAAA/T,CAAA;QAAA,OAAAA,CAAA,CAAA7H,EAAA,KAAA2b,OAAA,CAAArd,aAAA;MAAA;MACA,KAAAqM,IAAA;QACA,KAAApI,QAAA,CAAAsB,KAAA;QACA;MACA;;MAEA;MACA,KAAA8G,IAAA,CAAAtK,GAAA,SAAAZ,cAAA;QACA;QACA,IAAAF,QAAA,QAAAJ,SAAA,CAAAI,QAAA,CAAA0G,OAAA,iBAAAA,OAAA;QACA0E,IAAA,CAAAtK,GAAA,cAAAsD,MAAA,CAAApE,QAAA,OAAAoE,MAAA,CAAAgH,IAAA,CAAAzE,WAAA;MACA;MAEA,KAAA4J,kBAAA,GAAAnF,IAAA;MACA,KAAAoF,cAAA;IACA;IAEA8L,mBAAA,WAAAA,oBAAA7J,MAAA;MACA,IAAAjN,KAAA,QAAAzG,aAAA,CAAAwd,OAAA,CAAA9J,MAAA;MACA,IAAAjN,KAAA;QACA,KAAAzG,aAAA,CAAA4c,MAAA,CAAAnW,KAAA;MACA;QACA,KAAAzG,aAAA,CAAA6H,IAAA,CAAA6L,MAAA;MACA;;MAEA;MACA,KAAA3T,SAAA,QAAAC,aAAA,CAAA8C,MAAA,UAAAP,kBAAA,CAAAO,MAAA;IACA;IAEA2a,iBAAA,WAAAA,kBAAA9b,IAAA;MACA,IAAA+b,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAA/b,IAAA;IACA;IAEA;IACAgc,SAAA,WAAAA,UAAAC,GAAA;MAAA,IAAAC,OAAA;MACA,KAAAhe,WAAA,GAAA+d,GAAA;MACA,KAAA3d,WAAA;MACA,KAAAD,aAAA;MACA,KAAAD,SAAA;MAEA,IAAA+d,QAAA;QACA;QACA;MACA;MACA,KAAA7Z,QAAA,CAAAC,OAAA,sBAAAmB,MAAA,CAAAyY,QAAA,CAAAF,GAAA,KAAAA,GAAA;;MAEA;MACA,KAAArP,SAAA;QACAsP,OAAA,CAAApZ,4BAAA;MACA;IACA;IAEA;IACAsZ,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAA7a,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAA4a,UAAA;QAAA,IAAA5R,IAAA;QAAA,WAAAjJ,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAA2a,UAAA;UAAA,kBAAAA,UAAA,CAAAza,CAAA;YAAA;cAAA,MACAua,OAAA,CAAAhe,aAAA,CAAA8C,MAAA;gBAAAob,UAAA,CAAAza,CAAA;gBAAA;cAAA;cACAua,OAAA,CAAA/Z,QAAA,CAAA6U,OAAA;cAAA,OAAAoF,UAAA,CAAAta,CAAA;YAAA;cAAA,MAGAoa,OAAA,CAAAhe,aAAA,CAAA8C,MAAA;gBAAAob,UAAA,CAAAza,CAAA;gBAAA;cAAA;cACAua,OAAA,CAAA/Z,QAAA,CAAA6U,OAAA;cAAA,OAAAoF,UAAA,CAAAta,CAAA;YAAA;cAIAyI,IAAA,GAAA2R,OAAA,CAAA/b,mBAAA,CAAAqb,IAAA,WAAA/T,CAAA;gBAAA,OAAAA,CAAA,CAAA7H,EAAA,KAAAsc,OAAA,CAAAhe,aAAA;cAAA;cACAge,OAAA,CAAAtG,OAAA;gBACAC,iBAAA;gBACAC,gBAAA;gBACAC,UAAA,EAAAxL,IAAA,CAAA3M;cACA,GAAAoY,IAAA;gBAAA,IAAAqG,KAAA,OAAAhb,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAA+a,UAAAC,KAAA;kBAAA,IAAAnG,KAAA,EAAAoG,WAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,SAAA,EAAAC,cAAA,EAAAC,QAAA,EAAAC,cAAA,EAAAC,IAAA,EAAAC,IAAA;kBAAA,WAAA1b,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAAwb,UAAA;oBAAA,kBAAAA,UAAA,CAAA/Z,CAAA,GAAA+Z,UAAA,CAAAtb,CAAA;sBAAA;wBAAAyU,KAAA,GAAAmG,KAAA,CAAAnG,KAAA;wBAAA6G,UAAA,CAAA/Z,CAAA;wBAAA,MAGA,CAAAkT,KAAA,IAAAA,KAAA,CAAAzN,IAAA;0BAAAsU,UAAA,CAAAtb,CAAA;0BAAA;wBAAA;wBACAua,OAAA,CAAA/Z,QAAA,CAAAsB,KAAA;wBAAA,OAAAwZ,UAAA,CAAAnb,CAAA;sBAAA;wBAIA0a,WAAA,GAAApG,KAAA,CAAAzN,IAAA,IAEA;wBAAA,MACAuT,OAAA,CAAAtd,WAAA,cAAAsd,OAAA,CAAA7c,cAAA;0BAAA4d,UAAA,CAAAtb,CAAA;0BAAA;wBAAA;wBACA;wBACA8a,cAAA;wBACA,IAAAlS,IAAA,CAAAzE,WAAA;0BACA2W,cAAA,GAAAlS,IAAA,CAAAzE,WAAA;wBACA;0BACA8W,eAAA,GAAAV,OAAA,CAAAne,WAAA;0BACA8e,SAAA,GAAAX,OAAA,CAAAvE,YAAA,CAAAiF,eAAA;0BACAH,cAAA,MAAAlZ,MAAA,CAAAsZ,SAAA,OAAAtZ,MAAA,CAAAgH,IAAA,CAAA3M,IAAA;wBACA;;wBAEA;wBACAgf,cAAA,GAAAV,OAAA,CAAAne,WAAA;wBACA8e,QAAA,GAAAX,OAAA,CAAAvE,YAAA,CAAAiF,cAAA;wBACAE,cAAA,MAAAvZ,MAAA,CAAAsZ,QAAA,OAAAtZ,MAAA,CAAAiZ,WAAA;wBAEAha,OAAA,CAAAC,GAAA,2BAAAc,MAAA,CAAAkZ,cAAA,UAAAlZ,MAAA,CAAAuZ,cAAA;wBAAAG,UAAA,CAAA/Z,CAAA;wBAAA+Z,UAAA,CAAAtb,CAAA;wBAAA,OAIAua,OAAA,CAAAtE,oBAAA,CAAA6E,cAAA,EAAAK,cAAA;sBAAA;wBAEA;wBACAvS,IAAA,CAAA3M,IAAA,GAAA4e,WAAA;wBACAjS,IAAA,CAAAzE,WAAA,GAAAgX,cAAA;wBAEAZ,OAAA,CAAA/Z,QAAA,CAAAC,OAAA;wBAAA6a,UAAA,CAAAtb,CAAA;wBAAA;sBAAA;wBAAAsb,UAAA,CAAA/Z,CAAA;wBAAA6Z,IAAA,GAAAE,UAAA,CAAAzZ,CAAA;wBAEAhB,OAAA,CAAAiB,KAAA,cAAAsZ,IAAA;wBACAb,OAAA,CAAA/Z,QAAA,CAAAsB,KAAA,oCAAAF,MAAA,CAAAwZ,IAAA,CAAAvV,OAAA;sBAAA;wBAAAyV,UAAA,CAAAtb,CAAA;wBAAA;sBAAA;wBAGA;wBACA4I,IAAA,CAAA3M,IAAA,GAAA4e,WAAA;wBACAN,OAAA,CAAA/Z,QAAA,CAAAC,OAAA;sBAAA;wBAAA6a,UAAA,CAAAtb,CAAA;wBAAA;sBAAA;wBAAAsb,UAAA,CAAA/Z,CAAA;wBAAA8Z,IAAA,GAAAC,UAAA,CAAAzZ,CAAA;wBAIAhB,OAAA,CAAAiB,KAAA,WAAAuZ,IAAA;wBACAd,OAAA,CAAA/Z,QAAA,CAAAsB,KAAA,oCAAAF,MAAA,CAAAyZ,IAAA,CAAAxV,OAAA;sBAAA;wBAAA,OAAAyV,UAAA,CAAAnb,CAAA;oBAAA;kBAAA,GAAAwa,SAAA;gBAAA,CAEA;gBAAA,iBAAAY,GAAA;kBAAA,OAAAb,KAAA,CAAArW,KAAA,OAAA2Q,SAAA;gBAAA;cAAA;YAAA;cAAA,OAAAyF,UAAA,CAAAta,CAAA;UAAA;QAAA,GAAAqa,SAAA;MAAA;IACA;IAEAgB,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAA/b,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAA8b,UAAA;QAAA,WAAA/b,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAA6b,UAAA;UAAA,kBAAAA,UAAA,CAAA3b,CAAA;YAAA;cAAA,MACAyb,OAAA,CAAAlf,aAAA,CAAA8C,MAAA;gBAAAsc,UAAA,CAAA3b,CAAA;gBAAA;cAAA;cACAyb,OAAA,CAAAjb,QAAA,CAAA6U,OAAA;cAAA,OAAAsG,UAAA,CAAAxb,CAAA;YAAA;cAIAsb,OAAA,CAAAnG,QAAA,qDAAA1T,MAAA,CAAA6Z,OAAA,CAAAlf,aAAA,CAAA8C,MAAA;gBACA6U,iBAAA;gBACAC,gBAAA;gBACAjW,IAAA;cACA,GAAAmW,IAAA,kBAAA3U,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAAgc,UAAA;gBAAA,IAAAC,WAAA,EAAAC,aAAA,EAAAC,cAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,IAAA;gBAAA,WAAAvc,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAAqc,UAAA;kBAAA,kBAAAA,UAAA,CAAA5a,CAAA,GAAA4a,UAAA,CAAAnc,CAAA;oBAAA;sBAAAmc,UAAA,CAAA5a,CAAA;sBAEAsa,WAAA,GAAAJ,OAAA,CAAArf,WAAA,aAAAqf,OAAA,CAAAhd,OAAA,GAAAgd,OAAA,CAAA/c,SAAA,EAEA;sBACAod,aAAA,GAAAL,OAAA,CAAAlf,aAAA,CAAAwG,GAAA,WAAAkN,MAAA;wBACA,OAAA4L,WAAA,CAAAhC,IAAA,WAAA/T,CAAA;0BAAA,OAAAA,CAAA,CAAA7H,EAAA,KAAAgS,MAAA;wBAAA;sBACA,GAAArN,MAAA,WAAAgG,IAAA;wBAAA,OAAAA,IAAA;sBAAA;sBAEA/H,OAAA,CAAAC,GAAA,aAAAgb,aAAA;;sBAEA;sBAAA,MACAL,OAAA,CAAAxe,WAAA,cAAAwe,OAAA,CAAA/d,cAAA;wBAAAye,UAAA,CAAAnc,CAAA;wBAAA;sBAAA;sBACA+b,cAAA,GAAAD,aAAA,CAAA/Y,GAAA;wBAAA,IAAAqZ,KAAA,OAAA1c,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAAyc,UAAAzT,IAAA;0BAAA,IAAA0T,WAAA,EAAArB,cAAA,EAAAC,QAAA,EAAAqB,IAAA;0BAAA,WAAA5c,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAA0c,UAAA;4BAAA,kBAAAA,UAAA,CAAAjb,CAAA,GAAAib,UAAA,CAAAxc,CAAA;8BAAA;gCAAAwc,UAAA,CAAAjb,CAAA;gCAEA;gCACA+a,WAAA;gCACA,IAAA1T,IAAA,CAAAzE,WAAA;kCACA;kCACAmY,WAAA,GAAA1T,IAAA,CAAAzE,WAAA;gCACA;kCACA;kCACA8W,cAAA,GAAAQ,OAAA,CAAArf,WAAA;kCACA8e,QAAA,GAAAO,OAAA,CAAAzF,YAAA,CAAAiF,cAAA;kCACAqB,WAAA,MAAA1a,MAAA,CAAAsZ,QAAA,OAAAtZ,MAAA,CAAAgH,IAAA,CAAA3M,IAAA;gCACA;gCAEA4E,OAAA,CAAAC,GAAA,iCAAAc,MAAA,CAAA0a,WAAA;gCAAAE,UAAA,CAAAxc,CAAA;gCAAA,OACA,IAAAyc,4BAAA,EAAAH,WAAA;8BAAA;gCAAA,OAAAE,UAAA,CAAArc,CAAA,IACA;kCAAAM,OAAA;kCAAAmI,IAAA,EAAAA,IAAA,CAAA3M;gCAAA;8BAAA;gCAAAugB,UAAA,CAAAjb,CAAA;gCAAAgb,IAAA,GAAAC,UAAA,CAAA3a,CAAA;gCAEAhB,OAAA,CAAAiB,KAAA,6CAAAF,MAAA,CAAAgH,IAAA,CAAA3M,IAAA,GAAAsgB,IAAA;gCAAA,OAAAC,UAAA,CAAArc,CAAA,IACA;kCAAAM,OAAA;kCAAAmI,IAAA,EAAAA,IAAA,CAAA3M,IAAA;kCAAA6F,KAAA,EAAAya,IAAA,CAAA1W;gCAAA;4BAAA;0BAAA,GAAAwW,SAAA;wBAAA,CAEA;wBAAA,iBAAAK,GAAA;0BAAA,OAAAN,KAAA,CAAA/X,KAAA,OAAA2Q,SAAA;wBAAA;sBAAA;sBAAAmH,UAAA,CAAAnc,CAAA;sBAAA,OAEA2c,OAAA,CAAAC,GAAA,CAAAb,cAAA;oBAAA;sBAAAC,aAAA,GAAAG,UAAA,CAAAta,CAAA;sBAEA;sBACAoa,aAAA,GAAAD,aAAA,CAAApZ,MAAA,WAAAP,MAAA;wBAAA,QAAAA,MAAA,CAAA5B,OAAA;sBAAA;sBACA,IAAAwb,aAAA,CAAA5c,MAAA;wBACAwB,OAAA,CAAA4G,IAAA,cAAAwU,aAAA;wBACAR,OAAA,CAAAjb,QAAA,CAAA6U,OAAA,IAAAzT,MAAA,CAAAqa,aAAA,CAAA5c,MAAA;sBACA;oBAAA;sBAGA;sBACAoc,OAAA,CAAAlf,aAAA,CAAA0O,OAAA,WAAAgF,MAAA;wBACA,IAAAjN,KAAA,GAAA6Y,WAAA,CAAA3C,SAAA,WAAApT,CAAA;0BAAA,OAAAA,CAAA,CAAA7H,EAAA,KAAAgS,MAAA;wBAAA;wBACA,IAAAjN,KAAA;0BACA6Y,WAAA,CAAA1C,MAAA,CAAAnW,KAAA;wBACA;sBACA;sBAEAyY,OAAA,CAAAlf,aAAA;sBACAkf,OAAA,CAAAnf,SAAA;sBACAmf,OAAA,CAAAjb,QAAA,CAAAC,OAAA;sBAAA0b,UAAA,CAAAnc,CAAA;sBAAA;oBAAA;sBAAAmc,UAAA,CAAA5a,CAAA;sBAAA2a,IAAA,GAAAC,UAAA,CAAAta,CAAA;sBAGAhB,OAAA,CAAAiB,KAAA,UAAAoa,IAAA;sBACAT,OAAA,CAAAjb,QAAA,CAAAsB,KAAA,8BAAAF,MAAA,CAAAsa,IAAA,CAAArW,OAAA;oBAAA;sBAAA,OAAAsW,UAAA,CAAAhc,CAAA;kBAAA;gBAAA,GAAAyb,SAAA;cAAA,CAEA;YAAA;cAAA,OAAAD,UAAA,CAAAxb,CAAA;UAAA;QAAA,GAAAub,SAAA;MAAA;IACA;IACAmB,gBAAA,WAAAA,iBAAA;MACA,KAAAjgB,mBAAA;MACA,KAAAE,QAAA;MACA,KAAAC,UAAA;IACA;IAEA+f,YAAA,WAAAA,aAAAlU,IAAA;MACA;MACA/H,OAAA,CAAAC,GAAA;QACA7E,IAAA,EAAA2M,IAAA,CAAA3M,IAAA;QACAiC,IAAA,EAAA0K,IAAA,CAAA1K,IAAA;QACAC,IAAA,EAAAyK,IAAA,CAAAzK,IAAA;QACA4e,UAAA,OAAA3gB;MACA;;MAEA;MACA,IAAA4gB,OAAA;MACA,SAAA5gB,WAAA;QACA4gB,OAAA;MACA;MAEA,IAAAC,WAAA,GAAArU,IAAA,CAAAzK,IAAA,iBAAA6e,OAAA;MACA,KAAAC,WAAA;QACA,KAAAzc,QAAA,CAAAsB,KAAA,qDAAAF,MAAA,CAAAob,OAAA;QACA;MACA;;MAEA;MACA,IAAAE,SAAA,GAAAtU,IAAA,CAAA3M,IAAA,CAAAoH,WAAA,GAAAH,KAAA,MAAAC,GAAA;MACA,IAAAI,SAAA;MACA,IAAAD,SAAA;MACA,IAAA6Z,SAAA;MAEA,SAAA/gB,WAAA,eAAAkH,SAAA,CAAAG,QAAA,CAAAyZ,SAAA;QACA,KAAA1c,QAAA,CAAAsB,KAAA,qFAAAF,MAAA,CAAA0B,SAAA,CAAA8Z,IAAA;QACA;MACA;MAEA,SAAAhhB,WAAA;QACA,IAAAihB,OAAA,MAAAzb,MAAA,CAAA2B,SAAA,EAAAD,SAAA,EAAA6Z,SAAA;QACA,KAAAE,OAAA,CAAA5Z,QAAA,CAAAyZ,SAAA;UACA,KAAA1c,QAAA,CAAAsB,KAAA,4HAAAF,MAAA,CAAAyb,OAAA,CAAAD,IAAA;UACA;QACA;MACA;MAEA;IACA;IAEAE,eAAA,WAAAA,gBAAApf,IAAA,EAAA+E,QAAA;MACA;MACA,IAAAia,SAAA,GAAAja,QAAA,CAAAI,WAAA,GAAAH,KAAA,MAAAC,GAAA;MAEA,SAAA/G,WAAA;QACA,IAAAmhB,UAAA,IACA,iEACA,6DACA,4BACA;QACA,IAAAC,eAAA;QAEA,OAAAD,UAAA,CAAA9Z,QAAA,CAAAvF,IAAA,KAAAsf,eAAA,CAAA/Z,QAAA,CAAAyZ,SAAA;MACA;QACA,IAAAO,UAAA,IACA,uDACA,uDACA,0BACA;QACA,IAAAC,eAAA;QAEA,OAAAD,UAAA,CAAAha,QAAA,CAAAvF,IAAA,KAAAwf,eAAA,CAAAja,QAAA,CAAAyZ,SAAA;MACA;IACA;IACAS,mBAAA,WAAAA,oBAAAC,QAAA,EAAAhV,IAAA;MACA;MACA,IAAAiV,OAAA;QACA5f,EAAA,EAAAyF,IAAA,CAAAC,GAAA,KAAAxE,IAAA,CAAAyE,MAAA;QACA3H,IAAA,EAAA2M,IAAA,CAAA3M,IAAA;QACAiC,IAAA,OAAA9B,WAAA;QACA+B,IAAA,EAAAyK,IAAA,CAAAzK,IAAA;QACAC,UAAA,MAAAsF,IAAA,GAAAI,cAAA,GAAA7E,KAAA;QACAZ,QAAA;QAAA;QACA0F,UAAA,OAAA3H,WAAA,6BAAA4H,SAAA;QACAC,OAAA,OAAA7H,WAAA,2BAAA4H;MACA;;MAEA;MACA,SAAA5H,WAAA;QACA,KAAA0hB,SAAA,CAAAtL,OAAA,CAAAqL,OAAA;MACA;QACA,KAAAE,SAAA,CAAAvL,OAAA,CAAAqL,OAAA;MACA;MAEA,KAAArd,QAAA,CAAAC,OAAA,IAAAmB,MAAA,CAAAgH,IAAA,CAAA3M,IAAA;IACA;IAEA+hB,iBAAA,WAAAA,kBAAAC,GAAA,EAAArV,IAAA;MACA,KAAApI,QAAA,CAAAsB,KAAA,IAAAF,MAAA,CAAAgH,IAAA,CAAA3M,IAAA;IACA;IAEAiiB,oBAAA,WAAAA,qBAAAxR,KAAA,EAAA9D,IAAA;MACA;MACA,IAAAuV,QAAA,GAAAhf,IAAA,CAAAqL,KAAA,CAAAkC,KAAA,CAAA0R,MAAA,GAAA1R,KAAA,CAAA2R,KAAA;;MAEA;MACA,KAAA/U,IAAA,MAAAtM,cAAA,EAAA4L,IAAA,CAAA3M,IAAA,EAAAkiB,QAAA;MAEAtd,OAAA,CAAAC,GAAA,iBAAAc,MAAA,CAAAgH,IAAA,CAAA3M,IAAA,iCAAA2F,MAAA,CAAAuc,QAAA;IACA;IAEAG,gBAAA,WAAAA,iBAAA1V,IAAA,EAAA9L,QAAA;MACA+D,OAAA,CAAAC,GAAA,UAAA8H,IAAA;MACA/H,OAAA,CAAAC,GAAA,YAAAhE,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;IACA;IAEAyhB,YAAA,WAAAA,aAAA3V,IAAA,EAAA9L,QAAA;MACA+D,OAAA,CAAAC,GAAA,UAAA8H,IAAA;MACA/H,OAAA,CAAAC,GAAA,cAAAhE,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;IACA;IAEA0hB,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAA/e,kBAAA,CAAA1B,OAAA,mBAAA2B,aAAA,CAAA3B,OAAA,IAAA4B,CAAA,UAAA8e,UAAA;QAAA,IAAAzL,SAAA,EAAAgI,cAAA,EAAAzX,QAAA,EAAAoD,UAAA,EAAA+X,WAAA,EAAAC,aAAA,EAAAC,IAAA,EAAAC,IAAA;QAAA,WAAAnf,aAAA,CAAA3B,OAAA,IAAA8B,CAAA,WAAAif,UAAA;UAAA,kBAAAA,UAAA,CAAAxd,CAAA,GAAAwd,UAAA,CAAA/e,CAAA;YAAA;cACAa,OAAA,CAAAC,GAAA,YAAA2d,OAAA,CAAA3hB,QAAA;cACA+D,OAAA,CAAAC,GAAA,YAAA2d,OAAA,CAAA3hB,QAAA,CAAAuC,MAAA;cAAA,MAEA,CAAAof,OAAA,CAAA3hB,QAAA,IAAA2hB,OAAA,CAAA3hB,QAAA,CAAAuC,MAAA;gBAAA0f,UAAA,CAAA/e,CAAA;gBAAA;cAAA;cACAye,OAAA,CAAAje,QAAA,CAAA6U,OAAA;cAAA,OAAA0J,UAAA,CAAA5e,CAAA;YAAA;cAIAse,OAAA,CAAA5hB,SAAA;cACAoW,SAAA,GAAAwL,OAAA,CAAA3hB,QAAA,CAAAuC,MAAA,EAEA;cACA4b,cAAA;cACAzX,QAAA;cAAAqb,IAAA,GAEAJ,OAAA,CAAAriB,WAAA;cAAA2iB,UAAA,CAAA/e,CAAA,GAAA6e,IAAA,KACA,YAAAA,IAAA,KAIA;cAAA;YAAA;cAHA5D,cAAA;cACAzX,QAAA;cAAA,OAAAub,UAAA,CAAA5e,CAAA;YAAA;cAGA8a,cAAA;cACAzX,QAAA;cAAA,OAAAub,UAAA,CAAA5e,CAAA;YAAA;cAGA8a,cAAA;cACAzX,QAAA;YAAA;cAGA;cACAoD,UAAA,GAAA6X,OAAA,CAAAzI,YAAA,CAAAiF,cAAA;cAEApa,OAAA,CAAAC,GAAA,WAAA2d,OAAA,CAAAriB,WAAA;cACAyE,OAAA,CAAAC,GAAA,WAAAma,cAAA;cACApa,OAAA,CAAAC,GAAA,YAAA8F,UAAA;cACA/F,OAAA,CAAAC,GAAA,UAAA0C,QAAA;;cAEA;cACAmb,WAAA,GAAAF,OAAA,CAAA3hB,QAAA,CAAAiG,GAAA,WAAAic,QAAA;gBACA;gBACA,OAAAA,QAAA,CAAAC,GAAA,IAAAD,QAAA,CAAApW,IAAA,IAAAoW,QAAA;cACA,GAAApc,MAAA,WAAAgG,IAAA;gBAAA,OAAAA,IAAA,YAAAsW,IAAA;cAAA;cAEAre,OAAA,CAAAC,GAAA,YAAA6d,WAAA;cAAA,MAEAA,WAAA,CAAAtf,MAAA;gBAAA0f,UAAA,CAAA/e,CAAA;gBAAA;cAAA;cACAye,OAAA,CAAAje,QAAA,CAAA6U,OAAA;cAAA,OAAA0J,UAAA,CAAA5e,CAAA;YAAA;cAAA4e,UAAA,CAAAxd,CAAA;cAKA;cACAod,WAAA,CAAA1T,OAAA,WAAArC,IAAA;gBACA6V,OAAA,CAAAnV,IAAA,CAAAmV,OAAA,CAAAzhB,cAAA,EAAA4L,IAAA,CAAA3M,IAAA;cACA;cAAA,MAEAwiB,OAAA,CAAAxhB,WAAA;gBAAA8hB,UAAA,CAAA/e,CAAA;gBAAA;cAAA;cAAA,IAEAye,OAAA,CAAA/gB,cAAA;gBAAAqhB,UAAA,CAAA/e,CAAA;gBAAA;cAAA;cACAye,OAAA,CAAAje,QAAA,CAAAsB,KAAA;cACA2c,OAAA,CAAApY,aAAA;cACAoY,OAAA,CAAA5hB,SAAA;cAAA,OAAAkiB,UAAA,CAAA5e,CAAA;YAAA;cAAA4e,UAAA,CAAA/e,CAAA;cAAA,OAKA,IAAAmf,2BAAA,EACAR,WAAA,EACAnb,QAAA,EACAoD,UAAA;cAAA;cACA,UAAA5D,KAAA,EAAAmb,QAAA,EAAAlb,QAAA,EAAAZ,MAAA,EAAAP,KAAA;gBACA,IAAAA,KAAA;kBACA2c,OAAA,CAAAnV,IAAA,CAAAmV,OAAA,CAAAzhB,cAAA,EAAAiG,QAAA;gBACA;kBACAwb,OAAA,CAAAnV,IAAA,CAAAmV,OAAA,CAAAzhB,cAAA,EAAAiG,QAAA,EAAAkb,QAAA;gBACA;cACA,CACA;YAAA;cAXAS,aAAA,GAAAG,UAAA,CAAAld,CAAA;cAaA;cACA+c,aAAA,CAAA3T,OAAA,WAAA5I,MAAA,EAAAW,KAAA;gBACA,IAAAX,MAAA,CAAA5B,OAAA;kBACA;kBACA,IAAA2C,aAAA,GAAAf,MAAA,CAAA+c,YAAA,CAAA/b,WAAA,GAAAH,KAAA,MAAAC,GAAA;kBACA,IAAAG,SAAA;kBACA,IAAAC,SAAA;kBACA,IAAA4Z,SAAA;kBAEA,IAAAkC,cAAA;kBACA,IAAA/b,SAAA,CAAAG,QAAA,CAAAL,aAAA;oBACAic,cAAA;kBACA,WAAA9b,SAAA,CAAAE,QAAA,CAAAL,aAAA;oBACAic,cAAA;kBACA,WAAAlC,SAAA,CAAA1Z,QAAA,CAAAL,aAAA;oBACAic,cAAA;kBACA;kBAEA,IAAAxB,OAAA;oBACA5f,EAAA,EAAAyF,IAAA,CAAAC,GAAA,KAAAxE,IAAA,CAAAyE,MAAA,KAAAZ,KAAA;oBACA/G,IAAA,EAAAoG,MAAA,CAAA+c,YAAA;oBACAlhB,IAAA,EAAAmhB,cAAA;oBACAlhB,IAAA,EAAAkE,MAAA,CAAAlE,IAAA;oBACAC,UAAA,MAAAsF,IAAA,GAAAI,cAAA,GAAA7E,KAAA;oBACAZ,QAAA,EAAAghB,cAAA,4BAAAA,cAAA,4BAAArb,SAAA;oBACAD,UAAA,EAAAsb,cAAA,6BAAArb,SAAA;oBACAC,OAAA,EAAAob,cAAA,2BAAArb,SAAA;oBACA1F,GAAA,EAAA+D,MAAA,CAAA/D,GAAA;oBACA6F,WAAA,EAAA9B,MAAA,CAAAY,QAAA;oBACAjB,MAAA,EAAA4E;kBACA;;kBAEA;kBACA,QAAA6X,OAAA,CAAAriB,WAAA;oBACA;sBACAqiB,OAAA,CAAAhgB,OAAA,CAAA+T,OAAA,CAAAqL,OAAA;sBACA;oBACA;sBACAY,OAAA,CAAA/f,SAAA,CAAA8T,OAAA,CAAAqL,OAAA;sBACA;kBACA;gBACA;cACA;cAEAY,OAAA,CAAAje,QAAA,CAAAC,OAAA,6BAAAmB,MAAA,CAAAgd,aAAA,CAAAhc,MAAA,WAAA0c,CAAA;gBAAA,OAAAA,CAAA,CAAA7e,OAAA;cAAA,GAAApB,MAAA;cAAA0f,UAAA,CAAA/e,CAAA;cAAA;YAAA;cAGA;cACA2e,WAAA,CAAA1T,OAAA,WAAArC,IAAA;gBACA;;gBAEA;gBACA,IAAA2W,gBAAA,GAAAC,WAAA;kBACA,IAAAC,eAAA,GAAAhB,OAAA,CAAAzhB,cAAA,CAAA4L,IAAA,CAAA3M,IAAA;kBACA,IAAAwjB,eAAA;oBACAhB,OAAA,CAAAnV,IAAA,CAAAmV,OAAA,CAAAzhB,cAAA,EAAA4L,IAAA,CAAA3M,IAAA,EAAAkD,IAAA,CAAA8K,GAAA,CAAAwV,eAAA,GAAAtgB,IAAA,CAAAyE,MAAA;kBACA;oBACA8b,aAAA,CAAAH,gBAAA;kBACA;gBACA;cACA;;cAEA;cACAvP,UAAA;gBACA;gBACA2O,WAAA,CAAA1T,OAAA,WAAArC,IAAA,EAAA5F,KAAA;kBACA;kBACA,IAAAI,aAAA,GAAAwF,IAAA,CAAA3M,IAAA,CAAAoH,WAAA,GAAAH,KAAA,MAAAC,GAAA;kBACA,IAAAG,SAAA;kBACA,IAAAC,SAAA;kBACA,IAAA4Z,SAAA;kBAEA,IAAAkC,cAAA;kBACA,IAAA/b,SAAA,CAAAG,QAAA,CAAAL,aAAA;oBACAic,cAAA;kBACA,WAAA9b,SAAA,CAAAE,QAAA,CAAAL,aAAA;oBACAic,cAAA;kBACA,WAAAlC,SAAA,CAAA1Z,QAAA,CAAAL,aAAA;oBACAic,cAAA;kBACA;kBAEA,IAAAxB,OAAA;oBACA5f,EAAA,EAAAyF,IAAA,CAAAC,GAAA,KAAAxE,IAAA,CAAAyE,MAAA,KAAAZ,KAAA;oBACA/G,IAAA,EAAA2M,IAAA,CAAA3M,IAAA;oBACAiC,IAAA,EAAAmhB,cAAA;oBACAlhB,IAAA,EAAAyK,IAAA,CAAAzK,IAAA;oBACAC,UAAA,MAAAsF,IAAA,GAAAI,cAAA,GAAA7E,KAAA;oBACAZ,QAAA,EAAAghB,cAAA,4BAAAA,cAAA,4BAAArb,SAAA;oBACAD,UAAA,EAAAsb,cAAA,6BAAArb,SAAA;oBACAC,OAAA,EAAAob,cAAA,2BAAArb,SAAA;oBACAhC,MAAA,EAAA4E,UAAA;oBACAtI,GAAA,EAAAmH,GAAA,CAAAka,eAAA,CAAA/W,IAAA;kBACA;;kBAEA;kBACA,QAAA6V,OAAA,CAAAriB,WAAA;oBACA;sBACAqiB,OAAA,CAAAhgB,OAAA,CAAA+T,OAAA,CAAAqL,OAAA;sBACA;oBACA;sBACAY,OAAA,CAAA/f,SAAA,CAAA8T,OAAA,CAAAqL,OAAA;sBACA;kBACA;gBACA;gBAEAY,OAAA,CAAAje,QAAA,CAAAC,OAAA,6BAAAmB,MAAA,CAAAqR,SAAA;;gBAEA;gBACAwL,OAAA,CAAAzd,4BAAA;cACA;YAAA;cAGAyd,OAAA,CAAA5hB,SAAA;cACA4hB,OAAA,CAAA7hB,mBAAA;cACA6hB,OAAA,CAAA3hB,QAAA;cACA2hB,OAAA,CAAAzhB,cAAA;cAAA+hB,UAAA,CAAA/e,CAAA;cAAA;YAAA;cAAA+e,UAAA,CAAAxd,CAAA;cAAAud,IAAA,GAAAC,UAAA,CAAAld,CAAA;cAGA4c,OAAA,CAAA5hB,SAAA;cACA4hB,OAAA,CAAAje,QAAA,CAAAsB,KAAA,kCAAAF,MAAA,CAAAkd,IAAA,CAAAjZ,OAAA;cACAhF,OAAA,CAAAiB,KAAA,UAAAgd,IAAA;YAAA;cAAA,OAAAC,UAAA,CAAA5e,CAAA;UAAA;QAAA,GAAAue,SAAA;MAAA;IAEA;EAAA,8BAAAvO,eAEAC,KAAA;IACA,IAAAA,KAAA;IACA,IAAAC,CAAA;IACA,IAAAC,KAAA;IACA,IAAAC,CAAA,GAAApR,IAAA,CAAAkN,KAAA,CAAAlN,IAAA,CAAA2B,GAAA,CAAAsP,KAAA,IAAAjR,IAAA,CAAA2B,GAAA,CAAAuP,CAAA;IACA,OAAAG,UAAA,EAAAJ,KAAA,GAAAjR,IAAA,CAAAsR,GAAA,CAAAJ,CAAA,EAAAE,CAAA,GAAAG,OAAA,aAAAJ,KAAA,CAAAC,CAAA;EACA;AAEA", "ignoreList": []}]}