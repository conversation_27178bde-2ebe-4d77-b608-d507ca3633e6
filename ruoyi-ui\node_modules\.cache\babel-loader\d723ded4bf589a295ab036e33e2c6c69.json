{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\editor.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\editor.vue", "mtime": 1755003111667}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\babel.config.js", "mtime": 1744968028000}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753759483709}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "templateInfo", "id", "resolution", "fps", "batchDialogVisible", "batchCount", "addSceneDialogVisible", "sceneForm", "duration", "videoLibraryId", "videoLibraryDialogVisible", "selectedVideoLibrary", "activeCollapse", "videoSettings", "backgroundColor", "brightness", "contrast", "saturation", "audioSettings", "masterVolume", "bgmVolume", "sfxVolume", "textSettings", "fontFamily", "fontSize", "color", "<PERSON><PERSON><PERSON>", "selectedScene", "selectedTextSegment", "timelineDuration", "pixelsPerSecond", "currentTime", "scenes", "startTime", "maxDuration", "textSegment", "currentVideo", "sucaiList", "videoLibraries", "textLibrary", "fullText", "segments", "tracks", "type", "icon", "clips", "originalDuration", "computed", "timelineWidth", "created", "templateId", "$route", "query", "loadTemplate", "loadMaterialsFromUpPage", "initializeSceneVideos", "methods", "goBack", "$router", "go", "console", "log", "saveTemplate", "$message", "success", "showBatchDialog", "startBatchClip", "concat", "push", "path", "selectClip", "clip", "formatTime", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "getClipStyle", "left", "width", "onDragStart", "event", "dataTransfer", "setData", "JSON", "stringify", "onDrop", "track", "clipData", "parse", "getData", "startResize", "direction", "warning", "maxAllowedDuration", "startResizeWithLimit", "_this", "startX", "clientX", "startDuration", "onMouseMove", "e", "deltaX", "deltaSeconds", "newDuration", "max", "min", "round", "showDurationTip", "onMouseUp", "document", "removeEventListener", "hideDurationTip", "addEventListener", "percentage", "toFixed", "getPreviewResolution", "resolutionMap", "_this2", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "mockSucaiList", "w", "_context", "n", "size", "uploadTime", "url", "folder", "thumbnail", "createVideoLibrariesFromSucai", "error", "a", "_this3", "folderMap", "Map", "for<PERSON>ach", "item", "folderPath", "folderName", "split", "pop", "has", "set", "videos", "video", "parseDuration", "get", "Array", "from", "values", "durationStr", "parts", "length", "minutes", "parseInt", "_this4", "scene", "library", "find", "lib", "randomIndex", "random", "<PERSON><PERSON><PERSON><PERSON>", "recalculateSceneTimeline", "showAddSceneDialog", "confirmAddScene", "_this5", "lastScene", "newScene", "Date", "now", "selectScene", "selectVideoFromLibrary", "selectTextSegment", "index", "getSceneStyle", "getTextPreview", "text", "substring", "segmentText", "_this6", "_callee2", "mockSegments", "_context2", "info", "segment", "onDragStartScene", "onDropScene", "sceneData", "startResizeScene", "startResizeSceneWithLimit", "_this7", "showSceneDurationTip", "hideSceneDurationTip", "onTimelineClick", "rect", "currentTarget", "getBoundingClientRect", "clickX", "clickedTime", "previewAtTime", "time", "currentScene", "sceneProgress", "videoProgress", "onTimelineDragStart", "_this8", "preventDefault", "startClickX", "currentX", "newClickX", "newTime"], "sources": ["src/views/store/editor.vue"], "sourcesContent": ["<template>\n  <div class=\"video-editor\">\n    <!-- 头部工具栏 -->\n    <div class=\"editor-header\">\n      <div class=\"header-left\">\n        <el-button icon=\"el-icon-arrow-left\" @click=\"goBack\">返回</el-button>\n        <h3 class=\"template-title\">{{ templateInfo.name }}</h3>\n      </div>\n      <div class=\"header-right\">\n        <el-button @click=\"saveTemplate\">保存</el-button>\n        <el-button type=\"primary\" @click=\"showBatchDialog\">开始剪辑</el-button>\n      </div>\n    </div>\n\n    <!-- 主编辑区域 -->\n    <div class=\"editor-main\">\n      <!-- 预览面板 -->\n      <div class=\"preview-panel\">\n        <div class=\"preview-container\">\n          <div class=\"video-preview\">\n            <div class=\"preview-frame\">\n              <!-- 显示选中场景的视频 -->\n              <div v-if=\"selectedScene && selectedScene.currentVideo\" class=\"scene-preview\">\n                <img :src=\"selectedScene.currentVideo.thumbnail\" :alt=\"selectedScene.name\" />\n                <div class=\"scene-overlay\">\n                  <div class=\"scene-title\">{{ selectedScene.name }}</div>\n                  <div class=\"scene-video-name\">{{ selectedScene.currentVideo.name }}</div>\n                  <div class=\"scene-time-info\">\n                    <span>当前时间: {{ formatTime(currentTime) }}</span>\n                    <span>场景时间: {{ formatTime(currentTime - selectedScene.startTime) }}</span>\n                  </div>\n                  <div class=\"scene-text-preview\">{{ selectedScene.textSegment }}</div>\n                </div>\n              </div>\n\n              <!-- 默认预览占位符 -->\n              <div v-else class=\"preview-placeholder\">\n                <i class=\"el-icon-video-play\"></i>\n                <p>视频预览</p>\n                <p class=\"preview-info\">{{ getPreviewResolution() }} • {{ templateInfo.fps }}fps</p>\n                <div class=\"current-time-display\">\n                  <p>当前时间: {{ formatTime(currentTime) }}</p>\n                </div>\n                <div class=\"preview-tips\">\n                  <p>9:16 竖屏比例</p>\n                  <p>适配短视频平台</p>\n                  <p>点击时间轴查看预览</p>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div class=\"preview-controls\">\n            <el-button-group>\n              <el-button icon=\"el-icon-video-play\" size=\"small\">播放</el-button>\n              <el-button icon=\"el-icon-video-pause\" size=\"small\">暂停</el-button>\n              <el-button icon=\"el-icon-refresh-left\" size=\"small\">重置</el-button>\n            </el-button-group>\n            <div class=\"time-display\">00:00 / 02:30</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 属性设置面板 -->\n      <div class=\"properties-panel\">\n        <div class=\"panel-header\">\n          <h4>属性设置</h4>\n        </div>\n        <div class=\"panel-content\">\n          <el-collapse v-model=\"activeCollapse\">\n            <el-collapse-item title=\"视频设置\" name=\"video\">\n              <el-form label-width=\"80px\" size=\"small\">\n                <el-form-item label=\"背景色\">\n                  <el-color-picker v-model=\"videoSettings.backgroundColor\"></el-color-picker>\n                </el-form-item>\n                <el-form-item label=\"亮度\">\n                  <el-slider v-model=\"videoSettings.brightness\" :min=\"-100\" :max=\"100\"></el-slider>\n                </el-form-item>\n                <el-form-item label=\"对比度\">\n                  <el-slider v-model=\"videoSettings.contrast\" :min=\"-100\" :max=\"100\"></el-slider>\n                </el-form-item>\n                <el-form-item label=\"饱和度\">\n                  <el-slider v-model=\"videoSettings.saturation\" :min=\"-100\" :max=\"100\"></el-slider>\n                </el-form-item>\n              </el-form>\n            </el-collapse-item>\n            \n            <el-collapse-item title=\"音频设置\" name=\"audio\">\n              <el-form label-width=\"80px\" size=\"small\">\n                <el-form-item label=\"主音量\">\n                  <el-slider v-model=\"audioSettings.masterVolume\" :max=\"100\"></el-slider>\n                </el-form-item>\n                <el-form-item label=\"背景音乐\">\n                  <el-slider v-model=\"audioSettings.bgmVolume\" :max=\"100\"></el-slider>\n                </el-form-item>\n                <el-form-item label=\"音效\">\n                  <el-slider v-model=\"audioSettings.sfxVolume\" :max=\"100\"></el-slider>\n                </el-form-item>\n              </el-form>\n            </el-collapse-item>\n            \n            <el-collapse-item title=\"文字设置\" name=\"text\">\n              <el-form label-width=\"80px\" size=\"small\">\n                <el-form-item label=\"字体\">\n                  <el-select v-model=\"textSettings.fontFamily\" size=\"small\">\n                    <el-option label=\"微软雅黑\" value=\"Microsoft YaHei\"></el-option>\n                    <el-option label=\"宋体\" value=\"SimSun\"></el-option>\n                    <el-option label=\"黑体\" value=\"SimHei\"></el-option>\n                  </el-select>\n                </el-form-item>\n                <el-form-item label=\"字号\">\n                  <el-input-number v-model=\"textSettings.fontSize\" :min=\"12\" :max=\"72\" size=\"small\"></el-input-number>\n                </el-form-item>\n                <el-form-item label=\"颜色\">\n                  <el-color-picker v-model=\"textSettings.color\"></el-color-picker>\n                </el-form-item>\n              </el-form>\n            </el-collapse-item>\n          </el-collapse>\n        </div>\n      </div>\n    </div>\n\n    <!-- 时间轴区域 -->\n    <div class=\"timeline-area\">\n      <div class=\"timeline-header\">\n        <h4>时间轴</h4>\n        <div class=\"timeline-controls\">\n          <el-button-group size=\"small\">\n            <el-button icon=\"el-icon-zoom-in\">放大</el-button>\n            <el-button icon=\"el-icon-zoom-out\">缩小</el-button>\n          </el-button-group>\n        </div>\n      </div>\n      \n      <div class=\"timeline-container\">\n        <!-- 时间刻度 -->\n        <div class=\"time-ruler-row\">\n          <div class=\"time-ruler-label\"></div>\n          <div class=\"time-ruler\" @click=\"onTimelineClick\" @mousedown=\"onTimelineDragStart\">\n            <div class=\"time-marks-container\" :style=\"{ width: timelineWidth + 'px' }\">\n              <!-- 每15秒一个主刻度 -->\n              <div class=\"time-mark\" v-for=\"i in 5\" :key=\"i\" :style=\"{left: (i-1) * 150 + 'px'}\">\n                {{ formatTime((i-1) * 15) }}\n              </div>\n              <!-- 每5秒一个小刻度 -->\n              <div class=\"time-tick\" v-for=\"i in 11\" :key=\"'tick-' + i\" :style=\"{left: (i * 50 + 25) + 'px'}\"></div>\n              <!-- 播放指针 -->\n              <div class=\"playhead\" :style=\"{left: currentTime * pixelsPerSecond + 'px'}\"></div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 轨道区域 -->\n        <div class=\"tracks-container\">\n          <!-- 视频场景轨道 -->\n          <div class=\"track-row video\">\n            <div class=\"track-label\">\n              <i class=\"el-icon-video-camera\"></i>\n              <span>视频场景</span>\n              <el-button size=\"mini\" type=\"primary\" @click=\"showAddSceneDialog\" style=\"margin-left: 8px\">\n                <i class=\"el-icon-plus\"></i>\n              </el-button>\n            </div>\n            <div class=\"track-content\" @drop=\"onDropScene($event)\" @dragover.prevent\">\n              <div class=\"track-timeline\" :style=\"{ width: timelineWidth + 'px' }\">\n                <div\n                  v-for=\"scene in scenes\"\n                  :key=\"scene.id\"\n                  class=\"scene-clip\"\n                  :style=\"getSceneStyle(scene)\"\n                  @click=\"selectScene(scene)\"\n                  :class=\"{ active: selectedScene && selectedScene.id === scene.id }\"\n                  draggable=\"true\"\n                  @dragstart=\"onDragStartScene($event, scene)\"\n                >\n                  <div class=\"scene-content\">\n                    <span class=\"scene-name\">{{ scene.name }}</span>\n                    <div class=\"scene-duration\">\n                      {{ formatTime(scene.duration) }}\n                      <span v-if=\"scene.maxDuration\" class=\"max-duration\">\n                        / {{ formatTime(scene.maxDuration) }}\n                      </span>\n                    </div>\n                    <div class=\"scene-video-info\" v-if=\"scene.currentVideo\">\n                      {{ scene.currentVideo.name }}\n                    </div>\n                  </div>\n                  <!-- 调整手柄 -->\n                  <div class=\"resize-handle left\" @mousedown=\"startResizeScene($event, scene, 'left')\"></div>\n                  <div class=\"resize-handle right\" @mousedown=\"startResizeScene($event, scene, 'right')\"></div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 文案轨道 -->\n          <div class=\"track-row text\">\n            <div class=\"track-label\">\n              <i class=\"el-icon-edit\"></i>\n              <span>文案</span>\n              <el-button size=\"mini\" type=\"success\" @click=\"segmentText\" style=\"margin-left: 8px;\">\n                分段\n              </el-button>\n            </div>\n            <div class=\"track-content\">\n              <div class=\"track-timeline\" :style=\"{ width: timelineWidth + 'px' }\">\n                <div\n                  v-for=\"(scene, index) in scenes\"\n                  :key=\"'text-' + scene.id\"\n                  class=\"text-clip\"\n                  :style=\"getSceneStyle(scene)\"\n                  @click=\"selectTextSegment(scene, index)\"\n                  :class=\"{ active: selectedTextSegment && selectedTextSegment.id === scene.id }\"\n                >\n                  <div class=\"text-content\">\n                    <span class=\"text-preview\">{{ getTextPreview(scene.textSegment) }}</span>\n                    <div class=\"text-duration\">{{ formatTime(scene.duration) }}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 其他轨道 -->\n          <div\n            v-for=\"track in tracks.filter(t => t.type !== 'video' && t.type !== 'text')\"\n            :key=\"track.id\"\n            class=\"track-row\"\n            :class=\"track.type\"\n          >\n            <div class=\"track-label\">\n              <i :class=\"track.icon\"></i>\n              <span>{{ track.name }}</span>\n            </div>\n            <div class=\"track-content\" @drop=\"onDrop($event, track)\" @dragover.prevent>\n              <div class=\"track-timeline\" :style=\"{ width: timelineWidth + 'px' }\">\n                <div\n                  v-for=\"clip in track.clips\"\n                  :key=\"clip.id\"\n                  class=\"clip\"\n                  :style=\"getClipStyle(clip)\"\n                  @click=\"selectClip(clip)\"\n                  :class=\"{ active: selectedClip && selectedClip.id === clip.id }\"\n                  draggable=\"true\"\n                  @dragstart=\"onDragStart($event, clip)\"\n                >\n                  <div class=\"clip-content\">\n                    <span class=\"clip-name\">{{ clip.name }}</span>\n                    <div class=\"clip-duration\">\n                      {{ formatTime(clip.duration) }}\n                      <span v-if=\"clip.maxDuration || clip.originalDuration\" class=\"max-duration\">\n                        / {{ formatTime(clip.maxDuration || clip.originalDuration) }}\n                      </span>\n                    </div>\n                  </div>\n                  <!-- 调整手柄 -->\n                  <div class=\"resize-handle left\" @mousedown=\"startResize($event, clip, 'left')\"></div>\n                  <div class=\"resize-handle right\" @mousedown=\"startResize($event, clip, 'right')\"></div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 对话框区域 -->\n    <!-- 批量剪辑对话框 -->\n    <el-dialog\n      title=\"开始批量剪辑\"\n      :visible.sync=\"batchDialogVisible\"\n      width=\"400px\"\n    >\n      <div class=\"batch-config\">\n        <h4>{{ templateInfo.name }}</h4>\n        <p>请选择要生成的视频数量：</p>\n        <el-input-number\n          v-model=\"batchCount\"\n          :min=\"1\"\n          :max=\"50\"\n          label=\"生成数量\"\n        ></el-input-number>\n        <p class=\"batch-tip\">最多可生成50条视频</p>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"batchDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"startBatchClip\">开始剪辑</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 添加场景对话框 -->\n    <el-dialog\n      title=\"添加视频场景\"\n      :visible.sync=\"addSceneDialogVisible\"\n      width=\"500px\"\n    >\n      <el-form :model=\"sceneForm\" label-width=\"100px\">\n        <el-form-item label=\"场景名称\">\n          <el-input v-model=\"sceneForm.name\" placeholder=\"请输入场景名称\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"场景时长\">\n          <el-input-number v-model=\"sceneForm.duration\" :min=\"1\" :max=\"60\" label=\"秒\"></el-input-number>\n          <span style=\"margin-left: 8px; color: #909399;\">秒</span>\n        </el-form-item>\n        <el-form-item label=\"视频库\">\n          <el-select v-model=\"sceneForm.videoLibraryId\" placeholder=\"选择视频库\">\n            <el-option\n              v-for=\"lib in videoLibraries\"\n              :key=\"lib.id\"\n              :label=\"lib.name\"\n              :value=\"lib.id\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"addSceneDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmAddScene\">确定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 视频库选择对话框 -->\n    <el-dialog\n      title=\"选择视频库\"\n      :visible.sync=\"videoLibraryDialogVisible\"\n      width=\"800px\"\n    >\n      <div class=\"video-library-content\" v-if=\"selectedVideoLibrary\">\n        <h4>{{ selectedVideoLibrary.name }}</h4>\n        <div class=\"video-grid\">\n          <div\n            v-for=\"video in selectedVideoLibrary.videos\"\n            :key=\"video.id\"\n            class=\"video-item\"\n            @click=\"selectVideoFromLibrary(video)\"\n          >\n            <div class=\"video-thumbnail\">\n              <img :src=\"video.thumbnail\" :alt=\"video.name\" />\n              <div class=\"video-duration-badge\">{{ formatTime(video.duration) }}</div>\n            </div>\n            <div class=\"video-info\">\n              <p class=\"video-name\">{{ video.name }}</p>\n              <p class=\"video-duration-text\">时长: {{ formatTime(video.duration) }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"videoLibraryDialogVisible = false\">关闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'VideoEditor',\n  data() {\n    return {\n      // 模板信息\n      templateInfo: {\n        id: '',\n        name: '视频模板',\n        resolution: '1080x1920',\n        fps: '30'\n      },\n      \n      // 批量剪辑\n      batchDialogVisible: false,\n      batchCount: 1,\n\n      // 场景管理对话框\n      addSceneDialogVisible: false,\n      sceneForm: {\n        name: '',\n        duration: 10,\n        videoLibraryId: ''\n      },\n\n      // 视频库选择对话框\n      videoLibraryDialogVisible: false,\n      selectedVideoLibrary: null,\n\n      // 属性面板\n      activeCollapse: ['video'],\n\n      // 设置项\n      videoSettings: {\n        backgroundColor: '#000000',\n        brightness: 0,\n        contrast: 0,\n        saturation: 0\n      },\n\n      audioSettings: {\n        masterVolume: 80,\n        bgmVolume: 60,\n        sfxVolume: 70\n      },\n\n      textSettings: {\n        fontFamily: 'Microsoft YaHei',\n        fontSize: 24,\n        color: '#FFFFFF'\n      },\n\n      // 选中的元素\n      selectedClip: null,\n      selectedScene: null,\n      selectedTextSegment: null,\n\n      // 时间轴设置\n      timelineDuration: 60, // 时间轴总长度60秒\n      pixelsPerSecond: 10, // 每秒10像素，60秒 = 600像素（稀疏显示）\n      currentTime: 0, // 当前播放时间（秒）\n      \n      // 场景列表\n      scenes: [\n        {\n          id: 'scene1',\n          name: '开场场景',\n          startTime: 0,\n          duration: 8,\n          maxDuration: 12, // 视频素材的最大时长\n          videoLibraryId: 'lib_开场',\n          textSegment: '欢迎来到我们的产品展示，今天为大家介绍一款革命性的产品。',\n          currentVideo: null\n        },\n        {\n          id: 'scene2',\n          name: '产品介绍',\n          startTime: 8,\n          duration: 10,\n          maxDuration: 15, // 视频素材的最大时长\n          videoLibraryId: 'lib_产品介绍',\n          textSegment: '这款产品具有独特的设计理念和先进的技术，能够为用户带来全新的体验。',\n          currentVideo: null\n        },\n        {\n          id: 'scene3',\n          name: '功能展示',\n          startTime: 18,\n          duration: 9,\n          maxDuration: 20, // 视频素材的最大时长\n          videoLibraryId: 'lib_功能展示',\n          textSegment: '让我们来看看它的核心功能和使用方法。',\n          currentVideo: null\n        }\n      ],\n\n      // 从up页面获取的素材库（视频库）\n      sucaiList: [], // 从up页面的素材列表获取\n\n      // 视频库分类（基于文件夹）\n      videoLibraries: [],\n\n      // AI文案库\n      textLibrary: {\n        id: 'text1',\n        name: 'AI剪辑文案库',\n        fullText: '欢迎来到我们的产品展示，今天为大家介绍一款革命性的产品。这款产品具有独特的设计理念和先进的技术，能够为用户带来全新的体验。让我们来看看它的核心功能和使用方法。通过简单的操作，您就能体验到前所未有的便利性。',\n        segments: [] // 将通过阿里云接口分段\n      },\n\n      // 轨道数据（简化版）\n      tracks: [\n        {\n          id: 'video',\n          name: '视频场景',\n          type: 'video',\n          icon: 'el-icon-video-camera'\n        },\n        {\n          id: 'text',\n          name: '文案',\n          type: 'text',\n          icon: 'el-icon-edit'\n        },\n        {\n          id: 'music',\n          name: '背景音乐',\n          type: 'audio',\n          icon: 'el-icon-headset',\n          clips: [\n            {\n              id: 'm1',\n              name: '背景音乐.mp3',\n              startTime: 0,\n              duration: 45, // 当前使用时长\n              maxDuration: 180, // 音频文件的实际时长（3分钟）\n              originalDuration: 180, // 原始文件时长\n              type: 'audio'\n            }\n          ]\n        },\n        {\n          id: 'sound',\n          name: '音效',\n          type: 'audio',\n          icon: 'el-icon-bell',\n          clips: [\n            {\n              id: 's1',\n              name: '点击音效.wav',\n              startTime: 8,\n              duration: 1,\n              maxDuration: 2, // 音效文件时长\n              originalDuration: 2,\n              type: 'audio'\n            },\n            {\n              id: 's2',\n              name: '转场音效.wav',\n              startTime: 18,\n              duration: 2,\n              maxDuration: 3,\n              originalDuration: 3,\n              type: 'audio'\n            }\n          ]\n        }\n      ]\n    }\n  },\n\n  computed: {\n    // 时间轴总宽度\n    timelineWidth() {\n      return this.timelineDuration * this.pixelsPerSecond\n    }\n  },\n\n  created() {\n    // 获取模板ID\n    const templateId = this.$route.query.templateId\n    if (templateId) {\n      this.loadTemplate(templateId)\n    }\n\n    // 加载素材库\n    this.loadMaterialsFromUpPage()\n\n    // 初始化场景视频\n    this.initializeSceneVideos()\n  },\n  \n  methods: {\n    // 返回上一页\n    goBack() {\n      this.$router.go(-1)\n    },\n    \n    // 加载模板\n    loadTemplate(templateId) {\n      // TODO: 从API加载模板数据\n      this.templateInfo.id = templateId\n      console.log('加载模板:', templateId)\n    },\n    \n    // 保存模板\n    saveTemplate() {\n      this.$message.success('模板保存成功')\n      // TODO: 调用保存API\n    },\n    \n    // 显示批量剪辑对话框\n    showBatchDialog() {\n      this.batchDialogVisible = true\n    },\n    \n    // 开始批量剪辑\n    startBatchClip() {\n      this.$message.success(`开始生成 ${this.batchCount} 条视频`)\n      this.batchDialogVisible = false\n      \n      // 跳转到进度页面\n      this.$router.push({\n        path: '/storer/progress',\n        query: { templateId: this.templateInfo.id }\n      })\n    },\n    \n    // 选择片段\n    selectClip(clip) {\n      this.selectedClip = clip\n    },\n    \n    // 格式化时间\n    formatTime(seconds) {\n      const mins = Math.floor(seconds / 60)\n      const secs = seconds % 60\n      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`\n    },\n    \n    // 获取片段样式\n    getClipStyle(clip) {\n      return {\n        left: clip.startTime * this.pixelsPerSecond + 'px',\n        width: clip.duration * this.pixelsPerSecond + 'px'\n      }\n    },\n    \n    // 拖拽开始\n    onDragStart(event, clip) {\n      event.dataTransfer.setData('text/plain', JSON.stringify(clip))\n    },\n    \n    // 拖拽放置\n    onDrop(event, track) {\n      const clipData = JSON.parse(event.dataTransfer.getData('text/plain'))\n      console.log('拖拽到轨道:', track.name, clipData)\n      // TODO: 实现拖拽逻辑\n    },\n    \n    // 开始调整大小\n    startResize(event, clip, direction) {\n      console.log('调整大小:', clip.name, direction)\n\n      // 检查是否可以调整\n      if (!clip.maxDuration && !clip.originalDuration) {\n        this.$message.warning('该素材没有时长限制信息')\n        return\n      }\n\n      const maxAllowedDuration = clip.maxDuration || clip.originalDuration\n\n      if (direction === 'right') {\n        // 向右拖拽（增加时长）\n        if (clip.duration >= maxAllowedDuration) {\n          this.$message.warning(`素材最大时长为 ${this.formatTime(maxAllowedDuration)}，无法继续延长`)\n          return\n        }\n      }\n\n      // TODO: 实现具体的拖拽调整逻辑\n      this.startResizeWithLimit(event, clip, direction, maxAllowedDuration)\n    },\n\n    // 带时长限制的调整大小\n    startResizeWithLimit(event, clip, direction, maxDuration) {\n      const startX = event.clientX\n      const startDuration = clip.duration\n      const pixelsPerSecond = this.pixelsPerSecond\n\n      const onMouseMove = (e) => {\n        const deltaX = e.clientX - startX\n        const deltaSeconds = deltaX / pixelsPerSecond\n\n        let newDuration = startDuration\n\n        if (direction === 'right') {\n          newDuration = Math.max(1, Math.min(maxDuration, startDuration + deltaSeconds))\n        } else if (direction === 'left') {\n          newDuration = Math.max(1, Math.min(maxDuration, startDuration - deltaSeconds))\n        }\n\n        clip.duration = Math.round(newDuration)\n\n        // 显示时长提示\n        this.showDurationTip(clip, maxDuration)\n      }\n\n      const onMouseUp = () => {\n        document.removeEventListener('mousemove', onMouseMove)\n        document.removeEventListener('mouseup', onMouseUp)\n        this.hideDurationTip()\n      }\n\n      document.addEventListener('mousemove', onMouseMove)\n      document.addEventListener('mouseup', onMouseUp)\n    },\n\n    // 显示时长提示\n    showDurationTip(clip, maxDuration) {\n      const percentage = (clip.duration / maxDuration * 100).toFixed(1)\n      console.log(`${clip.name}: ${this.formatTime(clip.duration)} / ${this.formatTime(maxDuration)} (${percentage}%)`)\n    },\n\n    // 隐藏时长提示\n    hideDurationTip() {\n      // 清除提示\n    },\n\n    // 获取预览分辨率显示文本\n    getPreviewResolution() {\n      // 根据模板分辨率返回对应的抖音尺寸\n      const resolutionMap = {\n        '1920x1080': '1080x1920',\n        '1280x720': '720x1280',\n        '1080x1920': '1080x1920',\n        '720x1280': '720x1280'\n      }\n      return resolutionMap[this.templateInfo.resolution] || '1080x1920'\n    },\n\n    // 从up页面加载素材库\n    async loadMaterialsFromUpPage() {\n      try {\n        // 模拟从up页面获取素材数据\n        // 实际应用中，这里应该调用API或从localStorage获取\n        const mockSucaiList = [\n          {\n            id: 'sucai1',\n            name: '开场视频1.mp4',\n            type: 'video',\n            size: 1024000,\n            uploadTime: '2024-01-01 12:00:00',\n            duration: '00:12',\n            url: 'https://www.w3schools.com/html/mov_bbb.mp4',\n            folder: 'asucai/admin/开场',\n            thumbnail: 'https://via.placeholder.com/160x90/4A90E2/FFFFFF?text=Opening1'\n          },\n          {\n            id: 'sucai2',\n            name: '开场视频2.mp4',\n            type: 'video',\n            size: 1024000,\n            uploadTime: '2024-01-01 12:00:00',\n            duration: '00:10',\n            url: 'https://www.w3schools.com/html/mov_bbb.mp4',\n            folder: 'asucai/admin/开场',\n            thumbnail: 'https://via.placeholder.com/160x90/4A90E2/FFFFFF?text=Opening2'\n          },\n          {\n            id: 'sucai3',\n            name: '产品介绍1.mp4',\n            type: 'video',\n            size: 1024000,\n            uploadTime: '2024-01-01 12:00:00',\n            duration: '00:18',\n            url: 'https://www.w3schools.com/html/mov_bbb.mp4',\n            folder: 'asucai/admin/产品介绍',\n            thumbnail: 'https://via.placeholder.com/160x90/50C878/FFFFFF?text=Product1'\n          },\n          {\n            id: 'sucai4',\n            name: '产品介绍2.mp4',\n            type: 'video',\n            size: 1024000,\n            uploadTime: '2024-01-01 12:00:00',\n            duration: '00:22',\n            url: 'https://www.w3schools.com/html/mov_bbb.mp4',\n            folder: 'asucai/admin/产品介绍',\n            thumbnail: 'https://via.placeholder.com/160x90/50C878/FFFFFF?text=Product2'\n          },\n          {\n            id: 'sucai5',\n            name: '功能展示1.mp4',\n            type: 'video',\n            size: 1024000,\n            uploadTime: '2024-01-01 12:00:00',\n            duration: '00:25',\n            url: 'https://www.w3schools.com/html/mov_bbb.mp4',\n            folder: 'asucai/admin/功能展示',\n            thumbnail: 'https://via.placeholder.com/160x90/FF6B6B/FFFFFF?text=Feature1'\n          }\n        ]\n\n        this.sucaiList = mockSucaiList\n\n        // 根据文件夹分组创建视频库\n        this.createVideoLibrariesFromSucai()\n\n        console.log('素材库加载完成:', this.videoLibraries)\n\n      } catch (error) {\n        console.error('加载素材库失败:', error)\n        this.$message.error('加载素材库失败')\n      }\n    },\n\n    // 根据素材列表创建视频库\n    createVideoLibrariesFromSucai() {\n      const folderMap = new Map()\n\n      // 按文件夹分组\n      this.sucaiList.forEach(item => {\n        if (item.type === 'video') {\n          // 提取文件夹名称\n          const folderPath = item.folder || 'asucai/admin/总'\n          const folderName = folderPath.split('/').pop() || '总'\n\n          if (!folderMap.has(folderName)) {\n            folderMap.set(folderName, {\n              id: 'lib_' + folderName,\n              name: folderName + '视频库',\n              videos: []\n            })\n          }\n\n          // 转换为视频库格式\n          const video = {\n            id: item.id,\n            name: item.name,\n            url: item.url,\n            duration: this.parseDuration(item.duration),\n            thumbnail: item.thumbnail || 'https://via.placeholder.com/160x90/999/FFFFFF?text=Video'\n          }\n\n          folderMap.get(folderName).videos.push(video)\n        }\n      })\n\n      // 转换为数组\n      this.videoLibraries = Array.from(folderMap.values())\n    },\n\n    // 解析时长字符串为秒数\n    parseDuration(durationStr) {\n      if (!durationStr) return 10\n\n      const parts = durationStr.split(':')\n      if (parts.length === 2) {\n        const minutes = parseInt(parts[0]) || 0\n        const seconds = parseInt(parts[1]) || 0\n        return minutes * 60 + seconds\n      }\n\n      return 10 // 默认10秒\n    },\n\n    // 初始化场景视频（为每个场景随机选择一个视频）\n    initializeSceneVideos() {\n      this.scenes.forEach(scene => {\n        const library = this.videoLibraries.find(lib => lib.id === scene.videoLibraryId)\n        if (library && library.videos.length > 0) {\n          // 随机选择一个视频\n          const randomIndex = Math.floor(Math.random() * library.videos.length)\n          const selectedVideo = library.videos[randomIndex]\n          scene.currentVideo = selectedVideo\n\n          // 设置场景的最大时长为视频时长\n          scene.maxDuration = selectedVideo.duration\n\n          // 如果当前场景时长超过视频时长，调整为视频时长\n          if (scene.duration > selectedVideo.duration) {\n            scene.duration = selectedVideo.duration\n          }\n        }\n      })\n\n      // 重新计算时间轴\n      this.recalculateSceneTimeline()\n    },\n\n    // 显示添加场景对话框\n    showAddSceneDialog() {\n      this.sceneForm = {\n        name: '',\n        duration: 10,\n        videoLibraryId: ''\n      }\n      this.addSceneDialogVisible = true\n    },\n\n    // 确认添加场景\n    confirmAddScene() {\n      if (!this.sceneForm.name || !this.sceneForm.videoLibraryId) {\n        this.$message.error('请填写完整的场景信息')\n        return\n      }\n\n      // 计算新场景的开始时间\n      const lastScene = this.scenes[this.scenes.length - 1]\n      const startTime = lastScene ? lastScene.startTime + lastScene.duration : 0\n\n      const newScene = {\n        id: 'scene' + Date.now(),\n        name: this.sceneForm.name,\n        startTime: startTime,\n        duration: this.sceneForm.duration,\n        videoLibraryId: this.sceneForm.videoLibraryId,\n        textSegment: '新场景的文案内容...',\n        currentVideo: null\n      }\n\n      // 为新场景随机选择视频\n      const library = this.videoLibraries.find(lib => lib.id === this.sceneForm.videoLibraryId)\n      if (library && library.videos.length > 0) {\n        const randomIndex = Math.floor(Math.random() * library.videos.length)\n        newScene.currentVideo = library.videos[randomIndex]\n      }\n\n      this.scenes.push(newScene)\n      this.addSceneDialogVisible = false\n      this.$message.success('场景添加成功')\n    },\n\n    // 选择场景\n    selectScene(scene) {\n      this.selectedScene = scene\n      this.selectedClip = null\n      this.selectedTextSegment = null\n\n      // 显示视频库选择对话框\n      const library = this.videoLibraries.find(lib => lib.id === scene.videoLibraryId)\n      if (library) {\n        this.selectedVideoLibrary = library\n        this.videoLibraryDialogVisible = true\n      }\n    },\n\n    // 从视频库选择视频\n    selectVideoFromLibrary(video) {\n      if (this.selectedScene) {\n        this.selectedScene.currentVideo = video\n        // 更新场景的最大时长限制\n        this.selectedScene.maxDuration = video.duration\n\n        // 如果当前时长超过新视频的时长，则调整为新视频的时长\n        if (this.selectedScene.duration > video.duration) {\n          this.selectedScene.duration = video.duration\n          this.$message.warning(`场景时长已调整为视频时长: ${this.formatTime(video.duration)}`)\n        }\n\n        this.$message.success(`已为场景\"${this.selectedScene.name}\"选择视频: ${video.name} (${this.formatTime(video.duration)})`)\n      }\n      this.videoLibraryDialogVisible = false\n    },\n\n    // 选择文案片段\n    selectTextSegment(scene, index) {\n      this.selectedTextSegment = scene\n      this.selectedClip = null\n      this.selectedScene = null\n    },\n\n    // 获取场景样式\n    getSceneStyle(scene) {\n      return {\n        left: scene.startTime * this.pixelsPerSecond + 'px',\n        width: scene.duration * this.pixelsPerSecond + 'px'\n      }\n    },\n\n    // 获取文案预览\n    getTextPreview(text) {\n      return text.length > 20 ? text.substring(0, 20) + '...' : text\n    },\n\n    // 文案分段（调用阿里云接口）\n    async segmentText() {\n      try {\n        this.$message.info('正在调用阿里云接口进行文案分段...')\n\n        // TODO: 调用阿里云文本分段接口\n        // const segments = await this.callAliCloudTextSegmentation(this.textLibrary.fullText)\n\n        // 模拟分段结果\n        const mockSegments = [\n          '欢迎来到我们的产品展示，今天为大家介绍一款革命性的产品。',\n          '这款产品具有独特的设计理念和先进的技术，能够为用户带来全新的体验。',\n          '让我们来看看它的核心功能和使用方法。',\n          '通过简单的操作，您就能体验到前所未有的便利性。'\n        ]\n\n        // 更新场景的文案片段\n        mockSegments.forEach((segment, index) => {\n          if (this.scenes[index]) {\n            this.scenes[index].textSegment = segment\n          }\n        })\n\n        this.$message.success('文案分段完成')\n\n      } catch (error) {\n        console.error('文案分段失败:', error)\n        this.$message.error('文案分段失败，请重试')\n      }\n    },\n\n    // 场景拖拽开始\n    onDragStartScene(event, scene) {\n      event.dataTransfer.setData('text/plain', JSON.stringify(scene))\n    },\n\n    // 场景拖拽放置\n    onDropScene(event) {\n      const sceneData = JSON.parse(event.dataTransfer.getData('text/plain'))\n      console.log('场景拖拽:', sceneData)\n      // TODO: 实现场景拖拽逻辑\n    },\n\n    // 开始调整场景大小\n    startResizeScene(event, scene, direction) {\n      console.log('调整场景大小:', scene.name, direction)\n\n      // 检查场景是否有视频和时长限制\n      if (!scene.currentVideo || !scene.maxDuration) {\n        this.$message.warning('请先为场景选择视频')\n        return\n      }\n\n      const maxAllowedDuration = scene.maxDuration\n\n      if (direction === 'right') {\n        // 向右拖拽（增加时长）\n        if (scene.duration >= maxAllowedDuration) {\n          this.$message.warning(`场景最大时长为 ${this.formatTime(maxAllowedDuration)}（视频时长限制）`)\n          return\n        }\n      }\n\n      // 实现场景大小调整\n      this.startResizeSceneWithLimit(event, scene, direction, maxAllowedDuration)\n    },\n\n    // 带时长限制的场景调整\n    startResizeSceneWithLimit(event, scene, direction, maxDuration) {\n      const startX = event.clientX\n      const startDuration = scene.duration\n      const pixelsPerSecond = this.pixelsPerSecond\n\n      const onMouseMove = (e) => {\n        const deltaX = e.clientX - startX\n        const deltaSeconds = deltaX / pixelsPerSecond\n\n        let newDuration = startDuration\n\n        if (direction === 'right') {\n          newDuration = Math.max(1, Math.min(maxDuration, startDuration + deltaSeconds))\n        } else if (direction === 'left') {\n          newDuration = Math.max(1, Math.min(maxDuration, startDuration - deltaSeconds))\n        }\n\n        scene.duration = Math.round(newDuration)\n\n        // 显示场景时长提示\n        this.showSceneDurationTip(scene, maxDuration)\n      }\n\n      const onMouseUp = () => {\n        document.removeEventListener('mousemove', onMouseMove)\n        document.removeEventListener('mouseup', onMouseUp)\n        this.hideSceneDurationTip()\n\n        // 调整完成后重新计算后续场景的开始时间\n        this.recalculateSceneTimeline()\n      }\n\n      document.addEventListener('mousemove', onMouseMove)\n      document.addEventListener('mouseup', onMouseUp)\n    },\n\n    // 显示场景时长提示\n    showSceneDurationTip(scene, maxDuration) {\n      const percentage = (scene.duration / maxDuration * 100).toFixed(1)\n      console.log(`${scene.name}: ${this.formatTime(scene.duration)} / ${this.formatTime(maxDuration)} (${percentage}%)`)\n    },\n\n    // 隐藏场景时长提示\n    hideSceneDurationTip() {\n      // 清除提示\n    },\n\n    // 重新计算场景时间轴\n    recalculateSceneTimeline() {\n      let currentTime = 0\n      this.scenes.forEach(scene => {\n        scene.startTime = currentTime\n        currentTime += scene.duration\n      })\n      console.log('场景时间轴已重新计算')\n    },\n\n    // 点击时间轴\n    onTimelineClick(event) {\n      const rect = event.currentTarget.getBoundingClientRect()\n      const clickX = event.clientX - rect.left\n      const clickedTime = Math.floor(clickX / this.pixelsPerSecond)\n\n      // 限制在0-60秒范围内\n      this.currentTime = Math.max(0, Math.min(60, clickedTime))\n\n      console.log(`点击时间轴: ${this.currentTime}秒`)\n\n      // 根据当前时间找到对应的场景并预览\n      this.previewAtTime(this.currentTime)\n    },\n\n    // 根据时间预览对应场景\n    previewAtTime(time) {\n      // 找到当前时间对应的场景\n      const currentScene = this.scenes.find(scene => {\n        return time >= scene.startTime && time < (scene.startTime + scene.duration)\n      })\n\n      if (currentScene) {\n        this.selectedScene = currentScene\n        console.log(`预览场景: ${currentScene.name} (${time}秒)`)\n\n        // 如果场景有视频，可以计算视频内的播放位置\n        if (currentScene.currentVideo) {\n          const sceneProgress = time - currentScene.startTime\n          const videoProgress = (sceneProgress / currentScene.duration * 100).toFixed(1)\n          console.log(`视频播放进度: ${videoProgress}%`)\n        }\n      } else {\n        // 如果没有找到场景，清除选择\n        this.selectedScene = null\n        console.log('当前时间没有对应的场景')\n      }\n    },\n\n    // 开始拖拽时间轴\n    onTimelineDragStart(event) {\n      event.preventDefault()\n\n      const startX = event.clientX\n      const rect = event.currentTarget.getBoundingClientRect()\n      const startClickX = event.clientX - rect.left\n      const startTime = Math.floor(startClickX / this.pixelsPerSecond)\n\n      // 设置初始时间\n      this.currentTime = Math.max(0, Math.min(60, startTime))\n      this.previewAtTime(this.currentTime)\n\n      const onMouseMove = (e) => {\n        const currentX = e.clientX\n        const deltaX = currentX - startX\n        const newClickX = startClickX + deltaX\n        const newTime = Math.floor(newClickX / this.pixelsPerSecond)\n\n        // 限制在0-60秒范围内\n        this.currentTime = Math.max(0, Math.min(60, newTime))\n        this.previewAtTime(this.currentTime)\n      }\n\n      const onMouseUp = () => {\n        document.removeEventListener('mousemove', onMouseMove)\n        document.removeEventListener('mouseup', onMouseUp)\n        console.log(`拖拽结束: ${this.currentTime}秒`)\n      }\n\n      document.addEventListener('mousemove', onMouseMove)\n      document.addEventListener('mouseup', onMouseUp)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.video-editor {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: #f5f7fa;\n}\n\n/* 头部工具栏 */\n.editor-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 20px;\n  background: white;\n  border-bottom: 1px solid #e4e7ed;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.template-title {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.header-right {\n  display: flex;\n  gap: 12px;\n}\n\n/* 主编辑区域 */\n.editor-main {\n  display: flex;\n  flex: 1;\n  min-height: 0;\n}\n\n/* 预览面板 */\n.preview-panel {\n  flex: 1;\n  padding: 20px;\n  background: white;\n  border-right: 1px solid #e4e7ed;\n}\n\n.preview-container {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.video-preview {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 16px;\n  padding: 20px;\n}\n\n.preview-frame {\n  width: 300px;\n  height: 533px; /* 300 * 16/9 = 533.33 */\n  background: #000;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);\n  border: 1px solid #ddd;\n  overflow: hidden;\n}\n\n/* 场景预览样式 */\n.scene-preview {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n\n.scene-preview img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.scene-overlay {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));\n  color: white;\n  padding: 20px 16px 16px;\n}\n\n.scene-title {\n  font-size: 16px;\n  font-weight: 600;\n  margin-bottom: 4px;\n}\n\n.scene-video-name {\n  font-size: 12px;\n  color: #ccc;\n  margin-bottom: 4px;\n}\n\n.scene-time-info {\n  font-size: 11px;\n  color: #ffd700;\n  margin-bottom: 8px;\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n}\n\n.scene-text-preview {\n  font-size: 11px;\n  line-height: 1.4;\n  color: #ddd;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.preview-placeholder {\n  text-align: center;\n  color: #909399;\n  padding: 40px 20px;\n}\n\n.preview-placeholder i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n  color: #666;\n}\n\n.preview-placeholder p {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n  font-weight: 500;\n  color: #909399;\n}\n\n.preview-info {\n  font-size: 12px !important;\n  margin: 12px 0 !important;\n  color: #999 !important;\n}\n\n.preview-tips {\n  margin-top: 20px;\n  padding-top: 16px;\n  border-top: 1px solid #333;\n}\n\n.preview-tips p {\n  font-size: 11px !important;\n  color: #666 !important;\n  margin: 4px 0 !important;\n}\n\n.current-time-display {\n  margin: 16px 0;\n  padding: 8px 12px;\n  background: rgba(255, 215, 0, 0.1);\n  border: 1px solid #ffd700;\n  border-radius: 4px;\n}\n\n.current-time-display p {\n  margin: 0 !important;\n  font-size: 14px !important;\n  color: #ffd700 !important;\n  font-weight: 600 !important;\n}\n\n.preview-controls {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 0;\n}\n\n.time-display {\n  font-family: monospace;\n  font-size: 14px;\n  color: #606266;\n}\n\n/* 属性设置面板 */\n.properties-panel {\n  width: 300px;\n  background: white;\n  border-left: 1px solid #e4e7ed;\n  display: flex;\n  flex-direction: column;\n}\n\n.panel-header {\n  padding: 16px 20px;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.panel-header h4 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.panel-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 16px;\n}\n\n/* 时间轴区域 */\n.timeline-area {\n  height: 300px;\n  background: white;\n  border-top: 1px solid #e4e7ed;\n  display: flex;\n  flex-direction: column;\n  min-width: 0; /* 允许收缩 */\n}\n\n.timeline-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 20px;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.timeline-header h4 {\n  margin: 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.timeline-container {\n  flex: 1;\n  position: relative;\n  overflow: hidden;\n}\n\n/* 时间刻度行 */\n.time-ruler-row {\n  display: flex;\n  height: 30px;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.time-ruler-label {\n  width: 150px;\n  background: #f8f9fa;\n  border-right: 1px solid #e4e7ed;\n  flex-shrink: 0;\n}\n\n/* 时间刻度 */\n.time-ruler {\n  flex: 1;\n  height: 100%;\n  background: #fafafa;\n  position: relative;\n  cursor: pointer;\n  user-select: none;\n  overflow: hidden;\n}\n\n.time-marks-container {\n  position: relative;\n  height: 100%;\n  width: 100%;\n}\n\n.time-mark {\n  position: absolute;\n  top: 0;\n  height: 100%;\n  font-size: 11px;\n  color: #606266;\n  display: flex;\n  align-items: center;\n  padding-left: 6px;\n  border-left: 2px solid #c0c4cc;\n  pointer-events: none;\n  font-weight: 500;\n}\n\n.time-tick {\n  position: absolute;\n  top: 22px;\n  width: 1px;\n  height: 8px;\n  background: #f0f0f0;\n  pointer-events: none;\n}\n\n.playhead {\n  position: absolute;\n  top: 0;\n  width: 2px;\n  height: 100%;\n  background: #ff4757;\n  z-index: 10;\n  pointer-events: none;\n}\n\n.playhead::before {\n  content: '';\n  position: absolute;\n  top: -4px;\n  left: -4px;\n  width: 10px;\n  height: 10px;\n  background: #ff4757;\n  border-radius: 50%;\n}\n\n/* 轨道容器 */\n.tracks-container {\n  flex: 1;\n}\n\n.track-row {\n  height: 40px;\n  display: flex;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.track-label {\n  width: 150px;\n  display: flex;\n  align-items: center;\n  padding: 0 12px;\n  background: #f8f9fa;\n  border-right: 1px solid #e4e7ed;\n  font-size: 14px;\n  color: #606266;\n  flex-shrink: 0;\n}\n\n.track-label i {\n  margin-right: 8px;\n  font-size: 16px;\n}\n\n.track-content {\n  flex: 1;\n  position: relative;\n  background: white;\n  overflow: hidden;\n}\n\n.track-timeline {\n  position: relative;\n  height: 100%;\n  width: 100%;\n}\n\n/* 片段样式 */\n.clip {\n  position: absolute;\n  top: 4px;\n  height: 32px;\n  background: #409EFF;\n  border-radius: 4px;\n  cursor: pointer;\n  user-select: none;\n  display: flex;\n  align-items: center;\n  padding: 0 8px;\n  color: white;\n  font-size: 12px;\n  transition: all 0.2s ease;\n}\n\n.clip:hover {\n  background: #337ecc;\n  transform: translateY(-1px);\n}\n\n.clip.active {\n  background: #E6A23C;\n  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.3);\n}\n\n/* 场景片段样式 */\n.scene-clip {\n  position: absolute;\n  top: 2px;\n  height: 36px;\n  background: #67C23A;\n  border-radius: 6px;\n  cursor: pointer;\n  user-select: none;\n  display: flex;\n  align-items: center;\n  padding: 0;\n  color: white;\n  font-size: 11px;\n  transition: all 0.2s ease;\n  overflow: hidden;\n}\n\n.scene-clip:hover {\n  background: #5daf34;\n  transform: translateY(-1px);\n}\n\n.scene-clip.active {\n  background: #E6A23C;\n  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.3);\n}\n\n.scene-content {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  padding: 4px 8px;\n  overflow: hidden;\n}\n\n.scene-name {\n  display: block;\n  font-weight: 600;\n  font-size: 12px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  line-height: 1.2;\n  margin-bottom: 2px;\n}\n\n.scene-duration {\n  font-size: 10px;\n  opacity: 0.8;\n  line-height: 1;\n  margin-bottom: 1px;\n}\n\n.scene-duration .max-duration {\n  color: rgba(255, 255, 255, 0.6);\n  font-size: 9px;\n}\n\n.scene-video-info {\n  font-size: 9px;\n  opacity: 0.7;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n/* 文案片段样式 */\n.text-clip {\n  position: absolute;\n  top: 4px;\n  height: 32px;\n  background: #F56C6C;\n  border-radius: 4px;\n  cursor: pointer;\n  user-select: none;\n  display: flex;\n  align-items: center;\n  padding: 0 8px;\n  color: white;\n  font-size: 11px;\n  transition: all 0.2s ease;\n}\n\n.text-clip:hover {\n  background: #f45656;\n  transform: translateY(-1px);\n}\n\n.text-clip.active {\n  background: #E6A23C;\n  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.3);\n}\n\n.text-content {\n  flex: 1;\n  overflow: hidden;\n}\n\n.text-preview {\n  display: block;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  font-weight: 500;\n  line-height: 1.2;\n}\n\n.text-duration {\n  font-size: 10px;\n  opacity: 0.8;\n}\n\n.clip-content {\n  flex: 1;\n  overflow: hidden;\n}\n\n.clip-name {\n  display: block;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  font-weight: 500;\n}\n\n.clip-duration {\n  font-size: 10px;\n  opacity: 0.8;\n}\n\n.max-duration {\n  color: rgba(255, 255, 255, 0.6);\n  font-size: 9px;\n}\n\n/* 调整手柄 */\n.resize-handle {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  width: 4px;\n  background: rgba(255, 255, 255, 0.3);\n  cursor: ew-resize;\n  opacity: 0;\n  transition: opacity 0.2s ease;\n}\n\n.resize-handle.left {\n  left: 0;\n}\n\n.resize-handle.right {\n  right: 0;\n}\n\n.clip:hover .resize-handle {\n  opacity: 1;\n}\n\n/* 轨道类型样式 */\n.track-row.video .clip {\n  background: #409EFF;\n}\n\n.track-row.audio .clip {\n  background: #67C23A;\n}\n\n.track-row.text .clip {\n  background: #E6A23C;\n}\n\n.track-row.effect .clip {\n  background: #F56C6C;\n}\n\n/* 批量配置 */\n.batch-config {\n  text-align: center;\n  padding: 20px 0;\n}\n\n.batch-config h4 {\n  margin: 0 0 20px 0;\n  color: #303133;\n  font-size: 18px;\n}\n\n.batch-config p {\n  margin: 0 0 16px 0;\n  color: #606266;\n}\n\n.batch-tip {\n  margin-top: 12px !important;\n  color: #909399;\n  font-size: 12px;\n}\n\n/* 视频库对话框样式 */\n.video-library-content h4 {\n  margin: 0 0 16px 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.video-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));\n  gap: 16px;\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.video-item {\n  cursor: pointer;\n  border-radius: 8px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  background: white;\n}\n\n.video-item:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  border-color: #409EFF;\n}\n\n.video-thumbnail {\n  position: relative;\n  width: 100%;\n  height: 90px;\n}\n\n.video-thumbnail img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  display: block;\n}\n\n.video-duration-badge {\n  position: absolute;\n  bottom: 4px;\n  right: 4px;\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-size: 10px;\n  font-weight: 500;\n}\n\n.video-info {\n  padding: 8px;\n  background: #f8f9fa;\n}\n\n.video-name {\n  margin: 0 0 4px 0;\n  font-size: 12px;\n  color: #303133;\n  font-weight: 500;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.video-duration-text {\n  margin: 0;\n  font-size: 11px;\n  color: #909399;\n}\n\n/* 响应式设计 - 9:16 视频预览适配 */\n@media (max-width: 1200px) {\n  .preview-frame {\n    width: 250px;\n    height: 444px; /* 250 * 16/9 = 444.44 */\n  }\n}\n\n@media (max-width: 768px) {\n  .editor-main {\n    flex-direction: column;\n  }\n\n  .preview-panel {\n    border-right: none;\n    border-bottom: 1px solid #e4e7ed;\n  }\n\n  .properties-panel {\n    width: 100%;\n    max-height: 300px;\n  }\n\n  .preview-frame {\n    width: 200px;\n    height: 356px; /* 200 * 16/9 = 355.56 */\n  }\n\n  .timeline-area {\n    height: 250px;\n  }\n}\n\n@media (max-width: 480px) {\n  .preview-frame {\n    width: 150px;\n    height: 267px; /* 150 * 16/9 = 266.67 */\n  }\n\n  .preview-placeholder {\n    padding: 20px 10px;\n  }\n\n  .preview-placeholder i {\n    font-size: 32px;\n  }\n\n  .preview-placeholder p {\n    font-size: 14px;\n  }\n\n  .preview-tips p {\n    font-size: 10px !important;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAkWA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,YAAA;QACAC,EAAA;QACAH,IAAA;QACAI,UAAA;QACAC,GAAA;MACA;MAEA;MACAC,kBAAA;MACAC,UAAA;MAEA;MACAC,qBAAA;MACAC,SAAA;QACAT,IAAA;QACAU,QAAA;QACAC,cAAA;MACA;MAEA;MACAC,yBAAA;MACAC,oBAAA;MAEA;MACAC,cAAA;MAEA;MACAC,aAAA;QACAC,eAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;MACA;MAEAC,aAAA;QACAC,YAAA;QACAC,SAAA;QACAC,SAAA;MACA;MAEAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,KAAA;MACA;MAEA;MACAC,YAAA;MACAC,aAAA;MACAC,mBAAA;MAEA;MACAC,gBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,WAAA;MAAA;;MAEA;MACAC,MAAA,GACA;QACA/B,EAAA;QACAH,IAAA;QACAmC,SAAA;QACAzB,QAAA;QACA0B,WAAA;QAAA;QACAzB,cAAA;QACA0B,WAAA;QACAC,YAAA;MACA,GACA;QACAnC,EAAA;QACAH,IAAA;QACAmC,SAAA;QACAzB,QAAA;QACA0B,WAAA;QAAA;QACAzB,cAAA;QACA0B,WAAA;QACAC,YAAA;MACA,GACA;QACAnC,EAAA;QACAH,IAAA;QACAmC,SAAA;QACAzB,QAAA;QACA0B,WAAA;QAAA;QACAzB,cAAA;QACA0B,WAAA;QACAC,YAAA;MACA,EACA;MAEA;MACAC,SAAA;MAAA;;MAEA;MACAC,cAAA;MAEA;MACAC,WAAA;QACAtC,EAAA;QACAH,IAAA;QACA0C,QAAA;QACAC,QAAA;MACA;MAEA;MACAC,MAAA,GACA;QACAzC,EAAA;QACAH,IAAA;QACA6C,IAAA;QACAC,IAAA;MACA,GACA;QACA3C,EAAA;QACAH,IAAA;QACA6C,IAAA;QACAC,IAAA;MACA,GACA;QACA3C,EAAA;QACAH,IAAA;QACA6C,IAAA;QACAC,IAAA;QACAC,KAAA,GACA;UACA5C,EAAA;UACAH,IAAA;UACAmC,SAAA;UACAzB,QAAA;UAAA;UACA0B,WAAA;UAAA;UACAY,gBAAA;UAAA;UACAH,IAAA;QACA;MAEA,GACA;QACA1C,EAAA;QACAH,IAAA;QACA6C,IAAA;QACAC,IAAA;QACAC,KAAA,GACA;UACA5C,EAAA;UACAH,IAAA;UACAmC,SAAA;UACAzB,QAAA;UACA0B,WAAA;UAAA;UACAY,gBAAA;UACAH,IAAA;QACA,GACA;UACA1C,EAAA;UACAH,IAAA;UACAmC,SAAA;UACAzB,QAAA;UACA0B,WAAA;UACAY,gBAAA;UACAH,IAAA;QACA;MAEA;IAEA;EACA;EAEAI,QAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,YAAAnB,gBAAA,QAAAC,eAAA;IACA;EACA;EAEAmB,OAAA,WAAAA,QAAA;IACA;IACA,IAAAC,UAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,UAAA;IACA,IAAAA,UAAA;MACA,KAAAG,YAAA,CAAAH,UAAA;IACA;;IAEA;IACA,KAAAI,uBAAA;;IAEA;IACA,KAAAC,qBAAA;EACA;EAEAC,OAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IAEA;IACAN,YAAA,WAAAA,aAAAH,UAAA;MACA;MACA,KAAAlD,YAAA,CAAAC,EAAA,GAAAiD,UAAA;MACAU,OAAA,CAAAC,GAAA,UAAAX,UAAA;IACA;IAEA;IACAY,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,CAAAC,OAAA;MACA;IACA;IAEA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAA7D,kBAAA;IACA;IAEA;IACA8D,cAAA,WAAAA,eAAA;MACA,KAAAH,QAAA,CAAAC,OAAA,6BAAAG,MAAA,MAAA9D,UAAA;MACA,KAAAD,kBAAA;;MAEA;MACA,KAAAsD,OAAA,CAAAU,IAAA;QACAC,IAAA;QACAjB,KAAA;UAAAF,UAAA,OAAAlD,YAAA,CAAAC;QAAA;MACA;IACA;IAEA;IACAqE,UAAA,WAAAA,WAAAC,IAAA;MACA,KAAA7C,YAAA,GAAA6C,IAAA;IACA;IAEA;IACAC,UAAA,WAAAA,WAAAC,OAAA;MACA,IAAAC,IAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,OAAA;MACA,IAAAI,IAAA,GAAAJ,OAAA;MACA,UAAAN,MAAA,CAAAO,IAAA,CAAAI,QAAA,GAAAC,QAAA,eAAAZ,MAAA,CAAAU,IAAA,CAAAC,QAAA,GAAAC,QAAA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAAT,IAAA;MACA;QACAU,IAAA,EAAAV,IAAA,CAAAtC,SAAA,QAAAH,eAAA;QACAoD,KAAA,EAAAX,IAAA,CAAA/D,QAAA,QAAAsB,eAAA;MACA;IACA;IAEA;IACAqD,WAAA,WAAAA,YAAAC,KAAA,EAAAb,IAAA;MACAa,KAAA,CAAAC,YAAA,CAAAC,OAAA,eAAAC,IAAA,CAAAC,SAAA,CAAAjB,IAAA;IACA;IAEA;IACAkB,MAAA,WAAAA,OAAAL,KAAA,EAAAM,KAAA;MACA,IAAAC,QAAA,GAAAJ,IAAA,CAAAK,KAAA,CAAAR,KAAA,CAAAC,YAAA,CAAAQ,OAAA;MACAjC,OAAA,CAAAC,GAAA,WAAA6B,KAAA,CAAA5F,IAAA,EAAA6F,QAAA;MACA;IACA;IAEA;IACAG,WAAA,WAAAA,YAAAV,KAAA,EAAAb,IAAA,EAAAwB,SAAA;MACAnC,OAAA,CAAAC,GAAA,UAAAU,IAAA,CAAAzE,IAAA,EAAAiG,SAAA;;MAEA;MACA,KAAAxB,IAAA,CAAArC,WAAA,KAAAqC,IAAA,CAAAzB,gBAAA;QACA,KAAAiB,QAAA,CAAAiC,OAAA;QACA;MACA;MAEA,IAAAC,kBAAA,GAAA1B,IAAA,CAAArC,WAAA,IAAAqC,IAAA,CAAAzB,gBAAA;MAEA,IAAAiD,SAAA;QACA;QACA,IAAAxB,IAAA,CAAA/D,QAAA,IAAAyF,kBAAA;UACA,KAAAlC,QAAA,CAAAiC,OAAA,+CAAA7B,MAAA,MAAAK,UAAA,CAAAyB,kBAAA;UACA;QACA;MACA;;MAEA;MACA,KAAAC,oBAAA,CAAAd,KAAA,EAAAb,IAAA,EAAAwB,SAAA,EAAAE,kBAAA;IACA;IAEA;IACAC,oBAAA,WAAAA,qBAAAd,KAAA,EAAAb,IAAA,EAAAwB,SAAA,EAAA7D,WAAA;MAAA,IAAAiE,KAAA;MACA,IAAAC,MAAA,GAAAhB,KAAA,CAAAiB,OAAA;MACA,IAAAC,aAAA,GAAA/B,IAAA,CAAA/D,QAAA;MACA,IAAAsB,eAAA,QAAAA,eAAA;MAEA,IAAAyE,WAAA,YAAAA,YAAAC,CAAA;QACA,IAAAC,MAAA,GAAAD,CAAA,CAAAH,OAAA,GAAAD,MAAA;QACA,IAAAM,YAAA,GAAAD,MAAA,GAAA3E,eAAA;QAEA,IAAA6E,WAAA,GAAAL,aAAA;QAEA,IAAAP,SAAA;UACAY,WAAA,GAAAhC,IAAA,CAAAiC,GAAA,IAAAjC,IAAA,CAAAkC,GAAA,CAAA3E,WAAA,EAAAoE,aAAA,GAAAI,YAAA;QACA,WAAAX,SAAA;UACAY,WAAA,GAAAhC,IAAA,CAAAiC,GAAA,IAAAjC,IAAA,CAAAkC,GAAA,CAAA3E,WAAA,EAAAoE,aAAA,GAAAI,YAAA;QACA;QAEAnC,IAAA,CAAA/D,QAAA,GAAAmE,IAAA,CAAAmC,KAAA,CAAAH,WAAA;;QAEA;QACAR,KAAA,CAAAY,eAAA,CAAAxC,IAAA,EAAArC,WAAA;MACA;MAEA,IAAA8E,UAAA,YAAAA,UAAA;QACAC,QAAA,CAAAC,mBAAA,cAAAX,WAAA;QACAU,QAAA,CAAAC,mBAAA,YAAAF,UAAA;QACAb,KAAA,CAAAgB,eAAA;MACA;MAEAF,QAAA,CAAAG,gBAAA,cAAAb,WAAA;MACAU,QAAA,CAAAG,gBAAA,YAAAJ,UAAA;IACA;IAEA;IACAD,eAAA,WAAAA,gBAAAxC,IAAA,EAAArC,WAAA;MACA,IAAAmF,UAAA,IAAA9C,IAAA,CAAA/D,QAAA,GAAA0B,WAAA,QAAAoF,OAAA;MACA1D,OAAA,CAAAC,GAAA,IAAAM,MAAA,CAAAI,IAAA,CAAAzE,IAAA,QAAAqE,MAAA,MAAAK,UAAA,CAAAD,IAAA,CAAA/D,QAAA,UAAA2D,MAAA,MAAAK,UAAA,CAAAtC,WAAA,SAAAiC,MAAA,CAAAkD,UAAA;IACA;IAEA;IACAF,eAAA,WAAAA,gBAAA;MACA;IAAA,CACA;IAEA;IACAI,oBAAA,WAAAA,qBAAA;MACA;MACA,IAAAC,aAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,aAAA,MAAAxH,YAAA,CAAAE,UAAA;IACA;IAEA;IACAoD,uBAAA,WAAAA,wBAAA;MAAA,IAAAmE,MAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,aAAA;QAAA,WAAAH,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cACA;gBACA;gBACA;gBACAH,aAAA,IACA;kBACA9H,EAAA;kBACAH,IAAA;kBACA6C,IAAA;kBACAwF,IAAA;kBACAC,UAAA;kBACA5H,QAAA;kBACA6H,GAAA;kBACAC,MAAA;kBACAC,SAAA;gBACA,GACA;kBACAtI,EAAA;kBACAH,IAAA;kBACA6C,IAAA;kBACAwF,IAAA;kBACAC,UAAA;kBACA5H,QAAA;kBACA6H,GAAA;kBACAC,MAAA;kBACAC,SAAA;gBACA,GACA;kBACAtI,EAAA;kBACAH,IAAA;kBACA6C,IAAA;kBACAwF,IAAA;kBACAC,UAAA;kBACA5H,QAAA;kBACA6H,GAAA;kBACAC,MAAA;kBACAC,SAAA;gBACA,GACA;kBACAtI,EAAA;kBACAH,IAAA;kBACA6C,IAAA;kBACAwF,IAAA;kBACAC,UAAA;kBACA5H,QAAA;kBACA6H,GAAA;kBACAC,MAAA;kBACAC,SAAA;gBACA,GACA;kBACAtI,EAAA;kBACAH,IAAA;kBACA6C,IAAA;kBACAwF,IAAA;kBACAC,UAAA;kBACA5H,QAAA;kBACA6H,GAAA;kBACAC,MAAA;kBACAC,SAAA;gBACA,EACA;gBAEAd,MAAA,CAAApF,SAAA,GAAA0F,aAAA;;gBAEA;gBACAN,MAAA,CAAAe,6BAAA;gBAEA5E,OAAA,CAAAC,GAAA,aAAA4D,MAAA,CAAAnF,cAAA;cAEA,SAAAmG,KAAA;gBACA7E,OAAA,CAAA6E,KAAA,aAAAA,KAAA;gBACAhB,MAAA,CAAA1D,QAAA,CAAA0E,KAAA;cACA;YAAA;cAAA,OAAAR,QAAA,CAAAS,CAAA;UAAA;QAAA,GAAAZ,OAAA;MAAA;IACA;IAEA;IACAU,6BAAA,WAAAA,8BAAA;MAAA,IAAAG,MAAA;MACA,IAAAC,SAAA,OAAAC,GAAA;;MAEA;MACA,KAAAxG,SAAA,CAAAyG,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAApG,IAAA;UACA;UACA,IAAAqG,UAAA,GAAAD,IAAA,CAAAT,MAAA;UACA,IAAAW,UAAA,GAAAD,UAAA,CAAAE,KAAA,MAAAC,GAAA;UAEA,KAAAP,SAAA,CAAAQ,GAAA,CAAAH,UAAA;YACAL,SAAA,CAAAS,GAAA,CAAAJ,UAAA;cACAhJ,EAAA,WAAAgJ,UAAA;cACAnJ,IAAA,EAAAmJ,UAAA;cACAK,MAAA;YACA;UACA;;UAEA;UACA,IAAAC,KAAA;YACAtJ,EAAA,EAAA8I,IAAA,CAAA9I,EAAA;YACAH,IAAA,EAAAiJ,IAAA,CAAAjJ,IAAA;YACAuI,GAAA,EAAAU,IAAA,CAAAV,GAAA;YACA7H,QAAA,EAAAmI,MAAA,CAAAa,aAAA,CAAAT,IAAA,CAAAvI,QAAA;YACA+H,SAAA,EAAAQ,IAAA,CAAAR,SAAA;UACA;UAEAK,SAAA,CAAAa,GAAA,CAAAR,UAAA,EAAAK,MAAA,CAAAlF,IAAA,CAAAmF,KAAA;QACA;MACA;;MAEA;MACA,KAAAjH,cAAA,GAAAoH,KAAA,CAAAC,IAAA,CAAAf,SAAA,CAAAgB,MAAA;IACA;IAEA;IACAJ,aAAA,WAAAA,cAAAK,WAAA;MACA,KAAAA,WAAA;MAEA,IAAAC,KAAA,GAAAD,WAAA,CAAAX,KAAA;MACA,IAAAY,KAAA,CAAAC,MAAA;QACA,IAAAC,OAAA,GAAAC,QAAA,CAAAH,KAAA;QACA,IAAArF,OAAA,GAAAwF,QAAA,CAAAH,KAAA;QACA,OAAAE,OAAA,QAAAvF,OAAA;MACA;MAEA;IACA;IAEA;IACAlB,qBAAA,WAAAA,sBAAA;MAAA,IAAA2G,MAAA;MACA,KAAAlI,MAAA,CAAA8G,OAAA,WAAAqB,KAAA;QACA,IAAAC,OAAA,GAAAF,MAAA,CAAA5H,cAAA,CAAA+H,IAAA,WAAAC,GAAA;UAAA,OAAAA,GAAA,CAAArK,EAAA,KAAAkK,KAAA,CAAA1J,cAAA;QAAA;QACA,IAAA2J,OAAA,IAAAA,OAAA,CAAAd,MAAA,CAAAS,MAAA;UACA;UACA,IAAAQ,WAAA,GAAA5F,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAA6F,MAAA,KAAAJ,OAAA,CAAAd,MAAA,CAAAS,MAAA;UACA,IAAAU,aAAA,GAAAL,OAAA,CAAAd,MAAA,CAAAiB,WAAA;UACAJ,KAAA,CAAA/H,YAAA,GAAAqI,aAAA;;UAEA;UACAN,KAAA,CAAAjI,WAAA,GAAAuI,aAAA,CAAAjK,QAAA;;UAEA;UACA,IAAA2J,KAAA,CAAA3J,QAAA,GAAAiK,aAAA,CAAAjK,QAAA;YACA2J,KAAA,CAAA3J,QAAA,GAAAiK,aAAA,CAAAjK,QAAA;UACA;QACA;MACA;;MAEA;MACA,KAAAkK,wBAAA;IACA;IAEA;IACAC,kBAAA,WAAAA,mBAAA;MACA,KAAApK,SAAA;QACAT,IAAA;QACAU,QAAA;QACAC,cAAA;MACA;MACA,KAAAH,qBAAA;IACA;IAEA;IACAsK,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,UAAAtK,SAAA,CAAAT,IAAA,UAAAS,SAAA,CAAAE,cAAA;QACA,KAAAsD,QAAA,CAAA0E,KAAA;QACA;MACA;;MAEA;MACA,IAAAqC,SAAA,QAAA9I,MAAA,MAAAA,MAAA,CAAA+H,MAAA;MACA,IAAA9H,SAAA,GAAA6I,SAAA,GAAAA,SAAA,CAAA7I,SAAA,GAAA6I,SAAA,CAAAtK,QAAA;MAEA,IAAAuK,QAAA;QACA9K,EAAA,YAAA+K,IAAA,CAAAC,GAAA;QACAnL,IAAA,OAAAS,SAAA,CAAAT,IAAA;QACAmC,SAAA,EAAAA,SAAA;QACAzB,QAAA,OAAAD,SAAA,CAAAC,QAAA;QACAC,cAAA,OAAAF,SAAA,CAAAE,cAAA;QACA0B,WAAA;QACAC,YAAA;MACA;;MAEA;MACA,IAAAgI,OAAA,QAAA9H,cAAA,CAAA+H,IAAA,WAAAC,GAAA;QAAA,OAAAA,GAAA,CAAArK,EAAA,KAAA4K,MAAA,CAAAtK,SAAA,CAAAE,cAAA;MAAA;MACA,IAAA2J,OAAA,IAAAA,OAAA,CAAAd,MAAA,CAAAS,MAAA;QACA,IAAAQ,WAAA,GAAA5F,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAA6F,MAAA,KAAAJ,OAAA,CAAAd,MAAA,CAAAS,MAAA;QACAgB,QAAA,CAAA3I,YAAA,GAAAgI,OAAA,CAAAd,MAAA,CAAAiB,WAAA;MACA;MAEA,KAAAvI,MAAA,CAAAoC,IAAA,CAAA2G,QAAA;MACA,KAAAzK,qBAAA;MACA,KAAAyD,QAAA,CAAAC,OAAA;IACA;IAEA;IACAkH,WAAA,WAAAA,YAAAf,KAAA;MACA,KAAAxI,aAAA,GAAAwI,KAAA;MACA,KAAAzI,YAAA;MACA,KAAAE,mBAAA;;MAEA;MACA,IAAAwI,OAAA,QAAA9H,cAAA,CAAA+H,IAAA,WAAAC,GAAA;QAAA,OAAAA,GAAA,CAAArK,EAAA,KAAAkK,KAAA,CAAA1J,cAAA;MAAA;MACA,IAAA2J,OAAA;QACA,KAAAzJ,oBAAA,GAAAyJ,OAAA;QACA,KAAA1J,yBAAA;MACA;IACA;IAEA;IACAyK,sBAAA,WAAAA,uBAAA5B,KAAA;MACA,SAAA5H,aAAA;QACA,KAAAA,aAAA,CAAAS,YAAA,GAAAmH,KAAA;QACA;QACA,KAAA5H,aAAA,CAAAO,WAAA,GAAAqH,KAAA,CAAA/I,QAAA;;QAEA;QACA,SAAAmB,aAAA,CAAAnB,QAAA,GAAA+I,KAAA,CAAA/I,QAAA;UACA,KAAAmB,aAAA,CAAAnB,QAAA,GAAA+I,KAAA,CAAA/I,QAAA;UACA,KAAAuD,QAAA,CAAAiC,OAAA,8EAAA7B,MAAA,MAAAK,UAAA,CAAA+E,KAAA,CAAA/I,QAAA;QACA;QAEA,KAAAuD,QAAA,CAAAC,OAAA,8BAAAG,MAAA,MAAAxC,aAAA,CAAA7B,IAAA,kCAAAqE,MAAA,CAAAoF,KAAA,CAAAzJ,IAAA,QAAAqE,MAAA,MAAAK,UAAA,CAAA+E,KAAA,CAAA/I,QAAA;MACA;MACA,KAAAE,yBAAA;IACA;IAEA;IACA0K,iBAAA,WAAAA,kBAAAjB,KAAA,EAAAkB,KAAA;MACA,KAAAzJ,mBAAA,GAAAuI,KAAA;MACA,KAAAzI,YAAA;MACA,KAAAC,aAAA;IACA;IAEA;IACA2J,aAAA,WAAAA,cAAAnB,KAAA;MACA;QACAlF,IAAA,EAAAkF,KAAA,CAAAlI,SAAA,QAAAH,eAAA;QACAoD,KAAA,EAAAiF,KAAA,CAAA3J,QAAA,QAAAsB,eAAA;MACA;IACA;IAEA;IACAyJ,cAAA,WAAAA,eAAAC,IAAA;MACA,OAAAA,IAAA,CAAAzB,MAAA,QAAAyB,IAAA,CAAAC,SAAA,kBAAAD,IAAA;IACA;IAEA;IACAE,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,WAAAjE,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA+D,SAAA;QAAA,IAAAC,YAAA;QAAA,WAAAjE,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAA8D,SAAA;UAAA,kBAAAA,SAAA,CAAA5D,CAAA;YAAA;cACA;gBACAyD,MAAA,CAAA5H,QAAA,CAAAgI,IAAA;;gBAEA;gBACA;;gBAEA;gBACAF,YAAA,IACA,gCACA,qCACA,sBACA,0BACA,EAEA;gBACAA,YAAA,CAAA/C,OAAA,WAAAkD,OAAA,EAAAX,KAAA;kBACA,IAAAM,MAAA,CAAA3J,MAAA,CAAAqJ,KAAA;oBACAM,MAAA,CAAA3J,MAAA,CAAAqJ,KAAA,EAAAlJ,WAAA,GAAA6J,OAAA;kBACA;gBACA;gBAEAL,MAAA,CAAA5H,QAAA,CAAAC,OAAA;cAEA,SAAAyE,KAAA;gBACA7E,OAAA,CAAA6E,KAAA,YAAAA,KAAA;gBACAkD,MAAA,CAAA5H,QAAA,CAAA0E,KAAA;cACA;YAAA;cAAA,OAAAqD,SAAA,CAAApD,CAAA;UAAA;QAAA,GAAAkD,QAAA;MAAA;IACA;IAEA;IACAK,gBAAA,WAAAA,iBAAA7G,KAAA,EAAA+E,KAAA;MACA/E,KAAA,CAAAC,YAAA,CAAAC,OAAA,eAAAC,IAAA,CAAAC,SAAA,CAAA2E,KAAA;IACA;IAEA;IACA+B,WAAA,WAAAA,YAAA9G,KAAA;MACA,IAAA+G,SAAA,GAAA5G,IAAA,CAAAK,KAAA,CAAAR,KAAA,CAAAC,YAAA,CAAAQ,OAAA;MACAjC,OAAA,CAAAC,GAAA,UAAAsI,SAAA;MACA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAAhH,KAAA,EAAA+E,KAAA,EAAApE,SAAA;MACAnC,OAAA,CAAAC,GAAA,YAAAsG,KAAA,CAAArK,IAAA,EAAAiG,SAAA;;MAEA;MACA,KAAAoE,KAAA,CAAA/H,YAAA,KAAA+H,KAAA,CAAAjI,WAAA;QACA,KAAA6B,QAAA,CAAAiC,OAAA;QACA;MACA;MAEA,IAAAC,kBAAA,GAAAkE,KAAA,CAAAjI,WAAA;MAEA,IAAA6D,SAAA;QACA;QACA,IAAAoE,KAAA,CAAA3J,QAAA,IAAAyF,kBAAA;UACA,KAAAlC,QAAA,CAAAiC,OAAA,+CAAA7B,MAAA,MAAAK,UAAA,CAAAyB,kBAAA;UACA;QACA;MACA;;MAEA;MACA,KAAAoG,yBAAA,CAAAjH,KAAA,EAAA+E,KAAA,EAAApE,SAAA,EAAAE,kBAAA;IACA;IAEA;IACAoG,yBAAA,WAAAA,0BAAAjH,KAAA,EAAA+E,KAAA,EAAApE,SAAA,EAAA7D,WAAA;MAAA,IAAAoK,MAAA;MACA,IAAAlG,MAAA,GAAAhB,KAAA,CAAAiB,OAAA;MACA,IAAAC,aAAA,GAAA6D,KAAA,CAAA3J,QAAA;MACA,IAAAsB,eAAA,QAAAA,eAAA;MAEA,IAAAyE,WAAA,YAAAA,YAAAC,CAAA;QACA,IAAAC,MAAA,GAAAD,CAAA,CAAAH,OAAA,GAAAD,MAAA;QACA,IAAAM,YAAA,GAAAD,MAAA,GAAA3E,eAAA;QAEA,IAAA6E,WAAA,GAAAL,aAAA;QAEA,IAAAP,SAAA;UACAY,WAAA,GAAAhC,IAAA,CAAAiC,GAAA,IAAAjC,IAAA,CAAAkC,GAAA,CAAA3E,WAAA,EAAAoE,aAAA,GAAAI,YAAA;QACA,WAAAX,SAAA;UACAY,WAAA,GAAAhC,IAAA,CAAAiC,GAAA,IAAAjC,IAAA,CAAAkC,GAAA,CAAA3E,WAAA,EAAAoE,aAAA,GAAAI,YAAA;QACA;QAEAyD,KAAA,CAAA3J,QAAA,GAAAmE,IAAA,CAAAmC,KAAA,CAAAH,WAAA;;QAEA;QACA2F,MAAA,CAAAC,oBAAA,CAAApC,KAAA,EAAAjI,WAAA;MACA;MAEA,IAAA8E,WAAA,YAAAA,UAAA;QACAC,QAAA,CAAAC,mBAAA,cAAAX,WAAA;QACAU,QAAA,CAAAC,mBAAA,YAAAF,WAAA;QACAsF,MAAA,CAAAE,oBAAA;;QAEA;QACAF,MAAA,CAAA5B,wBAAA;MACA;MAEAzD,QAAA,CAAAG,gBAAA,cAAAb,WAAA;MACAU,QAAA,CAAAG,gBAAA,YAAAJ,WAAA;IACA;IAEA;IACAuF,oBAAA,WAAAA,qBAAApC,KAAA,EAAAjI,WAAA;MACA,IAAAmF,UAAA,IAAA8C,KAAA,CAAA3J,QAAA,GAAA0B,WAAA,QAAAoF,OAAA;MACA1D,OAAA,CAAAC,GAAA,IAAAM,MAAA,CAAAgG,KAAA,CAAArK,IAAA,QAAAqE,MAAA,MAAAK,UAAA,CAAA2F,KAAA,CAAA3J,QAAA,UAAA2D,MAAA,MAAAK,UAAA,CAAAtC,WAAA,SAAAiC,MAAA,CAAAkD,UAAA;IACA;IAEA;IACAmF,oBAAA,WAAAA,qBAAA;MACA;IAAA,CACA;IAEA;IACA9B,wBAAA,WAAAA,yBAAA;MACA,IAAA3I,WAAA;MACA,KAAAC,MAAA,CAAA8G,OAAA,WAAAqB,KAAA;QACAA,KAAA,CAAAlI,SAAA,GAAAF,WAAA;QACAA,WAAA,IAAAoI,KAAA,CAAA3J,QAAA;MACA;MACAoD,OAAA,CAAAC,GAAA;IACA;IAEA;IACA4I,eAAA,WAAAA,gBAAArH,KAAA;MACA,IAAAsH,IAAA,GAAAtH,KAAA,CAAAuH,aAAA,CAAAC,qBAAA;MACA,IAAAC,MAAA,GAAAzH,KAAA,CAAAiB,OAAA,GAAAqG,IAAA,CAAAzH,IAAA;MACA,IAAA6H,WAAA,GAAAnI,IAAA,CAAAC,KAAA,CAAAiI,MAAA,QAAA/K,eAAA;;MAEA;MACA,KAAAC,WAAA,GAAA4C,IAAA,CAAAiC,GAAA,IAAAjC,IAAA,CAAAkC,GAAA,KAAAiG,WAAA;MAEAlJ,OAAA,CAAAC,GAAA,oCAAAM,MAAA,MAAApC,WAAA;;MAEA;MACA,KAAAgL,aAAA,MAAAhL,WAAA;IACA;IAEA;IACAgL,aAAA,WAAAA,cAAAC,IAAA;MACA;MACA,IAAAC,YAAA,QAAAjL,MAAA,CAAAqI,IAAA,WAAAF,KAAA;QACA,OAAA6C,IAAA,IAAA7C,KAAA,CAAAlI,SAAA,IAAA+K,IAAA,GAAA7C,KAAA,CAAAlI,SAAA,GAAAkI,KAAA,CAAA3J,QAAA;MACA;MAEA,IAAAyM,YAAA;QACA,KAAAtL,aAAA,GAAAsL,YAAA;QACArJ,OAAA,CAAAC,GAAA,8BAAAM,MAAA,CAAA8I,YAAA,CAAAnN,IAAA,QAAAqE,MAAA,CAAA6I,IAAA;;QAEA;QACA,IAAAC,YAAA,CAAA7K,YAAA;UACA,IAAA8K,aAAA,GAAAF,IAAA,GAAAC,YAAA,CAAAhL,SAAA;UACA,IAAAkL,aAAA,IAAAD,aAAA,GAAAD,YAAA,CAAAzM,QAAA,QAAA8G,OAAA;UACA1D,OAAA,CAAAC,GAAA,0CAAAM,MAAA,CAAAgJ,aAAA;QACA;MACA;QACA;QACA,KAAAxL,aAAA;QACAiC,OAAA,CAAAC,GAAA;MACA;IACA;IAEA;IACAuJ,mBAAA,WAAAA,oBAAAhI,KAAA;MAAA,IAAAiI,MAAA;MACAjI,KAAA,CAAAkI,cAAA;MAEA,IAAAlH,MAAA,GAAAhB,KAAA,CAAAiB,OAAA;MACA,IAAAqG,IAAA,GAAAtH,KAAA,CAAAuH,aAAA,CAAAC,qBAAA;MACA,IAAAW,WAAA,GAAAnI,KAAA,CAAAiB,OAAA,GAAAqG,IAAA,CAAAzH,IAAA;MACA,IAAAhD,SAAA,GAAA0C,IAAA,CAAAC,KAAA,CAAA2I,WAAA,QAAAzL,eAAA;;MAEA;MACA,KAAAC,WAAA,GAAA4C,IAAA,CAAAiC,GAAA,IAAAjC,IAAA,CAAAkC,GAAA,KAAA5E,SAAA;MACA,KAAA8K,aAAA,MAAAhL,WAAA;MAEA,IAAAwE,WAAA,YAAAA,YAAAC,CAAA;QACA,IAAAgH,QAAA,GAAAhH,CAAA,CAAAH,OAAA;QACA,IAAAI,MAAA,GAAA+G,QAAA,GAAApH,MAAA;QACA,IAAAqH,SAAA,GAAAF,WAAA,GAAA9G,MAAA;QACA,IAAAiH,OAAA,GAAA/I,IAAA,CAAAC,KAAA,CAAA6I,SAAA,GAAAJ,MAAA,CAAAvL,eAAA;;QAEA;QACAuL,MAAA,CAAAtL,WAAA,GAAA4C,IAAA,CAAAiC,GAAA,IAAAjC,IAAA,CAAAkC,GAAA,KAAA6G,OAAA;QACAL,MAAA,CAAAN,aAAA,CAAAM,MAAA,CAAAtL,WAAA;MACA;MAEA,IAAAiF,WAAA,YAAAA,UAAA;QACAC,QAAA,CAAAC,mBAAA,cAAAX,WAAA;QACAU,QAAA,CAAAC,mBAAA,YAAAF,WAAA;QACApD,OAAA,CAAAC,GAAA,8BAAAM,MAAA,CAAAkJ,MAAA,CAAAtL,WAAA;MACA;MAEAkF,QAAA,CAAAG,gBAAA,cAAAb,WAAA;MACAU,QAAA,CAAAG,gBAAA,YAAAJ,WAAA;IACA;EACA;AACA", "ignoreList": []}]}