<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频轨道修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        /* 模拟编辑器布局 */
        .editor-demo {
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .time-ruler-row {
            display: flex;
            height: 30px;
            border-bottom: 1px solid #e4e7ed;
        }
        
        .time-ruler-label {
            width: 150px;
            background: #f8f9fa;
            border-right: 1px solid #e4e7ed;
            flex-shrink: 0;
        }
        
        .time-ruler {
            flex: 1;
            height: 100%;
            background: #fafafa;
            position: relative;
            cursor: pointer;
            user-select: none;
            overflow: hidden;
        }
        
        .time-marks-container {
            position: relative;
            width: 600px;
            height: 100%;
        }
        
        .time-mark {
            position: absolute;
            top: 0;
            height: 100%;
            font-size: 11px;
            color: #606266;
            display: flex;
            align-items: center;
            padding-left: 6px;
            border-left: 2px solid #c0c4cc;
            font-weight: 500;
        }
        
        .time-tick {
            position: absolute;
            top: 22px;
            width: 1px;
            height: 8px;
            background: #f0f0f0;
        }
        
        .playhead {
            position: absolute;
            top: 0;
            width: 2px;
            height: 100%;
            background: #ff4757;
            z-index: 10;
        }
        
        .playhead::before {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            width: 10px;
            height: 10px;
            background: #ff4757;
            border-radius: 50%;
        }
        
        .track-row {
            height: 40px;
            display: flex;
            border-bottom: 1px solid #e4e7ed;
        }
        
        .track-label {
            width: 150px;
            display: flex;
            align-items: center;
            padding: 0 12px;
            background: #f8f9fa;
            border-right: 1px solid #e4e7ed;
            font-size: 14px;
            color: #606266;
            flex-shrink: 0;
        }
        
        .track-content {
            flex: 1;
            position: relative;
            background: white;
            overflow: hidden;
        }
        
        .track-timeline {
            position: relative;
            height: 100%;
            width: 600px;
        }
        
        /* 修复后的场景片段样式 - 无图片版本 */
        .scene-clip {
            position: absolute;
            top: 2px;
            height: 36px;
            background: #67C23A;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            color: white;
            font-size: 11px;
            overflow: hidden;
        }
        
        .scene-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 100%;
            height: 100%;
            padding: 4px 8px;
            overflow: hidden;
        }
        
        .scene-name {
            display: block;
            font-weight: 600;
            font-size: 12px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.2;
            margin-bottom: 2px;
        }
        
        .scene-duration {
            font-size: 10px;
            opacity: 0.8;
            line-height: 1;
            margin-bottom: 1px;
        }
        
        .scene-video-info {
            font-size: 9px;
            opacity: 0.7;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison-item {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        
        .comparison-item h4 {
            margin-top: 0;
            color: #333;
        }
        
        .old-scene-clip {
            position: absolute;
            top: 2px;
            height: 36px;
            background: #67C23A;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            color: white;
            font-size: 11px;
            overflow: hidden;
        }
        
        .old-scene-content {
            display: flex;
            align-items: center;
            width: 100%;
            height: 100%;
        }
        
        .old-scene-thumbnail {
            width: 32px;
            height: 32px;
            margin: 2px;
            border-radius: 4px;
            background: #4A90E2;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
        }
        
        .old-scene-info {
            flex: 1;
            padding: 0 6px;
            overflow: hidden;
        }
        
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .status.fixed {
            background: #d4edda;
            color: #155724;
        }
        
        .status.removed {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>视频轨道修复完成</h1>
        <p>修复了HTML结构问题，移除了视频轨道的图片显示</p>

        <div class="demo-section">
            <h3>修复内容</h3>
            <ul>
                <li><span class="status fixed">✅ 修复</span>HTML结构问题，消除多余的 ">" 符号</li>
                <li><span class="status removed">🗑️ 移除</span>视频轨道中的图片显示</li>
                <li><span class="status fixed">✅ 优化</span>场景片段的文字布局和样式</li>
                <li><span class="status fixed">✅ 改进</span>场景信息的显示层次</li>
            </ul>
        </div>

        <div class="demo-section">
            <h3>修复后的视频轨道</h3>
            <div class="editor-demo">
                <!-- 时间刻度行 -->
                <div class="time-ruler-row">
                    <div class="time-ruler-label"></div>
                    <div class="time-ruler">
                        <div class="time-marks-container">
                            <!-- 每15秒一个主刻度 -->
                            <div class="time-mark" style="left: 0px;">00:00</div>
                            <div class="time-mark" style="left: 150px;">00:15</div>
                            <div class="time-mark" style="left: 300px;">00:30</div>
                            <div class="time-mark" style="left: 450px;">00:45</div>
                            <div class="time-mark" style="left: 600px;">01:00</div>
                            
                            <!-- 小刻度 -->
                            <div class="time-tick" style="left: 75px;"></div>
                            <div class="time-tick" style="left: 225px;"></div>
                            <div class="time-tick" style="left: 375px;"></div>
                            <div class="time-tick" style="left: 525px;"></div>
                            
                            <!-- 播放指针 -->
                            <div class="playhead" style="left: 100px;"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 视频场景轨道 - 修复后（无图片） -->
                <div class="track-row">
                    <div class="track-label">
                        📹 视频场景
                    </div>
                    <div class="track-content">
                        <div class="track-timeline">
                            <div class="scene-clip" style="left: 0px; width: 80px;">
                                <div class="scene-content">
                                    <span class="scene-name">开场场景</span>
                                    <div class="scene-duration">00:08 / 00:12</div>
                                    <div class="scene-video-info">开场视频1.mp4</div>
                                </div>
                            </div>
                            <div class="scene-clip" style="left: 80px; width: 100px;">
                                <div class="scene-content">
                                    <span class="scene-name">产品介绍</span>
                                    <div class="scene-duration">00:10 / 00:15</div>
                                    <div class="scene-video-info">产品视频2.mp4</div>
                                </div>
                            </div>
                            <div class="scene-clip" style="left: 180px; width: 90px;">
                                <div class="scene-content">
                                    <span class="scene-name">功能展示</span>
                                    <div class="scene-duration">00:09 / 00:20</div>
                                    <div class="scene-video-info">功能视频1.mp4</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 文案轨道 -->
                <div class="track-row">
                    <div class="track-label">
                        📝 文案
                    </div>
                    <div class="track-content">
                        <div class="track-timeline">
                            <div class="scene-clip" style="left: 0px; width: 80px; background: #F56C6C;">
                                <div class="scene-content">
                                    <span class="scene-name">开场文案</span>
                                    <div class="scene-duration">00:08</div>
                                    <div class="scene-video-info">欢迎来到我们的产品展示...</div>
                                </div>
                            </div>
                            <div class="scene-clip" style="left: 80px; width: 100px; background: #F56C6C;">
                                <div class="scene-content">
                                    <span class="scene-name">产品文案</span>
                                    <div class="scene-duration">00:10</div>
                                    <div class="scene-video-info">这款产品具有独特的设计...</div>
                                </div>
                            </div>
                            <div class="scene-clip" style="left: 180px; width: 90px; background: #F56C6C;">
                                <div class="scene-content">
                                    <span class="scene-name">功能文案</span>
                                    <div class="scene-duration">00:09</div>
                                    <div class="scene-video-info">让我们来看看它的核心功能...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>修复前后对比</h3>
            <div class="comparison">
                <div class="comparison-item">
                    <h4>修复前（有问题）</h4>
                    <div style="position: relative; height: 40px; background: #f8f9fa; border: 1px solid #ddd;">
                        <div class="old-scene-clip" style="left: 10px; width: 120px;">
                            <div class="old-scene-content">
                                <div class="old-scene-thumbnail">IMG</div>
                                <div class="old-scene-info">
                                    <span style="font-size: 11px;">开场场景</span>
                                </div>
                            </div>
                        </div>
                        <div style="position: absolute; left: 0; top: 0; color: red; font-size: 16px;">"&gt;</div>
                    </div>
                    <ul style="margin-top: 10px; font-size: 12px;">
                        <li>❌ 显示多余的 ">" 符号</li>
                        <li>❌ 包含不必要的图片</li>
                        <li>❌ HTML结构有问题</li>
                        <li>❌ 布局混乱</li>
                    </ul>
                </div>
                
                <div class="comparison-item">
                    <h4>修复后（正常）</h4>
                    <div style="position: relative; height: 40px; background: #f8f9fa; border: 1px solid #ddd;">
                        <div class="scene-clip" style="left: 10px; width: 120px;">
                            <div class="scene-content">
                                <span class="scene-name">开场场景</span>
                                <div class="scene-duration">00:08 / 00:12</div>
                                <div class="scene-video-info">开场视频1.mp4</div>
                            </div>
                        </div>
                    </div>
                    <ul style="margin-top: 10px; font-size: 12px;">
                        <li>✅ 没有多余符号</li>
                        <li>✅ 纯文字显示</li>
                        <li>✅ HTML结构正确</li>
                        <li>✅ 布局清晰</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>场景片段信息层次</h3>
            <div style="background: #67C23A; color: white; padding: 10px; border-radius: 6px; width: 200px;">
                <div style="font-weight: 600; font-size: 12px; margin-bottom: 2px;">开场场景</div>
                <div style="font-size: 10px; opacity: 0.8; margin-bottom: 1px;">00:08 / 00:12</div>
                <div style="font-size: 9px; opacity: 0.7;">开场视频1.mp4</div>
            </div>
            <p style="margin-top: 10px; font-size: 12px; color: #666;">
                <strong>信息层次：</strong><br>
                1. 场景名称（最重要，字体最大最粗）<br>
                2. 时长信息（中等重要，显示当前/最大时长）<br>
                3. 视频文件名（辅助信息，字体最小）
            </p>
        </div>

        <div class="demo-section">
            <h3>技术改进</h3>
            <ul>
                <li><strong>HTML结构修复</strong>：移除了多余的闭合标签，消除了 ">" 符号</li>
                <li><strong>布局优化</strong>：改为垂直布局，更好地利用空间</li>
                <li><strong>视觉简化</strong>：移除图片，专注于文字信息</li>
                <li><strong>信息层次</strong>：通过字体大小和透明度区分信息重要性</li>
                <li><strong>响应式设计</strong>：文字会根据容器宽度自动省略</li>
            </ul>
        </div>
    </div>

    <script>
        window.addEventListener('load', function() {
            console.log('✅ 视频轨道修复完成');
            console.log('🗑️ 已移除图片显示');
            console.log('🔧 HTML结构已修复');
            console.log('📝 文字布局已优化');
        });
    </script>
</body>
</html>
