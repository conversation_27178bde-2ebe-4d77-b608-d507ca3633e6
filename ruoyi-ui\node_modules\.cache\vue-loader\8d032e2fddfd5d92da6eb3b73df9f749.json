{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\progress.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\progress.vue", "mtime": 1754974569676}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753759483709}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnVmlkZW9Qcm9ncmVzcycsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOaooeadv+S/oeaBrwogICAgICB0ZW1wbGF0ZUluZm86IHsKICAgICAgICBpZDogJycsCiAgICAgICAgbmFtZTogJ+inhumikeaooeadvycKICAgICAgfSwKICAgICAgCiAgICAgIC8vIOWvueivneahhueKtuaAgQogICAgICBiYXRjaERpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBwbGF5RGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIAogICAgICAvLyDmibnph4/liarovpHmlbDph48KICAgICAgYmF0Y2hDb3VudDogMSwKICAgICAgCiAgICAgIC8vIOW9k+WJjeaSreaUvuinhumikQogICAgICBjdXJyZW50VmlkZW86IG51bGwsCiAgICAgIAogICAgICAvLyDnirbmgIHnrZvpgIkKICAgICAgc3RhdHVzRmlsdGVyOiAnJywKICAgICAgCiAgICAgIC8vIOe7n+iuoeaVsOaNrgogICAgICBzdGF0czogewogICAgICAgIGNvbXBsZXRlZDogOCwKICAgICAgICBwcm9jZXNzaW5nOiAyLAogICAgICAgIGZhaWxlZDogMSwKICAgICAgICB0b3RhbDogMTEKICAgICAgfSwKICAgICAgCiAgICAgIC8vIOinhumikeWIl+ihqAogICAgICB2aWRlb3M6IFsKICAgICAgICB7CiAgICAgICAgICBpZDogJ3YwMDEnLAogICAgICAgICAgbmFtZTogJ+S6p+WTgeWxleekuuinhumikV8wMDEnLAogICAgICAgICAgc3RhdHVzOiAnY29tcGxldGVkJywKICAgICAgICAgIHRodW1ibmFpbDogJ2h0dHBzOi8vdmlhLnBsYWNlaG9sZGVyLmNvbS8zMDB4MjAwLzRBOTBFMi9GRkZGRkY/dGV4dD1WaWRlbysxJywKICAgICAgICAgIHVybDogJ2h0dHBzOi8vZXhhbXBsZS5jb20vdmlkZW8xLm1wNCcsCiAgICAgICAgICBkdXJhdGlvbjogJzAwOjMyJywKICAgICAgICAgIGZpbGVTaXplOiAnMTUuMk1CJywKICAgICAgICAgIGNyZWF0ZVRpbWU6ICcyMDI1LTAxLTE4IDE0OjMwJywKICAgICAgICAgIHByb2dyZXNzOiAxMDAKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGlkOiAndjAwMicsCiAgICAgICAgICBuYW1lOiAn5Lqn5ZOB5bGV56S66KeG6aKRXzAwMicsCiAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnLAogICAgICAgICAgdGh1bWJuYWlsOiAnaHR0cHM6Ly92aWEucGxhY2Vob2xkZXIuY29tLzMwMHgyMDAvNTBDODc4L0ZGRkZGRj90ZXh0PVZpZGVvKzInLAogICAgICAgICAgdXJsOiAnaHR0cHM6Ly9leGFtcGxlLmNvbS92aWRlbzIubXA0JywKICAgICAgICAgIGR1cmF0aW9uOiAnMDA6MjgnLAogICAgICAgICAgZmlsZVNpemU6ICcxMi44TUInLAogICAgICAgICAgY3JlYXRlVGltZTogJzIwMjUtMDEtMTggMTQ6MzInLAogICAgICAgICAgcHJvZ3Jlc3M6IDEwMAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6ICd2MDAzJywKICAgICAgICAgIG5hbWU6ICfkuqflk4HlsZXnpLrop4bpopFfMDAzJywKICAgICAgICAgIHN0YXR1czogJ3Byb2Nlc3NpbmcnLAogICAgICAgICAgdGh1bWJuYWlsOiBudWxsLAogICAgICAgICAgdXJsOiBudWxsLAogICAgICAgICAgZHVyYXRpb246IG51bGwsCiAgICAgICAgICBmaWxlU2l6ZTogbnVsbCwKICAgICAgICAgIGNyZWF0ZVRpbWU6ICcyMDI1LTAxLTE4IDE0OjM1JywKICAgICAgICAgIHByb2dyZXNzOiA2NQogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6ICd2MDA0JywKICAgICAgICAgIG5hbWU6ICfkuqflk4HlsZXnpLrop4bpopFfMDA0JywKICAgICAgICAgIHN0YXR1czogJ2ZhaWxlZCcsCiAgICAgICAgICB0aHVtYm5haWw6IG51bGwsCiAgICAgICAgICB1cmw6IG51bGwsCiAgICAgICAgICBkdXJhdGlvbjogbnVsbCwKICAgICAgICAgIGZpbGVTaXplOiBudWxsLAogICAgICAgICAgY3JlYXRlVGltZTogJzIwMjUtMDEtMTggMTQ6MzYnLAogICAgICAgICAgcHJvZ3Jlc3M6IDAKICAgICAgICB9CiAgICAgIF0KICAgIH0KICB9LAogIAogIGNvbXB1dGVkOiB7CiAgICAvLyDnrZvpgInlkI7nmoTop4bpopHliJfooagKICAgIGZpbHRlcmVkVmlkZW9zKCkgewogICAgICBpZiAoIXRoaXMuc3RhdHVzRmlsdGVyKSB7CiAgICAgICAgcmV0dXJuIHRoaXMudmlkZW9zCiAgICAgIH0KICAgICAgcmV0dXJuIHRoaXMudmlkZW9zLmZpbHRlcih2aWRlbyA9PiB2aWRlby5zdGF0dXMgPT09IHRoaXMuc3RhdHVzRmlsdGVyKQogICAgfQogIH0sCiAgCiAgY3JlYXRlZCgpIHsKICAgIC8vIOiOt+WPluaooeadv0lECiAgICBjb25zdCB0ZW1wbGF0ZUlkID0gdGhpcy4kcm91dGUucXVlcnkudGVtcGxhdGVJZAogICAgaWYgKHRlbXBsYXRlSWQpIHsKICAgICAgdGhpcy5sb2FkVGVtcGxhdGUodGVtcGxhdGVJZCkKICAgIH0KICAgIAogICAgLy8g5a6a5pe25Yi35paw5aSE55CG5Lit55qE6KeG6aKR54q25oCBCiAgICB0aGlzLnN0YXJ0UHJvZ3Jlc3NQb2xsaW5nKCkKICB9LAogIAogIGJlZm9yZURlc3Ryb3koKSB7CiAgICAvLyDmuIXnkIblrprml7blmagKICAgIGlmICh0aGlzLnBvbGxpbmdUaW1lcikgewogICAgICBjbGVhckludGVydmFsKHRoaXMucG9sbGluZ1RpbWVyKQogICAgfQogIH0sCiAgCiAgbWV0aG9kczogewogICAgLy8g6L+U5Zue5LiK5LiA6aG1CiAgICBnb0JhY2soKSB7CiAgICAgIHRoaXMuJHJvdXRlci5nbygtMSkKICAgIH0sCiAgICAKICAgIC8vIOWKoOi9veaooeadv+S/oeaBrwogICAgbG9hZFRlbXBsYXRlKHRlbXBsYXRlSWQpIHsKICAgICAgdGhpcy50ZW1wbGF0ZUluZm8uaWQgPSB0ZW1wbGF0ZUlkCiAgICAgIC8vIFRPRE86IOS7jkFQSeWKoOi9veaooeadv+aVsOaNrgogICAgICBjb25zb2xlLmxvZygn5Yqg6L295qih5p2/OicsIHRlbXBsYXRlSWQpCiAgICB9LAogICAgCiAgICAvLyDliLfmlrDov5vluqYKICAgIHJlZnJlc2hQcm9ncmVzcygpIHsKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfov5vluqblt7LliLfmlrAnKQogICAgICAvLyBUT0RPOiDosIPnlKhBUEnliLfmlrDmlbDmja4KICAgIH0sCiAgICAKICAgIC8vIOW8gOWni+aWsOaJueasoQogICAgc3RhcnROZXdCYXRjaCgpIHsKICAgICAgdGhpcy5iYXRjaERpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICB9LAogICAgCiAgICAvLyDnoa7orqTmlrDlu7rmibnmrKEKICAgIGNvbmZpcm1OZXdCYXRjaCgpIHsKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDlvIDlp4vnlJ/miJAgJHt0aGlzLmJhdGNoQ291bnR9IOadoeaWsOinhumikWApCiAgICAgIHRoaXMuYmF0Y2hEaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgICAgCiAgICAgIC8vIFRPRE86IOiwg+eUqOaJuemHj+WJqui+kUFQSQogICAgICAvLyDmqKHmi5/mt7vliqDmlrDnmoTlpITnkIbkuK3op4bpopEKICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLmJhdGNoQ291bnQ7IGkrKykgewogICAgICAgIGNvbnN0IG5ld1ZpZGVvID0gewogICAgICAgICAgaWQ6ICd2JyArIERhdGUubm93KCkgKyBpLAogICAgICAgICAgbmFtZTogYOS6p+WTgeWxleekuuinhumikV8ke3RoaXMudmlkZW9zLmxlbmd0aCArIGkgKyAxfWAsCiAgICAgICAgICBzdGF0dXM6ICdwcm9jZXNzaW5nJywKICAgICAgICAgIHRodW1ibmFpbDogbnVsbCwKICAgICAgICAgIHVybDogbnVsbCwKICAgICAgICAgIGR1cmF0aW9uOiBudWxsLAogICAgICAgICAgZmlsZVNpemU6IG51bGwsCiAgICAgICAgICBjcmVhdGVUaW1lOiBuZXcgRGF0ZSgpLnRvTG9jYWxlU3RyaW5nKCkuc2xpY2UoMCwgMTYpLAogICAgICAgICAgcHJvZ3Jlc3M6IDAKICAgICAgICB9CiAgICAgICAgdGhpcy52aWRlb3MudW5zaGlmdChuZXdWaWRlbykKICAgICAgfQogICAgICAKICAgICAgLy8g5pu05paw57uf6K6hCiAgICAgIHRoaXMudXBkYXRlU3RhdHMoKQogICAgfSwKICAgIAogICAgLy8g5pKt5pS+6KeG6aKRCiAgICBwbGF5VmlkZW8odmlkZW8pIHsKICAgICAgdGhpcy5jdXJyZW50VmlkZW8gPSB2aWRlbwogICAgICB0aGlzLnBsYXlEaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgfSwKICAgIAogICAgLy8g5YGc5q2i6KeG6aKRCiAgICBzdG9wVmlkZW8oKSB7CiAgICAgIGlmICh0aGlzLiRyZWZzLnZpZGVvUGxheWVyKSB7CiAgICAgICAgdGhpcy4kcmVmcy52aWRlb1BsYXllci5wYXVzZSgpCiAgICAgIH0KICAgICAgdGhpcy5jdXJyZW50VmlkZW8gPSBudWxsCiAgICB9LAogICAgCiAgICAvLyDop4bpopHliqDovb3lrozmiJAKICAgIG9uVmlkZW9Mb2FkZWQoKSB7CiAgICAgIC8vIOinhumikeWFg+aVsOaNruWKoOi9veWujOaIkAogICAgfSwKICAgIAogICAgLy8g5LiL6L296KeG6aKRCiAgICBkb3dubG9hZFZpZGVvKHZpZGVvKSB7CiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5byA5aeL5LiL6L29OiAke3ZpZGVvLm5hbWV9YCkKICAgICAgLy8gVE9ETzog5a6e546w5LiL6L296YC76L6RCiAgICB9LAogICAgCiAgICAvLyDliIbkuqvop4bpopEKICAgIHNoYXJlVmlkZW8odmlkZW8pIHsKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDliIbkuqvpk77mjqXlt7LlpI3liLY6ICR7dmlkZW8ubmFtZX1gKQogICAgICAvLyBUT0RPOiDlrp7njrDliIbkuqvpgLvovpEKICAgIH0sCiAgICAKICAgIC8vIOWIoOmZpOinhumikQogICAgZGVsZXRlVmlkZW8odmlkZW8pIHsKICAgICAgdGhpcy4kY29uZmlybShg56Gu5a6a6KaB5Yig6Zmk6KeG6aKRICIke3ZpZGVvLm5hbWV9IiDlkJfvvJ9gLCAn56Gu6K6k5Yig6ZmkJywgewogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICBjb25zdCBpbmRleCA9IHRoaXMudmlkZW9zLmZpbmRJbmRleCh2ID0+IHYuaWQgPT09IHZpZGVvLmlkKQogICAgICAgIGlmIChpbmRleCA+IC0xKSB7CiAgICAgICAgICB0aGlzLnZpZGVvcy5zcGxpY2UoaW5kZXgsIDEpCiAgICAgICAgICB0aGlzLnVwZGF0ZVN0YXRzKCkKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJykKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KQogICAgfSwKICAgIAogICAgLy8g6YeN6K+V6KeG6aKRCiAgICByZXRyeVZpZGVvKHZpZGVvKSB7CiAgICAgIHZpZGVvLnN0YXR1cyA9ICdwcm9jZXNzaW5nJwogICAgICB2aWRlby5wcm9ncmVzcyA9IDAKICAgICAgdGhpcy51cGRhdGVTdGF0cygpCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg6YeN5paw5byA5aeL5aSE55CGOiAke3ZpZGVvLm5hbWV9YCkKICAgICAgLy8gVE9ETzog6LCD55So6YeN6K+VQVBJCiAgICB9LAogICAgCiAgICAvLyDojrflj5bnirbmgIHmlofmnKwKICAgIGdldFN0YXR1c1RleHQoc3RhdHVzKSB7CiAgICAgIGNvbnN0IHN0YXR1c01hcCA9IHsKICAgICAgICAnY29tcGxldGVkJzogJ+W3suWujOaIkCcsCiAgICAgICAgJ3Byb2Nlc3NpbmcnOiAn5aSE55CG5LitJywKICAgICAgICAnZmFpbGVkJzogJ+WkhOeQhuWksei0pScKICAgICAgfQogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgc3RhdHVzCiAgICB9LAogICAgCiAgICAvLyDmm7TmlrDnu5/orqHmlbDmja4KICAgIHVwZGF0ZVN0YXRzKCkgewogICAgICB0aGlzLnN0YXRzID0gewogICAgICAgIGNvbXBsZXRlZDogdGhpcy52aWRlb3MuZmlsdGVyKHYgPT4gdi5zdGF0dXMgPT09ICdjb21wbGV0ZWQnKS5sZW5ndGgsCiAgICAgICAgcHJvY2Vzc2luZzogdGhpcy52aWRlb3MuZmlsdGVyKHYgPT4gdi5zdGF0dXMgPT09ICdwcm9jZXNzaW5nJykubGVuZ3RoLAogICAgICAgIGZhaWxlZDogdGhpcy52aWRlb3MuZmlsdGVyKHYgPT4gdi5zdGF0dXMgPT09ICdmYWlsZWQnKS5sZW5ndGgsCiAgICAgICAgdG90YWw6IHRoaXMudmlkZW9zLmxlbmd0aAogICAgICB9CiAgICB9LAogICAgCiAgICAvLyDlvIDlp4vova7or6Lov5vluqYKICAgIHN0YXJ0UHJvZ3Jlc3NQb2xsaW5nKCkgewogICAgICB0aGlzLnBvbGxpbmdUaW1lciA9IHNldEludGVydmFsKCgpID0+IHsKICAgICAgICAvLyDmqKHmi5/ov5vluqbmm7TmlrAKICAgICAgICB0aGlzLnZpZGVvcy5mb3JFYWNoKHZpZGVvID0+IHsKICAgICAgICAgIGlmICh2aWRlby5zdGF0dXMgPT09ICdwcm9jZXNzaW5nJyAmJiB2aWRlby5wcm9ncmVzcyA8IDEwMCkgewogICAgICAgICAgICB2aWRlby5wcm9ncmVzcyArPSBNYXRoLnJhbmRvbSgpICogMTAKICAgICAgICAgICAgaWYgKHZpZGVvLnByb2dyZXNzID49IDEwMCkgewogICAgICAgICAgICAgIHZpZGVvLnN0YXR1cyA9ICdjb21wbGV0ZWQnCiAgICAgICAgICAgICAgdmlkZW8ucHJvZ3Jlc3MgPSAxMDAKICAgICAgICAgICAgICB2aWRlby50aHVtYm5haWwgPSBgaHR0cHM6Ly92aWEucGxhY2Vob2xkZXIuY29tLzMwMHgyMDAvNEE5MEUyL0ZGRkZGRj90ZXh0PVZpZGVvKyR7dmlkZW8uaWR9YAogICAgICAgICAgICAgIHZpZGVvLnVybCA9IGBodHRwczovL2V4YW1wbGUuY29tLyR7dmlkZW8uaWR9Lm1wNGAKICAgICAgICAgICAgICB2aWRlby5kdXJhdGlvbiA9ICcwMDonICsgKDI1ICsgTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogMjApKQogICAgICAgICAgICAgIHZpZGVvLmZpbGVTaXplID0gKDEwICsgTWF0aC5yYW5kb20oKSAqIDEwKS50b0ZpeGVkKDEpICsgJ01CJwogICAgICAgICAgICAgIHRoaXMudXBkYXRlU3RhdHMoKQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfSwgMzAwMCkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["progress.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "progress.vue", "sourceRoot": "src/views/store", "sourcesContent": ["<template>\n  <div class=\"video-progress\">\n    <!-- 头部工具栏 -->\n    <div class=\"header-toolbar\">\n      <div class=\"toolbar-left\">\n        <el-button icon=\"el-icon-arrow-left\" @click=\"goBack\">返回</el-button>\n        <h2 class=\"page-title\">\n          <i class=\"el-icon-video-camera\"></i>\n          剪辑进度 - {{ templateInfo.name }}\n        </h2>\n      </div>\n      <div class=\"toolbar-right\">\n        <el-button icon=\"el-icon-refresh\" @click=\"refreshProgress\">\n          刷新\n        </el-button>\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"startNewBatch\">\n          新建批次\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 统计信息 -->\n    <div class=\"stats-cards\">\n      <div class=\"stat-card\">\n        <div class=\"stat-icon completed\">\n          <i class=\"el-icon-check\"></i>\n        </div>\n        <div class=\"stat-info\">\n          <div class=\"stat-number\">{{ stats.completed }}</div>\n          <div class=\"stat-label\">已完成</div>\n        </div>\n      </div>\n      <div class=\"stat-card\">\n        <div class=\"stat-icon processing\">\n          <i class=\"el-icon-loading\"></i>\n        </div>\n        <div class=\"stat-info\">\n          <div class=\"stat-number\">{{ stats.processing }}</div>\n          <div class=\"stat-label\">处理中</div>\n        </div>\n      </div>\n      <div class=\"stat-card\">\n        <div class=\"stat-icon failed\">\n          <i class=\"el-icon-close\"></i>\n        </div>\n        <div class=\"stat-info\">\n          <div class=\"stat-number\">{{ stats.failed }}</div>\n          <div class=\"stat-label\">失败</div>\n        </div>\n      </div>\n      <div class=\"stat-card\">\n        <div class=\"stat-icon total\">\n          <i class=\"el-icon-document\"></i>\n        </div>\n        <div class=\"stat-info\">\n          <div class=\"stat-number\">{{ stats.total }}</div>\n          <div class=\"stat-label\">总计</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 视频列表 -->\n    <div class=\"videos-container\">\n      <div class=\"container-header\">\n        <h3>生成的视频</h3>\n        <div class=\"filter-controls\">\n          <el-select v-model=\"statusFilter\" placeholder=\"状态筛选\" size=\"small\" clearable>\n            <el-option label=\"全部\" value=\"\"></el-option>\n            <el-option label=\"已完成\" value=\"completed\"></el-option>\n            <el-option label=\"处理中\" value=\"processing\"></el-option>\n            <el-option label=\"失败\" value=\"failed\"></el-option>\n          </el-select>\n        </div>\n      </div>\n\n      <div class=\"videos-grid\">\n        <div \n          v-for=\"video in filteredVideos\" \n          :key=\"video.id\"\n          class=\"video-card\"\n          :class=\"video.status\"\n        >\n          <!-- 视频缩略图 -->\n          <div class=\"video-thumbnail\">\n            <img v-if=\"video.thumbnail\" :src=\"video.thumbnail\" :alt=\"video.name\" />\n            <div v-else class=\"thumbnail-placeholder\">\n              <i class=\"el-icon-video-camera\"></i>\n            </div>\n            \n            <!-- 状态覆盖层 -->\n            <div class=\"status-overlay\" v-if=\"video.status !== 'completed'\">\n              <div class=\"status-content\">\n                <i v-if=\"video.status === 'processing'\" class=\"el-icon-loading rotating\"></i>\n                <i v-else-if=\"video.status === 'failed'\" class=\"el-icon-warning-outline\"></i>\n                <p class=\"status-text\">{{ getStatusText(video.status) }}</p>\n                <div v-if=\"video.status === 'processing'\" class=\"progress-bar\">\n                  <div class=\"progress-fill\" :style=\"{ width: video.progress + '%' }\"></div>\n                </div>\n              </div>\n            </div>\n\n            <!-- 播放按钮 -->\n            <div v-if=\"video.status === 'completed'\" class=\"play-button\" @click=\"playVideo(video)\">\n              <i class=\"el-icon-video-play\"></i>\n            </div>\n\n            <!-- 视频时长 -->\n            <div v-if=\"video.duration\" class=\"video-duration\">{{ video.duration }}</div>\n          </div>\n\n          <!-- 视频信息 -->\n          <div class=\"video-info\">\n            <h4 class=\"video-name\">{{ video.name }}</h4>\n            <div class=\"video-meta\">\n              <span class=\"create-time\">{{ video.createTime }}</span>\n              <span class=\"video-size\" v-if=\"video.fileSize\">{{ video.fileSize }}</span>\n            </div>\n            \n            <!-- 操作按钮 -->\n            <div class=\"video-actions\" v-if=\"video.status === 'completed'\">\n              <el-button size=\"mini\" icon=\"el-icon-download\" @click=\"downloadVideo(video)\">\n                下载\n              </el-button>\n              <el-button size=\"mini\" icon=\"el-icon-share\" @click=\"shareVideo(video)\">\n                分享\n              </el-button>\n              <el-button size=\"mini\" icon=\"el-icon-delete\" type=\"danger\" @click=\"deleteVideo(video)\">\n                删除\n              </el-button>\n            </div>\n            \n            <!-- 重试按钮 -->\n            <div class=\"video-actions\" v-if=\"video.status === 'failed'\">\n              <el-button size=\"mini\" icon=\"el-icon-refresh\" type=\"primary\" @click=\"retryVideo(video)\">\n                重试\n              </el-button>\n              <el-button size=\"mini\" icon=\"el-icon-delete\" type=\"danger\" @click=\"deleteVideo(video)\">\n                删除\n              </el-button>\n            </div>\n          </div>\n        </div>\n\n        <!-- 空状态 -->\n        <div v-if=\"filteredVideos.length === 0\" class=\"empty-state\">\n          <i class=\"el-icon-video-camera-solid\"></i>\n          <h3>暂无视频</h3>\n          <p>还没有生成任何视频，点击\"新建批次\"开始剪辑</p>\n          <el-button type=\"primary\" @click=\"startNewBatch\">\n            新建批次\n          </el-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 新建批次对话框 -->\n    <el-dialog\n      title=\"新建剪辑批次\"\n      :visible.sync=\"batchDialogVisible\"\n      width=\"400px\"\n    >\n      <div class=\"batch-config\">\n        <h4>{{ templateInfo.name }}</h4>\n        <p>请选择要生成的视频数量：</p>\n        <el-input-number\n          v-model=\"batchCount\"\n          :min=\"1\"\n          :max=\"50\"\n          label=\"生成数量\"\n        ></el-input-number>\n        <p class=\"batch-tip\">最多可生成50条视频</p>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"batchDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmNewBatch\">开始剪辑</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 视频播放对话框 -->\n    <el-dialog\n      title=\"视频预览\"\n      :visible.sync=\"playDialogVisible\"\n      width=\"800px\"\n      @close=\"stopVideo\"\n    >\n      <div class=\"video-player\" v-if=\"currentVideo\">\n        <video \n          ref=\"videoPlayer\"\n          :src=\"currentVideo.url\" \n          controls \n          width=\"100%\"\n          @loadedmetadata=\"onVideoLoaded\"\n        ></video>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'VideoProgress',\n  data() {\n    return {\n      // 模板信息\n      templateInfo: {\n        id: '',\n        name: '视频模板'\n      },\n      \n      // 对话框状态\n      batchDialogVisible: false,\n      playDialogVisible: false,\n      \n      // 批量剪辑数量\n      batchCount: 1,\n      \n      // 当前播放视频\n      currentVideo: null,\n      \n      // 状态筛选\n      statusFilter: '',\n      \n      // 统计数据\n      stats: {\n        completed: 8,\n        processing: 2,\n        failed: 1,\n        total: 11\n      },\n      \n      // 视频列表\n      videos: [\n        {\n          id: 'v001',\n          name: '产品展示视频_001',\n          status: 'completed',\n          thumbnail: 'https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=Video+1',\n          url: 'https://example.com/video1.mp4',\n          duration: '00:32',\n          fileSize: '15.2MB',\n          createTime: '2025-01-18 14:30',\n          progress: 100\n        },\n        {\n          id: 'v002',\n          name: '产品展示视频_002',\n          status: 'completed',\n          thumbnail: 'https://via.placeholder.com/300x200/50C878/FFFFFF?text=Video+2',\n          url: 'https://example.com/video2.mp4',\n          duration: '00:28',\n          fileSize: '12.8MB',\n          createTime: '2025-01-18 14:32',\n          progress: 100\n        },\n        {\n          id: 'v003',\n          name: '产品展示视频_003',\n          status: 'processing',\n          thumbnail: null,\n          url: null,\n          duration: null,\n          fileSize: null,\n          createTime: '2025-01-18 14:35',\n          progress: 65\n        },\n        {\n          id: 'v004',\n          name: '产品展示视频_004',\n          status: 'failed',\n          thumbnail: null,\n          url: null,\n          duration: null,\n          fileSize: null,\n          createTime: '2025-01-18 14:36',\n          progress: 0\n        }\n      ]\n    }\n  },\n  \n  computed: {\n    // 筛选后的视频列表\n    filteredVideos() {\n      if (!this.statusFilter) {\n        return this.videos\n      }\n      return this.videos.filter(video => video.status === this.statusFilter)\n    }\n  },\n  \n  created() {\n    // 获取模板ID\n    const templateId = this.$route.query.templateId\n    if (templateId) {\n      this.loadTemplate(templateId)\n    }\n    \n    // 定时刷新处理中的视频状态\n    this.startProgressPolling()\n  },\n  \n  beforeDestroy() {\n    // 清理定时器\n    if (this.pollingTimer) {\n      clearInterval(this.pollingTimer)\n    }\n  },\n  \n  methods: {\n    // 返回上一页\n    goBack() {\n      this.$router.go(-1)\n    },\n    \n    // 加载模板信息\n    loadTemplate(templateId) {\n      this.templateInfo.id = templateId\n      // TODO: 从API加载模板数据\n      console.log('加载模板:', templateId)\n    },\n    \n    // 刷新进度\n    refreshProgress() {\n      this.$message.success('进度已刷新')\n      // TODO: 调用API刷新数据\n    },\n    \n    // 开始新批次\n    startNewBatch() {\n      this.batchDialogVisible = true\n    },\n    \n    // 确认新建批次\n    confirmNewBatch() {\n      this.$message.success(`开始生成 ${this.batchCount} 条新视频`)\n      this.batchDialogVisible = false\n      \n      // TODO: 调用批量剪辑API\n      // 模拟添加新的处理中视频\n      for (let i = 0; i < this.batchCount; i++) {\n        const newVideo = {\n          id: 'v' + Date.now() + i,\n          name: `产品展示视频_${this.videos.length + i + 1}`,\n          status: 'processing',\n          thumbnail: null,\n          url: null,\n          duration: null,\n          fileSize: null,\n          createTime: new Date().toLocaleString().slice(0, 16),\n          progress: 0\n        }\n        this.videos.unshift(newVideo)\n      }\n      \n      // 更新统计\n      this.updateStats()\n    },\n    \n    // 播放视频\n    playVideo(video) {\n      this.currentVideo = video\n      this.playDialogVisible = true\n    },\n    \n    // 停止视频\n    stopVideo() {\n      if (this.$refs.videoPlayer) {\n        this.$refs.videoPlayer.pause()\n      }\n      this.currentVideo = null\n    },\n    \n    // 视频加载完成\n    onVideoLoaded() {\n      // 视频元数据加载完成\n    },\n    \n    // 下载视频\n    downloadVideo(video) {\n      this.$message.success(`开始下载: ${video.name}`)\n      // TODO: 实现下载逻辑\n    },\n    \n    // 分享视频\n    shareVideo(video) {\n      this.$message.success(`分享链接已复制: ${video.name}`)\n      // TODO: 实现分享逻辑\n    },\n    \n    // 删除视频\n    deleteVideo(video) {\n      this.$confirm(`确定要删除视频 \"${video.name}\" 吗？`, '确认删除', {\n        type: 'warning'\n      }).then(() => {\n        const index = this.videos.findIndex(v => v.id === video.id)\n        if (index > -1) {\n          this.videos.splice(index, 1)\n          this.updateStats()\n          this.$message.success('删除成功')\n        }\n      }).catch(() => {})\n    },\n    \n    // 重试视频\n    retryVideo(video) {\n      video.status = 'processing'\n      video.progress = 0\n      this.updateStats()\n      this.$message.success(`重新开始处理: ${video.name}`)\n      // TODO: 调用重试API\n    },\n    \n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        'completed': '已完成',\n        'processing': '处理中',\n        'failed': '处理失败'\n      }\n      return statusMap[status] || status\n    },\n    \n    // 更新统计数据\n    updateStats() {\n      this.stats = {\n        completed: this.videos.filter(v => v.status === 'completed').length,\n        processing: this.videos.filter(v => v.status === 'processing').length,\n        failed: this.videos.filter(v => v.status === 'failed').length,\n        total: this.videos.length\n      }\n    },\n    \n    // 开始轮询进度\n    startProgressPolling() {\n      this.pollingTimer = setInterval(() => {\n        // 模拟进度更新\n        this.videos.forEach(video => {\n          if (video.status === 'processing' && video.progress < 100) {\n            video.progress += Math.random() * 10\n            if (video.progress >= 100) {\n              video.status = 'completed'\n              video.progress = 100\n              video.thumbnail = `https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=Video+${video.id}`\n              video.url = `https://example.com/${video.id}.mp4`\n              video.duration = '00:' + (25 + Math.floor(Math.random() * 20))\n              video.fileSize = (10 + Math.random() * 10).toFixed(1) + 'MB'\n              this.updateStats()\n            }\n          }\n        })\n      }, 3000)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.video-progress {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n}\n\n/* 头部工具栏 */\n.header-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  padding: 20px;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.toolbar-left {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.page-title {\n  margin: 0;\n  color: #303133;\n  font-size: 20px;\n  font-weight: 600;\n}\n\n.page-title i {\n  margin-right: 8px;\n  color: #409EFF;\n}\n\n.toolbar-right {\n  display: flex;\n  gap: 12px;\n}\n\n/* 统计卡片 */\n.stats-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  padding: 24px;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.stat-icon {\n  width: 48px;\n  height: 48px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20px;\n  color: white;\n}\n\n.stat-icon.completed {\n  background: #67C23A;\n}\n\n.stat-icon.processing {\n  background: #409EFF;\n}\n\n.stat-icon.failed {\n  background: #F56C6C;\n}\n\n.stat-icon.total {\n  background: #909399;\n}\n\n.stat-info {\n  flex: 1;\n}\n\n.stat-number {\n  font-size: 28px;\n  font-weight: 700;\n  color: #303133;\n  line-height: 1;\n  margin-bottom: 4px;\n}\n\n.stat-label {\n  color: #909399;\n  font-size: 14px;\n}\n\n/* 视频容器 */\n.videos-container {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n}\n\n.container-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.container-header h3 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #303133;\n}\n\n/* 视频网格 */\n.videos-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: 20px;\n  padding: 24px;\n}\n\n/* 视频卡片 */\n.video-card {\n  background: #f8f9fa;\n  border-radius: 8px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n.video-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n\n/* 视频缩略图 */\n.video-thumbnail {\n  position: relative;\n  width: 100%;\n  height: 160px;\n  background: #000;\n  overflow: hidden;\n}\n\n.video-thumbnail img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.thumbnail-placeholder {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #ddd;\n  color: #999;\n}\n\n.thumbnail-placeholder i {\n  font-size: 32px;\n}\n\n/* 状态覆盖层 */\n.status-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n}\n\n.status-content {\n  text-align: center;\n}\n\n.status-content i {\n  font-size: 32px;\n  margin-bottom: 12px;\n  display: block;\n}\n\n.status-text {\n  margin: 0 0 12px 0;\n  font-size: 14px;\n}\n\n.progress-bar {\n  width: 120px;\n  height: 4px;\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: 2px;\n  overflow: hidden;\n  margin: 0 auto;\n}\n\n.progress-fill {\n  height: 100%;\n  background: #409EFF;\n  transition: width 0.3s ease;\n}\n\n/* 播放按钮 */\n.play-button {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 48px;\n  height: 48px;\n  background: rgba(0, 0, 0, 0.7);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 20px;\n  cursor: pointer;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.video-card:hover .play-button {\n  opacity: 1;\n}\n\n.play-button:hover {\n  background: rgba(0, 0, 0, 0.9);\n}\n\n/* 视频时长 */\n.video-duration {\n  position: absolute;\n  bottom: 8px;\n  right: 8px;\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-size: 12px;\n}\n\n/* 视频信息 */\n.video-info {\n  padding: 16px;\n}\n\n.video-name {\n  margin: 0 0 8px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #303133;\n  line-height: 1.4;\n}\n\n.video-meta {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 12px;\n  font-size: 12px;\n  color: #909399;\n}\n\n.video-actions {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n/* 旋转动画 */\n.rotating {\n  animation: rotate 2s linear infinite;\n}\n\n@keyframes rotate {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 空状态 */\n.empty-state {\n  grid-column: 1 / -1;\n  text-align: center;\n  padding: 60px 20px;\n}\n\n.empty-state i {\n  font-size: 48px;\n  color: #C0C4CC;\n  margin-bottom: 16px;\n}\n\n.empty-state h3 {\n  margin: 0 0 8px 0;\n  color: #303133;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.empty-state p {\n  margin: 0 0 20px 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n/* 批量配置 */\n.batch-config {\n  text-align: center;\n  padding: 20px 0;\n}\n\n.batch-config h4 {\n  margin: 0 0 20px 0;\n  color: #303133;\n  font-size: 18px;\n}\n\n.batch-config p {\n  margin: 0 0 16px 0;\n  color: #606266;\n}\n\n.batch-tip {\n  margin-top: 12px !important;\n  color: #909399;\n  font-size: 12px;\n}\n\n/* 视频播放器 */\n.video-player {\n  text-align: center;\n}\n\n.video-player video {\n  max-width: 100%;\n  border-radius: 8px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .video-progress {\n    padding: 15px;\n  }\n\n  .header-toolbar {\n    flex-direction: column;\n    gap: 16px;\n    text-align: center;\n  }\n\n  .toolbar-right {\n    justify-content: center;\n  }\n\n  .stats-cards {\n    grid-template-columns: repeat(2, 1fr);\n    gap: 16px;\n  }\n\n  .videos-grid {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n}\n</style>\n"]}]}