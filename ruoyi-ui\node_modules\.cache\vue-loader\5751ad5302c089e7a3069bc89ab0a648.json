{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\editor.vue?vue&type=style&index=0&id=2fd681f5&scoped=true&lang=css", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\editor.vue", "mtime": 1755003825629}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753759480805}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753759474011}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753759476521}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi52aWRlby1lZGl0b3IgewogIGhlaWdodDogMTAwdmg7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGJhY2tncm91bmQ6ICNmNWY3ZmE7Cn0KCi8qIOWktOmDqOW3peWFt+agjyAqLwouZWRpdG9yLWhlYWRlciB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBwYWRkaW5nOiAxMnB4IDIwcHg7CiAgYmFja2dyb3VuZDogd2hpdGU7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNGU3ZWQ7CiAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwwLDAsMC4xKTsKfQoKLmhlYWRlci1sZWZ0IHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZ2FwOiAxNnB4Owp9CgoudGVtcGxhdGUtdGl0bGUgewogIG1hcmdpbjogMDsKICBmb250LXNpemU6IDE4cHg7CiAgZm9udC13ZWlnaHQ6IDYwMDsKICBjb2xvcjogIzMwMzEzMzsKfQoKLmhlYWRlci1yaWdodCB7CiAgZGlzcGxheTogZmxleDsKICBnYXA6IDEycHg7Cn0KCi8qIOS4u+e8lui+keWMuuWfnyAqLwouZWRpdG9yLW1haW4gewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleDogMTsKICBtaW4taGVpZ2h0OiAwOwp9CgovKiDpooTop4jpnaLmnb8gKi8KLnByZXZpZXctcGFuZWwgewogIGZsZXg6IDE7CiAgcGFkZGluZzogMjBweDsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICBib3JkZXItcmlnaHQ6IDFweCBzb2xpZCAjZTRlN2VkOwp9CgoucHJldmlldy1jb250YWluZXIgewogIGhlaWdodDogMTAwJTsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47Cn0KCi52aWRlby1wcmV2aWV3IHsKICBmbGV4OiAxOwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBtYXJnaW4tYm90dG9tOiAxNnB4OwogIHBhZGRpbmc6IDIwcHg7Cn0KCi5wcmV2aWV3LWZyYW1lIHsKICB3aWR0aDogMzAwcHg7CiAgaGVpZ2h0OiA1MzNweDsgLyogMzAwICogMTYvOSA9IDUzMy4zMyAqLwogIGJhY2tncm91bmQ6ICMwMDA7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMik7CiAgYm9yZGVyOiAxcHggc29saWQgI2RkZDsKICBvdmVyZmxvdzogaGlkZGVuOwp9CgovKiDlnLrmma/pooTop4jmoLflvI8gKi8KLnNjZW5lLXByZXZpZXcgewogIHdpZHRoOiAxMDAlOwogIGhlaWdodDogMTAwJTsKICBwb3NpdGlvbjogcmVsYXRpdmU7Cn0KCi5zY2VuZS1wcmV2aWV3IGltZyB7CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiAxMDAlOwogIG9iamVjdC1maXQ6IGNvdmVyOwp9Cgouc2NlbmUtb3ZlcmxheSB7CiAgcG9zaXRpb246IGFic29sdXRlOwogIGJvdHRvbTogMDsKICBsZWZ0OiAwOwogIHJpZ2h0OiAwOwogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCh0cmFuc3BhcmVudCwgcmdiYSgwLCAwLCAwLCAwLjgpKTsKICBjb2xvcjogd2hpdGU7CiAgcGFkZGluZzogMjBweCAxNnB4IDE2cHg7Cn0KCi5zY2VuZS10aXRsZSB7CiAgZm9udC1zaXplOiAxNnB4OwogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgbWFyZ2luLWJvdHRvbTogNHB4Owp9Cgouc2NlbmUtdmlkZW8tbmFtZSB7CiAgZm9udC1zaXplOiAxMnB4OwogIGNvbG9yOiAjY2NjOwogIG1hcmdpbi1ib3R0b206IDRweDsKfQoKLnNjZW5lLXRpbWUtaW5mbyB7CiAgZm9udC1zaXplOiAxMXB4OwogIGNvbG9yOiAjZmZkNzAwOwogIG1hcmdpbi1ib3R0b206IDhweDsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgZ2FwOiAycHg7Cn0KCi5zY2VuZS10ZXh0LXByZXZpZXcgewogIGZvbnQtc2l6ZTogMTFweDsKICBsaW5lLWhlaWdodDogMS40OwogIGNvbG9yOiAjZGRkOwogIGRpc3BsYXk6IC13ZWJraXQtYm94OwogIC13ZWJraXQtbGluZS1jbGFtcDogMzsKICAtd2Via2l0LWJveC1vcmllbnQ6IHZlcnRpY2FsOwogIG92ZXJmbG93OiBoaWRkZW47Cn0KCi5wcmV2aWV3LXBsYWNlaG9sZGVyIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgY29sb3I6ICM5MDkzOTk7CiAgcGFkZGluZzogNDBweCAyMHB4Owp9CgoucHJldmlldy1wbGFjZWhvbGRlciBpIHsKICBmb250LXNpemU6IDQ4cHg7CiAgbWFyZ2luLWJvdHRvbTogMTZweDsKICBkaXNwbGF5OiBibG9jazsKICBjb2xvcjogIzY2NjsKfQoKLnByZXZpZXctcGxhY2Vob2xkZXIgcCB7CiAgbWFyZ2luOiAwIDAgOHB4IDA7CiAgZm9udC1zaXplOiAxNnB4OwogIGZvbnQtd2VpZ2h0OiA1MDA7CiAgY29sb3I6ICM5MDkzOTk7Cn0KCi5wcmV2aWV3LWluZm8gewogIGZvbnQtc2l6ZTogMTJweCAhaW1wb3J0YW50OwogIG1hcmdpbjogMTJweCAwICFpbXBvcnRhbnQ7CiAgY29sb3I6ICM5OTkgIWltcG9ydGFudDsKfQoKLnByZXZpZXctdGlwcyB7CiAgbWFyZ2luLXRvcDogMjBweDsKICBwYWRkaW5nLXRvcDogMTZweDsKICBib3JkZXItdG9wOiAxcHggc29saWQgIzMzMzsKfQoKLnByZXZpZXctdGlwcyBwIHsKICBmb250LXNpemU6IDExcHggIWltcG9ydGFudDsKICBjb2xvcjogIzY2NiAhaW1wb3J0YW50OwogIG1hcmdpbjogNHB4IDAgIWltcG9ydGFudDsKfQoKLmN1cnJlbnQtdGltZS1kaXNwbGF5IHsKICBtYXJnaW46IDE2cHggMDsKICBwYWRkaW5nOiA4cHggMTJweDsKICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjE1LCAwLCAwLjEpOwogIGJvcmRlcjogMXB4IHNvbGlkICNmZmQ3MDA7CiAgYm9yZGVyLXJhZGl1czogNHB4Owp9CgouY3VycmVudC10aW1lLWRpc3BsYXkgcCB7CiAgbWFyZ2luOiAwICFpbXBvcnRhbnQ7CiAgZm9udC1zaXplOiAxNHB4ICFpbXBvcnRhbnQ7CiAgY29sb3I6ICNmZmQ3MDAgIWltcG9ydGFudDsKICBmb250LXdlaWdodDogNjAwICFpbXBvcnRhbnQ7Cn0KCi5wcmV2aWV3LWNvbnRyb2xzIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIHBhZGRpbmc6IDEycHggMDsKfQoKLnRpbWUtZGlzcGxheSB7CiAgZm9udC1mYW1pbHk6IG1vbm9zcGFjZTsKICBmb250LXNpemU6IDE0cHg7CiAgY29sb3I6ICM2MDYyNjY7Cn0KCi8qIOWxnuaAp+iuvue9rumdouadvyAqLwoucHJvcGVydGllcy1wYW5lbCB7CiAgd2lkdGg6IDMwMHB4OwogIGJhY2tncm91bmQ6IHdoaXRlOwogIGJvcmRlci1sZWZ0OiAxcHggc29saWQgI2U0ZTdlZDsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47Cn0KCi5wYW5lbC1oZWFkZXIgewogIHBhZGRpbmc6IDE2cHggMjBweDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U0ZTdlZDsKfQoKLnBhbmVsLWhlYWRlciBoNCB7CiAgbWFyZ2luOiAwOwogIGZvbnQtc2l6ZTogMTZweDsKICBmb250LXdlaWdodDogNjAwOwogIGNvbG9yOiAjMzAzMTMzOwp9CgoucGFuZWwtY29udGVudCB7CiAgZmxleDogMTsKICBvdmVyZmxvdy15OiBhdXRvOwogIHBhZGRpbmc6IDE2cHg7Cn0KCi8qIOaXtumXtOi9tOWMuuWfnyAqLwoudGltZWxpbmUtYXJlYSB7CiAgaGVpZ2h0OiAzMDBweDsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICBib3JkZXItdG9wOiAxcHggc29saWQgI2U0ZTdlZDsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgbWluLXdpZHRoOiAwOyAvKiDlhYHorrjmlLbnvKkgKi8KfQoKLnRpbWVsaW5lLWhlYWRlciB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBwYWRkaW5nOiAxMnB4IDIwcHg7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNGU3ZWQ7Cn0KCi50aW1lbGluZS1oZWFkZXIgaDQgewogIG1hcmdpbjogMDsKICBmb250LXNpemU6IDE0cHg7CiAgZm9udC13ZWlnaHQ6IDYwMDsKICBjb2xvcjogIzMwMzEzMzsKfQoKLnRpbWVsaW5lLWNvbnRhaW5lciB7CiAgZmxleDogMTsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKfQoKLyog5pe26Ze05Yi75bqm6KGMICovCi50aW1lLXJ1bGVyLXJvdyB7CiAgZGlzcGxheTogZmxleDsKICBoZWlnaHQ6IDMwcHg7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNGU3ZWQ7Cn0KCi50aW1lLXJ1bGVyLWxhYmVsIHsKICB3aWR0aDogMTUwcHg7CiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsKICBib3JkZXItcmlnaHQ6IDFweCBzb2xpZCAjZTRlN2VkOwogIGZsZXgtc2hyaW5rOiAwOwp9CgovKiDml7bpl7TliLvluqYgKi8KLnRpbWUtcnVsZXIgewogIGZsZXg6IDE7CiAgaGVpZ2h0OiAxMDAlOwogIGJhY2tncm91bmQ6ICNmYWZhZmE7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIGN1cnNvcjogcG9pbnRlcjsKICB1c2VyLXNlbGVjdDogbm9uZTsKICBvdmVyZmxvdzogaGlkZGVuOwp9CgoudGltZS1tYXJrcy1jb250YWluZXIgewogIHBvc2l0aW9uOiByZWxhdGl2ZTsKICBoZWlnaHQ6IDEwMCU7CiAgd2lkdGg6IDEwMCU7Cn0KCi50aW1lLW1hcmsgewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICB0b3A6IDA7CiAgaGVpZ2h0OiAxMDAlOwogIGZvbnQtc2l6ZTogMTFweDsKICBjb2xvcjogIzYwNjI2NjsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgcGFkZGluZy1sZWZ0OiA2cHg7CiAgYm9yZGVyLWxlZnQ6IDJweCBzb2xpZCAjYzBjNGNjOwogIHBvaW50ZXItZXZlbnRzOiBub25lOwogIGZvbnQtd2VpZ2h0OiA1MDA7Cn0KCi50aW1lLXRpY2sgewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICB0b3A6IDIycHg7CiAgd2lkdGg6IDFweDsKICBoZWlnaHQ6IDhweDsKICBiYWNrZ3JvdW5kOiAjZjBmMGYwOwogIHBvaW50ZXItZXZlbnRzOiBub25lOwp9CgoucGxheWhlYWQgewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICB0b3A6IDA7CiAgd2lkdGg6IDJweDsKICBoZWlnaHQ6IDEwMCU7CiAgYmFja2dyb3VuZDogI2ZmNDc1NzsKICB6LWluZGV4OiAxMDsKICBwb2ludGVyLWV2ZW50czogbm9uZTsKfQoKLnBsYXloZWFkOjpiZWZvcmUgewogIGNvbnRlbnQ6ICcnOwogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICB0b3A6IC00cHg7CiAgbGVmdDogLTRweDsKICB3aWR0aDogMTBweDsKICBoZWlnaHQ6IDEwcHg7CiAgYmFja2dyb3VuZDogI2ZmNDc1NzsKICBib3JkZXItcmFkaXVzOiA1MCU7Cn0KCi8qIOi9qOmBk+WuueWZqCAqLwoudHJhY2tzLWNvbnRhaW5lciB7CiAgZmxleDogMTsKfQoKLnRyYWNrLXJvdyB7CiAgaGVpZ2h0OiA0MHB4OwogIGRpc3BsYXk6IGZsZXg7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNGU3ZWQ7Cn0KCi50cmFjay1sYWJlbCB7CiAgd2lkdGg6IDE1MHB4OwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBwYWRkaW5nOiAwIDEycHg7CiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsKICBib3JkZXItcmlnaHQ6IDFweCBzb2xpZCAjZTRlN2VkOwogIGZvbnQtc2l6ZTogMTRweDsKICBjb2xvcjogIzYwNjI2NjsKICBmbGV4LXNocmluazogMDsKfQoKLnRyYWNrLWxhYmVsIGkgewogIG1hcmdpbi1yaWdodDogOHB4OwogIGZvbnQtc2l6ZTogMTZweDsKfQoKLnRyYWNrLWNvbnRlbnQgewogIGZsZXg6IDE7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIGJhY2tncm91bmQ6IHdoaXRlOwogIG92ZXJmbG93OiBoaWRkZW47Cn0KCi50cmFjay10aW1lbGluZSB7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIGhlaWdodDogMTAwJTsKICB3aWR0aDogMTAwJTsKfQoKLyog54mH5q615qC35byPICovCi5jbGlwIHsKICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgdG9wOiA0cHg7CiAgaGVpZ2h0OiAzMnB4OwogIGJhY2tncm91bmQ6ICM0MDlFRkY7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGN1cnNvcjogcG9pbnRlcjsKICB1c2VyLXNlbGVjdDogbm9uZTsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgcGFkZGluZzogMCA4cHg7CiAgY29sb3I6IHdoaXRlOwogIGZvbnQtc2l6ZTogMTJweDsKICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlOwp9CgouY2xpcDpob3ZlciB7CiAgYmFja2dyb3VuZDogIzMzN2VjYzsKICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7Cn0KCi5jbGlwLmFjdGl2ZSB7CiAgYmFja2dyb3VuZDogI0U2QTIzQzsKICBib3gtc2hhZG93OiAwIDAgMCAycHggcmdiYSgyMzAsIDE2MiwgNjAsIDAuMyk7Cn0KCi8qIOWcuuaZr+eJh+auteagt+W8jyAqLwouc2NlbmUtY2xpcCB7CiAgcG9zaXRpb246IGFic29sdXRlOwogIHRvcDogMnB4OwogIGhlaWdodDogMzZweDsKICBiYWNrZ3JvdW5kOiAjNjdDMjNBOwogIGJvcmRlci1yYWRpdXM6IDZweDsKICBjdXJzb3I6IHBvaW50ZXI7CiAgdXNlci1zZWxlY3Q6IG5vbmU7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIHBhZGRpbmc6IDA7CiAgY29sb3I6IHdoaXRlOwogIGZvbnQtc2l6ZTogMTFweDsKICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlOwogIG92ZXJmbG93OiBoaWRkZW47Cn0KCi5zY2VuZS1jbGlwOmhvdmVyIHsKICBiYWNrZ3JvdW5kOiAjNWRhZjM0OwogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTsKfQoKLnNjZW5lLWNsaXAuYWN0aXZlIHsKICBiYWNrZ3JvdW5kOiAjRTZBMjNDOwogIGJveC1zaGFkb3c6IDAgMCAwIDJweCByZ2JhKDIzMCwgMTYyLCA2MCwgMC4zKTsKfQoKLnNjZW5lLWNvbnRlbnQgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICB3aWR0aDogMTAwJTsKICBoZWlnaHQ6IDEwMCU7CiAgcGFkZGluZzogNHB4IDhweDsKICBvdmVyZmxvdzogaGlkZGVuOwp9Cgouc2NlbmUtbmFtZSB7CiAgZGlzcGxheTogYmxvY2s7CiAgZm9udC13ZWlnaHQ6IDYwMDsKICBmb250LXNpemU6IDEycHg7CiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsKICBvdmVyZmxvdzogaGlkZGVuOwogIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOwogIGxpbmUtaGVpZ2h0OiAxLjI7CiAgbWFyZ2luLWJvdHRvbTogMnB4Owp9Cgouc2NlbmUtZHVyYXRpb24gewogIGZvbnQtc2l6ZTogMTBweDsKICBvcGFjaXR5OiAwLjg7CiAgbGluZS1oZWlnaHQ6IDE7CiAgbWFyZ2luLWJvdHRvbTogMXB4Owp9Cgouc2NlbmUtZHVyYXRpb24gLm1heC1kdXJhdGlvbiB7CiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC42KTsKICBmb250LXNpemU6IDlweDsKfQoKLnNjZW5lLXZpZGVvLWluZm8gewogIGZvbnQtc2l6ZTogOXB4OwogIG9wYWNpdHk6IDAuNzsKICB3aGl0ZS1zcGFjZTogbm93cmFwOwogIG92ZXJmbG93OiBoaWRkZW47CiAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7Cn0KCi8qIOaWh+ahiOeJh+auteagt+W8jyAqLwoudGV4dC1jbGlwIHsKICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgdG9wOiA0cHg7CiAgaGVpZ2h0OiAzMnB4OwogIGJhY2tncm91bmQ6ICNGNTZDNkM7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGN1cnNvcjogcG9pbnRlcjsKICB1c2VyLXNlbGVjdDogbm9uZTsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgcGFkZGluZzogMCA4cHg7CiAgY29sb3I6IHdoaXRlOwogIGZvbnQtc2l6ZTogMTFweDsKICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlOwp9CgoudGV4dC1jbGlwOmhvdmVyIHsKICBiYWNrZ3JvdW5kOiAjZjQ1NjU2OwogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTsKfQoKLnRleHQtY2xpcC5hY3RpdmUgewogIGJhY2tncm91bmQ6ICNFNkEyM0M7CiAgYm94LXNoYWRvdzogMCAwIDAgMnB4IHJnYmEoMjMwLCAxNjIsIDYwLCAwLjMpOwp9CgoudGV4dC1jb250ZW50IHsKICBmbGV4OiAxOwogIG92ZXJmbG93OiBoaWRkZW47Cn0KCi50ZXh0LXByZXZpZXcgewogIGRpc3BsYXk6IGJsb2NrOwogIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsKICBmb250LXdlaWdodDogNTAwOwogIGxpbmUtaGVpZ2h0OiAxLjI7Cn0KCi50ZXh0LWR1cmF0aW9uIHsKICBmb250LXNpemU6IDEwcHg7CiAgb3BhY2l0eTogMC44Owp9CgouY2xpcC1jb250ZW50IHsKICBmbGV4OiAxOwogIG92ZXJmbG93OiBoaWRkZW47Cn0KCi5jbGlwLW5hbWUgewogIGRpc3BsYXk6IGJsb2NrOwogIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsKICBmb250LXdlaWdodDogNTAwOwp9CgouY2xpcC1kdXJhdGlvbiB7CiAgZm9udC1zaXplOiAxMHB4OwogIG9wYWNpdHk6IDAuODsKfQoKLm1heC1kdXJhdGlvbiB7CiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC42KTsKICBmb250LXNpemU6IDlweDsKfQoKLyog6LCD5pW05omL5p+EICovCi5yZXNpemUtaGFuZGxlIHsKICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgdG9wOiAwOwogIGJvdHRvbTogMDsKICB3aWR0aDogNHB4OwogIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKTsKICBjdXJzb3I6IGV3LXJlc2l6ZTsKICBvcGFjaXR5OiAwOwogIHRyYW5zaXRpb246IG9wYWNpdHkgMC4ycyBlYXNlOwp9CgoucmVzaXplLWhhbmRsZS5sZWZ0IHsKICBsZWZ0OiAwOwp9CgoucmVzaXplLWhhbmRsZS5yaWdodCB7CiAgcmlnaHQ6IDA7Cn0KCi5jbGlwOmhvdmVyIC5yZXNpemUtaGFuZGxlIHsKICBvcGFjaXR5OiAxOwp9CgovKiDovajpgZPnsbvlnovmoLflvI8gKi8KLnRyYWNrLXJvdy52aWRlbyAuY2xpcCB7CiAgYmFja2dyb3VuZDogIzQwOUVGRjsKfQoKLnRyYWNrLXJvdy5hdWRpbyAuY2xpcCB7CiAgYmFja2dyb3VuZDogIzY3QzIzQTsKfQoKLnRyYWNrLXJvdy50ZXh0IC5jbGlwIHsKICBiYWNrZ3JvdW5kOiAjRTZBMjNDOwp9CgoudHJhY2stcm93LmVmZmVjdCAuY2xpcCB7CiAgYmFja2dyb3VuZDogI0Y1NkM2QzsKfQoKLyog5om56YeP6YWN572uICovCi5iYXRjaC1jb25maWcgewogIHRleHQtYWxpZ246IGNlbnRlcjsKICBwYWRkaW5nOiAyMHB4IDA7Cn0KCi5iYXRjaC1jb25maWcgaDQgewogIG1hcmdpbjogMCAwIDIwcHggMDsKICBjb2xvcjogIzMwMzEzMzsKICBmb250LXNpemU6IDE4cHg7Cn0KCi5iYXRjaC1jb25maWcgcCB7CiAgbWFyZ2luOiAwIDAgMTZweCAwOwogIGNvbG9yOiAjNjA2MjY2Owp9CgouYmF0Y2gtdGlwIHsKICBtYXJnaW4tdG9wOiAxMnB4ICFpbXBvcnRhbnQ7CiAgY29sb3I6ICM5MDkzOTk7CiAgZm9udC1zaXplOiAxMnB4Owp9CgovKiDop4bpopHlupPlr7nor53moYbmoLflvI8gKi8KLnZpZGVvLWxpYnJhcnktY29udGVudCBoNCB7CiAgbWFyZ2luOiAwIDAgMTZweCAwOwogIGNvbG9yOiAjMzAzMTMzOwogIGZvbnQtc2l6ZTogMTZweDsKICBmb250LXdlaWdodDogNjAwOwp9CgoudmlkZW8tZ3JpZCB7CiAgZGlzcGxheTogZ3JpZDsKICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpbGwsIG1pbm1heCgxNjBweCwgMWZyKSk7CiAgZ2FwOiAxNnB4OwogIG1heC1oZWlnaHQ6IDQwMHB4OwogIG92ZXJmbG93LXk6IGF1dG87Cn0KCi52aWRlby1pdGVtIHsKICBjdXJzb3I6IHBvaW50ZXI7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIG92ZXJmbG93OiBoaWRkZW47CiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsKICBib3JkZXI6IDJweCBzb2xpZCB0cmFuc3BhcmVudDsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKfQoKLnZpZGVvLWl0ZW06aG92ZXIgewogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTsKICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7CiAgYm9yZGVyLWNvbG9yOiAjNDA5RUZGOwp9CgoudmlkZW8tdGh1bWJuYWlsIHsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiA5MHB4Owp9CgoudmlkZW8tdGh1bWJuYWlsIGltZyB7CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiAxMDAlOwogIG9iamVjdC1maXQ6IGNvdmVyOwogIGRpc3BsYXk6IGJsb2NrOwp9CgoudmlkZW8tZHVyYXRpb24tYmFkZ2UgewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICBib3R0b206IDRweDsKICByaWdodDogNHB4OwogIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC43KTsKICBjb2xvcjogd2hpdGU7CiAgcGFkZGluZzogMnB4IDZweDsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgZm9udC1zaXplOiAxMHB4OwogIGZvbnQtd2VpZ2h0OiA1MDA7Cn0KCi52aWRlby1pbmZvIHsKICBwYWRkaW5nOiA4cHg7CiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsKfQoKLnZpZGVvLW5hbWUgewogIG1hcmdpbjogMCAwIDRweCAwOwogIGZvbnQtc2l6ZTogMTJweDsKICBjb2xvcjogIzMwMzEzMzsKICBmb250LXdlaWdodDogNTAwOwogIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsKfQoKLnZpZGVvLWR1cmF0aW9uLXRleHQgewogIG1hcmdpbjogMDsKICBmb250LXNpemU6IDExcHg7CiAgY29sb3I6ICM5MDkzOTk7Cn0KCi8qIOWTjeW6lOW8j+iuvuiuoSAtIDk6MTYg6KeG6aKR6aKE6KeI6YCC6YWNICovCkBtZWRpYSAobWF4LXdpZHRoOiAxMjAwcHgpIHsKICAucHJldmlldy1mcmFtZSB7CiAgICB3aWR0aDogMjUwcHg7CiAgICBoZWlnaHQ6IDQ0NHB4OyAvKiAyNTAgKiAxNi85ID0gNDQ0LjQ0ICovCiAgfQp9CgpAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHsKICAuZWRpdG9yLW1haW4gewogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICB9CgogIC5wcmV2aWV3LXBhbmVsIHsKICAgIGJvcmRlci1yaWdodDogbm9uZTsKICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTRlN2VkOwogIH0KCiAgLnByb3BlcnRpZXMtcGFuZWwgewogICAgd2lkdGg6IDEwMCU7CiAgICBtYXgtaGVpZ2h0OiAzMDBweDsKICB9CgogIC5wcmV2aWV3LWZyYW1lIHsKICAgIHdpZHRoOiAyMDBweDsKICAgIGhlaWdodDogMzU2cHg7IC8qIDIwMCAqIDE2LzkgPSAzNTUuNTYgKi8KICB9CgogIC50aW1lbGluZS1hcmVhIHsKICAgIGhlaWdodDogMjUwcHg7CiAgfQp9CgpAbWVkaWEgKG1heC13aWR0aDogNDgwcHgpIHsKICAucHJldmlldy1mcmFtZSB7CiAgICB3aWR0aDogMTUwcHg7CiAgICBoZWlnaHQ6IDI2N3B4OyAvKiAxNTAgKiAxNi85ID0gMjY2LjY3ICovCiAgfQoKICAucHJldmlldy1wbGFjZWhvbGRlciB7CiAgICBwYWRkaW5nOiAyMHB4IDEwcHg7CiAgfQoKICAucHJldmlldy1wbGFjZWhvbGRlciBpIHsKICAgIGZvbnQtc2l6ZTogMzJweDsKICB9CgogIC5wcmV2aWV3LXBsYWNlaG9sZGVyIHAgewogICAgZm9udC1zaXplOiAxNHB4OwogIH0KCiAgLnByZXZpZXctdGlwcyBwIHsKICAgIGZvbnQtc2l6ZTogMTBweCAhaW1wb3J0YW50OwogIH0KfQo="}, {"version": 3, "sources": ["editor.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqnCA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "editor.vue", "sourceRoot": "src/views/store", "sourcesContent": ["<template>\n  <div class=\"video-editor\">\n    <!-- 头部工具栏 -->\n    <div class=\"editor-header\">\n      <div class=\"header-left\">\n        <el-button icon=\"el-icon-arrow-left\" @click=\"goBack\">返回</el-button>\n        <h3 class=\"template-title\">{{ templateInfo.name }}</h3>\n      </div>\n      <div class=\"header-right\">\n        <el-button @click=\"saveTemplate\">保存</el-button>\n        <el-button type=\"primary\" @click=\"showBatchDialog\">开始剪辑</el-button>\n      </div>\n    </div>\n\n    <!-- 主编辑区域 -->\n    <div class=\"editor-main\">\n      <!-- 预览面板 -->\n      <div class=\"preview-panel\">\n        <div class=\"preview-container\">\n          <div class=\"video-preview\">\n            <div class=\"preview-frame\">\n              <!-- 显示选中场景的视频 -->\n              <div v-if=\"selectedScene && selectedScene.currentVideo\" class=\"scene-preview\">\n                <img :src=\"selectedScene.currentVideo.thumbnail\" :alt=\"selectedScene.name\" />\n                <div class=\"scene-overlay\">\n                  <div class=\"scene-title\">{{ selectedScene.name }}</div>\n                  <div class=\"scene-video-name\">{{ selectedScene.currentVideo.name }}</div>\n                  <div class=\"scene-time-info\">\n                    <span>当前时间: {{ formatTime(currentTime) }}</span>\n                    <span>场景时间: {{ formatTime(currentTime - selectedScene.startTime) }}</span>\n                  </div>\n                  <div class=\"scene-text-preview\">{{ selectedScene.textSegment }}</div>\n                </div>\n              </div>\n\n              <!-- 默认预览占位符 -->\n              <div v-else class=\"preview-placeholder\">\n                <i class=\"el-icon-video-play\"></i>\n                <p>视频预览</p>\n                <p class=\"preview-info\">{{ getPreviewResolution() }} • {{ templateInfo.fps }}fps</p>\n                <div class=\"current-time-display\">\n                  <p>当前时间: {{ formatTime(currentTime) }}</p>\n                </div>\n                <div class=\"preview-tips\">\n                  <p>9:16 竖屏比例</p>\n                  <p>适配短视频平台</p>\n                  <p>点击时间轴查看预览</p>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div class=\"preview-controls\">\n            <el-button-group>\n              <el-button icon=\"el-icon-video-play\" size=\"small\">播放</el-button>\n              <el-button icon=\"el-icon-video-pause\" size=\"small\">暂停</el-button>\n              <el-button icon=\"el-icon-refresh-left\" size=\"small\">重置</el-button>\n            </el-button-group>\n            <div class=\"time-display\">00:00 / 02:30</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 属性设置面板 -->\n      <div class=\"properties-panel\">\n        <div class=\"panel-header\">\n          <h4>属性设置</h4>\n        </div>\n        <div class=\"panel-content\">\n          <el-collapse v-model=\"activeCollapse\">\n            <el-collapse-item title=\"视频设置\" name=\"video\">\n              <el-form label-width=\"80px\" size=\"small\">\n                <el-form-item label=\"背景色\">\n                  <el-color-picker v-model=\"videoSettings.backgroundColor\"></el-color-picker>\n                </el-form-item>\n                <el-form-item label=\"亮度\">\n                  <el-slider v-model=\"videoSettings.brightness\" :min=\"-100\" :max=\"100\"></el-slider>\n                </el-form-item>\n                <el-form-item label=\"对比度\">\n                  <el-slider v-model=\"videoSettings.contrast\" :min=\"-100\" :max=\"100\"></el-slider>\n                </el-form-item>\n                <el-form-item label=\"饱和度\">\n                  <el-slider v-model=\"videoSettings.saturation\" :min=\"-100\" :max=\"100\"></el-slider>\n                </el-form-item>\n              </el-form>\n            </el-collapse-item>\n            \n            <el-collapse-item title=\"音频设置\" name=\"audio\">\n              <el-form label-width=\"80px\" size=\"small\">\n                <el-form-item label=\"主音量\">\n                  <el-slider v-model=\"audioSettings.masterVolume\" :max=\"100\"></el-slider>\n                </el-form-item>\n                <el-form-item label=\"背景音乐\">\n                  <el-slider v-model=\"audioSettings.bgmVolume\" :max=\"100\"></el-slider>\n                </el-form-item>\n                <el-form-item label=\"音效\">\n                  <el-slider v-model=\"audioSettings.sfxVolume\" :max=\"100\"></el-slider>\n                </el-form-item>\n              </el-form>\n            </el-collapse-item>\n            \n            <el-collapse-item title=\"文字设置\" name=\"text\">\n              <el-form label-width=\"80px\" size=\"small\">\n                <el-form-item label=\"字体\">\n                  <el-select v-model=\"textSettings.fontFamily\" size=\"small\">\n                    <el-option label=\"微软雅黑\" value=\"Microsoft YaHei\"></el-option>\n                    <el-option label=\"宋体\" value=\"SimSun\"></el-option>\n                    <el-option label=\"黑体\" value=\"SimHei\"></el-option>\n                  </el-select>\n                </el-form-item>\n                <el-form-item label=\"字号\">\n                  <el-input-number v-model=\"textSettings.fontSize\" :min=\"12\" :max=\"72\" size=\"small\"></el-input-number>\n                </el-form-item>\n                <el-form-item label=\"颜色\">\n                  <el-color-picker v-model=\"textSettings.color\"></el-color-picker>\n                </el-form-item>\n              </el-form>\n            </el-collapse-item>\n          </el-collapse>\n        </div>\n      </div>\n    </div>\n\n    <!-- 时间轴区域 -->\n    <div class=\"timeline-area\">\n      <div class=\"timeline-header\">\n        <h4>时间轴</h4>\n        <div class=\"timeline-controls\">\n          <el-button-group size=\"small\">\n            <el-button icon=\"el-icon-zoom-in\">放大</el-button>\n            <el-button icon=\"el-icon-zoom-out\">缩小</el-button>\n          </el-button-group>\n        </div>\n      </div>\n      \n      <div class=\"timeline-container\">\n        <!-- 时间刻度 -->\n        <div class=\"time-ruler-row\">\n          <div class=\"time-ruler-label\"></div>\n          <div class=\"time-ruler\" @click=\"onTimelineClick\" @mousedown=\"onTimelineDragStart\">\n            <div class=\"time-marks-container\" :style=\"{ width: timelineWidth + 'px' }\">\n              <!-- 每15秒一个主刻度 -->\n              <div class=\"time-mark\" v-for=\"i in 5\" :key=\"i\" :style=\"{left: (i-1) * 150 + 'px'}\">\n                {{ formatTime((i-1) * 15) }}\n              </div>\n              <!-- 每5秒一个小刻度 -->\n              <div class=\"time-tick\" v-for=\"i in 11\" :key=\"'tick-' + i\" :style=\"{left: (i * 50 + 25) + 'px'}\"></div>\n              <!-- 播放指针 -->\n              <div class=\"playhead\" :style=\"{left: currentTime * pixelsPerSecond + 'px'}\"></div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 轨道区域 -->\n        <div class=\"tracks-container\">\n          <!-- 视频场景轨道 -->\n          <div class=\"track-row video\">\n            <div class=\"track-label\">\n              <i class=\"el-icon-video-camera\"></i>\n              <span>视频场景</span>\n              <el-button size=\"mini\" type=\"primary\" @click=\"showAddSceneDialog\" style=\"margin-left: 8px;\">\n                <i class=\"el-icon-plus\"></i>\n              </el-button>\n            </div>\n            <div class=\"track-content\" @drop=\"onDropScene($event)\" @dragover.prevent\">\n              <div class=\"track-timeline\" :style=\"{ width: timelineWidth + 'px' }\">\n                <div\n                  v-for=\"scene in scenes\"\n                  :key=\"scene.id\"\n                  class=\"scene-clip\"\n                  :style=\"getSceneStyle(scene)\"\n                  @click=\"selectScene(scene)\"\n                  :class=\"{ active: selectedScene && selectedScene.id === scene.id }\"\n                  draggable=\"true\"\n                  @dragstart=\"onDragStartScene($event, scene)\"\n                >\n                  <div class=\"scene-content\">\n                    <span class=\"scene-name\">{{ scene.name || 'Scene' }}</span>\n                    <div class=\"scene-duration\">\n                      {{ formatTime(scene.duration) }}\n                      <span v-if=\"scene.maxDuration\" class=\"max-duration\">\n                        / {{ formatTime(scene.maxDuration) }}\n                      </span>\n                    </div>\n                    <div class=\"scene-video-info\" v-if=\"scene.currentVideo\">\n                      {{ scene.currentVideo.name }}\n                    </div>\n                  </div>\n                  <!-- 调整手柄 -->\n                  <div class=\"resize-handle left\" @mousedown=\"startResizeScene($event, scene, 'left')\"></div>\n                  <div class=\"resize-handle right\" @mousedown=\"startResizeScene($event, scene, 'right')\"></div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 文案轨道 -->\n          <div class=\"track-row text\">\n            <div class=\"track-label\">\n              <i class=\"el-icon-edit\"></i>\n              <span>文案</span>\n              <el-button size=\"mini\" type=\"success\" @click=\"segmentText\" style=\"margin-left: 8px;\">\n                分段\n              </el-button>\n            </div>\n            <div class=\"track-content\">\n              <div class=\"track-timeline\" :style=\"{ width: timelineWidth + 'px' }\">\n                <div\n                  v-for=\"(scene, index) in scenes\"\n                  :key=\"'text-' + scene.id\"\n                  class=\"text-clip\"\n                  :style=\"getSceneStyle(scene)\"\n                  @click=\"selectTextSegment(scene, index)\"\n                  :class=\"{ active: selectedTextSegment && selectedTextSegment.id === scene.id }\"\n                >\n                  <div class=\"text-content\">\n                    <span class=\"text-preview\">{{ getTextPreview(scene.textSegment) }}</span>\n                    <div class=\"text-duration\">{{ formatTime(scene.duration) }}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 其他轨道 -->\n          <div\n            v-for=\"track in tracks.filter(t => t.type !== 'video' && t.type !== 'text')\"\n            :key=\"track.id\"\n            class=\"track-row\"\n            :class=\"track.type\"\n          >\n            <div class=\"track-label\">\n              <i :class=\"track.icon\"></i>\n              <span>{{ track.name }}</span>\n            </div>\n            <div class=\"track-content\" @drop=\"onDrop($event, track)\" @dragover.prevent>\n              <div class=\"track-timeline\" :style=\"{ width: timelineWidth + 'px' }\">\n                <div\n                  v-for=\"clip in track.clips\"\n                  :key=\"clip.id\"\n                  class=\"clip\"\n                  :style=\"getClipStyle(clip)\"\n                  @click=\"selectClip(clip)\"\n                  :class=\"{ active: selectedClip && selectedClip.id === clip.id }\"\n                  draggable=\"true\"\n                  @dragstart=\"onDragStart($event, clip)\"\n                >\n                  <div class=\"clip-content\">\n                    <span class=\"clip-name\">{{ clip.name }}</span>\n                    <div class=\"clip-duration\">\n                      {{ formatTime(clip.duration) }}\n                      <span v-if=\"clip.maxDuration || clip.originalDuration\" class=\"max-duration\">\n                        / {{ formatTime(clip.maxDuration || clip.originalDuration) }}\n                      </span>\n                    </div>\n                  </div>\n                  <!-- 调整手柄 -->\n                  <div class=\"resize-handle left\" @mousedown=\"startResize($event, clip, 'left')\"></div>\n                  <div class=\"resize-handle right\" @mousedown=\"startResize($event, clip, 'right')\"></div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 对话框区域 -->\n    <!-- 批量剪辑对话框 -->\n    <el-dialog\n      title=\"开始批量剪辑\"\n      :visible.sync=\"batchDialogVisible\"\n      width=\"400px\"\n    >\n      <div class=\"batch-config\">\n        <h4>{{ templateInfo.name }}</h4>\n        <p>请选择要生成的视频数量：</p>\n        <el-input-number\n          v-model=\"batchCount\"\n          :min=\"1\"\n          :max=\"50\"\n          label=\"生成数量\"\n        ></el-input-number>\n        <p class=\"batch-tip\">最多可生成50条视频</p>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"batchDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"startBatchClip\">开始剪辑</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 添加场景对话框 -->\n    <el-dialog\n      title=\"添加视频场景\"\n      :visible.sync=\"addSceneDialogVisible\"\n      width=\"500px\"\n    >\n      <el-form :model=\"sceneForm\" label-width=\"100px\">\n        <el-form-item label=\"场景名称\">\n          <el-input v-model=\"sceneForm.name\" placeholder=\"请输入场景名称\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"场景时长\">\n          <el-input-number v-model=\"sceneForm.duration\" :min=\"1\" :max=\"60\" label=\"秒\"></el-input-number>\n          <span style=\"margin-left: 8px; color: #909399;\">秒</span>\n        </el-form-item>\n        <el-form-item label=\"视频库\">\n          <el-select v-model=\"sceneForm.videoLibraryId\" placeholder=\"选择视频库\">\n            <el-option\n              v-for=\"lib in videoLibraries\"\n              :key=\"lib.id\"\n              :label=\"lib.name\"\n              :value=\"lib.id\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"addSceneDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmAddScene\">确定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 视频库选择对话框 -->\n    <el-dialog\n      title=\"选择视频库\"\n      :visible.sync=\"videoLibraryDialogVisible\"\n      width=\"800px\"\n    >\n      <div class=\"video-library-content\" v-if=\"selectedVideoLibrary\">\n        <h4>{{ selectedVideoLibrary.name }}</h4>\n        <div class=\"video-grid\">\n          <div\n            v-for=\"video in selectedVideoLibrary.videos\"\n            :key=\"video.id\"\n            class=\"video-item\"\n            @click=\"selectVideoFromLibrary(video)\"\n          >\n            <div class=\"video-thumbnail\">\n              <img :src=\"video.thumbnail\" :alt=\"video.name\" />\n              <div class=\"video-duration-badge\">{{ formatTime(video.duration) }}</div>\n            </div>\n            <div class=\"video-info\">\n              <p class=\"video-name\">{{ video.name }}</p>\n              <p class=\"video-duration-text\">时长: {{ formatTime(video.duration) }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"videoLibraryDialogVisible = false\">关闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'VideoEditor',\n  data() {\n    return {\n      // 模板信息\n      templateInfo: {\n        id: '',\n        name: '视频模板',\n        resolution: '1080x1920',\n        fps: '30'\n      },\n      \n      // 批量剪辑\n      batchDialogVisible: false,\n      batchCount: 1,\n\n      // 场景管理对话框\n      addSceneDialogVisible: false,\n      sceneForm: {\n        name: '',\n        duration: 10,\n        videoLibraryId: ''\n      },\n\n      // 视频库选择对话框\n      videoLibraryDialogVisible: false,\n      selectedVideoLibrary: null,\n\n      // 属性面板\n      activeCollapse: ['video'],\n\n      // 设置项\n      videoSettings: {\n        backgroundColor: '#000000',\n        brightness: 0,\n        contrast: 0,\n        saturation: 0\n      },\n\n      audioSettings: {\n        masterVolume: 80,\n        bgmVolume: 60,\n        sfxVolume: 70\n      },\n\n      textSettings: {\n        fontFamily: 'Microsoft YaHei',\n        fontSize: 24,\n        color: '#FFFFFF'\n      },\n\n      // 选中的元素\n      selectedClip: null,\n      selectedScene: null,\n      selectedTextSegment: null,\n\n      // 时间轴设置\n      timelineDuration: 60, // 时间轴总长度60秒\n      pixelsPerSecond: 10, // 每秒10像素，60秒 = 600像素（稀疏显示）\n      currentTime: 0, // 当前播放时间（秒）\n      \n      // 场景列表\n      scenes: [\n        {\n          id: 'scene1',\n          name: '开场',\n          startTime: 0,\n          duration: 8,\n          maxDuration: 12,\n          videoLibraryId: 'lib_开场',\n          textSegment: '欢迎来到我们的产品展示，今天为大家介绍一款革命性的产品。',\n          currentVideo: null\n        },\n        {\n          id: 'scene2',\n          name: '产品介绍',\n          startTime: 8,\n          duration: 10,\n          maxDuration: 15, // 视频素材的最大时长\n          videoLibraryId: 'lib_产品介绍',\n          textSegment: '这款产品具有独特的设计理念和先进的技术，能够为用户带来全新的体验。',\n          currentVideo: null\n        },\n        {\n          id: 'scene3',\n          name: '功能展示',\n          startTime: 18,\n          duration: 9,\n          maxDuration: 20, // 视频素材的最大时长\n          videoLibraryId: 'lib_功能展示',\n          textSegment: '让我们来看看它的核心功能和使用方法。',\n          currentVideo: null\n        }\n      ],\n\n      // 从up页面获取的素材库（视频库）\n      sucaiList: [], // 从up页面的素材列表获取\n\n      // 视频库分类（基于文件夹）\n      videoLibraries: [],\n\n      // AI文案库\n      textLibrary: {\n        id: 'text1',\n        name: 'AI剪辑文案库',\n        fullText: '欢迎来到我们的产品展示，今天为大家介绍一款革命性的产品。这款产品具有独特的设计理念和先进的技术，能够为用户带来全新的体验。让我们来看看它的核心功能和使用方法。通过简单的操作，您就能体验到前所未有的便利性。',\n        segments: [] // 将通过阿里云接口分段\n      },\n\n      // 轨道数据（简化版）\n      tracks: [\n        {\n          id: 'video',\n          name: '视频场景',\n          type: 'video',\n          icon: 'el-icon-video-camera'\n        },\n        {\n          id: 'text',\n          name: '文案',\n          type: 'text',\n          icon: 'el-icon-edit'\n        },\n        {\n          id: 'music',\n          name: '背景音乐',\n          type: 'audio',\n          icon: 'el-icon-headset',\n          clips: [\n            {\n              id: 'm1',\n              name: '背景音乐.mp3',\n              startTime: 0,\n              duration: 45, // 当前使用时长\n              maxDuration: 180, // 音频文件的实际时长（3分钟）\n              originalDuration: 180, // 原始文件时长\n              type: 'audio'\n            }\n          ]\n        },\n        {\n          id: 'sound',\n          name: '音效',\n          type: 'audio',\n          icon: 'el-icon-bell',\n          clips: [\n            {\n              id: 's1',\n              name: '点击音效.wav',\n              startTime: 8,\n              duration: 1,\n              maxDuration: 2, // 音效文件时长\n              originalDuration: 2,\n              type: 'audio'\n            },\n            {\n              id: 's2',\n              name: '转场音效.wav',\n              startTime: 18,\n              duration: 2,\n              maxDuration: 3,\n              originalDuration: 3,\n              type: 'audio'\n            }\n          ]\n        }\n      ]\n    }\n  },\n\n  computed: {\n    // 时间轴总宽度\n    timelineWidth() {\n      return this.timelineDuration * this.pixelsPerSecond\n    }\n  },\n\n  created() {\n    // 获取模板ID\n    const templateId = this.$route.query.templateId\n    if (templateId) {\n      this.loadTemplate(templateId)\n    }\n\n    // 加载素材库\n    this.loadMaterialsFromUpPage()\n\n    // 初始化场景视频\n    this.initializeSceneVideos()\n  },\n  \n  methods: {\n    // 返回上一页\n    goBack() {\n      this.$router.go(-1)\n    },\n    \n    // 加载模板\n    loadTemplate(templateId) {\n      // TODO: 从API加载模板数据\n      this.templateInfo.id = templateId\n      console.log('加载模板:', templateId)\n    },\n    \n    // 保存模板\n    saveTemplate() {\n      this.$message.success('模板保存成功')\n      // TODO: 调用保存API\n    },\n    \n    // 显示批量剪辑对话框\n    showBatchDialog() {\n      this.batchDialogVisible = true\n    },\n    \n    // 开始批量剪辑\n    startBatchClip() {\n      this.$message.success(`开始生成 ${this.batchCount} 条视频`)\n      this.batchDialogVisible = false\n      \n      // 跳转到进度页面\n      this.$router.push({\n        path: '/storer/progress',\n        query: { templateId: this.templateInfo.id }\n      })\n    },\n    \n    // 选择片段\n    selectClip(clip) {\n      this.selectedClip = clip\n    },\n    \n    // 格式化时间\n    formatTime(seconds) {\n      const mins = Math.floor(seconds / 60)\n      const secs = seconds % 60\n      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`\n    },\n    \n    // 获取片段样式\n    getClipStyle(clip) {\n      return {\n        left: clip.startTime * this.pixelsPerSecond + 'px',\n        width: clip.duration * this.pixelsPerSecond + 'px'\n      }\n    },\n    \n    // 拖拽开始\n    onDragStart(event, clip) {\n      event.dataTransfer.setData('text/plain', JSON.stringify(clip))\n    },\n    \n    // 拖拽放置\n    onDrop(event, track) {\n      const clipData = JSON.parse(event.dataTransfer.getData('text/plain'))\n      console.log('拖拽到轨道:', track.name, clipData)\n      // TODO: 实现拖拽逻辑\n    },\n    \n    // 开始调整大小\n    startResize(event, clip, direction) {\n      console.log('调整大小:', clip.name, direction)\n\n      // 检查是否可以调整\n      if (!clip.maxDuration && !clip.originalDuration) {\n        this.$message.warning('该素材没有时长限制信息')\n        return\n      }\n\n      const maxAllowedDuration = clip.maxDuration || clip.originalDuration\n\n      if (direction === 'right') {\n        // 向右拖拽（增加时长）\n        if (clip.duration >= maxAllowedDuration) {\n          this.$message.warning(`素材最大时长为 ${this.formatTime(maxAllowedDuration)}，无法继续延长`)\n          return\n        }\n      }\n\n      // TODO: 实现具体的拖拽调整逻辑\n      this.startResizeWithLimit(event, clip, direction, maxAllowedDuration)\n    },\n\n    // 带时长限制的调整大小\n    startResizeWithLimit(event, clip, direction, maxDuration) {\n      const startX = event.clientX\n      const startDuration = clip.duration\n      const pixelsPerSecond = this.pixelsPerSecond\n\n      const onMouseMove = (e) => {\n        const deltaX = e.clientX - startX\n        const deltaSeconds = deltaX / pixelsPerSecond\n\n        let newDuration = startDuration\n\n        if (direction === 'right') {\n          newDuration = Math.max(1, Math.min(maxDuration, startDuration + deltaSeconds))\n        } else if (direction === 'left') {\n          newDuration = Math.max(1, Math.min(maxDuration, startDuration - deltaSeconds))\n        }\n\n        clip.duration = Math.round(newDuration)\n\n        // 显示时长提示\n        this.showDurationTip(clip, maxDuration)\n      }\n\n      const onMouseUp = () => {\n        document.removeEventListener('mousemove', onMouseMove)\n        document.removeEventListener('mouseup', onMouseUp)\n        this.hideDurationTip()\n      }\n\n      document.addEventListener('mousemove', onMouseMove)\n      document.addEventListener('mouseup', onMouseUp)\n    },\n\n    // 显示时长提示\n    showDurationTip(clip, maxDuration) {\n      const percentage = (clip.duration / maxDuration * 100).toFixed(1)\n      console.log(`${clip.name}: ${this.formatTime(clip.duration)} / ${this.formatTime(maxDuration)} (${percentage}%)`)\n    },\n\n    // 隐藏时长提示\n    hideDurationTip() {\n      // 清除提示\n    },\n\n    // 获取预览分辨率显示文本\n    getPreviewResolution() {\n      // 根据模板分辨率返回对应的抖音尺寸\n      const resolutionMap = {\n        '1920x1080': '1080x1920',\n        '1280x720': '720x1280',\n        '1080x1920': '1080x1920',\n        '720x1280': '720x1280'\n      }\n      return resolutionMap[this.templateInfo.resolution] || '1080x1920'\n    },\n\n    // 从up页面加载素材库\n    async loadMaterialsFromUpPage() {\n      try {\n        // 模拟从up页面获取素材数据\n        // 实际应用中，这里应该调用API或从localStorage获取\n        const mockSucaiList = [\n          {\n            id: 'sucai1',\n            name: '开场视频1.mp4',\n            type: 'video',\n            size: 1024000,\n            uploadTime: '2024-01-01 12:00:00',\n            duration: '00:12',\n            url: 'https://www.w3schools.com/html/mov_bbb.mp4',\n            folder: 'asucai/admin/开场',\n            thumbnail: 'https://via.placeholder.com/160x90/4A90E2/FFFFFF?text=Opening1'\n          },\n          {\n            id: 'sucai2',\n            name: '开场视频2.mp4',\n            type: 'video',\n            size: 1024000,\n            uploadTime: '2024-01-01 12:00:00',\n            duration: '00:10',\n            url: 'https://www.w3schools.com/html/mov_bbb.mp4',\n            folder: 'asucai/admin/开场',\n            thumbnail: 'https://via.placeholder.com/160x90/4A90E2/FFFFFF?text=Opening2'\n          },\n          {\n            id: 'sucai3',\n            name: '产品介绍1.mp4',\n            type: 'video',\n            size: 1024000,\n            uploadTime: '2024-01-01 12:00:00',\n            duration: '00:18',\n            url: 'https://www.w3schools.com/html/mov_bbb.mp4',\n            folder: 'asucai/admin/产品介绍',\n            thumbnail: 'https://via.placeholder.com/160x90/50C878/FFFFFF?text=Product1'\n          },\n          {\n            id: 'sucai4',\n            name: '产品介绍2.mp4',\n            type: 'video',\n            size: 1024000,\n            uploadTime: '2024-01-01 12:00:00',\n            duration: '00:22',\n            url: 'https://www.w3schools.com/html/mov_bbb.mp4',\n            folder: 'asucai/admin/产品介绍',\n            thumbnail: 'https://via.placeholder.com/160x90/50C878/FFFFFF?text=Product2'\n          },\n          {\n            id: 'sucai5',\n            name: '功能展示1.mp4',\n            type: 'video',\n            size: 1024000,\n            uploadTime: '2024-01-01 12:00:00',\n            duration: '00:25',\n            url: 'https://www.w3schools.com/html/mov_bbb.mp4',\n            folder: 'asucai/admin/功能展示',\n            thumbnail: 'https://via.placeholder.com/160x90/FF6B6B/FFFFFF?text=Feature1'\n          }\n        ]\n\n        this.sucaiList = mockSucaiList\n\n        // 根据文件夹分组创建视频库\n        this.createVideoLibrariesFromSucai()\n\n        console.log('素材库加载完成:', this.videoLibraries)\n\n      } catch (error) {\n        console.error('加载素材库失败:', error)\n        this.$message.error('加载素材库失败')\n      }\n    },\n\n    // 根据素材列表创建视频库\n    createVideoLibrariesFromSucai() {\n      const folderMap = new Map()\n\n      // 按文件夹分组\n      this.sucaiList.forEach(item => {\n        if (item.type === 'video') {\n          // 提取文件夹名称\n          const folderPath = item.folder || 'asucai/admin/总'\n          const folderName = folderPath.split('/').pop() || '总'\n\n          if (!folderMap.has(folderName)) {\n            folderMap.set(folderName, {\n              id: 'lib_' + folderName,\n              name: folderName + '视频库',\n              videos: []\n            })\n          }\n\n          // 转换为视频库格式\n          const video = {\n            id: item.id,\n            name: item.name,\n            url: item.url,\n            duration: this.parseDuration(item.duration),\n            thumbnail: item.thumbnail || 'https://via.placeholder.com/160x90/999/FFFFFF?text=Video'\n          }\n\n          folderMap.get(folderName).videos.push(video)\n        }\n      })\n\n      // 转换为数组\n      this.videoLibraries = Array.from(folderMap.values())\n    },\n\n    // 解析时长字符串为秒数\n    parseDuration(durationStr) {\n      if (!durationStr) return 10\n\n      const parts = durationStr.split(':')\n      if (parts.length === 2) {\n        const minutes = parseInt(parts[0]) || 0\n        const seconds = parseInt(parts[1]) || 0\n        return minutes * 60 + seconds\n      }\n\n      return 10 // 默认10秒\n    },\n\n    // 初始化场景视频（为每个场景随机选择一个视频）\n    initializeSceneVideos() {\n      this.scenes.forEach(scene => {\n        const library = this.videoLibraries.find(lib => lib.id === scene.videoLibraryId)\n        if (library && library.videos.length > 0) {\n          // 随机选择一个视频\n          const randomIndex = Math.floor(Math.random() * library.videos.length)\n          const selectedVideo = library.videos[randomIndex]\n          scene.currentVideo = selectedVideo\n\n          // 设置场景的最大时长为视频时长\n          scene.maxDuration = selectedVideo.duration\n\n          // 如果当前场景时长超过视频时长，调整为视频时长\n          if (scene.duration > selectedVideo.duration) {\n            scene.duration = selectedVideo.duration\n          }\n        }\n      })\n\n      // 重新计算时间轴\n      this.recalculateSceneTimeline()\n    },\n\n    // 显示添加场景对话框\n    showAddSceneDialog() {\n      this.sceneForm = {\n        name: '',\n        duration: 10,\n        videoLibraryId: ''\n      }\n      this.addSceneDialogVisible = true\n    },\n\n    // 确认添加场景\n    confirmAddScene() {\n      if (!this.sceneForm.name || !this.sceneForm.videoLibraryId) {\n        this.$message.error('请填写完整的场景信息')\n        return\n      }\n\n      // 计算新场景的开始时间\n      const lastScene = this.scenes[this.scenes.length - 1]\n      const startTime = lastScene ? lastScene.startTime + lastScene.duration : 0\n\n      const newScene = {\n        id: 'scene' + Date.now(),\n        name: this.sceneForm.name,\n        startTime: startTime,\n        duration: this.sceneForm.duration,\n        videoLibraryId: this.sceneForm.videoLibraryId,\n        textSegment: '新场景的文案内容...',\n        currentVideo: null\n      }\n\n      // 为新场景随机选择视频\n      const library = this.videoLibraries.find(lib => lib.id === this.sceneForm.videoLibraryId)\n      if (library && library.videos.length > 0) {\n        const randomIndex = Math.floor(Math.random() * library.videos.length)\n        newScene.currentVideo = library.videos[randomIndex]\n      }\n\n      this.scenes.push(newScene)\n      this.addSceneDialogVisible = false\n      this.$message.success('场景添加成功')\n    },\n\n    // 选择场景\n    selectScene(scene) {\n      this.selectedScene = scene\n      this.selectedClip = null\n      this.selectedTextSegment = null\n\n      // 显示视频库选择对话框\n      const library = this.videoLibraries.find(lib => lib.id === scene.videoLibraryId)\n      if (library) {\n        this.selectedVideoLibrary = library\n        this.videoLibraryDialogVisible = true\n      }\n    },\n\n    // 从视频库选择视频\n    selectVideoFromLibrary(video) {\n      if (this.selectedScene) {\n        this.selectedScene.currentVideo = video\n        // 更新场景的最大时长限制\n        this.selectedScene.maxDuration = video.duration\n\n        // 如果当前时长超过新视频的时长，则调整为新视频的时长\n        if (this.selectedScene.duration > video.duration) {\n          this.selectedScene.duration = video.duration\n          this.$message.warning(`场景时长已调整为视频时长: ${this.formatTime(video.duration)}`)\n        }\n\n        this.$message.success(`已为场景\"${this.selectedScene.name}\"选择视频: ${video.name} (${this.formatTime(video.duration)})`)\n      }\n      this.videoLibraryDialogVisible = false\n    },\n\n    // 选择文案片段\n    selectTextSegment(scene, index) {\n      this.selectedTextSegment = scene\n      this.selectedClip = null\n      this.selectedScene = null\n    },\n\n    // 获取场景样式\n    getSceneStyle(scene) {\n      return {\n        left: scene.startTime * this.pixelsPerSecond + 'px',\n        width: scene.duration * this.pixelsPerSecond + 'px'\n      }\n    },\n\n    // 获取文案预览\n    getTextPreview(text) {\n      return text.length > 20 ? text.substring(0, 20) + '...' : text\n    },\n\n    // 文案分段（调用阿里云接口）\n    async segmentText() {\n      try {\n        this.$message.info('正在调用阿里云接口进行文案分段...')\n\n        // TODO: 调用阿里云文本分段接口\n        // const segments = await this.callAliCloudTextSegmentation(this.textLibrary.fullText)\n\n        // 模拟分段结果\n        const mockSegments = [\n          '欢迎来到我们的产品展示，今天为大家介绍一款革命性的产品。',\n          '这款产品具有独特的设计理念和先进的技术，能够为用户带来全新的体验。',\n          '让我们来看看它的核心功能和使用方法。',\n          '通过简单的操作，您就能体验到前所未有的便利性。'\n        ]\n\n        // 更新场景的文案片段\n        mockSegments.forEach((segment, index) => {\n          if (this.scenes[index]) {\n            this.scenes[index].textSegment = segment\n          }\n        })\n\n        this.$message.success('文案分段完成')\n\n      } catch (error) {\n        console.error('文案分段失败:', error)\n        this.$message.error('文案分段失败，请重试')\n      }\n    },\n\n    // 场景拖拽开始\n    onDragStartScene(event, scene) {\n      event.dataTransfer.setData('text/plain', JSON.stringify(scene))\n    },\n\n    // 场景拖拽放置\n    onDropScene(event) {\n      const sceneData = JSON.parse(event.dataTransfer.getData('text/plain'))\n      console.log('场景拖拽:', sceneData)\n      // TODO: 实现场景拖拽逻辑\n    },\n\n    // 开始调整场景大小\n    startResizeScene(event, scene, direction) {\n      console.log('调整场景大小:', scene.name, direction)\n\n      // 检查场景是否有视频和时长限制\n      if (!scene.currentVideo || !scene.maxDuration) {\n        this.$message.warning('请先为场景选择视频')\n        return\n      }\n\n      const maxAllowedDuration = scene.maxDuration\n\n      if (direction === 'right') {\n        // 向右拖拽（增加时长）\n        if (scene.duration >= maxAllowedDuration) {\n          this.$message.warning(`场景最大时长为 ${this.formatTime(maxAllowedDuration)}（视频时长限制）`)\n          return\n        }\n      }\n\n      // 实现场景大小调整\n      this.startResizeSceneWithLimit(event, scene, direction, maxAllowedDuration)\n    },\n\n    // 带时长限制的场景调整\n    startResizeSceneWithLimit(event, scene, direction, maxDuration) {\n      const startX = event.clientX\n      const startDuration = scene.duration\n      const pixelsPerSecond = this.pixelsPerSecond\n\n      const onMouseMove = (e) => {\n        const deltaX = e.clientX - startX\n        const deltaSeconds = deltaX / pixelsPerSecond\n\n        let newDuration = startDuration\n\n        if (direction === 'right') {\n          newDuration = Math.max(1, Math.min(maxDuration, startDuration + deltaSeconds))\n        } else if (direction === 'left') {\n          newDuration = Math.max(1, Math.min(maxDuration, startDuration - deltaSeconds))\n        }\n\n        scene.duration = Math.round(newDuration)\n\n        // 显示场景时长提示\n        this.showSceneDurationTip(scene, maxDuration)\n      }\n\n      const onMouseUp = () => {\n        document.removeEventListener('mousemove', onMouseMove)\n        document.removeEventListener('mouseup', onMouseUp)\n        this.hideSceneDurationTip()\n\n        // 调整完成后重新计算后续场景的开始时间\n        this.recalculateSceneTimeline()\n      }\n\n      document.addEventListener('mousemove', onMouseMove)\n      document.addEventListener('mouseup', onMouseUp)\n    },\n\n    // 显示场景时长提示\n    showSceneDurationTip(scene, maxDuration) {\n      const percentage = (scene.duration / maxDuration * 100).toFixed(1)\n      console.log(`${scene.name}: ${this.formatTime(scene.duration)} / ${this.formatTime(maxDuration)} (${percentage}%)`)\n    },\n\n    // 隐藏场景时长提示\n    hideSceneDurationTip() {\n      // 清除提示\n    },\n\n    // 重新计算场景时间轴\n    recalculateSceneTimeline() {\n      let currentTime = 0\n      this.scenes.forEach(scene => {\n        scene.startTime = currentTime\n        currentTime += scene.duration\n      })\n      console.log('场景时间轴已重新计算')\n    },\n\n    // 点击时间轴\n    onTimelineClick(event) {\n      const rect = event.currentTarget.getBoundingClientRect()\n      const clickX = event.clientX - rect.left\n      const clickedTime = Math.floor(clickX / this.pixelsPerSecond)\n\n      // 限制在0-60秒范围内\n      this.currentTime = Math.max(0, Math.min(60, clickedTime))\n\n      console.log(`点击时间轴: ${this.currentTime}秒`)\n\n      // 根据当前时间找到对应的场景并预览\n      this.previewAtTime(this.currentTime)\n    },\n\n    // 根据时间预览对应场景\n    previewAtTime(time) {\n      // 找到当前时间对应的场景\n      const currentScene = this.scenes.find(scene => {\n        return time >= scene.startTime && time < (scene.startTime + scene.duration)\n      })\n\n      if (currentScene) {\n        this.selectedScene = currentScene\n        console.log(`预览场景: ${currentScene.name} (${time}秒)`)\n\n        // 如果场景有视频，可以计算视频内的播放位置\n        if (currentScene.currentVideo) {\n          const sceneProgress = time - currentScene.startTime\n          const videoProgress = (sceneProgress / currentScene.duration * 100).toFixed(1)\n          console.log(`视频播放进度: ${videoProgress}%`)\n        }\n      } else {\n        // 如果没有找到场景，清除选择\n        this.selectedScene = null\n        console.log('当前时间没有对应的场景')\n      }\n    },\n\n    // 开始拖拽时间轴\n    onTimelineDragStart(event) {\n      event.preventDefault()\n\n      const startX = event.clientX\n      const rect = event.currentTarget.getBoundingClientRect()\n      const startClickX = event.clientX - rect.left\n      const startTime = Math.floor(startClickX / this.pixelsPerSecond)\n\n      // 设置初始时间\n      this.currentTime = Math.max(0, Math.min(60, startTime))\n      this.previewAtTime(this.currentTime)\n\n      const onMouseMove = (e) => {\n        const currentX = e.clientX\n        const deltaX = currentX - startX\n        const newClickX = startClickX + deltaX\n        const newTime = Math.floor(newClickX / this.pixelsPerSecond)\n\n        // 限制在0-60秒范围内\n        this.currentTime = Math.max(0, Math.min(60, newTime))\n        this.previewAtTime(this.currentTime)\n      }\n\n      const onMouseUp = () => {\n        document.removeEventListener('mousemove', onMouseMove)\n        document.removeEventListener('mouseup', onMouseUp)\n        console.log(`拖拽结束: ${this.currentTime}秒`)\n      }\n\n      document.addEventListener('mousemove', onMouseMove)\n      document.addEventListener('mouseup', onMouseUp)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.video-editor {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: #f5f7fa;\n}\n\n/* 头部工具栏 */\n.editor-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 20px;\n  background: white;\n  border-bottom: 1px solid #e4e7ed;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.template-title {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.header-right {\n  display: flex;\n  gap: 12px;\n}\n\n/* 主编辑区域 */\n.editor-main {\n  display: flex;\n  flex: 1;\n  min-height: 0;\n}\n\n/* 预览面板 */\n.preview-panel {\n  flex: 1;\n  padding: 20px;\n  background: white;\n  border-right: 1px solid #e4e7ed;\n}\n\n.preview-container {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.video-preview {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 16px;\n  padding: 20px;\n}\n\n.preview-frame {\n  width: 300px;\n  height: 533px; /* 300 * 16/9 = 533.33 */\n  background: #000;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);\n  border: 1px solid #ddd;\n  overflow: hidden;\n}\n\n/* 场景预览样式 */\n.scene-preview {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n\n.scene-preview img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.scene-overlay {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));\n  color: white;\n  padding: 20px 16px 16px;\n}\n\n.scene-title {\n  font-size: 16px;\n  font-weight: 600;\n  margin-bottom: 4px;\n}\n\n.scene-video-name {\n  font-size: 12px;\n  color: #ccc;\n  margin-bottom: 4px;\n}\n\n.scene-time-info {\n  font-size: 11px;\n  color: #ffd700;\n  margin-bottom: 8px;\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n}\n\n.scene-text-preview {\n  font-size: 11px;\n  line-height: 1.4;\n  color: #ddd;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.preview-placeholder {\n  text-align: center;\n  color: #909399;\n  padding: 40px 20px;\n}\n\n.preview-placeholder i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n  color: #666;\n}\n\n.preview-placeholder p {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n  font-weight: 500;\n  color: #909399;\n}\n\n.preview-info {\n  font-size: 12px !important;\n  margin: 12px 0 !important;\n  color: #999 !important;\n}\n\n.preview-tips {\n  margin-top: 20px;\n  padding-top: 16px;\n  border-top: 1px solid #333;\n}\n\n.preview-tips p {\n  font-size: 11px !important;\n  color: #666 !important;\n  margin: 4px 0 !important;\n}\n\n.current-time-display {\n  margin: 16px 0;\n  padding: 8px 12px;\n  background: rgba(255, 215, 0, 0.1);\n  border: 1px solid #ffd700;\n  border-radius: 4px;\n}\n\n.current-time-display p {\n  margin: 0 !important;\n  font-size: 14px !important;\n  color: #ffd700 !important;\n  font-weight: 600 !important;\n}\n\n.preview-controls {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 0;\n}\n\n.time-display {\n  font-family: monospace;\n  font-size: 14px;\n  color: #606266;\n}\n\n/* 属性设置面板 */\n.properties-panel {\n  width: 300px;\n  background: white;\n  border-left: 1px solid #e4e7ed;\n  display: flex;\n  flex-direction: column;\n}\n\n.panel-header {\n  padding: 16px 20px;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.panel-header h4 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.panel-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 16px;\n}\n\n/* 时间轴区域 */\n.timeline-area {\n  height: 300px;\n  background: white;\n  border-top: 1px solid #e4e7ed;\n  display: flex;\n  flex-direction: column;\n  min-width: 0; /* 允许收缩 */\n}\n\n.timeline-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 20px;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.timeline-header h4 {\n  margin: 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.timeline-container {\n  flex: 1;\n  position: relative;\n  overflow: hidden;\n}\n\n/* 时间刻度行 */\n.time-ruler-row {\n  display: flex;\n  height: 30px;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.time-ruler-label {\n  width: 150px;\n  background: #f8f9fa;\n  border-right: 1px solid #e4e7ed;\n  flex-shrink: 0;\n}\n\n/* 时间刻度 */\n.time-ruler {\n  flex: 1;\n  height: 100%;\n  background: #fafafa;\n  position: relative;\n  cursor: pointer;\n  user-select: none;\n  overflow: hidden;\n}\n\n.time-marks-container {\n  position: relative;\n  height: 100%;\n  width: 100%;\n}\n\n.time-mark {\n  position: absolute;\n  top: 0;\n  height: 100%;\n  font-size: 11px;\n  color: #606266;\n  display: flex;\n  align-items: center;\n  padding-left: 6px;\n  border-left: 2px solid #c0c4cc;\n  pointer-events: none;\n  font-weight: 500;\n}\n\n.time-tick {\n  position: absolute;\n  top: 22px;\n  width: 1px;\n  height: 8px;\n  background: #f0f0f0;\n  pointer-events: none;\n}\n\n.playhead {\n  position: absolute;\n  top: 0;\n  width: 2px;\n  height: 100%;\n  background: #ff4757;\n  z-index: 10;\n  pointer-events: none;\n}\n\n.playhead::before {\n  content: '';\n  position: absolute;\n  top: -4px;\n  left: -4px;\n  width: 10px;\n  height: 10px;\n  background: #ff4757;\n  border-radius: 50%;\n}\n\n/* 轨道容器 */\n.tracks-container {\n  flex: 1;\n}\n\n.track-row {\n  height: 40px;\n  display: flex;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.track-label {\n  width: 150px;\n  display: flex;\n  align-items: center;\n  padding: 0 12px;\n  background: #f8f9fa;\n  border-right: 1px solid #e4e7ed;\n  font-size: 14px;\n  color: #606266;\n  flex-shrink: 0;\n}\n\n.track-label i {\n  margin-right: 8px;\n  font-size: 16px;\n}\n\n.track-content {\n  flex: 1;\n  position: relative;\n  background: white;\n  overflow: hidden;\n}\n\n.track-timeline {\n  position: relative;\n  height: 100%;\n  width: 100%;\n}\n\n/* 片段样式 */\n.clip {\n  position: absolute;\n  top: 4px;\n  height: 32px;\n  background: #409EFF;\n  border-radius: 4px;\n  cursor: pointer;\n  user-select: none;\n  display: flex;\n  align-items: center;\n  padding: 0 8px;\n  color: white;\n  font-size: 12px;\n  transition: all 0.2s ease;\n}\n\n.clip:hover {\n  background: #337ecc;\n  transform: translateY(-1px);\n}\n\n.clip.active {\n  background: #E6A23C;\n  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.3);\n}\n\n/* 场景片段样式 */\n.scene-clip {\n  position: absolute;\n  top: 2px;\n  height: 36px;\n  background: #67C23A;\n  border-radius: 6px;\n  cursor: pointer;\n  user-select: none;\n  display: flex;\n  align-items: center;\n  padding: 0;\n  color: white;\n  font-size: 11px;\n  transition: all 0.2s ease;\n  overflow: hidden;\n}\n\n.scene-clip:hover {\n  background: #5daf34;\n  transform: translateY(-1px);\n}\n\n.scene-clip.active {\n  background: #E6A23C;\n  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.3);\n}\n\n.scene-content {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  padding: 4px 8px;\n  overflow: hidden;\n}\n\n.scene-name {\n  display: block;\n  font-weight: 600;\n  font-size: 12px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  line-height: 1.2;\n  margin-bottom: 2px;\n}\n\n.scene-duration {\n  font-size: 10px;\n  opacity: 0.8;\n  line-height: 1;\n  margin-bottom: 1px;\n}\n\n.scene-duration .max-duration {\n  color: rgba(255, 255, 255, 0.6);\n  font-size: 9px;\n}\n\n.scene-video-info {\n  font-size: 9px;\n  opacity: 0.7;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n/* 文案片段样式 */\n.text-clip {\n  position: absolute;\n  top: 4px;\n  height: 32px;\n  background: #F56C6C;\n  border-radius: 4px;\n  cursor: pointer;\n  user-select: none;\n  display: flex;\n  align-items: center;\n  padding: 0 8px;\n  color: white;\n  font-size: 11px;\n  transition: all 0.2s ease;\n}\n\n.text-clip:hover {\n  background: #f45656;\n  transform: translateY(-1px);\n}\n\n.text-clip.active {\n  background: #E6A23C;\n  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.3);\n}\n\n.text-content {\n  flex: 1;\n  overflow: hidden;\n}\n\n.text-preview {\n  display: block;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  font-weight: 500;\n  line-height: 1.2;\n}\n\n.text-duration {\n  font-size: 10px;\n  opacity: 0.8;\n}\n\n.clip-content {\n  flex: 1;\n  overflow: hidden;\n}\n\n.clip-name {\n  display: block;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  font-weight: 500;\n}\n\n.clip-duration {\n  font-size: 10px;\n  opacity: 0.8;\n}\n\n.max-duration {\n  color: rgba(255, 255, 255, 0.6);\n  font-size: 9px;\n}\n\n/* 调整手柄 */\n.resize-handle {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  width: 4px;\n  background: rgba(255, 255, 255, 0.3);\n  cursor: ew-resize;\n  opacity: 0;\n  transition: opacity 0.2s ease;\n}\n\n.resize-handle.left {\n  left: 0;\n}\n\n.resize-handle.right {\n  right: 0;\n}\n\n.clip:hover .resize-handle {\n  opacity: 1;\n}\n\n/* 轨道类型样式 */\n.track-row.video .clip {\n  background: #409EFF;\n}\n\n.track-row.audio .clip {\n  background: #67C23A;\n}\n\n.track-row.text .clip {\n  background: #E6A23C;\n}\n\n.track-row.effect .clip {\n  background: #F56C6C;\n}\n\n/* 批量配置 */\n.batch-config {\n  text-align: center;\n  padding: 20px 0;\n}\n\n.batch-config h4 {\n  margin: 0 0 20px 0;\n  color: #303133;\n  font-size: 18px;\n}\n\n.batch-config p {\n  margin: 0 0 16px 0;\n  color: #606266;\n}\n\n.batch-tip {\n  margin-top: 12px !important;\n  color: #909399;\n  font-size: 12px;\n}\n\n/* 视频库对话框样式 */\n.video-library-content h4 {\n  margin: 0 0 16px 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.video-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));\n  gap: 16px;\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.video-item {\n  cursor: pointer;\n  border-radius: 8px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  background: white;\n}\n\n.video-item:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  border-color: #409EFF;\n}\n\n.video-thumbnail {\n  position: relative;\n  width: 100%;\n  height: 90px;\n}\n\n.video-thumbnail img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  display: block;\n}\n\n.video-duration-badge {\n  position: absolute;\n  bottom: 4px;\n  right: 4px;\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-size: 10px;\n  font-weight: 500;\n}\n\n.video-info {\n  padding: 8px;\n  background: #f8f9fa;\n}\n\n.video-name {\n  margin: 0 0 4px 0;\n  font-size: 12px;\n  color: #303133;\n  font-weight: 500;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.video-duration-text {\n  margin: 0;\n  font-size: 11px;\n  color: #909399;\n}\n\n/* 响应式设计 - 9:16 视频预览适配 */\n@media (max-width: 1200px) {\n  .preview-frame {\n    width: 250px;\n    height: 444px; /* 250 * 16/9 = 444.44 */\n  }\n}\n\n@media (max-width: 768px) {\n  .editor-main {\n    flex-direction: column;\n  }\n\n  .preview-panel {\n    border-right: none;\n    border-bottom: 1px solid #e4e7ed;\n  }\n\n  .properties-panel {\n    width: 100%;\n    max-height: 300px;\n  }\n\n  .preview-frame {\n    width: 200px;\n    height: 356px; /* 200 * 16/9 = 355.56 */\n  }\n\n  .timeline-area {\n    height: 250px;\n  }\n}\n\n@media (max-width: 480px) {\n  .preview-frame {\n    width: 150px;\n    height: 267px; /* 150 * 16/9 = 266.67 */\n  }\n\n  .preview-placeholder {\n    padding: 20px 10px;\n  }\n\n  .preview-placeholder i {\n    font-size: 32px;\n  }\n\n  .preview-placeholder p {\n    font-size: 14px;\n  }\n\n  .preview-tips p {\n    font-size: 10px !important;\n  }\n}\n</style>\n"]}]}