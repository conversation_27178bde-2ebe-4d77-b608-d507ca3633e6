import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout/SimpleLayout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/update-progress',
    component: () => import('@/views/update/UpdateProgressFixed'),
    hidden: true,
    meta: { title: '系统更新进度' }
  },

  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '/',
    redirect: '/storer/index',
    hidden: true
  },
  {
    path: '/index',
    redirect: '/storer/index',
    hidden: true
  },
  {
    path: '/agent',
    component: Layout,
    children: [
      {
        path: 'list',
        component: () => import('@/views/marketing/config.vue'),
        name: 'MarketingConfig',
        meta: { title: '营销配置管理', icon: 'peoples' }
      }
    ]
  },
  {
    path: '/merchant',
    component: Layout,
    children: [
      {
        path: 'list',
        component: () => import('@/views/merchant/list.vue'),
        name: 'MerchantList',
        meta: { title: '商家列表', icon: 'shopping' }
      }
    ]
  },
  {
    path: '/finance',
    component: Layout,
    redirect: 'noRedirect',
    name: 'Finance',
    meta: { title: '财务管理', icon: 'money' },
    children: [
      {
        path: 'overview',
        component: () => import('@/views/finance/overview.vue'),
        name: 'FinanceOverview',
        meta: { title: '财务概览' }
      },
      {
        path: 'record',
        component: () => import('@/views/finance/record.vue'),
        name: 'FinanceRecord',
        meta: { title: '交易记录' }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    redirect: 'noRedirect',
    name: 'System',
    meta: { title: '系统管理', icon: 'system' },
    children: [
      {
        path: 'menu',
        component: () => import('@/views/system/menu/index.vue'),
        name: 'Menu',
        meta: { title: '菜单管理' }
      },
      {
        path: 'dict',
        component: () => import('@/views/system/dict/index.vue'),
        name: 'Dict',
        meta: { title: '字典管理' }
      },
      {
        path: 'config',
        component: () => import('@/views/system/config/index.vue'),
        name: 'Config',
        meta: { title: '参数设置' }
      }
    ]
  },
  {
    path: '/storer',
    component: Layout,
    redirect: '/storer/index',
    name: 'Storer',
    meta: { title: '店铺管理', icon: 'shopping' },
    children: [
      {
        path: 'index',
        component: () => import('@/views/dashboard/index.vue'),
        name: 'StorerIndex',
        meta: { title: '工作台' }
      },
      {
        path: 'store',
        component: () => import('@/views/store/store.vue'),
        name: 'StorerStore',
        meta: { title: '门店列表' }
      },
      {
        path: 'shipin',
        component: () => import('@/views/store/shipin.vue'),
        name: 'StorerShipin',
        meta: { title: 'AI剪辑文案' }
      },
      {
        path: 'dou',
        component: () => import('@/views/store/dou.vue'),
        name: 'StorerDou',
        meta: { title: '抖音/快手文案' }
      },

      {
        path: 'hong',
        component: () => import('@/views/store/hong.vue'),
        name: 'StorerHong',
        meta: { title: '小红书文案' }
      },
      {
        path: 'daka',
        component: () => import('@/views/store/daka.vue'),
        name: 'StorerDaka',
        meta: { title: '打卡点评/朋友圈文案' }
      },
      {
        path: 'up',
        component: () => import('@/views/store/up.vue'),
        name: 'StorerUp',
        meta: { title: '素材上传' }
      },
      {
        path: 'huati',
        component: () => import('@/views/store/huati.vue'),
        name: 'StorerHuati',
        meta: { title: '话题创建' }
      },
      {
        path: 'mingxi',
        component: () => import('@/views/store/mingxi.vue'),
        name: 'StorerMingxi',
        meta: { title: '算力明细' }
      },
      {
        path: 'sk',
        component: () => import('@/views/store/sk.vue'),
        name: 'StorerSk',
        meta: { title: '待发布视频库' }
      },
      {
        path: 'tk',
        component: () => import('@/views/store/tk.vue'),
        name: 'StorerTk',
        meta: { title: '图片库营销活动' }
      },
      {
        path: 'jiang',
        component: () => import('@/views/store/jiang.vue'),
        name: 'StorerJiang',
        meta: { title: '奖品设置' }
      },
      {
        path: 'zhuanp',
        component: () => import('@/views/store/zhuanp.vue'),
        name: 'StorerZhuanp',
        meta: { title: '大转盘配置' }
      },
      {
        path: 'daijiang',
        component: () => import('@/views/store/daijiang.vue'),
        name: 'StorerDaijiang',
        meta: { title: '待领取信息' }
      },
      {
        path: 'dijin',
        component: () => import('@/views/store/dijin.vue'),
        name: 'StorerDijin',
        meta: { title: 'AI递进式剪辑' }
      },
      {
        path: 'editor',
        component: () => import('@/views/store/editor.vue'),
        name: 'VideoEditor',
        meta: { title: '视频编辑器' }
      },
      {
        path: 'progress',
        component: () => import('@/views/store/progress.vue'),
        name: 'VideoProgress',
        meta: { title: '剪辑进度' }
      },
      {
        path: 'video-platform',
        component: () => import('@/views/store/video-platform.vue'),
        name: 'VideoPlatform',
        meta: { title: '智能视频制作平台' }
      },
      {
        path: 'video-demo',
        component: () => import('@/views/store/video-demo.vue'),
        name: 'VideoDemo',
        meta: { title: '视频平台演示' }
      },
      {
        path: 'test-page',
        component: () => import('@/views/store/test-page.vue'),
        name: 'TestPage',
        meta: { title: '测试页面' }
      },
      {
        path: 'djindu',
        component: () => import('@/views/store/djindu.vue'),
        name: 'StorerDjindu',
        meta: { title: '递进剪辑进度' }
      },
      {
        path: 'ai-test',
        component: () => import('@/views/ai/test.vue'),
        name: 'AiTest',
        meta: { title: 'AI接口测试' }
      },
      {
        path: 'quick-test',
        component: () => import('@/views/ai/quick-test.vue'),
        name: 'QuickTest',
        meta: { title: 'AI快速测试' }
      }
    ]
  },
  // DIY编辑器路由
  {
    path: '/promotion/:storeId/diy',
    component: () => import('@/views/promotion/PromotionDIYFixed.vue'),
    name: 'PromotionDIY',
    hidden: true,
    meta: { title: '专业DIY页面编辑器' }
  },
  // NFC推广页面路由（客户访问的页面）
  {
    path: '/promotion/:id',
    component: () => import('@/views/promotion/PromotionPreview.vue'),
    name: 'PromotionPreview',
    hidden: true,
    meta: { title: '推广页面预览' }
  },
  {
    path: '/promotion/config',
    component: Layout,
    name: 'PromotionConfig',
    hidden: true,
    children: [
      {
        path: 'index',
        component: () => import('@/views/promotion/PromotionPageConfig.vue'),
        name: 'PromotionConfigIndex',
        meta: { title: '推广页面配置' }
      }
    ]
  },
  {
    path: '/changelog',
    component: Layout,
    children: [
      {
        path: 'index',
        component: () => import('@/views/changelog/index.vue'),
        name: 'Changelog',
        meta: { title: '更新日志', icon: 'documentation' }
      }
    ]
  },

  // 404 页面必须放在末尾
  { path: '*', redirect: '/404', hidden: true }
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = []

const router = new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

// 重置路由
export function resetRouter() {
  const newRouter = new Router({
    mode: 'history',
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
  })
  router.matcher = newRouter.matcher
}

export default router
