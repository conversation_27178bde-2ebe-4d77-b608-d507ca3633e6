<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间轴修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        /* 模拟编辑器布局 */
        .editor-demo {
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .time-ruler-row {
            display: flex;
            height: 30px;
            border-bottom: 1px solid #e4e7ed;
        }
        
        .time-ruler-label {
            width: 150px;
            background: #f8f9fa;
            border-right: 1px solid #e4e7ed;
            flex-shrink: 0;
        }
        
        .time-ruler {
            flex: 1;
            height: 100%;
            background: #fafafa;
            position: relative;
            cursor: pointer;
            user-select: none;
            overflow: hidden;
        }
        
        .time-marks-container {
            position: relative;
            width: 600px;
            height: 100%;
        }
        
        .time-mark {
            position: absolute;
            top: 0;
            height: 100%;
            font-size: 11px;
            color: #606266;
            display: flex;
            align-items: center;
            padding-left: 6px;
            border-left: 2px solid #c0c4cc;
            font-weight: 500;
        }
        
        .time-tick {
            position: absolute;
            top: 22px;
            width: 1px;
            height: 8px;
            background: #f0f0f0;
        }
        
        .playhead {
            position: absolute;
            top: 0;
            width: 2px;
            height: 100%;
            background: #ff4757;
            z-index: 10;
            transition: left 0.1s ease;
        }
        
        .playhead::before {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            width: 10px;
            height: 10px;
            background: #ff4757;
            border-radius: 50%;
        }
        
        .track-row {
            height: 40px;
            display: flex;
            border-bottom: 1px solid #e4e7ed;
        }
        
        .track-label {
            width: 150px;
            display: flex;
            align-items: center;
            padding: 0 12px;
            background: #f8f9fa;
            border-right: 1px solid #e4e7ed;
            font-size: 14px;
            color: #606266;
            flex-shrink: 0;
        }
        
        .track-content {
            flex: 1;
            position: relative;
            background: white;
            overflow: hidden;
        }
        
        .track-timeline {
            position: relative;
            height: 100%;
            width: 600px;
        }
        
        .scene-clip {
            position: absolute;
            top: 2px;
            height: 36px;
            background: #67C23A;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            padding: 0 8px;
            color: white;
            font-size: 11px;
        }
        
        .fix-list {
            list-style: none;
            padding: 0;
        }
        
        .fix-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .fix-list li:last-child {
            border-bottom: none;
        }
        
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .status.fixed {
            background: #d4edda;
            color: #155724;
        }
        
        .status.improved {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .current-time-display {
            position: absolute;
            top: -30px;
            left: 0;
            background: #ff4757;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            transform: translateX(-50%);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>时间轴修复完成</h1>
        <p>修复了视频轨道显示问题，优化了时间轴布局，添加了拖拽功能</p>

        <div class="demo-section">
            <h3>修复内容</h3>
            <ul class="fix-list">
                <li><span class="status fixed">✅ 修复</span>视频轨道多出来的 ">" 字符问题</li>
                <li><span class="status improved">🔧 优化</span>时间轴更稀疏：每15秒一个主刻度</li>
                <li><span class="status improved">➕ 新增</span>时间轴拖拽查看功能</li>
                <li><span class="status fixed">✅ 修复</span>时间轴与轨道内容对齐问题</li>
            </ul>
        </div>

        <div class="demo-section">
            <h3>新的时间轴布局演示</h3>
            <div class="editor-demo">
                <!-- 时间刻度行 -->
                <div class="time-ruler-row">
                    <div class="time-ruler-label"></div>
                    <div class="time-ruler" onclick="updateTime(event)" onmousedown="startDrag(event)">
                        <div class="time-marks-container">
                            <!-- 每15秒一个主刻度 -->
                            <div class="time-mark" style="left: 0px;">00:00</div>
                            <div class="time-mark" style="left: 150px;">00:15</div>
                            <div class="time-mark" style="left: 300px;">00:30</div>
                            <div class="time-mark" style="left: 450px;">00:45</div>
                            <div class="time-mark" style="left: 600px;">01:00</div>
                            
                            <!-- 每5秒一个小刻度 -->
                            <div class="time-tick" style="left: 75px;"></div>
                            <div class="time-tick" style="left: 125px;"></div>
                            <div class="time-tick" style="left: 225px;"></div>
                            <div class="time-tick" style="left: 275px;"></div>
                            <div class="time-tick" style="left: 375px;"></div>
                            <div class="time-tick" style="left: 425px;"></div>
                            <div class="time-tick" style="left: 525px;"></div>
                            <div class="time-tick" style="left: 575px;"></div>
                            
                            <!-- 播放指针 -->
                            <div class="playhead" id="playhead" style="left: 100px;">
                                <div class="current-time-display" id="timeDisplay">00:10</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 视频场景轨道 -->
                <div class="track-row">
                    <div class="track-label">
                        📹 视频场景
                    </div>
                    <div class="track-content">
                        <div class="track-timeline">
                            <div class="scene-clip" style="left: 0px; width: 80px;">开场场景</div>
                            <div class="scene-clip" style="left: 80px; width: 100px;">产品介绍</div>
                            <div class="scene-clip" style="left: 180px; width: 90px;">功能展示</div>
                        </div>
                    </div>
                </div>
                
                <!-- 文案轨道 -->
                <div class="track-row">
                    <div class="track-label">
                        📝 文案
                    </div>
                    <div class="track-content">
                        <div class="track-timeline">
                            <div class="scene-clip" style="left: 0px; width: 80px; background: #F56C6C;">文案1</div>
                            <div class="scene-clip" style="left: 80px; width: 100px; background: #F56C6C;">文案2</div>
                            <div class="scene-clip" style="left: 180px; width: 90px; background: #F56C6C;">文案3</div>
                        </div>
                    </div>
                </div>
            </div>
            <p><small>💡 点击或拖拽时间轴来移动播放指针</small></p>
        </div>

        <div class="demo-section">
            <h3>技术改进</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h4>时间轴优化</h4>
                    <ul>
                        <li><strong>主刻度间隔</strong>：15秒（更稀疏）</li>
                        <li><strong>小刻度间隔</strong>：5秒（辅助定位）</li>
                        <li><strong>总宽度</strong>：600像素</li>
                        <li><strong>像素比例</strong>：10像素/秒</li>
                    </ul>
                </div>
                <div>
                    <h4>交互功能</h4>
                    <ul>
                        <li><strong>点击定位</strong>：点击任意位置跳转</li>
                        <li><strong>拖拽查看</strong>：按住拖拽连续预览</li>
                        <li><strong>实时反馈</strong>：播放指针实时跟随</li>
                        <li><strong>场景预览</strong>：自动显示对应场景</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>使用说明</h3>
            <ol>
                <li><strong>点击时间轴</strong>：直接点击想要查看的时间位置</li>
                <li><strong>拖拽时间轴</strong>：按住鼠标左键拖拽，可以连续查看不同时间的内容</li>
                <li><strong>场景预览</strong>：播放指针会自动找到对应时间的场景并在预览面板显示</li>
                <li><strong>时间显示</strong>：播放指针上方显示当前精确时间</li>
            </ol>
        </div>
    </div>

    <script>
        let isDragging = false;
        
        function updateTime(event) {
            if (isDragging) return; // 如果正在拖拽，不处理点击
            
            const rect = event.currentTarget.getBoundingClientRect();
            const clickX = event.clientX - rect.left;
            const time = Math.floor((clickX / 600) * 60); // 600px对应60秒
            
            setTime(Math.max(0, Math.min(60, time)));
        }
        
        function startDrag(event) {
            event.preventDefault();
            isDragging = true;
            
            const rect = event.currentTarget.getBoundingClientRect();
            const startX = event.clientX - rect.left;
            const startTime = Math.floor((startX / 600) * 60);
            
            setTime(Math.max(0, Math.min(60, startTime)));
            
            const onMouseMove = (e) => {
                const currentX = e.clientX - rect.left;
                const time = Math.floor((currentX / 600) * 60);
                setTime(Math.max(0, Math.min(60, time)));
            };
            
            const onMouseUp = () => {
                document.removeEventListener('mousemove', onMouseMove);
                document.removeEventListener('mouseup', onMouseUp);
                setTimeout(() => { isDragging = false; }, 10); // 延迟重置，避免触发点击
            };
            
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
        }
        
        function setTime(time) {
            const playhead = document.getElementById('playhead');
            const timeDisplay = document.getElementById('timeDisplay');
            
            const position = (time / 60) * 600;
            playhead.style.left = position + 'px';
            
            const minutes = Math.floor(time / 60);
            const seconds = time % 60;
            const timeStr = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            timeDisplay.textContent = timeStr;
            
            console.log(`时间轴: ${timeStr} (${time}秒)`);
        }
        
        window.addEventListener('load', function() {
            console.log('✅ 时间轴修复完成');
            console.log('🎯 支持点击和拖拽查看');
            console.log('📏 更稀疏的时间刻度布局');
        });
    </script>
</body>
</html>
