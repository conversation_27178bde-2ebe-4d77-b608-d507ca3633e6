{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\up.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\up.vue", "mtime": 1754971237942}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753759483709}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBpbml0T1NTQ2xpZW50LCB1cGxvYWRGaWxlc1RvT1NTLCBnZXRPU1NGaWxlTGlzdCwgZGVsZXRlRmlsZUZyb21PU1MsIGdldE9TU0NsaWVudCB9IGZyb20gJ0AvdXRpbHMvb3NzVXBsb2FkJw0KaW1wb3J0IHsgZ2V0VG9rZW4gfSBmcm9tICdAL3V0aWxzL2F1dGgnDQppbXBvcnQgc3RvcmUgZnJvbSAnQC9zdG9yZScNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnU3RvcmVyVXAnLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDnlYzpnaLmjqfliLYNCiAgICAgIG1hdGVyaWFsVGFiOiAnc3VjYWknLCAvLyBzdWNhaSwgYmdtDQogICAgICBzZWxlY3RlZEZvbGRlcjogMSwNCiAgICAgIHNlbGVjdEFsbDogZmFsc2UsDQogICAgICBzZWxlY3RlZEZpbGVzOiBbXSwNCiAgICAgIGN1cnJlbnRQYWdlOiAxLA0KICAgICAgcGFnZVNpemU6IDEwLA0KDQogICAgICAvLyDliIbliKvkuLpCR03lkozntKDmnZDnu7TmiqTmlofku7blpLnnu5PmnoQNCiAgICAgIGJnbUZvbGRlclRyZWU6IFtdLA0KICAgICAgc3VjYWlGb2xkZXJUcmVlOiBbXSwNCg0KICAgICAgLy8g5LiK5Lyg55u45YWzDQogICAgICB1cGxvYWREaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHVwbG9hZGluZzogZmFsc2UsDQogICAgICBmaWxlTGlzdDogW10sDQogICAgICB1cGxvYWRGb3JtOiB7fSwNCiAgICAgIHVwbG9hZFByb2dyZXNzOiB7fSwgLy8g5LiK5Lyg6L+b5bqmDQogICAgICBzdG9yYWdlVHlwZTogJ29zcycsIC8vIOWtmOWCqOaWueW8jzogbG9jYWwgfCBvc3MNCg0KICAgICAgLy8gT1NT6YWN572u55u45YWzDQogICAgICBvc3NDb25maWdWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHRlc3RpbmdPU1M6IGZhbHNlLA0KICAgICAgb3NzQ29uZmlnOiB7DQogICAgICAgIHN0b3JhZ2VUeXBlOiAnb3NzJywgLy8g5a2Y5YKo5pa55byPDQogICAgICAgIGJ1Y2tldDogJycsIC8vIOWtmOWCqOepuumXtOWQjeensA0KICAgICAgICBhY2Nlc3NLZXlJZDogJycsIC8vIEFDQ0VTU19LRVkNCiAgICAgICAgYWNjZXNzS2V5U2VjcmV0OiAnJywgLy8gU0VDUkVUX0tFWQ0KICAgICAgICBlbmRwb2ludDogJycsIC8vIOepuumXtOWfn+WQjQ0KICAgICAgICBzdGF0dXM6ICdlbmFibGVkJyAvLyDnirbmgIHvvJplbmFibGVkL2Rpc2FibGVkDQogICAgICB9LA0KICAgICAgb3NzSW5pdGlhbGl6ZWQ6IGZhbHNlLCAvLyBPU1PmmK/lkKblt7LliJ3lp4vljJYNCg0KICAgICAgLy8g5paH5Lu25aS5566h55CGDQogICAgICBjdXJyZW50Rm9sZGVyOiAn5oC7JywgLy8g5b2T5YmN6YCJ5Lit55qE5paH5Lu25aS5DQogICAgICBmb2xkZXJEaWFsb2dWaXNpYmxlOiBmYWxzZSwgLy8g5paH5Lu25aS5566h55CG5a+56K+d5qGGDQogICAgICBuZXdGb2xkZXJOYW1lOiAnJywgLy8g5paw5paH5Lu25aS55ZCN56ewDQogICAgICByZW5hbWVGb2xkZXJOYW1lOiAnJywgLy8g6YeN5ZG95ZCN5paH5Lu25aS55ZCN56ewDQogICAgICBzZWxlY3RlZEZvbGRlcjogJycsIC8vIOmAieS4reeahOaWh+S7tuWkuQ0KDQogICAgICAvLyDlj7PplK7oj5zljZUNCiAgICAgIGNvbnRleHRNZW51VmlzaWJsZTogZmFsc2UsDQogICAgICBjb250ZXh0TWVudVg6IDAsDQogICAgICBjb250ZXh0TWVudVk6IDAsDQogICAgICBjb250ZXh0TWVudUZvbGRlcjogbnVsbCwNCg0KICAgICAgLy8g5paw5bu65paH5Lu25aS5DQogICAgICBjcmVhdGVGb2xkZXJWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGNyZWF0ZUZvbGRlckZvcm06IHsNCiAgICAgICAgbmFtZTogJycNCiAgICAgIH0sDQoNCiAgICAgIC8vIOaWh+S7tumihOiniA0KICAgICAgcHJldmlld1Zpc2libGU6IGZhbHNlLA0KICAgICAgY3VycmVudFByZXZpZXdGaWxlOiBudWxsLA0KDQogICAgICAvLyDmjqfliLbmjInpkq7pmpDol4/lrprml7blmagNCiAgICAgIGNvbnRyb2xUaW1lcnM6IHt9LA0KDQoNCg0KICAgICAgLy8gQkdN5paH5Lu25pWw5o2uIChhYmdtL2FkbWluL+aAuy8g5paH5Lu25aS5KSAtIOa3u+WKoOa1i+ivleaVsOaNrg0KICAgICAgYmdtTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgaWQ6ICdiZ20xJywNCiAgICAgICAgICBuYW1lOiAn5rWL6K+V6Z+z6aKRLm1wMycsDQogICAgICAgICAgdHlwZTogJ2F1ZGlvJywNCiAgICAgICAgICBzaXplOiA1MTIwMDAsDQogICAgICAgICAgdXBsb2FkVGltZTogJzIwMjQtMDEtMDEgMTI6MDA6MDAnLA0KICAgICAgICAgIGR1cmF0aW9uOiAnMDI6MzAnLA0KICAgICAgICAgIHVybDogJ2h0dHBzOi8vd3d3LnNvdW5kamF5LmNvbS9taXNjL3NvdW5kcy9iZWxsLXJpbmdpbmctMDUud2F2Jw0KICAgICAgICB9DQogICAgICBdLA0KDQogICAgICAvLyDntKDmnZDmlofku7bmlbDmja4gKGFzdWNhaS9hZG1pbi/mgLsvIOaWh+S7tuWkuSkgLSDmt7vliqDmtYvor5XmlbDmja4NCiAgICAgIHN1Y2FpTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgaWQ6ICdzdWNhaTEnLA0KICAgICAgICAgIG5hbWU6ICfmtYvor5Xop4bpopEubXA0JywNCiAgICAgICAgICB0eXBlOiAndmlkZW8nLA0KICAgICAgICAgIHNpemU6IDEwMjQwMDAsDQogICAgICAgICAgdXBsb2FkVGltZTogJzIwMjQtMDEtMDEgMTI6MDA6MDAnLA0KICAgICAgICAgIGR1cmF0aW9uOiAnMDA6MzAnLA0KICAgICAgICAgIHVybDogJ2h0dHBzOi8vd3d3Lnczc2Nob29scy5jb20vaHRtbC9tb3ZfYmJiLm1wNCcNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAnc3VjYWkyJywNCiAgICAgICAgICBuYW1lOiAn5rWL6K+V5Zu+54mHLmpwZycsDQogICAgICAgICAgdHlwZTogJ2ltYWdlJywNCiAgICAgICAgICBzaXplOiAyNTYwMDAsDQogICAgICAgICAgdXBsb2FkVGltZTogJzIwMjQtMDEtMDEgMTI6MDA6MDAnLA0KICAgICAgICAgIHVybDogJ2h0dHBzOi8vcGljc3VtLnBob3Rvcy80MDAvMzAwJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6ICdzdWNhaTMnLA0KICAgICAgICAgIG5hbWU6ICfmtYvor5Xpn7PpopEubXAzJywNCiAgICAgICAgICB0eXBlOiAnYXVkaW8nLA0KICAgICAgICAgIHNpemU6IDUxMjAwMCwNCiAgICAgICAgICB1cGxvYWRUaW1lOiAnMjAyNC0wMS0wMSAxMjowMDowMCcsDQogICAgICAgICAgZHVyYXRpb246ICcwMTo0NScsDQogICAgICAgICAgdXJsOiAnaHR0cHM6Ly93d3cuc291bmRqYXkuY29tL21pc2Mvc291bmRzL2JlbGwtcmluZ2luZy0wNS53YXYnDQogICAgICAgIH0NCiAgICAgIF0NCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLy8g5qC55o2u5b2T5YmN5qCH562+6aG16L+U5Zue5a+55bqU55qE5paH5Lu25YiX6KGoDQogICAgY3VycmVudE1hdGVyaWFsTGlzdCgpIHsNCiAgICAgIHN3aXRjaCAodGhpcy5tYXRlcmlhbFRhYikgew0KICAgICAgICBjYXNlICdiZ20nOg0KICAgICAgICAgIHJldHVybiB0aGlzLmJnbUxpc3QNCiAgICAgICAgY2FzZSAnc3VjYWknOg0KICAgICAgICAgIHJldHVybiB0aGlzLnN1Y2FpTGlzdA0KICAgICAgICBkZWZhdWx0Og0KICAgICAgICAgIHJldHVybiB0aGlzLnN1Y2FpTGlzdA0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDmoLnmja7lvZPliY3moIfnrb7pobXov5Tlm57lr7nlupTnmoTmlofku7blpLnliJfooagNCiAgICBjdXJyZW50Rm9sZGVyVHJlZSgpIHsNCiAgICAgIHN3aXRjaCAodGhpcy5tYXRlcmlhbFRhYikgew0KICAgICAgICBjYXNlICdiZ20nOg0KICAgICAgICAgIHJldHVybiB0aGlzLmJnbUZvbGRlclRyZWUNCiAgICAgICAgY2FzZSAnc3VjYWknOg0KICAgICAgICAgIHJldHVybiB0aGlzLnN1Y2FpRm9sZGVyVHJlZQ0KICAgICAgICBkZWZhdWx0Og0KICAgICAgICAgIHJldHVybiB0aGlzLnN1Y2FpRm9sZGVyVHJlZQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBmaWx0ZXJlZE1hdGVyaWFsTGlzdCgpIHsNCiAgICAgIGxldCBsaXN0ID0gdGhpcy5jdXJyZW50TWF0ZXJpYWxMaXN0DQoNCiAgICAgIC8vIOaMieaWh+S7tuWkuei/h+a7pO+8iOeugOWMlueJiOacrO+8iQ0KICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRGb2xkZXIgIT09IDEpIHsNCiAgICAgICAgLy8g6L+Z6YeM5Y+v5Lul5qC55o2u5a6e6ZmF6ZyA5rGC5re75Yqg6L+H5ruk6YC76L6RDQogICAgICAgIC8vIOebruWJjeaYvuekuuaJgOacieaWh+S7tg0KICAgICAgfQ0KDQogICAgICByZXR1cm4gbGlzdA0KICAgIH0sDQoNCiAgICBwYWdpbmF0ZWRNYXRlcmlhbHMoKSB7DQogICAgICBjb25zdCBzdGFydCA9ICh0aGlzLmN1cnJlbnRQYWdlIC0gMSkgKiB0aGlzLnBhZ2VTaXplDQogICAgICBjb25zdCBlbmQgPSBzdGFydCArIHRoaXMucGFnZVNpemUNCiAgICAgIHJldHVybiB0aGlzLmZpbHRlcmVkTWF0ZXJpYWxMaXN0LnNsaWNlKHN0YXJ0LCBlbmQpDQogICAgfSwNCg0KICAgIHRvdGFsUGFnZXMoKSB7DQogICAgICByZXR1cm4gTWF0aC5jZWlsKHRoaXMuZmlsdGVyZWRNYXRlcmlhbExpc3QubGVuZ3RoIC8gdGhpcy5wYWdlU2l6ZSkNCiAgICB9LA0KDQogICAgLy8g5LiK5Lyg5a+56K+d5qGG55u45YWz6K6h566X5bGe5oCnDQogICAgdXBsb2FkRGlhbG9nVGl0bGUoKSB7DQogICAgICBzd2l0Y2ggKHRoaXMubWF0ZXJpYWxUYWIpIHsNCiAgICAgICAgY2FzZSAnYmdtJzoNCiAgICAgICAgICByZXR1cm4gJ+S4iuS8oEJHTeaWh+S7ticNCiAgICAgICAgY2FzZSAnc3VjYWknOg0KICAgICAgICAgIHJldHVybiAn5LiK5Lyg57Sg5p2Q5paH5Lu2Jw0KICAgICAgICBkZWZhdWx0Og0KICAgICAgICAgIHJldHVybiAn5LiK5Lyg5paH5Lu2Jw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICB1cGxvYWRUaXBUZXh0KCkgew0KICAgICAgc3dpdGNoICh0aGlzLm1hdGVyaWFsVGFiKSB7DQogICAgICAgIGNhc2UgJ2JnbSc6DQogICAgICAgICAgcmV0dXJuICfmlK/mjIEgTVAz44CBV0FW44CBRkxBQ+OAgUFBQ+OAgU00QeOAgU9HR+OAgVdNQSDnrYnpn7PpopHmoLzlvI/vvIzljZXkuKrmlofku7bkuI3otoXov4cxMDBNQicNCiAgICAgICAgY2FzZSAnc3VjYWknOg0KICAgICAgICAgIHJldHVybiAn5pSv5oyB5ZCE56eN6KeG6aKR44CB6Z+z6aKR44CB5Zu+54mH5qC85byP77yM5Y2V5Liq5paH5Lu25LiN6LaF6L+HNTAwTUInDQogICAgICAgIGRlZmF1bHQ6DQogICAgICAgICAgcmV0dXJuICfor7fpgInmi6nopoHkuIrkvKDnmoTmlofku7YnDQogICAgICB9DQogICAgfSwNCg0KDQogIH0sDQogIGFzeW5jIGNyZWF0ZWQoKSB7DQogICAgLy8g5YWI5Yqg6L29T1NT6YWN572uDQogICAgdGhpcy5sb2FkT1NTQ29uZmlnKCkNCg0KICAgIC8vIOWmguaenE9TU+W3sumFjee9ru+8jOWImeWKoOi9veaWh+S7tuWIl+ihqA0KICAgIGlmICh0aGlzLm9zc0luaXRpYWxpemVkKSB7DQogICAgICBhd2FpdCB0aGlzLmxvYWRNYXRlcmlhbExpc3QoKQ0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIEJHTeWFjei0ueS4i+i9vQ0KICAgIG9wZW5CR01Eb3dubG9hZFNpdGUoKSB7DQogICAgICB3aW5kb3cub3BlbignaHR0cHM6Ly93d3cuYnVndXl5LnRvcC8nLCAnX2JsYW5rJykNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5q2j5Zyo5omT5byA5YWN6LS5QkdN5LiL6L29572R56uZLi4uJykNCiAgICB9LA0KDQogICAgLy8g5Z+656GA5pa55rOVDQogICAgYXN5bmMgbG9hZE1hdGVyaWFsTGlzdCgpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfliqDovb3ntKDmnZDliJfooagnKQ0KDQogICAgICAvLyDlpoLmnpxPU1Plt7LliJ3lp4vljJbvvIzku45PU1PliqDovb3mlofku7bliJfooagNCiAgICAgIGlmICh0aGlzLm9zc0luaXRpYWxpemVkKSB7DQogICAgICAgIGF3YWl0IHRoaXMubG9hZEZpbGVzRnJvbU9TUygpDQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb25zb2xlLmxvZygnT1NT5pyq5Yid5aeL5YyW77yM6Lez6L+H5paH5Lu25Yqg6L29JykNCiAgICAgIH0NCg0KICAgICAgLy8g5pu05paw5paH5Lu25aS56K6h5pWwDQogICAgICB0aGlzLnVwZGF0ZUN1cnJlbnRUYWJGb2xkZXJDb3VudHMoKQ0KICAgIH0sDQoNCiAgICAvLyDku45PU1PliqDovb3mlofku7bliJfooagNCiAgICBhc3luYyBsb2FkRmlsZXNGcm9tT1NTKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgY2xpZW50ID0gZ2V0T1NTQ2xpZW50KCkNCiAgICAgICAgY29uc3QgdXNlciA9IHRoaXMuZ2V0Q3VycmVudFVzZXIoKQ0KICAgICAgICBjb25zdCBjdXJyZW50Rm9sZGVyID0gdGhpcy5nZXRDdXJyZW50Rm9sZGVyKCkNCg0KICAgICAgICAvLyDmuIXnqbrnjrDmnInliJfooagNCiAgICAgICAgdGhpcy5iZ21MaXN0ID0gW10NCiAgICAgICAgdGhpcy5zdWNhaUxpc3QgPSBbXQ0KDQogICAgICAgIC8vIOWKoOi9vUJHTeaWh+S7tg0KICAgICAgICBhd2FpdCB0aGlzLmxvYWRGaWxlc0Zyb21Gb2xkZXIoJ2FiZ20nLCB1c2VyLCBjdXJyZW50Rm9sZGVyLCAnYmdtJykNCg0KICAgICAgICAvLyDliqDovb3ntKDmnZDmlofku7YNCiAgICAgICAgYXdhaXQgdGhpcy5sb2FkRmlsZXNGcm9tRm9sZGVyKCdhc3VjYWknLCB1c2VyLCBjdXJyZW50Rm9sZGVyLCAnc3VjYWknKQ0KDQogICAgICAgIGNvbnNvbGUubG9nKGDku45PU1PliqDovb3mlofku7blrozmiJAgLSBCR006ICR7dGhpcy5iZ21MaXN0Lmxlbmd0aH3kuKosIOe0oOadkDogJHt0aGlzLnN1Y2FpTGlzdC5sZW5ndGh95LiqYCkNCg0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5LuOT1NT5Yqg6L295paH5Lu25YiX6KGo5aSx6LSlOicsIGVycm9yKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDku47mjIflrprmlofku7blpLnliqDovb3mlofku7YNCiAgICBhc3luYyBsb2FkRmlsZXNGcm9tRm9sZGVyKGJhc2VGb2xkZXIsIHVzZXIsIGZvbGRlciwgbGlzdFR5cGUpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IGNsaWVudCA9IGdldE9TU0NsaWVudCgpDQogICAgICAgIGNvbnN0IHByZWZpeCA9IGAke2Jhc2VGb2xkZXJ9LyR7dXNlcn0vJHtmb2xkZXJ9L2ANCg0KICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBjbGllbnQubGlzdCh7DQogICAgICAgICAgcHJlZml4OiBwcmVmaXgsDQogICAgICAgICAgJ21heC1rZXlzJzogMTAwMA0KICAgICAgICB9KQ0KDQogICAgICAgIGlmIChyZXN1bHQub2JqZWN0cykgew0KICAgICAgICAgIGNvbnN0IGZpbGVzID0gcmVzdWx0Lm9iamVjdHMNCiAgICAgICAgICAgIC5maWx0ZXIob2JqID0+ICFvYmoubmFtZS5lbmRzV2l0aCgnLmtlZXAnKSAmJiAhb2JqLm5hbWUuZW5kc1dpdGgoJy8nKSkNCiAgICAgICAgICAgIC5tYXAoKG9iaiwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgICAgY29uc3QgZmlsZU5hbWUgPSBvYmoubmFtZS5zcGxpdCgnLycpLnBvcCgpDQogICAgICAgICAgICAgIGNvbnN0IGZpbGVFeHRlbnNpb24gPSBmaWxlTmFtZS50b0xvd2VyQ2FzZSgpLnNwbGl0KCcuJykucG9wKCkNCg0KICAgICAgICAgICAgICAvLyDliKTmlq3mlofku7bnsbvlnosNCiAgICAgICAgICAgICAgY29uc3QgYXVkaW9FeHRzID0gWydtcDMnLCAnd2F2JywgJ2ZsYWMnLCAnYWFjJywgJ200YScsICdvZ2cnLCAnd21hJ10NCiAgICAgICAgICAgICAgY29uc3QgdmlkZW9FeHRzID0gWydtcDQnLCAnYXZpJywgJ21vdicsICd3bXYnLCAnZmx2JywgJ3dlYm0nLCAnM2dwJywgJ21rdicsICdtNHYnXQ0KDQogICAgICAgICAgICAgIGxldCBmaWxlVHlwZSA9ICd1bmtub3duJw0KICAgICAgICAgICAgICBpZiAoYXVkaW9FeHRzLmluY2x1ZGVzKGZpbGVFeHRlbnNpb24pKSB7DQogICAgICAgICAgICAgICAgZmlsZVR5cGUgPSAnYXVkaW8nDQogICAgICAgICAgICAgIH0gZWxzZSBpZiAodmlkZW9FeHRzLmluY2x1ZGVzKGZpbGVFeHRlbnNpb24pKSB7DQogICAgICAgICAgICAgICAgZmlsZVR5cGUgPSAndmlkZW8nDQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICAgIGlkOiBEYXRlLm5vdygpICsgTWF0aC5yYW5kb20oKSArIGluZGV4LA0KICAgICAgICAgICAgICAgIG5hbWU6IGZpbGVOYW1lLA0KICAgICAgICAgICAgICAgIHR5cGU6IGZpbGVUeXBlLA0KICAgICAgICAgICAgICAgIHNpemU6IG9iai5zaXplLA0KICAgICAgICAgICAgICAgIHVwbG9hZFRpbWU6IG5ldyBEYXRlKG9iai5sYXN0TW9kaWZpZWQpLnRvTG9jYWxlU3RyaW5nKCkuc2xpY2UoMCwgMTYpLA0KICAgICAgICAgICAgICAgIGR1cmF0aW9uOiBmaWxlVHlwZSA9PT0gJ3ZpZGVvJyA/ICcwMDowMjozMCcgOiAnMDA6MDM6NDUnLCAvLyDpu5jorqTlgLzvvIzlrp7pmYXlupTor6Xku47mlofku7blhYPmlbDmja7ojrflj5YNCiAgICAgICAgICAgICAgICByZXNvbHV0aW9uOiBmaWxlVHlwZSA9PT0gJ3ZpZGVvJyA/ICcxOTIweDEwODAnIDogdW5kZWZpbmVkLA0KICAgICAgICAgICAgICAgIGJpdHJhdGU6IGZpbGVUeXBlID09PSAnYXVkaW8nID8gJzEyOGticHMnIDogdW5kZWZpbmVkLA0KICAgICAgICAgICAgICAgIHVybDogYGh0dHBzOi8vJHt0aGlzLm9zc0NvbmZpZy5idWNrZXR9LiR7dGhpcy5vc3NDb25maWcuZW5kcG9pbnQucmVwbGFjZSgnaHR0cHM6Ly8nLCAnJykucmVwbGFjZSh0aGlzLm9zc0NvbmZpZy5idWNrZXQgKyAnLicsICcnKX0vJHtvYmoubmFtZX1gLA0KICAgICAgICAgICAgICAgIG9zc0ZpbGVOYW1lOiBvYmoubmFtZSwNCiAgICAgICAgICAgICAgICBmb2xkZXI6IGAke2Jhc2VGb2xkZXJ9LyR7dXNlcn0vJHtmb2xkZXJ9YA0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KQ0KDQogICAgICAgICAgLy8g5re75Yqg5Yiw5a+55bqU55qE5YiX6KGoDQogICAgICAgICAgaWYgKGxpc3RUeXBlID09PSAnYmdtJykgew0KICAgICAgICAgICAgdGhpcy5iZ21MaXN0LnB1c2goLi4uZmlsZXMpDQogICAgICAgICAgfSBlbHNlIGlmIChsaXN0VHlwZSA9PT0gJ3N1Y2FpJykgew0KICAgICAgICAgICAgdGhpcy5zdWNhaUxpc3QucHVzaCguLi5maWxlcykNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcihg5LuO5paH5Lu25aS5ICR7YmFzZUZvbGRlcn0vJHt1c2VyfS8ke2ZvbGRlcn0g5Yqg6L295paH5Lu25aSx6LSlOmAsIGVycm9yKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyBPU1PphY3nva7nm7jlhbPmlrnms5UNCiAgICBsb2FkT1NTQ29uZmlnKCkgew0KICAgICAgLy8g5LuObG9jYWxTdG9yYWdl5Yqg6L29T1NT6YWN572uDQogICAgICBjb25zdCBzYXZlZENvbmZpZyA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdvc3NDb25maWcnKQ0KICAgICAgaWYgKHNhdmVkQ29uZmlnKSB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgdGhpcy5vc3NDb25maWcgPSB7IC4uLnRoaXMub3NzQ29uZmlnLCAuLi5KU09OLnBhcnNlKHNhdmVkQ29uZmlnKSB9DQogICAgICAgICAgaWYgKHRoaXMub3NzQ29uZmlnLmFjY2Vzc0tleUlkICYmIHRoaXMub3NzQ29uZmlnLmFjY2Vzc0tleVNlY3JldCkgew0KICAgICAgICAgICAgdGhpcy5pbml0aWFsaXplT1NTKCkNCiAgICAgICAgICB9DQogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L29T1NT6YWN572u5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCg0KICAgIGFzeW5jIHNhdmVPU1NDb25maWcoKSB7DQogICAgICAvLyDpqozor4Hlv4XloavlrZfmrrUNCiAgICAgIGlmICghdGhpcy5vc3NDb25maWcuYnVja2V0KSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+i+k+WFpeWtmOWCqOepuumXtOWQjeensCcpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgaWYgKCF0aGlzLm9zc0NvbmZpZy5hY2Nlc3NLZXlJZCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfor7fovpPlhaVBQ0NFU1NfS0VZJykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBpZiAoIXRoaXMub3NzQ29uZmlnLmFjY2Vzc0tleVNlY3JldCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfor7fovpPlhaVTRUNSRVRfS0VZJykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBpZiAoIXRoaXMub3NzQ29uZmlnLmVuZHBvaW50KSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+i+k+WFpeepuumXtOWfn+WQjScpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICAvLyDpqozor4Hln5/lkI3moLzlvI8NCiAgICAgIGlmICghdGhpcy5vc3NDb25maWcuZW5kcG9pbnQuc3RhcnRzV2l0aCgnaHR0cDovLycpICYmICF0aGlzLm9zc0NvbmZpZy5lbmRwb2ludC5zdGFydHNXaXRoKCdodHRwczovLycpKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+epuumXtOWfn+WQjeW/hemhu+S7pWh0dHA6Ly/miJZodHRwczovL+W8gOWktCcpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICAvLyDpqozor4HmmK/lkKbkuLrmoIflh4bnmoRPU1Pln5/lkI3moLzlvI8NCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHVybCA9IG5ldyBVUkwodGhpcy5vc3NDb25maWcuZW5kcG9pbnQpDQogICAgICAgIGNvbnN0IGhvc3RuYW1lID0gdXJsLmhvc3RuYW1lDQogICAgICAgIGNvbnN0IG1hdGNoID0gaG9zdG5hbWUubWF0Y2goL14oW14uXSspXC5vc3MtKFteLl0rKVwuYWxpeXVuY3NcLmNvbSQvKQ0KDQogICAgICAgIGlmICghbWF0Y2gpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfor7fovpPlhaXmoIflh4bnmoRPU1Pln5/lkI3moLzlvI/vvIzlpoLvvJpodHRwczovL2J1Y2tldC5vc3MtcmVnaW9uLmFsaXl1bmNzLmNvbScpDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCg0KICAgICAgICBjb25zdCBbLCBidWNrZXQsIHJlZ2lvbl0gPSBtYXRjaA0KICAgICAgICBpZiAoYnVja2V0ICE9PSB0aGlzLm9zc0NvbmZpZy5idWNrZXQpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDln5/lkI3kuK3nmoTlrZjlgqjmobblkI3np7AoJHtidWNrZXR9KeS4jumFjee9rueahOWtmOWCqOahtuWQjeensCgke3RoaXMub3NzQ29uZmlnLmJ1Y2tldH0p5LiN5Yy56YWNYCkNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Z+f5ZCN5qC85byP5LiN5q2j56GuJykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIHRoaXMudGVzdGluZ09TUyA9IHRydWUNCg0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g55u05o6l5L2/55So6YWN572u5Yid5aeL5YyWT1NT5a6i5oi356uvDQogICAgICAgIGF3YWl0IHRoaXMuaW5pdGlhbGl6ZU9TUygpDQoNCiAgICAgICAgLy8g5L+d5a2Y6YWN572u5YiwbG9jYWxTdG9yYWdlDQogICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdvc3NDb25maWcnLCBKU09OLnN0cmluZ2lmeSh0aGlzLm9zc0NvbmZpZykpDQoNCiAgICAgICAgdGhpcy5vc3NDb25maWdWaXNpYmxlID0gZmFsc2UNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCdPU1PphY3nva7kv53lrZjmiJDlip/vvIEnKQ0KDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCdPU1PphY3nva7mtYvor5XlpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYE9TU+mFjee9ruWksei0pTogJHtlcnJvci5tZXNzYWdlfWApDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLnRlc3RpbmdPU1MgPSBmYWxzZQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBhc3luYyBpbml0aWFsaXplT1NTKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgY2xpZW50ID0gaW5pdE9TU0NsaWVudCh0aGlzLm9zc0NvbmZpZykNCiAgICAgICAgdGhpcy5vc3NJbml0aWFsaXplZCA9IHRydWUNCg0KICAgICAgICAvLyBPU1PliJ3lp4vljJbmiJDlip/lkI7vvIzojrflj5bnlKjmiLfnmoTmlofku7blpLnliJfooagNCiAgICAgICAgYXdhaXQgdGhpcy5sb2FkVXNlckZvbGRlcnNGcm9tT1NTKCkNCg0KICAgICAgICBjb25zb2xlLmxvZygnT1NT5a6i5oi356uv5Yid5aeL5YyW5oiQ5YqfJykNCiAgICAgICAgcmV0dXJuIGNsaWVudA0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgdGhpcy5vc3NJbml0aWFsaXplZCA9IGZhbHNlDQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ09TU+WuouaIt+err+WIneWni+WMluWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhyb3cgZXJyb3INCiAgICAgIH0NCiAgICB9LA0KDQogICAgb3Blbk9TU0NvbmZpZygpIHsNCiAgICAgIHRoaXMub3NzQ29uZmlnVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KDQogICAgLy8g5pi+56S65paw5bu65paH5Lu25aS55a+56K+d5qGGDQogICAgc2hvd0NyZWF0ZUZvbGRlckRpYWxvZygpIHsNCiAgICAgIHRoaXMuY3JlYXRlRm9sZGVyRm9ybS5uYW1lID0gJycNCiAgICAgIHRoaXMuY3JlYXRlRm9sZGVyVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KDQogICAgLy8g5Yib5bu65paH5Lu25aS5DQogICAgYXN5bmMgY3JlYXRlRm9sZGVyKCkgew0KICAgICAgY29uc29sZS5sb2coJ/CfmoAg5byA5aeL5Yib5bu65paH5Lu25aS5JykNCg0KICAgICAgaWYgKCF0aGlzLmNyZWF0ZUZvbGRlckZvcm0ubmFtZSB8fCB0aGlzLmNyZWF0ZUZvbGRlckZvcm0ubmFtZS50cmltKCkgPT09ICcnKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aWh+S7tuWkueWQjeensOS4jeiDveS4uuepuicpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICBjb25zdCBmb2xkZXJOYW1lID0gdGhpcy5jcmVhdGVGb2xkZXJGb3JtLm5hbWUudHJpbSgpDQogICAgICBjb25zb2xlLmxvZygn8J+TgSDmlofku7blpLnlkI3np7A6JywgZm9sZGVyTmFtZSkNCiAgICAgIGNvbnNvbGUubG9nKCfwn5OLIOW9k+WJjeagh+etvumhtTonLCB0aGlzLm1hdGVyaWFsVGFiKQ0KDQogICAgICAvLyDmo4Dmn6XlvZPliY3moIfnrb7pobXnmoTmlofku7blpLnmmK/lkKblt7LlrZjlnKgNCiAgICAgIGNvbnN0IGN1cnJlbnRGb2xkZXJzID0gdGhpcy5jdXJyZW50Rm9sZGVyVHJlZQ0KICAgICAgY29uc29sZS5sb2coJ/Cfk4Ig5b2T5YmN5paH5Lu25aS55YiX6KGoOicsIGN1cnJlbnRGb2xkZXJzLm1hcChmID0+IGYubmFtZSkpDQoNCiAgICAgIGlmIChjdXJyZW50Rm9sZGVycy5zb21lKGZvbGRlciA9PiBmb2xkZXIubmFtZSA9PT0gZm9sZGVyTmFtZSkpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihgJHt0aGlzLm1hdGVyaWFsVGFiID09PSAnYmdtJyA/ICdCR00nIDogJ+e0oOadkCd95paH5Lu25aS55bey5a2Y5ZyoYCkNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnNvbGUubG9nKCfinIUg5byA5aeL5Yib5bu65pys5Zyw5paH5Lu25aS56K6w5b2VJykNCg0KICAgICAgICAvLyDlhYjliJvlu7rmnKzlnLDmlofku7blpLnorrDlvZUNCiAgICAgICAgY29uc3QgbmV3Rm9sZGVyID0gew0KICAgICAgICAgIGlkOiBEYXRlLm5vdygpICsgTWF0aC5yYW5kb20oKSwgLy8g5L2/55So5pe26Ze05oiz6YG/5YWNSUTlhrLnqoENCiAgICAgICAgICBuYW1lOiBmb2xkZXJOYW1lLA0KICAgICAgICAgIGNvdW50OiAwDQogICAgICAgIH0NCg0KICAgICAgICBpZiAodGhpcy5tYXRlcmlhbFRhYiA9PT0gJ2JnbScpIHsNCiAgICAgICAgICB0aGlzLmJnbUZvbGRlclRyZWUucHVzaChuZXdGb2xkZXIpDQogICAgICAgICAgY29uc29sZS5sb2coJ/Cfk4Eg5bey5re75Yqg5YiwQkdN5paH5Lu25aS55qCRJykNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLnN1Y2FpRm9sZGVyVHJlZS5wdXNoKG5ld0ZvbGRlcikNCiAgICAgICAgICBjb25zb2xlLmxvZygn8J+TgSDlt7Lmt7vliqDliLDntKDmnZDmlofku7blpLnmoJEnKQ0KICAgICAgICB9DQoNCiAgICAgICAgY29uc29sZS5sb2coJ+KchSDmnKzlnLDmlofku7blpLnliJvlu7rmiJDlip8nKQ0KICAgICAgICB0aGlzLmNyZWF0ZUZvbGRlclZpc2libGUgPSBmYWxzZQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYCR7dGhpcy5tYXRlcmlhbFRhYiA9PT0gJ2JnbScgPyAnQkdNJyA6ICfntKDmnZAnfeaWh+S7tuWkueWIm+W7uuaIkOWKn2ApDQoNCiAgICAgICAgLy8g5byC5q2l5aSE55CGT1NT5Yib5bu677yM5LiN6Zi75aGeVUkNCiAgICAgICAgaWYgKHRoaXMuc3RvcmFnZVR5cGUgPT09ICdvc3MnICYmIHRoaXMub3NzSW5pdGlhbGl6ZWQpIHsNCiAgICAgICAgICB0aGlzLmNyZWF0ZU9TU0ZvbGRlckFzeW5jKGZvbGRlck5hbWUpDQogICAgICAgIH0NCg0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOWIm+W7uuaWh+S7tuWkueWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihg5Yib5bu65aSx6LSlOiAke2Vycm9yLm1lc3NhZ2V9YCkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5byC5q2l5Yib5bu6T1NT5paH5Lu25aS577yM5LiN6Zi75aGeVUkNCiAgICBhc3luYyBjcmVhdGVPU1NGb2xkZXJBc3luYyhmb2xkZXJOYW1lKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zb2xlLmxvZygn8J+MkCDlvIDlp4vlvILmraXliJvlu7pPU1Pmlofku7blpLk6JywgZm9sZGVyTmFtZSkNCiAgICAgICAgYXdhaXQgdGhpcy5jcmVhdGVPU1NGb2xkZXJGb3JDdXJyZW50VGFiKGZvbGRlck5hbWUpDQogICAgICAgIGNvbnNvbGUubG9nKCfinIUgT1NT5paH5Lu25aS55Yib5bu65oiQ5YqfJykNCiAgICAgIH0gY2F0Y2ggKG9zc0Vycm9yKSB7DQogICAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIE9TU+aWh+S7tuWkueWIm+W7uuWksei0pTonLCBvc3NFcnJvci5tZXNzYWdlKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyBPU1Pmlofku7blpLnliJvlu7rvvIjml6fmlrnms5XvvIzkuLrkuKTnp43nsbvlnovpg73liJvlu7rvvIkNCiAgICBhc3luYyBjcmVhdGVPU1NGb2xkZXIoZm9sZGVyTmFtZSkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgY2xpZW50ID0gZ2V0T1NTQ2xpZW50KCkNCiAgICAgICAgY29uc3QgdXNlciA9IHRoaXMuZ2V0Q3VycmVudFVzZXIoKQ0KDQogICAgICAgIC8vIOS4uuS4pOenjeexu+Wei+WIm+W7uuepuuaWh+S7tuWkue+8iOmAmui/h+S4iuS8oOepuuaWh+S7tuWunueOsO+8iQ0KICAgICAgICBjb25zdCBiYXNlRm9sZGVycyA9IFsnYWJnbScsICdhc3VjYWknXQ0KDQogICAgICAgIGZvciAoY29uc3QgYmFzZUZvbGRlciBvZiBiYXNlRm9sZGVycykgew0KICAgICAgICAgIGNvbnN0IGZvbGRlclBhdGggPSBgJHtiYXNlRm9sZGVyfS8ke3VzZXJ9LyR7Zm9sZGVyTmFtZX0vLmtlZXBgDQoNCiAgICAgICAgICAvLyDkuIrkvKDkuIDkuKrnqbrmlofku7bmnaXliJvlu7rmlofku7blpLnnu5PmnoQNCiAgICAgICAgICBhd2FpdCBjbGllbnQucHV0KGZvbGRlclBhdGgsIEJ1ZmZlci5mcm9tKCcnKSkNCiAgICAgICAgICBjb25zb2xlLmxvZyhgT1NT5paH5Lu25aS55Yib5bu6OiAke2ZvbGRlclBhdGh9YCkNCiAgICAgICAgfQ0KDQogICAgICAgIHJldHVybiB0cnVlDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCdPU1Pmlofku7blpLnliJvlu7rlpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRocm93IGVycm9yDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWPquS4uuW9k+WJjeagh+etvumhteWIm+W7uk9TU+aWh+S7tuWkuQ0KICAgIGFzeW5jIGNyZWF0ZU9TU0ZvbGRlckZvckN1cnJlbnRUYWIoZm9sZGVyTmFtZSkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc29sZS5sb2coYOW8gOWni+WIm+W7uk9TU+aWh+S7tuWkuTogJHtmb2xkZXJOYW1lfSAoJHt0aGlzLm1hdGVyaWFsVGFifSlgKQ0KDQogICAgICAgIGlmICghZm9sZGVyTmFtZSB8fCAhZm9sZGVyTmFtZS50cmltKCkpIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+aWh+S7tuWkueWQjeensOS4jeiDveS4uuepuicpDQogICAgICAgIH0NCg0KICAgICAgICBjb25zdCBjbGllbnQgPSBnZXRPU1NDbGllbnQoKQ0KICAgICAgICBpZiAoIWNsaWVudCkgew0KICAgICAgICAgIHRocm93IG5ldyBFcnJvcignT1NT5a6i5oi356uv5pyq5Yid5aeL5YyWJykNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnN0IHVzZXIgPSB0aGlzLmdldEN1cnJlbnRVc2VyKCkNCiAgICAgICAgaWYgKCF1c2VyKSB7DQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCfml6Dms5Xojrflj5blvZPliY3nlKjmiLfkv6Hmga8nKQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5qC55o2u5b2T5YmN5qCH562+6aG156Gu5a6a5Z+656GA5paH5Lu25aS5DQogICAgICAgIGNvbnN0IGJhc2VGb2xkZXIgPSB0aGlzLm1hdGVyaWFsVGFiID09PSAnYmdtJyA/ICdhYmdtJyA6ICdhc3VjYWknDQogICAgICAgIGNvbnN0IGZvbGRlclBhdGggPSBgJHtiYXNlRm9sZGVyfS8ke3VzZXJ9LyR7Zm9sZGVyTmFtZX0vLmtlZXBgDQoNCiAgICAgICAgY29uc29sZS5sb2coYE9TU+aWh+S7tuWkuei3r+W+hDogJHtmb2xkZXJQYXRofWApDQoNCiAgICAgICAgLy8g5LiK5Lyg5LiA5Liq56m65paH5Lu25p2l5Yib5bu65paH5Lu25aS557uT5p6EDQogICAgICAgIGF3YWl0IGNsaWVudC5wdXQoZm9sZGVyUGF0aCwgQnVmZmVyLmZyb20oJycpKQ0KICAgICAgICBjb25zb2xlLmxvZyhgT1NT5paH5Lu25aS55Yib5bu65oiQ5YqfICgke3RoaXMubWF0ZXJpYWxUYWJ9KTogJHtmb2xkZXJQYXRofWApDQoNCiAgICAgICAgcmV0dXJuIHRydWUNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoYE9TU+aWh+S7tuWkueWIm+W7uuWksei0pSAoJHt0aGlzLm1hdGVyaWFsVGFifSk6YCwgZXJyb3IpDQogICAgICAgIHRocm93IG5ldyBFcnJvcihgT1NT5paH5Lu25aS55Yib5bu65aSx6LSlOiAke2Vycm9yLm1lc3NhZ2V9YCkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5aSE55CG5paH5Lu25Y+M5Ye75LqL5Lu2DQogICAgaGFuZGxlRmlsZURvdWJsZUNsaWNrKGZpbGUpIHsNCiAgICAgIC8vIOWPquacieinhumikeOAgemfs+mikeOAgeWbvueJh+exu+Wei+aJjeaUr+aMgemihOiniA0KICAgICAgaWYgKFsndmlkZW8nLCAnYXVkaW8nLCAnaW1hZ2UnXS5pbmNsdWRlcyhmaWxlLnR5cGUpKSB7DQogICAgICAgIHRoaXMucHJldmlld0ZpbGUoZmlsZSkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5YiH5o2i6KeG6aKR5pKt5pS+L+aaguWBnA0KICAgIHRvZ2dsZVZpZGVvUGxheShmaWxlKSB7DQogICAgICBjb25zdCB2aWRlb1JlZiA9IHRoaXMuJHJlZnNbYHZpZGVvLSR7ZmlsZS5pZH1gXQ0KICAgICAgaWYgKHZpZGVvUmVmICYmIHZpZGVvUmVmLmxlbmd0aCA+IDApIHsNCiAgICAgICAgY29uc3QgdmlkZW8gPSB2aWRlb1JlZlswXQ0KDQogICAgICAgIGlmICh2aWRlby5wYXVzZWQpIHsNCiAgICAgICAgICAvLyDmmoLlgZzmiYDmnInlhbbku5bop4bpopENCiAgICAgICAgICB0aGlzLnBhdXNlQWxsVmlkZW9zKCkNCg0KICAgICAgICAgIC8vIOWPlua2iOmdmemfs+W5tuaSreaUvuW9k+WJjeinhumikQ0KICAgICAgICAgIHZpZGVvLm11dGVkID0gZmFsc2UNCiAgICAgICAgICB2aWRlby5wbGF5KCkNCiAgICAgICAgICB0aGlzLiRzZXQoZmlsZSwgJ2lzUGxheWluZycsIHRydWUpDQogICAgICAgICAgdGhpcy4kc2V0KGZpbGUsICdzaG93Q29udHJvbHMnLCB0cnVlKQ0KDQogICAgICAgICAgLy8g5byA5aeLM+enkumakOiXj+WumuaXtuWZqA0KICAgICAgICAgIHRoaXMuc3RhcnRDb250cm9sVGltZXIoZmlsZSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDmmoLlgZzlvZPliY3op4bpopHlubbpnZnpn7MNCiAgICAgICAgICB2aWRlby5wYXVzZSgpDQogICAgICAgICAgdmlkZW8ubXV0ZWQgPSB0cnVlDQogICAgICAgICAgdGhpcy4kc2V0KGZpbGUsICdpc1BsYXlpbmcnLCBmYWxzZSkNCiAgICAgICAgICB0aGlzLiRzZXQoZmlsZSwgJ3Nob3dDb250cm9scycsIHRydWUpDQoNCiAgICAgICAgICAvLyDmuIXpmaTpmpDol4/lrprml7blmagNCiAgICAgICAgICB0aGlzLmNsZWFyQ29udHJvbFRpbWVyKGZpbGUuaWQpDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5YiH5o2i6KeG6aKR5pKt5pS+L+aaguWBnOW5tue8qeaUvg0KICAgIHRvZ2dsZVZpZGVvUGxheVdpdGhTY2FsZShmaWxlKSB7DQogICAgICBjb25zdCB2aWRlb1JlZiA9IHRoaXMuJHJlZnNbYHZpZGVvLSR7ZmlsZS5pZH1gXQ0KICAgICAgaWYgKHZpZGVvUmVmICYmIHZpZGVvUmVmLmxlbmd0aCA+IDApIHsNCiAgICAgICAgY29uc3QgdmlkZW8gPSB2aWRlb1JlZlswXQ0KDQogICAgICAgIGlmICh2aWRlby5wYXVzZWQpIHsNCiAgICAgICAgICAvLyDmmoLlgZzmiYDmnInlhbbku5bop4bpopHlubblj5bmtojnvKnmlL4NCiAgICAgICAgICB0aGlzLnBhdXNlQWxsVmlkZW9zQW5kUmVzZXRTY2FsZSgpDQoNCiAgICAgICAgICAvLyDnrYnlvoXop4bpopHlhYPmlbDmja7liqDovb3lrozmiJANCiAgICAgICAgICBjb25zdCBoYW5kbGVMb2FkZWRNZXRhZGF0YSA9ICgpID0+IHsNCiAgICAgICAgICAgIC8vIOiOt+WPluinhumikeeahOecn+WunuWwuuWvuA0KICAgICAgICAgICAgY29uc3QgdmlkZW9XaWR0aCA9IHZpZGVvLnZpZGVvV2lkdGgNCiAgICAgICAgICAgIGNvbnN0IHZpZGVvSGVpZ2h0ID0gdmlkZW8udmlkZW9IZWlnaHQNCg0KICAgICAgICAgICAgaWYgKHZpZGVvV2lkdGggJiYgdmlkZW9IZWlnaHQpIHsNCiAgICAgICAgICAgICAgLy8g6K6h566X5ZCI6YCC55qE5pi+56S65bC65a+477yI5pyA5aSn5a695bqmODAwcHjvvIzmnIDlpKfpq5jluqY2MDBweO+8iQ0KICAgICAgICAgICAgICBjb25zdCBtYXhXaWR0aCA9IE1hdGgubWluKDgwMCwgd2luZG93LmlubmVyV2lkdGggKiAwLjgpDQogICAgICAgICAgICAgIGNvbnN0IG1heEhlaWdodCA9IE1hdGgubWluKDYwMCwgd2luZG93LmlubmVySGVpZ2h0ICogMC44KQ0KDQogICAgICAgICAgICAgIGNvbnN0IGFzcGVjdFJhdGlvID0gdmlkZW9XaWR0aCAvIHZpZGVvSGVpZ2h0DQogICAgICAgICAgICAgIGxldCBkaXNwbGF5V2lkdGgsIGRpc3BsYXlIZWlnaHQNCg0KICAgICAgICAgICAgICBpZiAoYXNwZWN0UmF0aW8gPiBtYXhXaWR0aCAvIG1heEhlaWdodCkgew0KICAgICAgICAgICAgICAgIC8vIOinhumikeavlOi+g+Wuve+8jOS7peWuveW6puS4uuWHhg0KICAgICAgICAgICAgICAgIGRpc3BsYXlXaWR0aCA9IG1heFdpZHRoDQogICAgICAgICAgICAgICAgZGlzcGxheUhlaWdodCA9IG1heFdpZHRoIC8gYXNwZWN0UmF0aW8NCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAvLyDop4bpopHmr5TovoPpq5jvvIzku6Xpq5jluqbkuLrlh4YNCiAgICAgICAgICAgICAgICBkaXNwbGF5SGVpZ2h0ID0gbWF4SGVpZ2h0DQogICAgICAgICAgICAgICAgZGlzcGxheVdpZHRoID0gbWF4SGVpZ2h0ICogYXNwZWN0UmF0aW8NCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC8vIOiuvue9ruinhumikeeahOaYvuekuuWwuuWvuA0KICAgICAgICAgICAgICB0aGlzLiRzZXQoZmlsZSwgJ2Rpc3BsYXlXaWR0aCcsIE1hdGgucm91bmQoZGlzcGxheVdpZHRoKSkNCiAgICAgICAgICAgICAgdGhpcy4kc2V0KGZpbGUsICdkaXNwbGF5SGVpZ2h0JywgTWF0aC5yb3VuZChkaXNwbGF5SGVpZ2h0KSkNCg0KICAgICAgICAgICAgICBjb25zb2xlLmxvZyhg6KeG6aKRICR7ZmlsZS5uYW1lfSDnnJ/lrp7lsLrlr7g6ICR7dmlkZW9XaWR0aH14JHt2aWRlb0hlaWdodH0sIOaYvuekuuWwuuWvuDogJHtkaXNwbGF5V2lkdGh9eCR7ZGlzcGxheUhlaWdodH1gKQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvLyDnp7vpmaTkuovku7bnm5HlkKzlmagNCiAgICAgICAgICAgIHZpZGVvLnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2xvYWRlZG1ldGFkYXRhJywgaGFuZGxlTG9hZGVkTWV0YWRhdGEpDQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g5aaC5p6c5YWD5pWw5o2u5bey57uP5Yqg6L2977yM55u05o6l5aSE55CG77yb5ZCm5YiZ562J5b6F5Yqg6L29DQogICAgICAgICAgaWYgKHZpZGVvLnJlYWR5U3RhdGUgPj0gMSkgew0KICAgICAgICAgICAgaGFuZGxlTG9hZGVkTWV0YWRhdGEoKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB2aWRlby5hZGRFdmVudExpc3RlbmVyKCdsb2FkZWRtZXRhZGF0YScsIGhhbmRsZUxvYWRlZE1ldGFkYXRhKQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOaSreaUvuW9k+WJjeinhumikeW5tuaUvuWkpw0KICAgICAgICAgIHZpZGVvLm11dGVkID0gZmFsc2UNCiAgICAgICAgICB2aWRlby5wbGF5KCkNCiAgICAgICAgICB0aGlzLiRzZXQoZmlsZSwgJ2lzUGxheWluZycsIHRydWUpDQogICAgICAgICAgdGhpcy4kc2V0KGZpbGUsICdpc1NjYWxlZCcsIHRydWUpIC8vIOiuvue9ruaUvuWkp+eKtuaAgQ0KICAgICAgICAgIHRoaXMuJHNldChmaWxlLCAnc2hvd0NvbnRyb2xzJywgdHJ1ZSkNCg0KICAgICAgICAgIGNvbnNvbGUubG9nKGDop4bpopEgJHtmaWxlLm5hbWV9IOW8gOWni+aSreaUvuW5tuaUvuWkp++8jGlzU2NhbGVkOiAke2ZpbGUuaXNTY2FsZWR9YCkNCg0KICAgICAgICAgIC8vIOa3u+WKoOiDjOaZr+mBrue9qQ0KICAgICAgICAgIHRoaXMuYWRkQmFja2Ryb3AoKQ0KDQogICAgICAgICAgLy8g5by65Yi25pu05pawRE9N5Lul5pi+56S66L+b5bqm5p2hDQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgY29uc29sZS5sb2coJ0RPTeabtOaWsOWujOaIkO+8jOi/m+W6puadoeW6lOivpeaYvuekuicpDQogICAgICAgICAgfSkNCg0KICAgICAgICAgIC8vIOW8gOWnizPnp5LpmpDol4/lrprml7blmagNCiAgICAgICAgICB0aGlzLnN0YXJ0Q29udHJvbFRpbWVyKGZpbGUpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g5pqC5YGc6KeG6aKR5bm25oGi5aSN5aSn5bCPDQogICAgICAgICAgdmlkZW8ucGF1c2UoKQ0KICAgICAgICAgIHZpZGVvLm11dGVkID0gdHJ1ZQ0KICAgICAgICAgIHRoaXMuJHNldChmaWxlLCAnaXNQbGF5aW5nJywgZmFsc2UpDQogICAgICAgICAgdGhpcy4kc2V0KGZpbGUsICdpc1NjYWxlZCcsIGZhbHNlKSAvLyDlj5bmtojmlL7lpKfnirbmgIENCiAgICAgICAgICB0aGlzLiRzZXQoZmlsZSwgJ2Rpc3BsYXlXaWR0aCcsIG51bGwpIC8vIOa4hemZpOaYvuekuuWuveW6pg0KICAgICAgICAgIHRoaXMuJHNldChmaWxlLCAnZGlzcGxheUhlaWdodCcsIG51bGwpIC8vIOa4hemZpOaYvuekuumrmOW6pg0KICAgICAgICAgIHRoaXMuJHNldChmaWxlLCAnc2hvd0NvbnRyb2xzJywgdHJ1ZSkNCg0KICAgICAgICAgIGNvbnNvbGUubG9nKGDop4bpopEgJHtmaWxlLm5hbWV9IOW3suaaguWBnOW5tuaBouWkjeWkp+Wwj2ApDQoNCiAgICAgICAgICAvLyDnp7vpmaTog4zmma/pga7nvakNCiAgICAgICAgICB0aGlzLnJlbW92ZUJhY2tkcm9wKCkNCg0KICAgICAgICAgIC8vIOa4hemZpOmakOiXj+WumuaXtuWZqA0KICAgICAgICAgIHRoaXMuY2xlYXJDb250cm9sVGltZXIoZmlsZS5pZCkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDmmoLlgZzmiYDmnInop4bpopENCiAgICBwYXVzZUFsbFZpZGVvcygpIHsNCiAgICAgIC8vIOmBjeWOhuaJgOacieaWh+S7tu+8jOaaguWBnOato+WcqOaSreaUvueahOinhumikQ0KICAgICAgdGhpcy5wYWdpbmF0ZWRNYXRlcmlhbHMuZm9yRWFjaChmaWxlID0+IHsNCiAgICAgICAgaWYgKGZpbGUudHlwZSA9PT0gJ3ZpZGVvJyAmJiBmaWxlLmlzUGxheWluZykgew0KICAgICAgICAgIGNvbnN0IHZpZGVvUmVmID0gdGhpcy4kcmVmc1tgdmlkZW8tJHtmaWxlLmlkfWBdDQogICAgICAgICAgaWYgKHZpZGVvUmVmICYmIHZpZGVvUmVmLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIGNvbnN0IHZpZGVvID0gdmlkZW9SZWZbMF0NCiAgICAgICAgICAgIHZpZGVvLnBhdXNlKCkNCiAgICAgICAgICAgIHZpZGVvLm11dGVkID0gdHJ1ZQ0KICAgICAgICAgICAgdGhpcy4kc2V0KGZpbGUsICdpc1BsYXlpbmcnLCBmYWxzZSkNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOaaguWBnOaJgOacieinhumikeW5tumHjee9rue8qeaUvg0KICAgIHBhdXNlQWxsVmlkZW9zQW5kUmVzZXRTY2FsZSgpIHsNCiAgICAgIC8vIOmBjeWOhuaJgOacieaWh+S7tu+8jOaaguWBnOato+WcqOaSreaUvueahOinhumikeW5tumHjee9rue8qeaUvg0KICAgICAgdGhpcy5wYWdpbmF0ZWRNYXRlcmlhbHMuZm9yRWFjaChmaWxlID0+IHsNCiAgICAgICAgaWYgKGZpbGUudHlwZSA9PT0gJ3ZpZGVvJykgew0KICAgICAgICAgIGlmIChmaWxlLmlzUGxheWluZykgew0KICAgICAgICAgICAgY29uc3QgdmlkZW9SZWYgPSB0aGlzLiRyZWZzW2B2aWRlby0ke2ZpbGUuaWR9YF0NCiAgICAgICAgICAgIGlmICh2aWRlb1JlZiAmJiB2aWRlb1JlZi5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgIGNvbnN0IHZpZGVvID0gdmlkZW9SZWZbMF0NCiAgICAgICAgICAgICAgdmlkZW8ucGF1c2UoKQ0KICAgICAgICAgICAgICB2aWRlby5tdXRlZCA9IHRydWUNCiAgICAgICAgICAgICAgdGhpcy4kc2V0KGZpbGUsICdpc1BsYXlpbmcnLCBmYWxzZSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgLy8g6YeN572u57yp5pS+54q25oCB5ZKM5pi+56S65bC65a+4DQogICAgICAgICAgdGhpcy4kc2V0KGZpbGUsICdpc1NjYWxlZCcsIGZhbHNlKQ0KICAgICAgICAgIHRoaXMuJHNldChmaWxlLCAnZGlzcGxheVdpZHRoJywgbnVsbCkNCiAgICAgICAgICB0aGlzLiRzZXQoZmlsZSwgJ2Rpc3BsYXlIZWlnaHQnLCBudWxsKQ0KICAgICAgICB9DQogICAgICB9KQ0KDQogICAgICAvLyDnp7vpmaTog4zmma/pga7nvakNCiAgICAgIHRoaXMucmVtb3ZlQmFja2Ryb3AoKQ0KICAgIH0sDQoNCiAgICAvLyDmt7vliqDog4zmma/pga7nvakNCiAgICBhZGRCYWNrZHJvcCgpIHsNCiAgICAgIC8vIOenu+mZpOW3suWtmOWcqOeahOmBrue9qQ0KICAgICAgdGhpcy5yZW1vdmVCYWNrZHJvcCgpDQoNCiAgICAgIGNvbnN0IGJhY2tkcm9wID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnZGl2JykNCiAgICAgIGJhY2tkcm9wLmNsYXNzTmFtZSA9ICdzY2FsZS1lbmxhcmdlZC1iYWNrZHJvcCcNCiAgICAgIGJhY2tkcm9wLmlkID0gJ3ZpZGVvLXNjYWxlLWJhY2tkcm9wJw0KICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChiYWNrZHJvcCkNCg0KICAgICAgLy8g54K55Ye76YGu572p5YWz6Zet5pS+5aSnDQogICAgICBiYWNrZHJvcC5hZGRFdmVudExpc3RlbmVyKCdjbGljaycsICgpID0+IHsNCiAgICAgICAgdGhpcy5wYXVzZUFsbFZpZGVvc0FuZFJlc2V0U2NhbGUoKQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g56e76Zmk6IOM5pmv6YGu572pDQogICAgcmVtb3ZlQmFja2Ryb3AoKSB7DQogICAgICBjb25zdCBiYWNrZHJvcCA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCd2aWRlby1zY2FsZS1iYWNrZHJvcCcpDQogICAgICBpZiAoYmFja2Ryb3ApIHsNCiAgICAgICAgYmFja2Ryb3AucmVtb3ZlKCkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g6I635Y+W6KeG6aKR5pKt5pS+6L+b5bqm55m+5YiG5q+UDQogICAgZ2V0VmlkZW9Qcm9ncmVzcyhmaWxlKSB7DQogICAgICBjb25zdCB2aWRlb1JlZiA9IHRoaXMuJHJlZnNbYHZpZGVvLSR7ZmlsZS5pZH1gXQ0KICAgICAgaWYgKHZpZGVvUmVmICYmIHZpZGVvUmVmLmxlbmd0aCA+IDApIHsNCiAgICAgICAgY29uc3QgdmlkZW8gPSB2aWRlb1JlZlswXQ0KICAgICAgICBpZiAodmlkZW8uZHVyYXRpb24gJiYgdmlkZW8uY3VycmVudFRpbWUpIHsNCiAgICAgICAgICByZXR1cm4gKHZpZGVvLmN1cnJlbnRUaW1lIC8gdmlkZW8uZHVyYXRpb24pICogMTAwDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiAwDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluW9k+WJjeaSreaUvuaXtumXtA0KICAgIGdldEN1cnJlbnRUaW1lKGZpbGUpIHsNCiAgICAgIGNvbnN0IHZpZGVvUmVmID0gdGhpcy4kcmVmc1tgdmlkZW8tJHtmaWxlLmlkfWBdDQogICAgICBpZiAodmlkZW9SZWYgJiYgdmlkZW9SZWYubGVuZ3RoID4gMCkgew0KICAgICAgICBjb25zdCB2aWRlbyA9IHZpZGVvUmVmWzBdDQogICAgICAgIHJldHVybiB2aWRlby5jdXJyZW50VGltZSB8fCAwDQogICAgICB9DQogICAgICByZXR1cm4gMA0KICAgIH0sDQoNCiAgICAvLyDojrflj5bop4bpopHmgLvml7bplb8NCiAgICBnZXREdXJhdGlvbihmaWxlKSB7DQogICAgICBjb25zdCB2aWRlb1JlZiA9IHRoaXMuJHJlZnNbYHZpZGVvLSR7ZmlsZS5pZH1gXQ0KICAgICAgaWYgKHZpZGVvUmVmICYmIHZpZGVvUmVmLmxlbmd0aCA+IDApIHsNCiAgICAgICAgY29uc3QgdmlkZW8gPSB2aWRlb1JlZlswXQ0KICAgICAgICByZXR1cm4gdmlkZW8uZHVyYXRpb24gfHwgMA0KICAgICAgfQ0KICAgICAgcmV0dXJuIDANCiAgICB9LA0KDQogICAgLy8g5qC85byP5YyW5pe26Ze05pi+56S6DQogICAgZm9ybWF0VGltZShzZWNvbmRzKSB7DQogICAgICBpZiAoIXNlY29uZHMgfHwgaXNOYU4oc2Vjb25kcykpIHJldHVybiAnMDowMCcNCg0KICAgICAgY29uc3QgbWludXRlcyA9IE1hdGguZmxvb3Ioc2Vjb25kcyAvIDYwKQ0KICAgICAgY29uc3QgcmVtYWluaW5nU2Vjb25kcyA9IE1hdGguZmxvb3Ioc2Vjb25kcyAlIDYwKQ0KICAgICAgcmV0dXJuIGAke21pbnV0ZXN9OiR7cmVtYWluaW5nU2Vjb25kcy50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyl9YA0KICAgIH0sDQoNCiAgICAvLyDngrnlh7vov5vluqbmnaHot7PovawNCiAgICBzZWVrVmlkZW8oZXZlbnQsIGZpbGUpIHsNCiAgICAgIGNvbnN0IHZpZGVvUmVmID0gdGhpcy4kcmVmc1tgdmlkZW8tJHtmaWxlLmlkfWBdDQogICAgICBpZiAodmlkZW9SZWYgJiYgdmlkZW9SZWYubGVuZ3RoID4gMCkgew0KICAgICAgICBjb25zdCB2aWRlbyA9IHZpZGVvUmVmWzBdDQogICAgICAgIGNvbnN0IHByb2dyZXNzQmFyID0gZXZlbnQuY3VycmVudFRhcmdldC5xdWVyeVNlbGVjdG9yKCcucHJvZ3Jlc3MtdHJhY2snKQ0KICAgICAgICBjb25zdCByZWN0ID0gcHJvZ3Jlc3NCYXIuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCkNCiAgICAgICAgY29uc3QgY2xpY2tYID0gZXZlbnQuY2xpZW50WCAtIHJlY3QubGVmdA0KICAgICAgICBjb25zdCBwZXJjZW50YWdlID0gY2xpY2tYIC8gcmVjdC53aWR0aA0KICAgICAgICBjb25zdCBuZXdUaW1lID0gcGVyY2VudGFnZSAqIHZpZGVvLmR1cmF0aW9uDQoNCiAgICAgICAgaWYgKG5ld1RpbWUgPj0gMCAmJiBuZXdUaW1lIDw9IHZpZGVvLmR1cmF0aW9uKSB7DQogICAgICAgICAgdmlkZW8uY3VycmVudFRpbWUgPSBuZXdUaW1lDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g6KeG6aKR5pe26Ze05pu05paw5LqL5Lu2DQogICAgb25WaWRlb1RpbWVVcGRhdGUoZmlsZSkgew0KICAgICAgLy8g5by65Yi25pu05paw6L+b5bqm5p2h5pi+56S6DQogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpDQogICAgfSwNCg0KICAgIC8vIOinhumikeaVsOaNruWKoOi9veWujOaIkOS6i+S7tg0KICAgIG9uVmlkZW9Mb2FkZWREYXRhKGZpbGUpIHsNCiAgICAgIC8vIOinhumikeaVsOaNruWKoOi9veWujOaIkO+8jOWPr+S7peiOt+WPluaXtumVv+etieS/oeaBrw0KICAgICAgY29uc29sZS5sb2coYOinhumikSAke2ZpbGUubmFtZX0g5pWw5o2u5Yqg6L295a6M5oiQYCkNCiAgICB9LA0KDQogICAgLy8g5YiH5o2i6Z+z6aKR5pKt5pS+L+aaguWBnA0KICAgIHRvZ2dsZUF1ZGlvUGxheShmaWxlKSB7DQogICAgICBjb25zdCBhdWRpb1JlZiA9IHRoaXMuJHJlZnNbYGF1ZGlvLSR7ZmlsZS5pZH1gXQ0KICAgICAgaWYgKGF1ZGlvUmVmICYmIGF1ZGlvUmVmLmxlbmd0aCA+IDApIHsNCiAgICAgICAgY29uc3QgYXVkaW8gPSBhdWRpb1JlZlswXQ0KDQogICAgICAgIGlmIChhdWRpby5wYXVzZWQpIHsNCiAgICAgICAgICAvLyDmmoLlgZzmiYDmnInlhbbku5bpn7PpopHlkozop4bpopENCiAgICAgICAgICB0aGlzLnBhdXNlQWxsTWVkaWEoKQ0KDQogICAgICAgICAgLy8g5pKt5pS+5b2T5YmN6Z+z6aKRDQogICAgICAgICAgYXVkaW8ucGxheSgpDQogICAgICAgICAgdGhpcy4kc2V0KGZpbGUsICdpc1BsYXlpbmcnLCB0cnVlKQ0KICAgICAgICAgIHRoaXMuJHNldChmaWxlLCAnc2hvd0NvbnRyb2xzJywgdHJ1ZSkNCg0KICAgICAgICAgIC8vIOW8gOWnizPnp5LpmpDol4/lrprml7blmagNCiAgICAgICAgICB0aGlzLnN0YXJ0Q29udHJvbFRpbWVyKGZpbGUpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g5pqC5YGc5b2T5YmN6Z+z6aKRDQogICAgICAgICAgYXVkaW8ucGF1c2UoKQ0KICAgICAgICAgIHRoaXMuJHNldChmaWxlLCAnaXNQbGF5aW5nJywgZmFsc2UpDQogICAgICAgICAgdGhpcy4kc2V0KGZpbGUsICdzaG93Q29udHJvbHMnLCB0cnVlKQ0KDQogICAgICAgICAgLy8g5riF6Zmk6ZqQ6JeP5a6a5pe25ZmoDQogICAgICAgICAgdGhpcy5jbGVhckNvbnRyb2xUaW1lcihmaWxlLmlkKQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOaaguWBnOaJgOacieWqkuS9k++8iOmfs+mikeWSjOinhumike+8iQ0KICAgIHBhdXNlQWxsTWVkaWEoKSB7DQogICAgICB0aGlzLnBhdXNlQWxsVmlkZW9zKCkNCiAgICAgIHRoaXMucGF1c2VBbGxBdWRpb3MoKQ0KICAgIH0sDQoNCiAgICAvLyDmmoLlgZzmiYDmnInpn7PpopENCiAgICBwYXVzZUFsbEF1ZGlvcygpIHsNCiAgICAgIHRoaXMucGFnaW5hdGVkTWF0ZXJpYWxzLmZvckVhY2goZmlsZSA9PiB7DQogICAgICAgIGlmIChmaWxlLnR5cGUgPT09ICdhdWRpbycgJiYgZmlsZS5pc1BsYXlpbmcpIHsNCiAgICAgICAgICBjb25zdCBhdWRpb1JlZiA9IHRoaXMuJHJlZnNbYGF1ZGlvLSR7ZmlsZS5pZH1gXQ0KICAgICAgICAgIGlmIChhdWRpb1JlZiAmJiBhdWRpb1JlZi5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICBhdWRpb1JlZlswXS5wYXVzZSgpDQogICAgICAgICAgICB0aGlzLiRzZXQoZmlsZSwgJ2lzUGxheWluZycsIGZhbHNlKQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g6aKE6KeI5paH5Lu2DQogICAgcHJldmlld0ZpbGUoZmlsZSkgew0KICAgICAgLy8g5p6E5bu65paH5Lu2VVJMDQogICAgICBpZiAoIWZpbGUudXJsICYmIHRoaXMub3NzSW5pdGlhbGl6ZWQpIHsNCiAgICAgICAgLy8g5aaC5p6c5rKh5pyJVVJM77yM5p6E5bu6T1NTIFVSTA0KICAgICAgICBjb25zdCBlbmRwb2ludCA9IHRoaXMub3NzQ29uZmlnLmVuZHBvaW50LnJlcGxhY2UoJ2h0dHBzOi8vJywgJycpLnJlcGxhY2UoJ2h0dHA6Ly8nLCAnJykNCiAgICAgICAgZmlsZS51cmwgPSBgaHR0cHM6Ly8ke2VuZHBvaW50fS8ke2ZpbGUub3NzRmlsZU5hbWV9YA0KICAgICAgfQ0KDQogICAgICB0aGlzLmN1cnJlbnRQcmV2aWV3RmlsZSA9IGZpbGUNCiAgICAgIHRoaXMucHJldmlld1Zpc2libGUgPSB0cnVlDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluaWh+S7tlVSTA0KICAgIGdldEZpbGVVcmwoZmlsZSkgew0KICAgICAgaWYgKGZpbGUudXJsKSB7DQogICAgICAgIHJldHVybiBmaWxlLnVybA0KICAgICAgfQ0KDQogICAgICBpZiAodGhpcy5vc3NJbml0aWFsaXplZCAmJiBmaWxlLm9zc0ZpbGVOYW1lKSB7DQogICAgICAgIGNvbnN0IGVuZHBvaW50ID0gdGhpcy5vc3NDb25maWcuZW5kcG9pbnQucmVwbGFjZSgnaHR0cHM6Ly8nLCAnJykucmVwbGFjZSgnaHR0cDovLycsICcnKQ0KICAgICAgICByZXR1cm4gYGh0dHBzOi8vJHtlbmRwb2ludH0vJHtmaWxlLm9zc0ZpbGVOYW1lfWANCiAgICAgIH0NCg0KICAgICAgcmV0dXJuICcnDQogICAgfSwNCg0KICAgIC8vIOWBnOatouaJgOacieWqkuS9k+aSreaUvg0KICAgIHN0b3BBbGxNZWRpYSgpIHsNCiAgICAgIC8vIOWBnOatoumihOiniOWvueivneahhuS4reeahOinhumikeaSreaUvg0KICAgICAgaWYgKHRoaXMuJHJlZnMucHJldmlld1ZpZGVvKSB7DQogICAgICAgIHRoaXMuJHJlZnMucHJldmlld1ZpZGVvLnBhdXNlKCkNCiAgICAgICAgdGhpcy4kcmVmcy5wcmV2aWV3VmlkZW8uY3VycmVudFRpbWUgPSAwDQogICAgICB9DQoNCiAgICAgIC8vIOWBnOatoumihOiniOWvueivneahhuS4reeahOmfs+mikeaSreaUvg0KICAgICAgaWYgKHRoaXMuJHJlZnMucHJldmlld0F1ZGlvKSB7DQogICAgICAgIHRoaXMuJHJlZnMucHJldmlld0F1ZGlvLnBhdXNlKCkNCiAgICAgICAgdGhpcy4kcmVmcy5wcmV2aWV3QXVkaW8uY3VycmVudFRpbWUgPSAwDQogICAgICB9DQoNCiAgICAgIC8vIOWBnOatoue8qeeVpeWbvuS4reeahOaJgOacieWqkuS9k+aSreaUvg0KICAgICAgdGhpcy5wYXVzZUFsbE1lZGlhKCkNCg0KICAgICAgLy8g5riF56m66aKE6KeI5paH5Lu2DQogICAgICB0aGlzLmN1cnJlbnRQcmV2aWV3RmlsZSA9IG51bGwNCiAgICB9LA0KDQogICAgLy8g6KeG6aKR5Yqg6L295a6M5oiQDQogICAgb25WaWRlb0xvYWRlZChldmVudCkgew0KICAgICAgLy8g5Y+v5Lul5Zyo6L+Z6YeM6I635Y+W6KeG6aKR55qE56ys5LiA5bin5L2c5Li657yp55Wl5Zu+DQogICAgICBjb25zdCB2aWRlbyA9IGV2ZW50LnRhcmdldA0KICAgICAgdmlkZW8uY3VycmVudFRpbWUgPSAxIC8vIOi3s+WIsOesrDHnp5Lojrflj5bnvKnnlaXlm74NCiAgICB9LA0KDQogICAgLy8g6KeG6aKR5pKt5pS+57uT5p2fDQogICAgb25WaWRlb0VuZGVkKGZpbGUpIHsNCiAgICAgIHRoaXMuJHNldChmaWxlLCAnaXNQbGF5aW5nJywgZmFsc2UpDQogICAgICB0aGlzLiRzZXQoZmlsZSwgJ2lzU2NhbGVkJywgZmFsc2UpDQogICAgICB0aGlzLiRzZXQoZmlsZSwgJ2Rpc3BsYXlXaWR0aCcsIG51bGwpDQogICAgICB0aGlzLiRzZXQoZmlsZSwgJ2Rpc3BsYXlIZWlnaHQnLCBudWxsKQ0KICAgICAgdGhpcy5yZW1vdmVCYWNrZHJvcCgpDQogICAgfSwNCg0KICAgIC8vIOinhumikeaaguWBnA0KICAgIG9uVmlkZW9QYXVzZWQoZmlsZSkgew0KICAgICAgdGhpcy4kc2V0KGZpbGUsICdpc1BsYXlpbmcnLCBmYWxzZSkNCiAgICAgIC8vIOWmguaenOaYr+aUvuWkp+eKtuaAge+8jOS5n+imgea4heeQhg0KICAgICAgaWYgKGZpbGUuaXNTY2FsZWQpIHsNCiAgICAgICAgdGhpcy4kc2V0KGZpbGUsICdpc1NjYWxlZCcsIGZhbHNlKQ0KICAgICAgICB0aGlzLiRzZXQoZmlsZSwgJ2Rpc3BsYXlXaWR0aCcsIG51bGwpDQogICAgICAgIHRoaXMuJHNldChmaWxlLCAnZGlzcGxheUhlaWdodCcsIG51bGwpDQogICAgICAgIHRoaXMucmVtb3ZlQmFja2Ryb3AoKQ0KICAgICAgfQ0KDQogICAgICAvLyDlkIzmraVQb3J0YWzop4bpopENCiAgICAgIGlmICh0aGlzLmhvdmVyZWRGaWxlICYmIHRoaXMuaG92ZXJlZEZpbGUuaWQgPT09IGZpbGUuaWQpIHsNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIHRoaXMuc3luY1BvcnRhbFZpZGVvKCkNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g6KeG6aKR5byA5aeL5pKt5pS+DQogICAgb25WaWRlb1BsYXllZChmaWxlKSB7DQogICAgICB0aGlzLiRzZXQoZmlsZSwgJ2lzUGxheWluZycsIHRydWUpDQoNCiAgICAgIC8vIOWQjOatpVBvcnRhbOinhumikQ0KICAgICAgaWYgKHRoaXMuaG92ZXJlZEZpbGUgJiYgdGhpcy5ob3ZlcmVkRmlsZS5pZCA9PT0gZmlsZS5pZCkgew0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgdGhpcy5zeW5jUG9ydGFsVmlkZW8oKQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDpn7PpopHmkq3mlL7nu5PmnZ8NCiAgICBvbkF1ZGlvRW5kZWQoZmlsZSkgew0KICAgICAgdGhpcy4kc2V0KGZpbGUsICdpc1BsYXlpbmcnLCBmYWxzZSkNCiAgICB9LA0KDQogICAgLy8g6Z+z6aKR5pqC5YGcDQogICAgb25BdWRpb1BhdXNlZChmaWxlKSB7DQogICAgICB0aGlzLiRzZXQoZmlsZSwgJ2lzUGxheWluZycsIGZhbHNlKQ0KICAgIH0sDQoNCiAgICAvLyDpn7PpopHlvIDlp4vmkq3mlL4NCiAgICBvbkF1ZGlvUGxheWVkKGZpbGUpIHsNCiAgICAgIHRoaXMuJHNldChmaWxlLCAnaXNQbGF5aW5nJywgdHJ1ZSkNCiAgICB9LA0KDQogICAgLy8g5Zu+54mH5Yqg6L296ZSZ6K+vDQogICAgb25JbWFnZUVycm9yKGV2ZW50KSB7DQogICAgICAvLyDlm77niYfliqDovb3lpLHotKXml7bmmL7npLrpu5jorqTlm77moIcNCiAgICAgIGV2ZW50LnRhcmdldC5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnDQogICAgICBldmVudC50YXJnZXQucGFyZW50Tm9kZS5pbm5lckhUTUwgPSAnPGkgY2xhc3M9ImVsLWljb24tcGljdHVyZS1vdXRsaW5lIj48L2k+Jw0KICAgIH0sDQoNCiAgICAvLyDop4bpopHpvKDmoIfov5vlhaXkuovku7YNCiAgICBvblZpZGVvTW91c2VFbnRlcihmaWxlKSB7DQogICAgICBjb25zb2xlLmxvZygn6KeG6aKR6byg5qCH6L+b5YWlOicsIGZpbGUubmFtZSwgJ+aYvuekuuaOp+WItuaMiemSricpDQogICAgICB0aGlzLiRzZXQoZmlsZSwgJ3Nob3dDb250cm9scycsIHRydWUpDQogICAgICB0aGlzLmNsZWFyQ29udHJvbFRpbWVyKGZpbGUuaWQpDQogICAgfSwNCg0KICAgIC8vIOinhumikem8oOagh+emu+W8gOS6i+S7tg0KICAgIG9uVmlkZW9Nb3VzZUxlYXZlKGZpbGUpIHsNCiAgICAgIGlmIChmaWxlLmlzUGxheWluZykgew0KICAgICAgICB0aGlzLnN0YXJ0Q29udHJvbFRpbWVyKGZpbGUpDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRzZXQoZmlsZSwgJ3Nob3dDb250cm9scycsIGZhbHNlKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDop4bpopHpvKDmoIfnp7vliqjkuovku7YNCiAgICBvblZpZGVvTW91c2VNb3ZlKGZpbGUpIHsNCiAgICAgIHRoaXMuJHNldChmaWxlLCAnc2hvd0NvbnRyb2xzJywgdHJ1ZSkNCiAgICAgIGlmIChmaWxlLmlzUGxheWluZykgew0KICAgICAgICB0aGlzLmNsZWFyQ29udHJvbFRpbWVyKGZpbGUuaWQpDQogICAgICAgIHRoaXMuc3RhcnRDb250cm9sVGltZXIoZmlsZSkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g6Z+z6aKR6byg5qCH6L+b5YWl5LqL5Lu2DQogICAgb25BdWRpb01vdXNlRW50ZXIoZmlsZSkgew0KICAgICAgY29uc29sZS5sb2coJ+mfs+mikem8oOagh+i/m+WFpTonLCBmaWxlLm5hbWUpDQogICAgICB0aGlzLiRzZXQoZmlsZSwgJ3Nob3dDb250cm9scycsIHRydWUpDQogICAgICB0aGlzLmNsZWFyQ29udHJvbFRpbWVyKGZpbGUuaWQpDQogICAgfSwNCg0KICAgIC8vIOmfs+mikem8oOagh+emu+W8gOS6i+S7tg0KICAgIG9uQXVkaW9Nb3VzZUxlYXZlKGZpbGUpIHsNCiAgICAgIGlmIChmaWxlLmlzUGxheWluZykgew0KICAgICAgICB0aGlzLnN0YXJ0Q29udHJvbFRpbWVyKGZpbGUpDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRzZXQoZmlsZSwgJ3Nob3dDb250cm9scycsIGZhbHNlKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDpn7PpopHpvKDmoIfnp7vliqjkuovku7YNCiAgICBvbkF1ZGlvTW91c2VNb3ZlKGZpbGUpIHsNCiAgICAgIHRoaXMuJHNldChmaWxlLCAnc2hvd0NvbnRyb2xzJywgdHJ1ZSkNCiAgICAgIGlmIChmaWxlLmlzUGxheWluZykgew0KICAgICAgICB0aGlzLmNsZWFyQ29udHJvbFRpbWVyKGZpbGUuaWQpDQogICAgICAgIHRoaXMuc3RhcnRDb250cm9sVGltZXIoZmlsZSkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5Zu+54mH6byg5qCH6L+b5YWl5LqL5Lu2DQogICAgb25JbWFnZU1vdXNlRW50ZXIoZmlsZSkgew0KICAgICAgY29uc29sZS5sb2coJ+WbvueJh+m8oOagh+i/m+WFpTonLCBmaWxlLm5hbWUpDQogICAgfSwNCg0KICAgIC8vIOWbvueJh+m8oOagh+emu+W8gOS6i+S7tg0KICAgIG9uSW1hZ2VNb3VzZUxlYXZlKGZpbGUpIHsNCiAgICAgIGNvbnNvbGUubG9nKCflm77niYfpvKDmoIfnprvlvIA6JywgZmlsZS5uYW1lKQ0KICAgIH0sDQoNCiAgICAvLyDlvIDlp4vmjqfliLbmjInpkq7pmpDol4/lrprml7blmagNCiAgICBzdGFydENvbnRyb2xUaW1lcihmaWxlKSB7DQogICAgICB0aGlzLmNsZWFyQ29udHJvbFRpbWVyKGZpbGUuaWQpDQogICAgICB0aGlzLmNvbnRyb2xUaW1lcnNbZmlsZS5pZF0gPSBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgdGhpcy4kc2V0KGZpbGUsICdzaG93Q29udHJvbHMnLCBmYWxzZSkNCiAgICAgIH0sIDMwMDApIC8vIDPnp5LlkI7pmpDol48NCiAgICB9LA0KDQogICAgLy8g5riF6Zmk5o6n5Yi25oyJ6ZKu6ZqQ6JeP5a6a5pe25ZmoDQogICAgY2xlYXJDb250cm9sVGltZXIoZmlsZUlkKSB7DQogICAgICBpZiAodGhpcy5jb250cm9sVGltZXJzW2ZpbGVJZF0pIHsNCiAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMuY29udHJvbFRpbWVyc1tmaWxlSWRdKQ0KICAgICAgICBkZWxldGUgdGhpcy5jb250cm9sVGltZXJzW2ZpbGVJZF0NCiAgICAgIH0NCiAgICB9LA0KDQoNCg0KDQoNCiAgICAvLyDmoLzlvI/ljJbmlofku7blpKflsI8NCiAgICBmb3JtYXRGaWxlU2l6ZShieXRlcykgew0KICAgICAgaWYgKGJ5dGVzID09PSAwKSByZXR1cm4gJzAgQicNCiAgICAgIGNvbnN0IGsgPSAxMDI0DQogICAgICBjb25zdCBzaXplcyA9IFsnQicsICdLQicsICdNQicsICdHQiddDQogICAgICBjb25zdCBpID0gTWF0aC5mbG9vcihNYXRoLmxvZyhieXRlcykgLyBNYXRoLmxvZyhrKSkNCiAgICAgIHJldHVybiBwYXJzZUZsb2F0KChieXRlcyAvIE1hdGgucG93KGssIGkpKS50b0ZpeGVkKDIpKSArICcgJyArIHNpemVzW2ldDQogICAgfSwNCg0KICAgIC8vIOS4i+i9veaWh+S7tg0KICAgIGRvd25sb2FkRmlsZShmaWxlKSB7DQogICAgICBpZiAoZmlsZS51cmwpIHsNCiAgICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKQ0KICAgICAgICBsaW5rLmhyZWYgPSBmaWxlLnVybA0KICAgICAgICBsaW5rLmRvd25sb2FkID0gZmlsZS5uYW1lDQogICAgICAgIGxpbmsudGFyZ2V0ID0gJ19ibGFuaycNCiAgICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChsaW5rKQ0KICAgICAgICBsaW5rLmNsaWNrKCkNCiAgICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChsaW5rKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5paH5Lu26ZO+5o6l5LiN5Y+v55SoJykNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5LuOT1NT6I635Y+W55So5oi35paH5Lu25aS55YiX6KGo77yI5YiG5Yir5Li6QkdN5ZKM57Sg5p2Q77yJDQogICAgYXN5bmMgbG9hZFVzZXJGb2xkZXJzRnJvbU9TUygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IGNsaWVudCA9IGdldE9TU0NsaWVudCgpDQogICAgICAgIGNvbnN0IHVzZXIgPSB0aGlzLmdldEN1cnJlbnRVc2VyKCkNCg0KICAgICAgICAvLyDliIbliKvojrflj5ZCR03lkozntKDmnZDnmoTmlofku7blpLkNCiAgICAgICAgYXdhaXQgdGhpcy5sb2FkRm9sZGVyc0ZvclR5cGUoJ2FiZ20nLCAnYmdtRm9sZGVyVHJlZScpDQogICAgICAgIGF3YWl0IHRoaXMubG9hZEZvbGRlcnNGb3JUeXBlKCdhc3VjYWknLCAnc3VjYWlGb2xkZXJUcmVlJykNCg0KICAgICAgICBjb25zb2xlLmxvZygnQkdN5paH5Lu25aS5OicsIHRoaXMuYmdtRm9sZGVyVHJlZS5tYXAoZiA9PiBmLm5hbWUpKQ0KICAgICAgICBjb25zb2xlLmxvZygn57Sg5p2Q5paH5Lu25aS5OicsIHRoaXMuc3VjYWlGb2xkZXJUcmVlLm1hcChmID0+IGYubmFtZSkpDQoNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlk9TU+aWh+S7tuWkueWIl+ihqOWksei0pTonLCBlcnJvcikNCiAgICAgICAgLy8g5aaC5p6c6I635Y+W5aSx6LSl77yM6Iez5bCR5pi+56S66buY6K6k55qEIuaAuyLmlofku7blpLkNCiAgICAgICAgdGhpcy5iZ21Gb2xkZXJUcmVlID0gW3sgaWQ6IDEsIG5hbWU6ICfmgLsnLCBjb3VudDogMCB9XQ0KICAgICAgICB0aGlzLnN1Y2FpRm9sZGVyVHJlZSA9IFt7IGlkOiAxLCBuYW1lOiAn5oC7JywgY291bnQ6IDAgfV0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5Li65oyH5a6a57G75Z6L5Yqg6L295paH5Lu25aS5DQogICAgYXN5bmMgbG9hZEZvbGRlcnNGb3JUeXBlKGJhc2VGb2xkZXIsIHRyZWVQcm9wZXJ0eSkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgY2xpZW50ID0gZ2V0T1NTQ2xpZW50KCkNCiAgICAgICAgY29uc3QgdXNlciA9IHRoaXMuZ2V0Q3VycmVudFVzZXIoKQ0KICAgICAgICBjb25zdCBmb2xkZXJTZXQgPSBuZXcgU2V0KCkNCiAgICAgICAgY29uc3QgcHJlZml4ID0gYCR7YmFzZUZvbGRlcn0vJHt1c2VyfS9gDQoNCiAgICAgICAgLy8g5YiX5Ye66K+l5YmN57yA5LiL55qE5omA5pyJ5a+56LGhDQogICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGNsaWVudC5saXN0KHsNCiAgICAgICAgICBwcmVmaXg6IHByZWZpeCwNCiAgICAgICAgICBkZWxpbWl0ZXI6ICcvJywNCiAgICAgICAgICAnbWF4LWtleXMnOiAxMDAwDQogICAgICAgIH0pDQoNCiAgICAgICAgLy8g5LuOY29tbW9uUHJlZml4ZXPkuK3mj5Dlj5bmlofku7blpLnlkI3np7ANCiAgICAgICAgaWYgKHJlc3VsdC5wcmVmaXhlcykgew0KICAgICAgICAgIHJlc3VsdC5wcmVmaXhlcy5mb3JFYWNoKHByZWZpeEluZm8gPT4gew0KICAgICAgICAgICAgY29uc3QgZnVsbFByZWZpeCA9IHByZWZpeEluZm8ubmFtZSB8fCBwcmVmaXhJbmZvDQogICAgICAgICAgICAvLyDmj5Dlj5bmlofku7blpLnlkI3np7DvvJphYmdtL2FkbWluL+aAuy8gLT4g5oC7DQogICAgICAgICAgICBpZiAoZnVsbFByZWZpeCAmJiB0eXBlb2YgZnVsbFByZWZpeCA9PT0gJ3N0cmluZycpIHsNCiAgICAgICAgICAgICAgY29uc3QgZm9sZGVyTmFtZSA9IGZ1bGxQcmVmaXgucmVwbGFjZShwcmVmaXgsICcnKS5yZXBsYWNlKCcvJywgJycpDQogICAgICAgICAgICAgIGlmIChmb2xkZXJOYW1lKSB7DQogICAgICAgICAgICAgICAgZm9sZGVyU2V0LmFkZChmb2xkZXJOYW1lKQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOWmguaenOaciW9iamVjdHPvvIzkuZ/ku47kuK3mj5Dlj5bmlofku7blpLnlkI3np7ANCiAgICAgICAgaWYgKHJlc3VsdC5vYmplY3RzKSB7DQogICAgICAgICAgcmVzdWx0Lm9iamVjdHMuZm9yRWFjaChvYmogPT4gew0KICAgICAgICAgICAgY29uc3Qgb2JqZWN0S2V5ID0gb2JqLm5hbWUNCiAgICAgICAgICAgIGlmIChvYmplY3RLZXkuc3RhcnRzV2l0aChwcmVmaXgpKSB7DQogICAgICAgICAgICAgIGNvbnN0IHJlbGF0aXZlUGF0aCA9IG9iamVjdEtleS5yZXBsYWNlKHByZWZpeCwgJycpDQogICAgICAgICAgICAgIGNvbnN0IGZvbGRlck5hbWUgPSByZWxhdGl2ZVBhdGguc3BsaXQoJy8nKVswXQ0KICAgICAgICAgICAgICBpZiAoZm9sZGVyTmFtZSAmJiBmb2xkZXJOYW1lICE9PSAnLmtlZXAnKSB7DQogICAgICAgICAgICAgICAgZm9sZGVyU2V0LmFkZChmb2xkZXJOYW1lKQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOi9rOaNouS4uuaWh+S7tuWkueagkeagvOW8jw0KICAgICAgICBjb25zdCBmb2xkZXJzID0gQXJyYXkuZnJvbShmb2xkZXJTZXQpLm1hcCgoZm9sZGVyTmFtZSwgaW5kZXgpID0+ICh7DQogICAgICAgICAgaWQ6IERhdGUubm93KCkgKyBpbmRleCwgLy8g5L2/55So5pe26Ze05oiz6YG/5YWNSUTlhrLnqoENCiAgICAgICAgICBuYW1lOiBmb2xkZXJOYW1lLA0KICAgICAgICAgIGNvdW50OiAwIC8vIOeojeWQjuWPr+S7peiuoeeul+WunumZheaWh+S7tuaVsOmHjw0KICAgICAgICB9KSkNCg0KICAgICAgICAvLyDnoa7kv50i5oC7IuaWh+S7tuWkueWtmOWcqA0KICAgICAgICBpZiAoIWZvbGRlclNldC5oYXMoJ+aAuycpKSB7DQogICAgICAgICAgZm9sZGVycy51bnNoaWZ0KHsgaWQ6IERhdGUubm93KCksIG5hbWU6ICfmgLsnLCBjb3VudDogMCB9KQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5pu05paw5a+55bqU55qE5paH5Lu25aS55qCRDQogICAgICAgIGlmIChiYXNlRm9sZGVyID09PSAnYWJnbScpIHsNCiAgICAgICAgICB0aGlzLmJnbUZvbGRlclRyZWUgPSBmb2xkZXJzDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5zdWNhaUZvbGRlclRyZWUgPSBmb2xkZXJzDQogICAgICAgIH0NCg0KICAgICAgICBjb25zb2xlLmxvZyhgJHtiYXNlRm9sZGVyfSDmlofku7blpLnliqDovb3lrozmiJA6YCwgZm9sZGVycy5tYXAoZiA9PiBmLm5hbWUpKQ0KDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKGDojrflj5YgJHtiYXNlRm9sZGVyfSDmlofku7blpLnliJfooajlpLHotKU6YCwgZXJyb3IpDQogICAgICAgIC8vIOWmguaenOiOt+WPluWksei0pe+8jOiHs+WwkeaYvuekuum7mOiupOeahCLmgLsi5paH5Lu25aS5DQogICAgICAgIGNvbnN0IGRlZmF1bHRGb2xkZXIgPSBbeyBpZDogRGF0ZS5ub3coKSwgbmFtZTogJ+aAuycsIGNvdW50OiAwIH1dDQogICAgICAgIGlmIChiYXNlRm9sZGVyID09PSAnYWJnbScpIHsNCiAgICAgICAgICB0aGlzLmJnbUZvbGRlclRyZWUgPSBkZWZhdWx0Rm9sZGVyDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5zdWNhaUZvbGRlclRyZWUgPSBkZWZhdWx0Rm9sZGVyDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5pu05paw5paH5Lu25aS555qE5paH5Lu25pWw6YePDQogICAgYXN5bmMgdXBkYXRlRm9sZGVyRmlsZUNvdW50cygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IGNsaWVudCA9IGdldE9TU0NsaWVudCgpDQogICAgICAgIGNvbnN0IHVzZXIgPSB0aGlzLmdldEN1cnJlbnRVc2VyKCkNCg0KICAgICAgICAvLyDnlLHkuo7njrDlnKjkvb/nlKjliIbnprvnmoTmlofku7blpLnmoJHvvIzov5nkuKrmlrnms5XmmoLml7bnpoHnlKgNCiAgICAgICAgLy8g5Y+v5Lul5qC55o2u6ZyA6KaB5Li65q+P5Liq5qCH562+6aG15Y2V54us5pu05paw5paH5Lu25pWw6YePDQogICAgICAgIGNvbnNvbGUubG9nKCfmlofku7blpLnmlbDph4/mm7TmlrDlt7LmlLnkuLrliIbnprvmqKHlvI/vvIzor7fkvb/nlKjlhbfkvZPnmoTmoIfnrb7pobXmm7TmlrDmlrnms5UnKQ0KICAgICAgICByZXR1cm4NCg0KICAgICAgICBmb3IgKGxldCBmb2xkZXIgb2YgW10pIHsgLy8g5Li05pe256aB55SoDQogICAgICAgICAgbGV0IHRvdGFsQ291bnQgPSAwDQoNCiAgICAgICAgICAvLyDmo4Dmn6XkuKTnp43nsbvlnovnmoTmlofku7blpLnkuK3nmoTmlofku7bmlbDph48NCiAgICAgICAgICBjb25zdCBiYXNlRm9sZGVycyA9IFsnYWJnbScsICdhc3VjYWknXQ0KDQogICAgICAgICAgZm9yIChjb25zdCBiYXNlRm9sZGVyIG9mIGJhc2VGb2xkZXJzKSB7DQogICAgICAgICAgICBjb25zdCBwcmVmaXggPSBgJHtiYXNlRm9sZGVyfS8ke3VzZXJ9LyR7Zm9sZGVyLm5hbWV9L2ANCg0KICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY2xpZW50Lmxpc3Qoew0KICAgICAgICAgICAgICAgIHByZWZpeDogcHJlZml4LA0KICAgICAgICAgICAgICAgICdtYXgta2V5cyc6IDEwMDANCiAgICAgICAgICAgICAgfSkNCg0KICAgICAgICAgICAgICBpZiAocmVzdWx0Lm9iamVjdHMpIHsNCiAgICAgICAgICAgICAgICAvLyDov4fmu6Tmjokua2VlcOaWh+S7tg0KICAgICAgICAgICAgICAgIGNvbnN0IGZpbGVDb3VudCA9IHJlc3VsdC5vYmplY3RzLmZpbHRlcihvYmogPT4NCiAgICAgICAgICAgICAgICAgICFvYmoubmFtZS5lbmRzV2l0aCgnLmtlZXAnKSAmJiAhb2JqLm5hbWUuZW5kc1dpdGgoJy8nKQ0KICAgICAgICAgICAgICAgICkubGVuZ3RoDQogICAgICAgICAgICAgICAgdG90YWxDb3VudCArPSBmaWxlQ291bnQNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICAgICAgY29uc29sZS53YXJuKGDojrflj5bmlofku7blpLkgJHtmb2xkZXIubmFtZX0g5ZyoICR7YmFzZUZvbGRlcn0g5Lit55qE5paH5Lu25pWw6YeP5aSx6LSlOmAsIGVycm9yKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGZvbGRlci5jb3VudCA9IHRvdGFsQ291bnQNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnNvbGUubG9nKCfmlofku7blpLnmlofku7bmlbDph4/mm7TmlrDlrozmiJA6JywgdGhpcy5zaW1wbGVGb2xkZXJUcmVlKQ0KDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfmm7TmlrDmlofku7blpLnmlofku7bmlbDph4/lpLHotKU6JywgZXJyb3IpDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOaWh+S7tuWkueWPs+mUruiPnOWNleebuOWFs+aWueazlQ0KICAgIHNob3dGb2xkZXJDb250ZXh0TWVudShldmVudCwgZm9sZGVyKSB7DQogICAgICB0aGlzLmNvbnRleHRNZW51VmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuY29udGV4dE1lbnVYID0gZXZlbnQuY2xpZW50WA0KICAgICAgdGhpcy5jb250ZXh0TWVudVkgPSBldmVudC5jbGllbnRZDQogICAgICB0aGlzLmNvbnRleHRNZW51Rm9sZGVyID0gZm9sZGVyDQoNCiAgICAgIC8vIOeCueWHu+WFtuS7luWcsOaWuemakOiXj+iPnOWNlQ0KICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCB0aGlzLmhpZGVGb2xkZXJDb250ZXh0TWVudSkNCiAgICB9LA0KDQogICAgaGlkZUZvbGRlckNvbnRleHRNZW51KCkgew0KICAgICAgdGhpcy5jb250ZXh0TWVudVZpc2libGUgPSBmYWxzZQ0KICAgICAgdGhpcy5jb250ZXh0TWVudUZvbGRlciA9IG51bGwNCiAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgdGhpcy5oaWRlRm9sZGVyQ29udGV4dE1lbnUpDQogICAgfSwNCg0KICAgIC8vIOmHjeWRveWQjeaWh+S7tuWkuQ0KICAgIGFzeW5jIHJlbmFtZUZvbGRlckFjdGlvbigpIHsNCiAgICAgIGlmICghdGhpcy5jb250ZXh0TWVudUZvbGRlcikgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmnKrpgInmi6nmlofku7blpLknKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgY29uc3QgZm9sZGVyTmFtZSA9IHRoaXMuY29udGV4dE1lbnVGb2xkZXIubmFtZSB8fCB0aGlzLmNvbnRleHRNZW51Rm9sZGVyDQoNCiAgICAgIHRoaXMuJHByb21wdCgn6K+36L6T5YWl5paw55qE5paH5Lu25aS55ZCN56ewJywgJ+mHjeWRveWQjeaWh+S7tuWkuScsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgICAgaW5wdXRWYWx1ZTogZm9sZGVyTmFtZQ0KICAgICAgfSkudGhlbihhc3luYyAoeyB2YWx1ZSB9KSA9PiB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgaWYgKCF2YWx1ZSB8fCB2YWx1ZS50cmltKCkgPT09ICcnKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmlofku7blpLnlkI3np7DkuI3og73kuLrnqbonKQ0KICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgfQ0KDQogICAgICAgICAgY29uc3QgbmV3Rm9sZGVyTmFtZSA9IHZhbHVlLnRyaW0oKQ0KICAgICAgICAgIGNvbnN0IG9sZEZvbGRlck5hbWUgPSB0aGlzLmNvbnRleHRNZW51Rm9sZGVyLm5hbWUgfHwgdGhpcy5jb250ZXh0TWVudUZvbGRlcg0KDQogICAgICAgICAgaWYgKG5ld0ZvbGRlck5hbWUgPT09IG9sZEZvbGRlck5hbWUpIHsNCiAgICAgICAgICAgIHRoaXMuaGlkZUZvbGRlckNvbnRleHRNZW51KCkNCiAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOWmguaenOS9v+eUqE9TU+WtmOWCqO+8jOmcgOimgemHjeWRveWQjU9TU+S4reeahOaWh+S7tuWkuQ0KICAgICAgICAgIGlmICh0aGlzLnN0b3JhZ2VUeXBlID09PSAnb3NzJyAmJiB0aGlzLm9zc0luaXRpYWxpemVkKSB7DQogICAgICAgICAgICBhd2FpdCB0aGlzLnJlbmFtZU9TU0ZvbGRlcihvbGRGb2xkZXJOYW1lLCBuZXdGb2xkZXJOYW1lKQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOabtOaWsOWJjeerr+aWh+S7tuWkueWQjeensA0KICAgICAgICAgIGlmICh0eXBlb2YgdGhpcy5jb250ZXh0TWVudUZvbGRlciA9PT0gJ29iamVjdCcpIHsNCiAgICAgICAgICAgIHRoaXMuY29udGV4dE1lbnVGb2xkZXIubmFtZSA9IG5ld0ZvbGRlck5hbWUNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDlpoLmnpzlvZPliY3pgInkuK3nmoTmmK/ooqvph43lkb3lkI3nmoTmlofku7blpLnvvIzmm7TmlrDlvZPliY3mlofku7blpLkNCiAgICAgICAgICBpZiAodGhpcy5jdXJyZW50Rm9sZGVyID09PSBvbGRGb2xkZXJOYW1lKSB7DQogICAgICAgICAgICB0aGlzLmN1cnJlbnRGb2xkZXIgPSBuZXdGb2xkZXJOYW1lDQogICAgICAgICAgfQ0KDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmlofku7blpLnph43lkb3lkI3miJDlip8nKQ0KICAgICAgICAgIHRoaXMuaGlkZUZvbGRlckNvbnRleHRNZW51KCkNCg0KICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+mHjeWRveWQjeaWh+S7tuWkueWksei0pTonLCBlcnJvcikNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDph43lkb3lkI3lpLHotKU6ICR7ZXJyb3IubWVzc2FnZX1gKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDliKDpmaTmlofku7blpLkNCiAgICBhc3luYyBkZWxldGVGb2xkZXJBY3Rpb24oKSB7DQogICAgICBjb25zb2xlLmxvZygn5Yig6Zmk5paH5Lu25aS55pON5L2c5byA5aeL77yMY29udGV4dE1lbnVGb2xkZXI6JywgdGhpcy5jb250ZXh0TWVudUZvbGRlcikNCg0KICAgICAgaWYgKCF0aGlzLmNvbnRleHRNZW51Rm9sZGVyKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+acqumAieaLqeaWh+S7tuWkuScpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICAvLyDlronlhajlnLDojrflj5bmlofku7blpLnlkI3np7ANCiAgICAgIGxldCBmb2xkZXJOYW1lDQogICAgICBpZiAodHlwZW9mIHRoaXMuY29udGV4dE1lbnVGb2xkZXIgPT09ICdzdHJpbmcnKSB7DQogICAgICAgIGZvbGRlck5hbWUgPSB0aGlzLmNvbnRleHRNZW51Rm9sZGVyDQogICAgICB9IGVsc2UgaWYgKHRoaXMuY29udGV4dE1lbnVGb2xkZXIgJiYgdGhpcy5jb250ZXh0TWVudUZvbGRlci5uYW1lKSB7DQogICAgICAgIGZvbGRlck5hbWUgPSB0aGlzLmNvbnRleHRNZW51Rm9sZGVyLm5hbWUNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aXoOazleiOt+WPluaWh+S7tuWkueWQjeensO+8jGNvbnRleHRNZW51Rm9sZGVyOicsIHRoaXMuY29udGV4dE1lbnVGb2xkZXIpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aXoOazleiOt+WPluaWh+S7tuWkueWQjeensCcpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICBjb25zb2xlLmxvZygn5YeG5aSH5Yig6Zmk5paH5Lu25aS5OicsIGZvbGRlck5hbWUpDQoNCiAgICAgIGlmIChmb2xkZXJOYW1lID09PSAn5oC7Jykgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+S4jeiDveWIoOmZpCLmgLsi5paH5Lu25aS5JykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIHRoaXMuJGNvbmZpcm0oYOehruWumuimgeWIoOmZpOaWh+S7tuWkuSIke2ZvbGRlck5hbWV9IuWQl++8n+aWh+S7tuWkueWGheeahOaJgOacieaWh+S7tuS5n+S8muiiq+WIoOmZpO+8gWAsICfliKDpmaTmlofku7blpLknLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6a5Yig6ZmkJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkudGhlbihhc3luYyAoKSA9PiB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgLy8g5L2/55So5LmL5YmN6I635Y+W55qEZm9sZGVyTmFtZe+8jOmBv+WFjeWcqOW8guatpeaTjeS9nOS4rWNvbnRleHRNZW51Rm9sZGVy5Y+Y5Li6bnVsbA0KICAgICAgICAgIGNvbnN0IHRhcmdldEZvbGRlck5hbWUgPSBmb2xkZXJOYW1lDQoNCiAgICAgICAgICAvLyDlpoLmnpzkvb/nlKhPU1PlrZjlgqjvvIzlj6rliKDpmaTlvZPliY3moIfnrb7pobXnmoRPU1Pmlofku7blpLkNCiAgICAgICAgICBpZiAodGhpcy5zdG9yYWdlVHlwZSA9PT0gJ29zcycgJiYgdGhpcy5vc3NJbml0aWFsaXplZCkgew0KICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgYXdhaXQgdGhpcy5kZWxldGVPU1NGb2xkZXJGb3JDdXJyZW50VGFiKHRhcmdldEZvbGRlck5hbWUpDQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKGAke3RoaXMubWF0ZXJpYWxUYWIgPT09ICdiZ20nID8gJ0JHTScgOiAn57Sg5p2QJ33mlofku7blpLnliKDpmaTmiJDlip9gKQ0KICAgICAgICAgICAgfSBjYXRjaCAob3NzRXJyb3IpIHsNCiAgICAgICAgICAgICAgY29uc29sZS53YXJuKCdPU1Pmlofku7blpLnliKDpmaTlpLHotKXvvIzkvYbnu6fnu63liKDpmaTliY3nq6/orrDlvZU6Jywgb3NzRXJyb3IubWVzc2FnZSkNCiAgICAgICAgICAgICAgLy8g5Y2z5L2/T1NT5Yig6Zmk5aSx6LSl77yM5Lmf57un57ut5Yig6Zmk5YmN56uv6K6w5b2V77yI5Y+v6IO95piv56m65paH5Lu25aS577yJDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g5LuO5YmN56uv56e76Zmk5paH5Lu25aS555u45YWz55qE5paH5Lu2DQogICAgICAgICAgdGhpcy5yZW1vdmVGaWxlc0Zyb21Gb2xkZXIodGFyZ2V0Rm9sZGVyTmFtZSkNCg0KICAgICAgICAgIC8vIOWmguaenOW9k+WJjemAieS4reeahOaYr+iiq+WIoOmZpOeahOaWh+S7tuWkue+8jOWIh+aNouWIsCLmgLsi5paH5Lu25aS5DQogICAgICAgICAgaWYgKHRoaXMuY3VycmVudEZvbGRlciA9PT0gdGFyZ2V0Rm9sZGVyTmFtZSkgew0KICAgICAgICAgICAgdGhpcy5jdXJyZW50Rm9sZGVyID0gJ+aAuycNCiAgICAgICAgICB9DQoNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aWh+S7tuWkueWIoOmZpOaIkOWKnycpDQogICAgICAgICAgdGhpcy5oaWRlRm9sZGVyQ29udGV4dE1lbnUoKQ0KDQogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign5Yig6Zmk5paH5Lu25aS55aSx6LSlOicsIGVycm9yKQ0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYOWIoOmZpOWksei0pTogJHtlcnJvci5tZXNzYWdlfWApDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluW9k+WJjeeUqOaIt+WQjQ0KICAgIGdldEN1cnJlbnRVc2VyKCkgew0KICAgICAgLy8g5LuOc3RvcmXkuK3ojrflj5bnlKjmiLfkv6Hmga8NCiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLm5hbWUgfHwgJ2FkbWluJw0KICAgIH0sDQoNCiAgICAvLyDojrflj5blvZPliY3mlofku7blpLnlkI3np7ANCiAgICBnZXRDdXJyZW50Rm9sZGVyKCkgew0KICAgICAgcmV0dXJuIHRoaXMuY3VycmVudEZvbGRlciB8fCAn5oC7Jw0KICAgIH0sDQoNCiAgICAvLyDmnoTlu7pPU1PkuIrkvKDot6/lvoQNCiAgICBidWlsZE9TU1BhdGgoYmFzZUZvbGRlcikgew0KICAgICAgY29uc3QgdXNlciA9IHRoaXMuZ2V0Q3VycmVudFVzZXIoKQ0KICAgICAgY29uc3QgZm9sZGVyID0gdGhpcy5nZXRDdXJyZW50Rm9sZGVyKCkNCiAgICAgIHJldHVybiBgJHtiYXNlRm9sZGVyfS8ke3VzZXJ9LyR7Zm9sZGVyfWANCiAgICB9LA0KDQogICAgLy8gT1NT5paH5Lu25aSN5Yi25ZKM5Yig6Zmk77yI55So5LqO6YeN5ZG95ZCN77yJDQogICAgYXN5bmMgY29weUFuZERlbGV0ZU9TU0ZpbGUob2xkUGF0aCwgbmV3UGF0aCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgY2xpZW50ID0gZ2V0T1NTQ2xpZW50KCkNCg0KICAgICAgICAvLyDlpI3liLbmlofku7bliLDmlrDot6/lvoQNCiAgICAgICAgYXdhaXQgY2xpZW50LmNvcHkobmV3UGF0aCwgb2xkUGF0aCkNCiAgICAgICAgY29uc29sZS5sb2coYE9TU+aWh+S7tuWkjeWItuaIkOWKnzogJHtvbGRQYXRofSAtPiAke25ld1BhdGh9YCkNCg0KICAgICAgICAvLyDliKDpmaTljp/mlofku7YNCiAgICAgICAgYXdhaXQgY2xpZW50LmRlbGV0ZShvbGRQYXRoKQ0KICAgICAgICBjb25zb2xlLmxvZyhgT1NT5Y6f5paH5Lu25Yig6Zmk5oiQ5YqfOiAke29sZFBhdGh9YCkNCg0KICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcignT1NT5paH5Lu25aSN5Yi25Yig6Zmk5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aHJvdyBlcnJvcg0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyBPU1Pmlofku7blpLnph43lkb3lkI0NCiAgICBhc3luYyByZW5hbWVPU1NGb2xkZXIob2xkRm9sZGVyTmFtZSwgbmV3Rm9sZGVyTmFtZSkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgY2xpZW50ID0gZ2V0T1NTQ2xpZW50KCkNCiAgICAgICAgY29uc3QgdXNlciA9IHRoaXMuZ2V0Q3VycmVudFVzZXIoKQ0KDQogICAgICAgIC8vIOiOt+WPluS4pOenjeexu+Wei+aWh+S7tuWkueeahOi3r+W+hA0KICAgICAgICBjb25zdCBiYXNlRm9sZGVycyA9IFsnYWJnbScsICdhc3VjYWknXQ0KDQogICAgICAgIGZvciAoY29uc3QgYmFzZUZvbGRlciBvZiBiYXNlRm9sZGVycykgew0KICAgICAgICAgIGNvbnN0IG9sZFByZWZpeCA9IGAke2Jhc2VGb2xkZXJ9LyR7dXNlcn0vJHtvbGRGb2xkZXJOYW1lfS9gDQogICAgICAgICAgY29uc3QgbmV3UHJlZml4ID0gYCR7YmFzZUZvbGRlcn0vJHt1c2VyfS8ke25ld0ZvbGRlck5hbWV9L2ANCg0KICAgICAgICAgIC8vIOWIl+WHuuaWh+S7tuWkueS4reeahOaJgOacieaWh+S7tg0KICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGNsaWVudC5saXN0KHsNCiAgICAgICAgICAgIHByZWZpeDogb2xkUHJlZml4LA0KICAgICAgICAgICAgJ21heC1rZXlzJzogMTAwMA0KICAgICAgICAgIH0pDQoNCiAgICAgICAgICBpZiAocmVzdWx0Lm9iamVjdHMgJiYgcmVzdWx0Lm9iamVjdHMubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgLy8g5aSN5Yi25omA5pyJ5paH5Lu25Yiw5paw6Lev5b6EDQogICAgICAgICAgICBmb3IgKGNvbnN0IG9iaiBvZiByZXN1bHQub2JqZWN0cykgew0KICAgICAgICAgICAgICBjb25zdCBuZXdLZXkgPSBvYmoubmFtZS5yZXBsYWNlKG9sZFByZWZpeCwgbmV3UHJlZml4KQ0KICAgICAgICAgICAgICBhd2FpdCBjbGllbnQuY29weShuZXdLZXksIG9iai5uYW1lKQ0KICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgT1NT5paH5Lu25aS56YeN5ZG95ZCNOiAke29iai5uYW1lfSAtPiAke25ld0tleX1gKQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvLyDliKDpmaTljp/mlofku7YNCiAgICAgICAgICAgIGZvciAoY29uc3Qgb2JqIG9mIHJlc3VsdC5vYmplY3RzKSB7DQogICAgICAgICAgICAgIGF3YWl0IGNsaWVudC5kZWxldGUob2JqLm5hbWUpDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgcmV0dXJuIHRydWUNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ09TU+aWh+S7tuWkuemHjeWRveWQjeWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhyb3cgZXJyb3INCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8gT1NT5paH5Lu25aS55Yig6ZmkDQogICAgYXN5bmMgZGVsZXRlT1NTRm9sZGVyKGZvbGRlck5hbWUpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnNvbGUubG9nKCflvIDlp4vliKDpmaRPU1Pmlofku7blpLk6JywgZm9sZGVyTmFtZSkNCg0KICAgICAgICBpZiAoIWZvbGRlck5hbWUpIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+aWh+S7tuWkueWQjeensOS4jeiDveS4uuepuicpDQogICAgICAgIH0NCg0KICAgICAgICBjb25zdCBjbGllbnQgPSBnZXRPU1NDbGllbnQoKQ0KICAgICAgICBpZiAoIWNsaWVudCkgew0KICAgICAgICAgIHRocm93IG5ldyBFcnJvcignT1NT5a6i5oi356uv5pyq5Yid5aeL5YyWJykNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnN0IHVzZXIgPSB0aGlzLmdldEN1cnJlbnRVc2VyKCkNCiAgICAgICAgY29uc29sZS5sb2coJ+W9k+WJjeeUqOaItzonLCB1c2VyKQ0KDQogICAgICAgIC8vIOiOt+WPluS4pOenjeexu+Wei+aWh+S7tuWkueeahOi3r+W+hA0KICAgICAgICBjb25zdCBiYXNlRm9sZGVycyA9IFsnYWJnbScsICdhc3VjYWknXQ0KICAgICAgICBsZXQgdG90YWxEZWxldGVkRmlsZXMgPSAwDQoNCiAgICAgICAgZm9yIChjb25zdCBiYXNlRm9sZGVyIG9mIGJhc2VGb2xkZXJzKSB7DQogICAgICAgICAgY29uc3QgcHJlZml4ID0gYCR7YmFzZUZvbGRlcn0vJHt1c2VyfS8ke2ZvbGRlck5hbWV9L2ANCiAgICAgICAgICBjb25zb2xlLmxvZygn5qOA5p+lT1NT6Lev5b6EOicsIHByZWZpeCkNCg0KICAgICAgICAgIC8vIOWIl+WHuuaWh+S7tuWkueS4reeahOaJgOacieaWh+S7tg0KICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGNsaWVudC5saXN0KHsNCiAgICAgICAgICAgIHByZWZpeDogcHJlZml4LA0KICAgICAgICAgICAgJ21heC1rZXlzJzogMTAwMA0KICAgICAgICAgIH0pDQoNCiAgICAgICAgICBjb25zb2xlLmxvZyhg6Lev5b6EICR7cHJlZml4fSDkuIvmib7liLAgJHtyZXN1bHQub2JqZWN0cyA/IHJlc3VsdC5vYmplY3RzLmxlbmd0aCA6IDB9IOS4quaWh+S7tmApDQoNCiAgICAgICAgICBpZiAocmVzdWx0Lm9iamVjdHMgJiYgcmVzdWx0Lm9iamVjdHMubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgLy8g5Yig6Zmk5omA5pyJ5paH5Lu2DQogICAgICAgICAgICBmb3IgKGNvbnN0IG9iaiBvZiByZXN1bHQub2JqZWN0cykgew0KICAgICAgICAgICAgICBhd2FpdCBjbGllbnQuZGVsZXRlKG9iai5uYW1lKQ0KICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgT1NT5paH5Lu25Yig6ZmkOiAke29iai5uYW1lfWApDQogICAgICAgICAgICAgIHRvdGFsRGVsZXRlZEZpbGVzKysNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICBjb25zb2xlLmxvZyhgT1NT5paH5Lu25aS55Yig6Zmk5a6M5oiQ77yM5YWx5Yig6ZmkICR7dG90YWxEZWxldGVkRmlsZXN9IOS4quaWh+S7tmApDQogICAgICAgIHJldHVybiB0cnVlDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCdPU1Pmlofku7blpLnliKDpmaTlpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRocm93IG5ldyBFcnJvcihg5Yig6ZmkT1NT5paH5Lu25aS55aSx6LSlOiAke2Vycm9yLm1lc3NhZ2V9YCkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5Y+q5Yig6Zmk5b2T5YmN5qCH562+6aG155qET1NT5paH5Lu25aS5DQogICAgYXN5bmMgZGVsZXRlT1NTRm9sZGVyRm9yQ3VycmVudFRhYihmb2xkZXJOYW1lKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zb2xlLmxvZyhg5byA5aeL5Yig6ZmkJHt0aGlzLm1hdGVyaWFsVGFiID09PSAnYmdtJyA/ICdCR00nIDogJ+e0oOadkCd95paH5Lu25aS5OmAsIGZvbGRlck5hbWUpDQoNCiAgICAgICAgaWYgKCFmb2xkZXJOYW1lKSB7DQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCfmlofku7blpLnlkI3np7DkuI3og73kuLrnqbonKQ0KICAgICAgICB9DQoNCiAgICAgICAgY29uc3QgY2xpZW50ID0gZ2V0T1NTQ2xpZW50KCkNCiAgICAgICAgaWYgKCFjbGllbnQpIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ09TU+WuouaIt+err+acquWIneWni+WMlicpDQogICAgICAgIH0NCg0KICAgICAgICBjb25zdCB1c2VyID0gdGhpcy5nZXRDdXJyZW50VXNlcigpDQogICAgICAgIGNvbnN0IGJhc2VGb2xkZXIgPSB0aGlzLm1hdGVyaWFsVGFiID09PSAnYmdtJyA/ICdhYmdtJyA6ICdhc3VjYWknDQogICAgICAgIGNvbnN0IHByZWZpeCA9IGAke2Jhc2VGb2xkZXJ9LyR7dXNlcn0vJHtmb2xkZXJOYW1lfS9gDQoNCiAgICAgICAgY29uc29sZS5sb2coJ+WIoOmZpE9TU+i3r+W+hDonLCBwcmVmaXgpDQoNCiAgICAgICAgLy8g5YiX5Ye65paH5Lu25aS55Lit55qE5omA5pyJ5paH5Lu2DQogICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGNsaWVudC5saXN0KHsNCiAgICAgICAgICBwcmVmaXg6IHByZWZpeCwNCiAgICAgICAgICAnbWF4LWtleXMnOiAxMDAwDQogICAgICAgIH0pDQoNCiAgICAgICAgbGV0IGRlbGV0ZWRGaWxlcyA9IDANCiAgICAgICAgaWYgKHJlc3VsdC5vYmplY3RzICYmIHJlc3VsdC5vYmplY3RzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAvLyDliKDpmaTmiYDmnInmlofku7YNCiAgICAgICAgICBmb3IgKGNvbnN0IG9iaiBvZiByZXN1bHQub2JqZWN0cykgew0KICAgICAgICAgICAgYXdhaXQgY2xpZW50LmRlbGV0ZShvYmoubmFtZSkNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBPU1Pmlofku7bliKDpmaQ6ICR7b2JqLm5hbWV9YCkNCiAgICAgICAgICAgIGRlbGV0ZWRGaWxlcysrDQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgY29uc29sZS5sb2coYCR7dGhpcy5tYXRlcmlhbFRhYiA9PT0gJ2JnbScgPyAnQkdNJyA6ICfntKDmnZAnfeaWh+S7tuWkueWIoOmZpOWujOaIkO+8jOWFseWIoOmZpCAke2RlbGV0ZWRGaWxlc30g5Liq5paH5Lu2YCkNCiAgICAgICAgcmV0dXJuIHRydWUNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoYOWIoOmZpCR7dGhpcy5tYXRlcmlhbFRhYiA9PT0gJ2JnbScgPyAnQkdNJyA6ICfntKDmnZAnfeaWh+S7tuWkueWksei0pTpgLCBlcnJvcikNCiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGDliKDpmaQke3RoaXMubWF0ZXJpYWxUYWIgPT09ICdiZ20nID8gJ0JHTScgOiAn57Sg5p2QJ33mlofku7blpLnlpLHotKU6ICR7ZXJyb3IubWVzc2FnZX1gKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDku47liY3nq6/np7vpmaTmlofku7blpLnnm7jlhbPnmoTmlofku7bvvIjlj6rliKDpmaTlvZPliY3moIfnrb7pobXnmoTvvIkNCiAgICByZW1vdmVGaWxlc0Zyb21Gb2xkZXIoZm9sZGVyTmFtZSkgew0KICAgICAgaWYgKHRoaXMubWF0ZXJpYWxUYWIgPT09ICdiZ20nKSB7DQogICAgICAgIC8vIOWPquenu+mZpEJHTeWIl+ihqOS4reivpeaWh+S7tuWkueeahOaWh+S7tg0KICAgICAgICB0aGlzLmJnbUxpc3QgPSB0aGlzLmJnbUxpc3QuZmlsdGVyKGZpbGUgPT4gew0KICAgICAgICAgIGNvbnN0IGZpbGVGb2xkZXJOYW1lID0gZmlsZS5mb2xkZXIgPyBmaWxlLmZvbGRlci5zcGxpdCgnLycpLnBvcCgpIDogJ+aAuycNCiAgICAgICAgICByZXR1cm4gZmlsZUZvbGRlck5hbWUgIT09IGZvbGRlck5hbWUNCiAgICAgICAgfSkNCg0KICAgICAgICAvLyDku45CR03mlofku7blpLnmoJHkuK3np7vpmaQNCiAgICAgICAgY29uc3QgdHJlZUluZGV4ID0gdGhpcy5iZ21Gb2xkZXJUcmVlLmZpbmRJbmRleChmb2xkZXIgPT4gZm9sZGVyLm5hbWUgPT09IGZvbGRlck5hbWUpDQogICAgICAgIGlmICh0cmVlSW5kZXggPiAtMSkgew0KICAgICAgICAgIHRoaXMuYmdtRm9sZGVyVHJlZS5zcGxpY2UodHJlZUluZGV4LCAxKQ0KICAgICAgICAgIGNvbnNvbGUubG9nKGDku45CR03mlofku7blpLnmoJHkuK3np7vpmaQ6ICR7Zm9sZGVyTmFtZX1gKQ0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDlj6rnp7vpmaTntKDmnZDliJfooajkuK3or6Xmlofku7blpLnnmoTmlofku7YNCiAgICAgICAgdGhpcy5zdWNhaUxpc3QgPSB0aGlzLnN1Y2FpTGlzdC5maWx0ZXIoZmlsZSA9PiB7DQogICAgICAgICAgY29uc3QgZmlsZUZvbGRlck5hbWUgPSBmaWxlLmZvbGRlciA/IGZpbGUuZm9sZGVyLnNwbGl0KCcvJykucG9wKCkgOiAn5oC7Jw0KICAgICAgICAgIHJldHVybiBmaWxlRm9sZGVyTmFtZSAhPT0gZm9sZGVyTmFtZQ0KICAgICAgICB9KQ0KDQogICAgICAgIC8vIOS7jue0oOadkOaWh+S7tuWkueagkeS4reenu+mZpA0KICAgICAgICBjb25zdCB0cmVlSW5kZXggPSB0aGlzLnN1Y2FpRm9sZGVyVHJlZS5maW5kSW5kZXgoZm9sZGVyID0+IGZvbGRlci5uYW1lID09PSBmb2xkZXJOYW1lKQ0KICAgICAgICBpZiAodHJlZUluZGV4ID4gLTEpIHsNCiAgICAgICAgICB0aGlzLnN1Y2FpRm9sZGVyVHJlZS5zcGxpY2UodHJlZUluZGV4LCAxKQ0KICAgICAgICAgIGNvbnNvbGUubG9nKGDku47ntKDmnZDmlofku7blpLnmoJHkuK3np7vpmaQ6ICR7Zm9sZGVyTmFtZX1gKQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOaWh+S7tuWkuemAieaLqQ0KICAgIGFzeW5jIHNlbGVjdEZvbGRlcihmb2xkZXIpIHsNCiAgICAgIHRoaXMuc2VsZWN0ZWRGb2xkZXIgPSBmb2xkZXIuaWQNCiAgICAgIHRoaXMuY3VycmVudEZvbGRlciA9IGZvbGRlci5uYW1lDQogICAgICBjb25zb2xlLmxvZygn6YCJ5oup5paH5Lu25aS5OicsIGZvbGRlci5uYW1lKQ0KDQogICAgICAvLyDliIfmjaLmlofku7blpLnml7bph43mlrDliqDovb3mlofku7bliJfooagNCiAgICAgIGlmICh0aGlzLm9zc0luaXRpYWxpemVkKSB7DQogICAgICAgIGF3YWl0IHRoaXMubG9hZE1hdGVyaWFsTGlzdCgpDQogICAgICB9DQoNCiAgICAgIC8vIOabtOaWsOaWh+S7tuWkueiuoeaVsA0KICAgICAgdGhpcy51cGRhdGVDdXJyZW50VGFiRm9sZGVyQ291bnRzKCkNCiAgICB9LA0KDQogICAgLy8g5pu05paw5b2T5YmN5qCH562+6aG155qE5paH5Lu25aS56K6h5pWwDQogICAgdXBkYXRlQ3VycmVudFRhYkZvbGRlckNvdW50cygpIHsNCiAgICAgIGNvbnN0IGN1cnJlbnRNYXRlcmlhbHMgPSB0aGlzLmN1cnJlbnRNYXRlcmlhbExpc3QNCiAgICAgIGNvbnN0IGN1cnJlbnRGb2xkZXJzID0gdGhpcy5jdXJyZW50Rm9sZGVyVHJlZQ0KDQogICAgICAvLyDkuLrmr4/kuKrmlofku7blpLnorqHnrpfmlofku7bmlbDph48NCiAgICAgIGN1cnJlbnRGb2xkZXJzLmZvckVhY2goZm9sZGVyID0+IHsNCiAgICAgICAgaWYgKGZvbGRlci5uYW1lID09PSAn5oC7Jykgew0KICAgICAgICAgIC8vICLmgLsi5paH5Lu25aS55pi+56S65omA5pyJ5paH5Lu25pWw6YePDQogICAgICAgICAgZm9sZGVyLmNvdW50ID0gY3VycmVudE1hdGVyaWFscy5sZW5ndGgNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDlhbbku5bmlofku7blpLnlj6rorqHnrpflsZ7kuo7or6Xmlofku7blpLnnmoTmlofku7YNCiAgICAgICAgICBmb2xkZXIuY291bnQgPSBjdXJyZW50TWF0ZXJpYWxzLmZpbHRlcihmaWxlID0+IHsNCiAgICAgICAgICAgIGNvbnN0IGZpbGVGb2xkZXJOYW1lID0gZmlsZS5mb2xkZXIgPyBmaWxlLmZvbGRlci5zcGxpdCgnLycpLnBvcCgpIDogJ+aAuycNCiAgICAgICAgICAgIHJldHVybiBmaWxlRm9sZGVyTmFtZSA9PT0gZm9sZGVyLm5hbWUNCiAgICAgICAgICB9KS5sZW5ndGgNCiAgICAgICAgfQ0KICAgICAgfSkNCg0KICAgICAgY29uc29sZS5sb2coYCR7dGhpcy5tYXRlcmlhbFRhYn0g5paH5Lu25aS56K6h5pWw5pu05pawOmAsIGN1cnJlbnRGb2xkZXJzLm1hcChmID0+IGAke2YubmFtZX0oJHtmLmNvdW50fSlgKSkNCiAgICB9LA0KDQogICAgLy8g5paH5Lu26YCJ5oup55u45YWz5pa55rOVDQogICAgaGFuZGxlU2VsZWN0QWxsKGNoZWNrZWQpIHsNCiAgICAgIGlmIChjaGVja2VkKSB7DQogICAgICAgIHRoaXMuc2VsZWN0ZWRGaWxlcyA9IHRoaXMucGFnaW5hdGVkTWF0ZXJpYWxzLm1hcChmID0+IGYuaWQpDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnNlbGVjdGVkRmlsZXMgPSBbXQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDpooTop4jmlofku7YNCiAgICBoYW5kbGVQcmV2aWV3KCkgew0KICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRGaWxlcy5sZW5ndGggIT09IDEpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nkuIDkuKrmlofku7bov5vooYzpooTop4gnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgY29uc3QgZmlsZSA9IHRoaXMuY3VycmVudE1hdGVyaWFsTGlzdC5maW5kKGYgPT4gZi5pZCA9PT0gdGhpcy5zZWxlY3RlZEZpbGVzWzBdKQ0KICAgICAgaWYgKCFmaWxlKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aWh+S7tuS4jeWtmOWcqCcpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICAvLyDmnoTlu7rmlofku7ZVUkwNCiAgICAgIGlmICghZmlsZS51cmwgJiYgdGhpcy5vc3NJbml0aWFsaXplZCkgew0KICAgICAgICAvLyDlpoLmnpzmsqHmnIlVUkzvvIzmnoTlu7pPU1MgVVJMDQogICAgICAgIGNvbnN0IGVuZHBvaW50ID0gdGhpcy5vc3NDb25maWcuZW5kcG9pbnQucmVwbGFjZSgnaHR0cHM6Ly8nLCAnJykucmVwbGFjZSgnaHR0cDovLycsICcnKQ0KICAgICAgICBmaWxlLnVybCA9IGBodHRwczovLyR7ZW5kcG9pbnR9LyR7ZmlsZS5vc3NGaWxlTmFtZX1gDQogICAgICB9DQoNCiAgICAgIHRoaXMuY3VycmVudFByZXZpZXdGaWxlID0gZmlsZQ0KICAgICAgdGhpcy5wcmV2aWV3VmlzaWJsZSA9IHRydWUNCiAgICB9LA0KDQogICAgdG9nZ2xlRmlsZVNlbGVjdGlvbihmaWxlSWQpIHsNCiAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5zZWxlY3RlZEZpbGVzLmluZGV4T2YoZmlsZUlkKQ0KICAgICAgaWYgKGluZGV4ID4gLTEpIHsNCiAgICAgICAgdGhpcy5zZWxlY3RlZEZpbGVzLnNwbGljZShpbmRleCwgMSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc2VsZWN0ZWRGaWxlcy5wdXNoKGZpbGVJZCkNCiAgICAgIH0NCg0KICAgICAgLy8g5pu05paw5YWo6YCJ54q25oCBDQogICAgICB0aGlzLnNlbGVjdEFsbCA9IHRoaXMuc2VsZWN0ZWRGaWxlcy5sZW5ndGggPT09IHRoaXMucGFnaW5hdGVkTWF0ZXJpYWxzLmxlbmd0aA0KICAgIH0sDQoNCiAgICBnZXRTaW1wbGVGaWxlSWNvbih0eXBlKSB7DQogICAgICBjb25zdCBpY29uTWFwID0gew0KICAgICAgICAndmlkZW8nOiAn8J+OrCcsDQogICAgICAgICdpbWFnZSc6ICfwn5a877iPJywNCiAgICAgICAgJ2F1ZGlvJzogJ/CfjrUnDQogICAgICB9DQogICAgICByZXR1cm4gaWNvbk1hcFt0eXBlXSB8fCAn8J+ThCcNCiAgICB9LA0KDQogICAgLy8g5qCH562+6aG15YiH5o2iDQogICAgc3dpdGNoVGFiKHRhYikgew0KICAgICAgdGhpcy5tYXRlcmlhbFRhYiA9IHRhYg0KICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDEgLy8g6YeN572u6aG156CBDQogICAgICB0aGlzLnNlbGVjdGVkRmlsZXMgPSBbXSAvLyDmuIXnqbrpgInmi6kNCiAgICAgIHRoaXMuc2VsZWN0QWxsID0gZmFsc2UNCg0KICAgICAgY29uc3QgdGFiTmFtZXMgPSB7DQogICAgICAgICdiZ20nOiAnQkdNJywNCiAgICAgICAgJ3N1Y2FpJzogJ+e0oOadkCcNCiAgICAgIH0NCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5YiH5o2i5YiwJHt0YWJOYW1lc1t0YWJdIHx8IHRhYn3kuIrkvKDpobXpnaJgKQ0KDQogICAgICAvLyDliIfmjaLmoIfnrb7pobXlkI7mm7TmlrDmlofku7blpLnorqHmlbANCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy51cGRhdGVDdXJyZW50VGFiRm9sZGVyQ291bnRzKCkNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOaWh+S7tuaTjeS9nA0KICAgIGFzeW5jIGhhbmRsZVJlbmFtZSgpIHsNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkRmlsZXMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup6KaB6YeN5ZG95ZCN55qE5paH5Lu2JykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICBpZiAodGhpcy5zZWxlY3RlZEZpbGVzLmxlbmd0aCA+IDEpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfkuIDmrKHlj6rog73ph43lkb3lkI3kuIDkuKrmlofku7YnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgY29uc3QgZmlsZSA9IHRoaXMuY3VycmVudE1hdGVyaWFsTGlzdC5maW5kKGYgPT4gZi5pZCA9PT0gdGhpcy5zZWxlY3RlZEZpbGVzWzBdKQ0KICAgICAgdGhpcy4kcHJvbXB0KCfor7fovpPlhaXmlrDnmoTmlofku7blkI0nLCAn6YeN5ZG95ZCN5paH5Lu2Jywgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICBpbnB1dFZhbHVlOiBmaWxlLm5hbWUNCiAgICAgIH0pLnRoZW4oYXN5bmMgKHsgdmFsdWUgfSkgPT4gew0KICAgICAgICB0cnkgew0KICAgICAgICAgIC8vIOmqjOivgeaWsOaWh+S7tuWQjQ0KICAgICAgICAgIGlmICghdmFsdWUgfHwgdmFsdWUudHJpbSgpID09PSAnJykgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5paH5Lu25ZCN5LiN6IO95Li656m6JykNCiAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGNvbnN0IG5ld0ZpbGVOYW1lID0gdmFsdWUudHJpbSgpDQoNCiAgICAgICAgICAvLyDlpoLmnpzkvb/nlKhPU1PlrZjlgqjvvIzpnIDopoHph43lkb3lkI1PU1PkuK3nmoTmlofku7YNCiAgICAgICAgICBpZiAodGhpcy5zdG9yYWdlVHlwZSA9PT0gJ29zcycgJiYgdGhpcy5vc3NJbml0aWFsaXplZCkgew0KICAgICAgICAgICAgLy8g5p6E5bu65Y6f5paH5Lu26Lev5b6EDQogICAgICAgICAgICBsZXQgb2xkT3NzRmlsZVBhdGggPSAnJw0KICAgICAgICAgICAgaWYgKGZpbGUub3NzRmlsZU5hbWUpIHsNCiAgICAgICAgICAgICAgb2xkT3NzRmlsZVBhdGggPSBmaWxlLm9zc0ZpbGVOYW1lDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBjb25zdCBiYXNlRm9sZGVyTmFtZSA9IHRoaXMubWF0ZXJpYWxUYWIgPT09ICdiZ20nID8gJ2FiZ20nIDogJ2FzdWNhaScNCiAgICAgICAgICAgICAgY29uc3QgZnVsbFBhdGggPSB0aGlzLmJ1aWxkT1NTUGF0aChiYXNlRm9sZGVyTmFtZSkNCiAgICAgICAgICAgICAgb2xkT3NzRmlsZVBhdGggPSBgJHtmdWxsUGF0aH0vJHtmaWxlLm5hbWV9YA0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvLyDmnoTlu7rmlrDmlofku7bot6/lvoQNCiAgICAgICAgICAgIGNvbnN0IGJhc2VGb2xkZXJOYW1lID0gdGhpcy5tYXRlcmlhbFRhYiA9PT0gJ2JnbScgPyAnYWJnbScgOiAnYXN1Y2FpJw0KICAgICAgICAgICAgY29uc3QgZnVsbFBhdGggPSB0aGlzLmJ1aWxkT1NTUGF0aChiYXNlRm9sZGVyTmFtZSkNCiAgICAgICAgICAgIGNvbnN0IG5ld09zc0ZpbGVQYXRoID0gYCR7ZnVsbFBhdGh9LyR7bmV3RmlsZU5hbWV9YA0KDQogICAgICAgICAgICBjb25zb2xlLmxvZyhgT1NT6YeN5ZG95ZCNOiAke29sZE9zc0ZpbGVQYXRofSAtPiAke25ld09zc0ZpbGVQYXRofWApDQoNCiAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgIC8vIE9TU+S4jeaUr+aMgeebtOaOpemHjeWRveWQje+8jOmcgOimgeWkjeWItuWQjuWIoOmZpA0KICAgICAgICAgICAgICBhd2FpdCB0aGlzLmNvcHlBbmREZWxldGVPU1NGaWxlKG9sZE9zc0ZpbGVQYXRoLCBuZXdPc3NGaWxlUGF0aCkNCg0KICAgICAgICAgICAgICAvLyDmm7TmlrDmlofku7bkv6Hmga8NCiAgICAgICAgICAgICAgZmlsZS5uYW1lID0gbmV3RmlsZU5hbWUNCiAgICAgICAgICAgICAgZmlsZS5vc3NGaWxlTmFtZSA9IG5ld09zc0ZpbGVQYXRoDQoNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfph43lkb3lkI3miJDlip8nKQ0KICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignT1NT6YeN5ZG95ZCN5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDph43lkb3lkI3lpLHotKU6ICR7ZXJyb3IubWVzc2FnZX1gKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAvLyDmnKzlnLDlrZjlgqjvvIznm7TmjqXmm7TmlrDmlofku7blkI0NCiAgICAgICAgICAgIGZpbGUubmFtZSA9IG5ld0ZpbGVOYW1lDQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+mHjeWRveWQjeaIkOWKnycpDQogICAgICAgICAgfQ0KDQogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6YeN5ZG95ZCN5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYOmHjeWRveWQjeWksei0pTogJHtlcnJvci5tZXNzYWdlfWApDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIGFzeW5jIGhhbmRsZURlbGV0ZSgpIHsNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkRmlsZXMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup6KaB5Yig6Zmk55qE5paH5Lu2JykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIHRoaXMuJGNvbmZpcm0oYOehruWumuimgeWIoOmZpOmAieS4reeahCAke3RoaXMuc2VsZWN0ZWRGaWxlcy5sZW5ndGh9IOS4quaWh+S7tuWQl++8n+WIoOmZpOWQjuaXoOazleaBouWkje+8gWAsICfliKDpmaTmlofku7YnLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6a5Yig6ZmkJywNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgfSkudGhlbihhc3luYyAoKSA9PiB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgY29uc3QgY3VycmVudExpc3QgPSB0aGlzLm1hdGVyaWFsVGFiID09PSAnYmdtJyA/IHRoaXMuYmdtTGlzdCA6IHRoaXMuc3VjYWlMaXN0DQoNCiAgICAgICAgICAvLyDojrflj5bopoHliKDpmaTnmoTmlofku7bkv6Hmga8NCiAgICAgICAgICBjb25zdCBmaWxlc1RvRGVsZXRlID0gdGhpcy5zZWxlY3RlZEZpbGVzLm1hcChmaWxlSWQgPT4gew0KICAgICAgICAgICAgcmV0dXJuIGN1cnJlbnRMaXN0LmZpbmQoZiA9PiBmLmlkID09PSBmaWxlSWQpDQogICAgICAgICAgfSkuZmlsdGVyKGZpbGUgPT4gZmlsZSkgLy8g6L+H5ruk5o6J5om+5LiN5Yiw55qE5paH5Lu2DQoNCiAgICAgICAgICBjb25zb2xlLmxvZygn5YeG5aSH5Yig6Zmk55qE5paH5Lu2OicsIGZpbGVzVG9EZWxldGUpDQoNCiAgICAgICAgICAvLyDlpoLmnpzkvb/nlKhPU1PlrZjlgqjvvIzliKDpmaRPU1PkuK3nmoTmlofku7YNCiAgICAgICAgICBpZiAodGhpcy5zdG9yYWdlVHlwZSA9PT0gJ29zcycgJiYgdGhpcy5vc3NJbml0aWFsaXplZCkgew0KICAgICAgICAgICAgY29uc3QgZGVsZXRlUHJvbWlzZXMgPSBmaWxlc1RvRGVsZXRlLm1hcChhc3luYyAoZmlsZSkgPT4gew0KICAgICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICAgIC8vIOaehOW7uk9TU+aWh+S7tui3r+W+hA0KICAgICAgICAgICAgICAgIGxldCBvc3NGaWxlUGF0aCA9ICcnDQogICAgICAgICAgICAgICAgaWYgKGZpbGUub3NzRmlsZU5hbWUpIHsNCiAgICAgICAgICAgICAgICAgIC8vIOWmguaenOaciU9TU+aWh+S7tuWQje+8jOebtOaOpeS9v+eUqA0KICAgICAgICAgICAgICAgICAgb3NzRmlsZVBhdGggPSBmaWxlLm9zc0ZpbGVOYW1lDQogICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgIC8vIOWQpuWImeagueaNruaWh+S7tuWkueWSjOaWh+S7tuWQjeaehOW7uui3r+W+hA0KICAgICAgICAgICAgICAgICAgY29uc3QgYmFzZUZvbGRlck5hbWUgPSB0aGlzLm1hdGVyaWFsVGFiID09PSAnYmdtJyA/ICdhYmdtJyA6ICdhc3VjYWknDQogICAgICAgICAgICAgICAgICBjb25zdCBmdWxsUGF0aCA9IHRoaXMuYnVpbGRPU1NQYXRoKGJhc2VGb2xkZXJOYW1lKQ0KICAgICAgICAgICAgICAgICAgb3NzRmlsZVBhdGggPSBgJHtmdWxsUGF0aH0vJHtmaWxlLm5hbWV9YA0KICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGDliKDpmaRPU1Pmlofku7Y6ICR7b3NzRmlsZVBhdGh9YCkNCiAgICAgICAgICAgICAgICBhd2FpdCBkZWxldGVGaWxlRnJvbU9TUyhvc3NGaWxlUGF0aCkNCiAgICAgICAgICAgICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBmaWxlOiBmaWxlLm5hbWUgfQ0KICAgICAgICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYOWIoOmZpE9TU+aWh+S7tuWksei0pTogJHtmaWxlLm5hbWV9YCwgZXJyb3IpDQogICAgICAgICAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGZpbGU6IGZpbGUubmFtZSwgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KQ0KDQogICAgICAgICAgICBjb25zdCBkZWxldGVSZXN1bHRzID0gYXdhaXQgUHJvbWlzZS5hbGwoZGVsZXRlUHJvbWlzZXMpDQoNCiAgICAgICAgICAgIC8vIOajgOafpeWIoOmZpOe7k+aenA0KICAgICAgICAgICAgY29uc3QgZmFpbGVkRGVsZXRlcyA9IGRlbGV0ZVJlc3VsdHMuZmlsdGVyKHJlc3VsdCA9PiAhcmVzdWx0LnN1Y2Nlc3MpDQogICAgICAgICAgICBpZiAoZmFpbGVkRGVsZXRlcy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgIGNvbnNvbGUud2Fybign6YOo5YiG5paH5Lu25Yig6Zmk5aSx6LSlOicsIGZhaWxlZERlbGV0ZXMpDQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhgJHtmYWlsZWREZWxldGVzLmxlbmd0aH0g5Liq5paH5Lu25Yig6Zmk5aSx6LSl77yM5L2G5bey5LuO5YiX6KGo5Lit56e76ZmkYCkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDku47liY3nq6/liJfooajkuK3liKDpmaTmlofku7YNCiAgICAgICAgICB0aGlzLnNlbGVjdGVkRmlsZXMuZm9yRWFjaChmaWxlSWQgPT4gew0KICAgICAgICAgICAgY29uc3QgaW5kZXggPSBjdXJyZW50TGlzdC5maW5kSW5kZXgoZiA9PiBmLmlkID09PSBmaWxlSWQpDQogICAgICAgICAgICBpZiAoaW5kZXggPiAtMSkgew0KICAgICAgICAgICAgICBjdXJyZW50TGlzdC5zcGxpY2UoaW5kZXgsIDEpDQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCg0KICAgICAgICAgIHRoaXMuc2VsZWN0ZWRGaWxlcyA9IFtdDQogICAgICAgICAgdGhpcy5zZWxlY3RBbGwgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJykNCg0KICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WIoOmZpOWksei0pTonLCBlcnJvcikNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGDliKDpmaTlpLHotKU6ICR7ZXJyb3IubWVzc2FnZX1gKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgc2hvd1VwbG9hZERpYWxvZygpIHsNCiAgICAgIHRoaXMudXBsb2FkRGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuZmlsZUxpc3QgPSBbXQ0KICAgICAgdGhpcy51cGxvYWRGb3JtID0ge30NCiAgICB9LA0KDQogICAgYmVmb3JlVXBsb2FkKGZpbGUpIHsNCiAgICAgIC8vIOa3u+WKoOiwg+ivleS/oeaBrw0KICAgICAgY29uc29sZS5sb2coJ+S4iuS8oOaWh+S7tuS/oeaBrzonLCB7DQogICAgICAgIG5hbWU6IGZpbGUubmFtZSwNCiAgICAgICAgdHlwZTogZmlsZS50eXBlLA0KICAgICAgICBzaXplOiBmaWxlLnNpemUsDQogICAgICAgIGN1cnJlbnRUYWI6IHRoaXMubWF0ZXJpYWxUYWINCiAgICAgIH0pDQoNCiAgICAgIC8vIOagueaNruagh+etvumhteajgOafpeaWh+S7tuWkp+Wwjw0KICAgICAgbGV0IG1heFNpemUgPSA1MDAgLy8g6buY6K6kNTAwTUINCiAgICAgIGlmICh0aGlzLm1hdGVyaWFsVGFiID09PSAnYmdtJykgew0KICAgICAgICBtYXhTaXplID0gMTAwIC8vIEJHTeaWh+S7tjEwME1CDQogICAgICB9DQoNCiAgICAgIGNvbnN0IGlzVmFsaWRTaXplID0gZmlsZS5zaXplIC8gMTAyNCAvIDEwMjQgPCBtYXhTaXplDQogICAgICBpZiAoIWlzVmFsaWRTaXplKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYOaWh+S7tuWkp+Wwj+S4jeiDvei2hei/hyAke21heFNpemV9TULvvIFgKQ0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCg0KICAgICAgLy8g5qC55o2u5qCH562+6aG15qOA5p+l5paH5Lu25qC85byPDQogICAgICBjb25zdCBleHRlbnNpb24gPSBmaWxlLm5hbWUudG9Mb3dlckNhc2UoKS5zcGxpdCgnLicpLnBvcCgpDQogICAgICBjb25zdCB2aWRlb0V4dHMgPSBbJ21wNCcsICdhdmknLCAnbW92JywgJ3dtdicsICdmbHYnLCAnd2VibScsICczZ3AnLCAnbWt2JywgJ200diddDQogICAgICBjb25zdCBhdWRpb0V4dHMgPSBbJ21wMycsICd3YXYnLCAnZmxhYycsICdhYWMnLCAnbTRhJywgJ29nZycsICd3bWEnXQ0KICAgICAgY29uc3QgaW1hZ2VFeHRzID0gWydqcGcnLCAnanBlZycsICdwbmcnLCAnZ2lmJywgJ2JtcCcsICd3ZWJwJ10NCg0KICAgICAgaWYgKHRoaXMubWF0ZXJpYWxUYWIgPT09ICdiZ20nICYmICFhdWRpb0V4dHMuaW5jbHVkZXMoZXh0ZW5zaW9uKSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGBCR03lj6rmlK/mjIHpn7PpopHmlofku7bvvIHmlK/mjIHmoLzlvI/vvJoke2F1ZGlvRXh0cy5qb2luKCcsICcpfWApDQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KDQogICAgICBpZiAodGhpcy5tYXRlcmlhbFRhYiA9PT0gJ3N1Y2FpJykgew0KICAgICAgICBjb25zdCBhbGxFeHRzID0gWy4uLnZpZGVvRXh0cywgLi4uYXVkaW9FeHRzLCAuLi5pbWFnZUV4dHNdDQogICAgICAgIGlmICghYWxsRXh0cy5pbmNsdWRlcyhleHRlbnNpb24pKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihg57Sg5p2Q5pSv5oyB6KeG6aKR44CB6Z+z6aKR44CB5Zu+54mH5paH5Lu277yB5pSv5oyB5qC85byP77yaJHthbGxFeHRzLmpvaW4oJywgJyl9YCkNCiAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICByZXR1cm4gdHJ1ZQ0KICAgIH0sDQoNCiAgICBpc1ZhbGlkRmlsZVR5cGUodHlwZSwgZmlsZU5hbWUpIHsNCiAgICAgIC8vIOiOt+WPluaWh+S7tuaJqeWxleWQjeS9nOS4uuWkh+eUqOmqjOivgQ0KICAgICAgY29uc3QgZXh0ZW5zaW9uID0gZmlsZU5hbWUudG9Mb3dlckNhc2UoKS5zcGxpdCgnLicpLnBvcCgpDQoNCiAgICAgIGlmICh0aGlzLm1hdGVyaWFsVGFiID09PSAndmlkZW8nKSB7DQogICAgICAgIGNvbnN0IHZpZGVvVHlwZXMgPSBbDQogICAgICAgICAgJ3ZpZGVvL21wNCcsICd2aWRlby9tcGVnJywgJ3ZpZGVvL3F1aWNrdGltZScsICd2aWRlby94LW1zdmlkZW8nLA0KICAgICAgICAgICd2aWRlby94LW1zLXdtdicsICd2aWRlby94LWZsdicsICd2aWRlby93ZWJtJywgJ3ZpZGVvLzNncHAnLA0KICAgICAgICAgICd2aWRlby9tcDJ0JywgJ3ZpZGVvL3gtbTR2Jw0KICAgICAgICBdDQogICAgICAgIGNvbnN0IHZpZGVvRXh0ZW5zaW9ucyA9IFsnbXA0JywgJ2F2aScsICdtb3YnLCAnd212JywgJ2ZsdicsICd3ZWJtJywgJzNncCcsICdta3YnLCAnbTR2JywgJ3RzJ10NCg0KICAgICAgICByZXR1cm4gdmlkZW9UeXBlcy5pbmNsdWRlcyh0eXBlKSB8fCB2aWRlb0V4dGVuc2lvbnMuaW5jbHVkZXMoZXh0ZW5zaW9uKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY29uc3QgYXVkaW9UeXBlcyA9IFsNCiAgICAgICAgICAnYXVkaW8vbXAzJywgJ2F1ZGlvL21wZWcnLCAnYXVkaW8vd2F2JywgJ2F1ZGlvL3gtd2F2JywNCiAgICAgICAgICAnYXVkaW8vZmxhYycsICdhdWRpby9hYWMnLCAnYXVkaW8vbXA0JywgJ2F1ZGlvL3gtbTRhJywNCiAgICAgICAgICAnYXVkaW8vb2dnJywgJ2F1ZGlvL3dlYm0nDQogICAgICAgIF0NCiAgICAgICAgY29uc3QgYXVkaW9FeHRlbnNpb25zID0gWydtcDMnLCAnd2F2JywgJ2ZsYWMnLCAnYWFjJywgJ200YScsICdvZ2cnLCAnd21hJ10NCg0KICAgICAgICByZXR1cm4gYXVkaW9UeXBlcy5pbmNsdWRlcyh0eXBlKSB8fCBhdWRpb0V4dGVuc2lvbnMuaW5jbHVkZXMoZXh0ZW5zaW9uKQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlVXBsb2FkU3VjY2VzcyhyZXNwb25zZSwgZmlsZSkgew0KICAgICAgLy8g5Yib5bu65paw55qE5paH5Lu25a+56LGhDQogICAgICBjb25zdCBuZXdGaWxlID0gew0KICAgICAgICBpZDogRGF0ZS5ub3coKSArIE1hdGgucmFuZG9tKCksDQogICAgICAgIG5hbWU6IGZpbGUubmFtZSwNCiAgICAgICAgdHlwZTogdGhpcy5tYXRlcmlhbFRhYiA9PT0gJ3ZpZGVvJyA/ICd2aWRlbycgOiAnYXVkaW8nLA0KICAgICAgICBzaXplOiBmaWxlLnNpemUsDQogICAgICAgIHVwbG9hZFRpbWU6IG5ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoKS5zbGljZSgwLCAxNiksDQogICAgICAgIGR1cmF0aW9uOiAnMDA6MDA6MDAnLCAvLyDlrp7pmYXlupTnlKjkuK3lupTor6Xku47mnI3liqHlmajov5Tlm54NCiAgICAgICAgcmVzb2x1dGlvbjogdGhpcy5tYXRlcmlhbFRhYiA9PT0gJ3ZpZGVvJyA/ICcxOTIweDEwODAnIDogdW5kZWZpbmVkLA0KICAgICAgICBiaXRyYXRlOiB0aGlzLm1hdGVyaWFsVGFiID09PSAnbXVzaWMnID8gJzEyOGticHMnIDogdW5kZWZpbmVkDQogICAgICB9DQoNCiAgICAgIC8vIOa3u+WKoOWIsOWvueW6lOeahOWIl+ihqA0KICAgICAgaWYgKHRoaXMubWF0ZXJpYWxUYWIgPT09ICd2aWRlbycpIHsNCiAgICAgICAgdGhpcy52aWRlb0xpc3QudW5zaGlmdChuZXdGaWxlKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5tdXNpY0xpc3QudW5zaGlmdChuZXdGaWxlKQ0KICAgICAgfQ0KDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYCR7ZmlsZS5uYW1lfSDkuIrkvKDmiJDlip/vvIFgKQ0KICAgIH0sDQoNCiAgICBoYW5kbGVVcGxvYWRFcnJvcihlcnIsIGZpbGUpIHsNCiAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYCR7ZmlsZS5uYW1lfSDkuIrkvKDlpLHotKXvvIFgKQ0KICAgIH0sDQoNCiAgICBoYW5kbGVVcGxvYWRQcm9ncmVzcyhldmVudCwgZmlsZSkgew0KICAgICAgLy8g6K6h566X5LiK5Lyg6L+b5bqm55m+5YiG5q+UDQogICAgICBjb25zdCBwcm9ncmVzcyA9IE1hdGgucm91bmQoKGV2ZW50LmxvYWRlZCAvIGV2ZW50LnRvdGFsKSAqIDEwMCkNCg0KICAgICAgLy8g5pu05paw6L+b5bqm5p2h5pi+56S6DQogICAgICB0aGlzLiRzZXQodGhpcy51cGxvYWRQcm9ncmVzcywgZmlsZS5uYW1lLCBwcm9ncmVzcykNCg0KICAgICAgY29uc29sZS5sb2coYOaWh+S7tiAke2ZpbGUubmFtZX0g5LiK5Lyg6L+b5bqmOiAke3Byb2dyZXNzfSVgKQ0KICAgIH0sDQoNCiAgICBoYW5kbGVGaWxlQ2hhbmdlKGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICBjb25zb2xlLmxvZygn5paH5Lu25Y+Y5YyWOicsIGZpbGUpDQogICAgICBjb25zb2xlLmxvZygn5b2T5YmN5paH5Lu25YiX6KGoOicsIGZpbGVMaXN0KQ0KICAgICAgdGhpcy5maWxlTGlzdCA9IGZpbGVMaXN0DQogICAgfSwNCg0KICAgIGhhbmRsZVJlbW92ZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgY29uc29sZS5sb2coJ+enu+mZpOaWh+S7tjonLCBmaWxlKQ0KICAgICAgY29uc29sZS5sb2coJ+abtOaWsOWQjueahOaWh+S7tuWIl+ihqDonLCBmaWxlTGlzdCkNCiAgICAgIHRoaXMuZmlsZUxpc3QgPSBmaWxlTGlzdA0KICAgIH0sDQoNCiAgICBhc3luYyBzdWJtaXRVcGxvYWQoKSB7DQogICAgICBjb25zb2xlLmxvZygn5b2T5YmN5paH5Lu25YiX6KGoOicsIHRoaXMuZmlsZUxpc3QpDQogICAgICBjb25zb2xlLmxvZygn5paH5Lu25YiX6KGo6ZW/5bqmOicsIHRoaXMuZmlsZUxpc3QubGVuZ3RoKQ0KDQogICAgICBpZiAoIXRoaXMuZmlsZUxpc3QgfHwgdGhpcy5maWxlTGlzdC5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6nopoHkuIrkvKDnmoTmlofku7YnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgdGhpcy51cGxvYWRpbmcgPSB0cnVlDQogICAgICBjb25zdCBmaWxlQ291bnQgPSB0aGlzLmZpbGVMaXN0Lmxlbmd0aA0KDQogICAgICAvLyDmoLnmja7moIfnrb7pobXnoa7lrprmlofku7blpLnlkoznsbvlnosNCiAgICAgIGxldCBiYXNlRm9sZGVyTmFtZSA9ICcnDQogICAgICBsZXQgZmlsZVR5cGUgPSAnJw0KDQogICAgICBzd2l0Y2ggKHRoaXMubWF0ZXJpYWxUYWIpIHsNCiAgICAgICAgY2FzZSAnYmdtJzoNCiAgICAgICAgICBiYXNlRm9sZGVyTmFtZSA9ICdhYmdtJw0KICAgICAgICAgIGZpbGVUeXBlID0gJ2F1ZGlvJw0KICAgICAgICAgIGJyZWFrDQogICAgICAgIGNhc2UgJ3N1Y2FpJzoNCiAgICAgICAgICBiYXNlRm9sZGVyTmFtZSA9ICdhc3VjYWknDQogICAgICAgICAgZmlsZVR5cGUgPSAnbWl4ZWQnIC8vIOa3t+WQiOexu+Weiw0KICAgICAgICAgIGJyZWFrDQogICAgICAgIGRlZmF1bHQ6DQogICAgICAgICAgYmFzZUZvbGRlck5hbWUgPSAnYWJnbScNCiAgICAgICAgICBmaWxlVHlwZSA9ICdhdWRpbycNCiAgICAgIH0NCg0KICAgICAgLy8g5p6E5bu65a6M5pW055qET1NT6Lev5b6E77yaYmFzZUZvbGRlck5hbWUv55So5oi35ZCNL+aWh+S7tuWkueWQjQ0KICAgICAgY29uc3QgZm9sZGVyTmFtZSA9IHRoaXMuYnVpbGRPU1NQYXRoKGJhc2VGb2xkZXJOYW1lKQ0KDQogICAgICBjb25zb2xlLmxvZygn5b2T5YmN5qCH562+6aG1OicsIHRoaXMubWF0ZXJpYWxUYWIpDQogICAgICBjb25zb2xlLmxvZygn5Z+656GA5paH5Lu25aS5OicsIGJhc2VGb2xkZXJOYW1lKQ0KICAgICAgY29uc29sZS5sb2coJ+WujOaVtOS4iuS8oOi3r+W+hDonLCBmb2xkZXJOYW1lKQ0KICAgICAgY29uc29sZS5sb2coJ+aWh+S7tuexu+WeizonLCBmaWxlVHlwZSkNCg0KICAgICAgLy8g6I635Y+W5a6e6ZmF55qE5paH5Lu25a+56LGhDQogICAgICBjb25zdCBhY3R1YWxGaWxlcyA9IHRoaXMuZmlsZUxpc3QubWFwKGZpbGVJdGVtID0+IHsNCiAgICAgICAgLy8gRWxlbWVudCBVSSDkuIrkvKDnu4Tku7bnmoTmlofku7blr7nosaHlj6/og73mnInkuI3lkIznmoTnu5PmnoQNCiAgICAgICAgcmV0dXJuIGZpbGVJdGVtLnJhdyB8fCBmaWxlSXRlbS5maWxlIHx8IGZpbGVJdGVtDQogICAgICB9KS5maWx0ZXIoZmlsZSA9PiBmaWxlIGluc3RhbmNlb2YgRmlsZSkNCg0KICAgICAgY29uc29sZS5sb2coJ+WunumZheaWh+S7tuWvueixoTonLCBhY3R1YWxGaWxlcykNCg0KICAgICAgaWYgKGFjdHVhbEZpbGVzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ayoeacieaJvuWIsOacieaViOeahOaWh+S7tuWvueixoScpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICB0cnkgew0KICAgICAgICAvLyDliJ3lp4vljJbmiYDmnInmlofku7bnmoTov5vluqbmnaENCiAgICAgICAgYWN0dWFsRmlsZXMuZm9yRWFjaChmaWxlID0+IHsNCiAgICAgICAgICB0aGlzLiRzZXQodGhpcy51cGxvYWRQcm9ncmVzcywgZmlsZS5uYW1lLCAwKQ0KICAgICAgICB9KQ0KDQogICAgICAgIGlmICh0aGlzLnN0b3JhZ2VUeXBlID09PSAnb3NzJykgew0KICAgICAgICAgIC8vIE9TU+S4iuS8oA0KICAgICAgICAgIGlmICghdGhpcy5vc3NJbml0aWFsaXplZCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcignT1NT5pyq6YWN572u77yM6K+35YWI6YWN572uT1NTJykNCiAgICAgICAgICAgIHRoaXMub3Blbk9TU0NvbmZpZygpDQogICAgICAgICAgICB0aGlzLnVwbG9hZGluZyA9IGZhbHNlDQogICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDkvb/nlKhPU1PkuIrkvKANCiAgICAgICAgICBjb25zdCB1cGxvYWRSZXN1bHRzID0gYXdhaXQgdXBsb2FkRmlsZXNUb09TUygNCiAgICAgICAgICAgIGFjdHVhbEZpbGVzLA0KICAgICAgICAgICAgZmlsZVR5cGUsDQogICAgICAgICAgICBmb2xkZXJOYW1lLCAvLyDkvb/nlKjlr7nlupTnmoTmlofku7blpLkNCiAgICAgICAgICAgIChpbmRleCwgcHJvZ3Jlc3MsIGZpbGVOYW1lLCByZXN1bHQsIGVycm9yKSA9PiB7DQogICAgICAgICAgICAgIGlmIChlcnJvcikgew0KICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnVwbG9hZFByb2dyZXNzLCBmaWxlTmFtZSwgLTEpIC8vIOihqOekuuWksei0pQ0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnVwbG9hZFByb2dyZXNzLCBmaWxlTmFtZSwgcHJvZ3Jlc3MpDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICApDQoNCiAgICAgICAgICAvLyDlpITnkIbkuIrkvKDnu5PmnpwNCiAgICAgICAgICB1cGxvYWRSZXN1bHRzLmZvckVhY2goKHJlc3VsdCwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXN1bHQuc3VjY2Vzcykgew0KICAgICAgICAgICAgICAvLyDmoLnmja7mlofku7bmianlsZXlkI3noa7lrprmlofku7bnsbvlnosNCiAgICAgICAgICAgICAgY29uc3QgZmlsZUV4dGVuc2lvbiA9IHJlc3VsdC5vcmlnaW5hbE5hbWUudG9Mb3dlckNhc2UoKS5zcGxpdCgnLicpLnBvcCgpDQogICAgICAgICAgICAgIGNvbnN0IGF1ZGlvRXh0cyA9IFsnbXAzJywgJ3dhdicsICdmbGFjJywgJ2FhYycsICdtNGEnLCAnb2dnJywgJ3dtYSddDQogICAgICAgICAgICAgIGNvbnN0IHZpZGVvRXh0cyA9IFsnbXA0JywgJ2F2aScsICdtb3YnLCAnd212JywgJ2ZsdicsICd3ZWJtJywgJzNncCcsICdta3YnLCAnbTR2J10NCiAgICAgICAgICAgICAgY29uc3QgaW1hZ2VFeHRzID0gWydqcGcnLCAnanBlZycsICdwbmcnLCAnZ2lmJywgJ2JtcCcsICd3ZWJwJywgJ3N2ZyddDQoNCiAgICAgICAgICAgICAgbGV0IGFjdHVhbEZpbGVUeXBlID0gJ3Vua25vd24nDQogICAgICAgICAgICAgIGlmIChhdWRpb0V4dHMuaW5jbHVkZXMoZmlsZUV4dGVuc2lvbikpIHsNCiAgICAgICAgICAgICAgICBhY3R1YWxGaWxlVHlwZSA9ICdhdWRpbycNCiAgICAgICAgICAgICAgfSBlbHNlIGlmICh2aWRlb0V4dHMuaW5jbHVkZXMoZmlsZUV4dGVuc2lvbikpIHsNCiAgICAgICAgICAgICAgICBhY3R1YWxGaWxlVHlwZSA9ICd2aWRlbycNCiAgICAgICAgICAgICAgfSBlbHNlIGlmIChpbWFnZUV4dHMuaW5jbHVkZXMoZmlsZUV4dGVuc2lvbikpIHsNCiAgICAgICAgICAgICAgICBhY3R1YWxGaWxlVHlwZSA9ICdpbWFnZScNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIGNvbnN0IG5ld0ZpbGUgPSB7DQogICAgICAgICAgICAgICAgaWQ6IERhdGUubm93KCkgKyBNYXRoLnJhbmRvbSgpICsgaW5kZXgsDQogICAgICAgICAgICAgICAgbmFtZTogcmVzdWx0Lm9yaWdpbmFsTmFtZSwNCiAgICAgICAgICAgICAgICB0eXBlOiBhY3R1YWxGaWxlVHlwZSwNCiAgICAgICAgICAgICAgICBzaXplOiByZXN1bHQuc2l6ZSwNCiAgICAgICAgICAgICAgICB1cGxvYWRUaW1lOiBuZXcgRGF0ZSgpLnRvTG9jYWxlU3RyaW5nKCkuc2xpY2UoMCwgMTYpLA0KICAgICAgICAgICAgICAgIGR1cmF0aW9uOiBhY3R1YWxGaWxlVHlwZSA9PT0gJ3ZpZGVvJyA/ICcwMDowMjozMCcgOiAoYWN0dWFsRmlsZVR5cGUgPT09ICdhdWRpbycgPyAnMDA6MDM6NDUnIDogdW5kZWZpbmVkKSwNCiAgICAgICAgICAgICAgICByZXNvbHV0aW9uOiBhY3R1YWxGaWxlVHlwZSA9PT0gJ3ZpZGVvJyA/ICcxOTIweDEwODAnIDogdW5kZWZpbmVkLA0KICAgICAgICAgICAgICAgIGJpdHJhdGU6IGFjdHVhbEZpbGVUeXBlID09PSAnYXVkaW8nID8gJzEyOGticHMnIDogdW5kZWZpbmVkLA0KICAgICAgICAgICAgICAgIHVybDogcmVzdWx0LnVybCwNCiAgICAgICAgICAgICAgICBvc3NGaWxlTmFtZTogcmVzdWx0LmZpbGVOYW1lLA0KICAgICAgICAgICAgICAgIGZvbGRlcjogZm9sZGVyTmFtZQ0KICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgLy8g5qC55o2u5qCH562+6aG15re75Yqg5Yiw5a+55bqU5YiX6KGoDQogICAgICAgICAgICAgIHN3aXRjaCAodGhpcy5tYXRlcmlhbFRhYikgew0KICAgICAgICAgICAgICAgIGNhc2UgJ2JnbSc6DQogICAgICAgICAgICAgICAgICB0aGlzLmJnbUxpc3QudW5zaGlmdChuZXdGaWxlKQ0KICAgICAgICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICAgICAgICBjYXNlICdzdWNhaSc6DQogICAgICAgICAgICAgICAgICB0aGlzLnN1Y2FpTGlzdC51bnNoaWZ0KG5ld0ZpbGUpDQogICAgICAgICAgICAgICAgICBicmVhaw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCg0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5oiQ5Yqf5LiK5LygICR7dXBsb2FkUmVzdWx0cy5maWx0ZXIociA9PiByLnN1Y2Nlc3MpLmxlbmd0aH0g5Liq5paH5Lu25Yiw6Zi/6YeM5LqRT1NT77yBYCkNCg0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOacrOWcsOaooeaLn+S4iuS8oA0KICAgICAgICAgIGFjdHVhbEZpbGVzLmZvckVhY2goKGZpbGUpID0+IHsNCiAgICAgICAgICAgIC8vIOi/m+W6puW3suWcqOS4iumdouWIneWni+WMlu+8jOi/memHjOebtOaOpeW8gOWni+aooeaLn+i/m+W6puabtOaWsA0KDQogICAgICAgICAgICAvLyDmqKHmi5/ov5vluqbmm7TmlrANCiAgICAgICAgICAgIGNvbnN0IHByb2dyZXNzSW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7DQogICAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRQcm9ncmVzcyA9IHRoaXMudXBsb2FkUHJvZ3Jlc3NbZmlsZS5uYW1lXSB8fCAwDQogICAgICAgICAgICAgIGlmIChjdXJyZW50UHJvZ3Jlc3MgPCAxMDApIHsNCiAgICAgICAgICAgICAgICB0aGlzLiRzZXQodGhpcy51cGxvYWRQcm9ncmVzcywgZmlsZS5uYW1lLCBNYXRoLm1pbihjdXJyZW50UHJvZ3Jlc3MgKyBNYXRoLnJhbmRvbSgpICogMzAsIDEwMCkpDQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgY2xlYXJJbnRlcnZhbChwcm9ncmVzc0ludGVydmFsKQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LCAyMDApDQogICAgICAgICAgfSkNCg0KICAgICAgICAgIC8vIOaooeaLn+S4iuS8oOWujOaIkA0KICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgICAgLy8g5Li65q+P5Liq5paH5Lu25Yib5bu65paw6K6w5b2VDQogICAgICAgICAgICBhY3R1YWxGaWxlcy5mb3JFYWNoKChmaWxlLCBpbmRleCkgPT4gew0KICAgICAgICAgICAgICAvLyDmoLnmja7mlofku7bmianlsZXlkI3noa7lrprmlofku7bnsbvlnosNCiAgICAgICAgICAgICAgY29uc3QgZmlsZUV4dGVuc2lvbiA9IGZpbGUubmFtZS50b0xvd2VyQ2FzZSgpLnNwbGl0KCcuJykucG9wKCkNCiAgICAgICAgICAgICAgY29uc3QgYXVkaW9FeHRzID0gWydtcDMnLCAnd2F2JywgJ2ZsYWMnLCAnYWFjJywgJ200YScsICdvZ2cnLCAnd21hJ10NCiAgICAgICAgICAgICAgY29uc3QgdmlkZW9FeHRzID0gWydtcDQnLCAnYXZpJywgJ21vdicsICd3bXYnLCAnZmx2JywgJ3dlYm0nLCAnM2dwJywgJ21rdicsICdtNHYnXQ0KICAgICAgICAgICAgICBjb25zdCBpbWFnZUV4dHMgPSBbJ2pwZycsICdqcGVnJywgJ3BuZycsICdnaWYnLCAnYm1wJywgJ3dlYnAnLCAnc3ZnJ10NCg0KICAgICAgICAgICAgICBsZXQgYWN0dWFsRmlsZVR5cGUgPSAndW5rbm93bicNCiAgICAgICAgICAgICAgaWYgKGF1ZGlvRXh0cy5pbmNsdWRlcyhmaWxlRXh0ZW5zaW9uKSkgew0KICAgICAgICAgICAgICAgIGFjdHVhbEZpbGVUeXBlID0gJ2F1ZGlvJw0KICAgICAgICAgICAgICB9IGVsc2UgaWYgKHZpZGVvRXh0cy5pbmNsdWRlcyhmaWxlRXh0ZW5zaW9uKSkgew0KICAgICAgICAgICAgICAgIGFjdHVhbEZpbGVUeXBlID0gJ3ZpZGVvJw0KICAgICAgICAgICAgICB9IGVsc2UgaWYgKGltYWdlRXh0cy5pbmNsdWRlcyhmaWxlRXh0ZW5zaW9uKSkgew0KICAgICAgICAgICAgICAgIGFjdHVhbEZpbGVUeXBlID0gJ2ltYWdlJw0KICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgY29uc3QgbmV3RmlsZSA9IHsNCiAgICAgICAgICAgICAgICBpZDogRGF0ZS5ub3coKSArIE1hdGgucmFuZG9tKCkgKyBpbmRleCwNCiAgICAgICAgICAgICAgICBuYW1lOiBmaWxlLm5hbWUsDQogICAgICAgICAgICAgICAgdHlwZTogYWN0dWFsRmlsZVR5cGUsDQogICAgICAgICAgICAgICAgc2l6ZTogZmlsZS5zaXplLA0KICAgICAgICAgICAgICAgIHVwbG9hZFRpbWU6IG5ldyBEYXRlKCkudG9Mb2NhbGVTdHJpbmcoKS5zbGljZSgwLCAxNiksDQogICAgICAgICAgICAgICAgZHVyYXRpb246IGFjdHVhbEZpbGVUeXBlID09PSAndmlkZW8nID8gJzAwOjAyOjMwJyA6IChhY3R1YWxGaWxlVHlwZSA9PT0gJ2F1ZGlvJyA/ICcwMDowMzo0NScgOiB1bmRlZmluZWQpLA0KICAgICAgICAgICAgICAgIHJlc29sdXRpb246IGFjdHVhbEZpbGVUeXBlID09PSAndmlkZW8nID8gJzE5MjB4MTA4MCcgOiB1bmRlZmluZWQsDQogICAgICAgICAgICAgICAgYml0cmF0ZTogYWN0dWFsRmlsZVR5cGUgPT09ICdhdWRpbycgPyAnMTI4a2JwcycgOiB1bmRlZmluZWQsDQogICAgICAgICAgICAgICAgZm9sZGVyOiBmb2xkZXJOYW1lLA0KICAgICAgICAgICAgICAgIHVybDogVVJMLmNyZWF0ZU9iamVjdFVSTChmaWxlKSAvLyDkuLrmnKzlnLDmlofku7bliJvlu7rpooTop4hVUkwNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC8vIOagueaNruagh+etvumhtea3u+WKoOWIsOWvueW6lOWIl+ihqA0KICAgICAgICAgICAgICBzd2l0Y2ggKHRoaXMubWF0ZXJpYWxUYWIpIHsNCiAgICAgICAgICAgICAgICBjYXNlICdiZ20nOg0KICAgICAgICAgICAgICAgICAgdGhpcy5iZ21MaXN0LnVuc2hpZnQobmV3RmlsZSkNCiAgICAgICAgICAgICAgICAgIGJyZWFrDQogICAgICAgICAgICAgICAgY2FzZSAnc3VjYWknOg0KICAgICAgICAgICAgICAgICAgdGhpcy5zdWNhaUxpc3QudW5zaGlmdChuZXdGaWxlKQ0KICAgICAgICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSkNCg0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDmiJDlip/kuIrkvKAgJHtmaWxlQ291bnR9IOS4quaWh+S7tu+8gWApDQoNCiAgICAgICAgICAgIC8vIOS4iuS8oOWujOaIkOWQjuabtOaWsOaWh+S7tuWkueiuoeaVsA0KICAgICAgICAgICAgdGhpcy51cGRhdGVDdXJyZW50VGFiRm9sZGVyQ291bnRzKCkNCiAgICAgICAgICB9LCAyMDAwKQ0KICAgICAgICB9DQoNCiAgICAgICAgdGhpcy51cGxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB0aGlzLnVwbG9hZERpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgICB0aGlzLmZpbGVMaXN0ID0gW10NCiAgICAgICAgdGhpcy51cGxvYWRQcm9ncmVzcyA9IHt9DQoNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIHRoaXMudXBsb2FkaW5nID0gZmFsc2UNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihg5LiK5Lyg5aSx6LSl77yaJHtlcnJvci5tZXNzYWdlfWApDQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S4iuS8oOmUmeivrzonLCBlcnJvcikNCiAgICAgIH0NCiAgICB9LA0KDQogICAgZm9ybWF0RmlsZVNpemUoYnl0ZXMpIHsNCiAgICAgIGlmIChieXRlcyA9PT0gMCkgcmV0dXJuICcwIEJ5dGVzJw0KICAgICAgY29uc3QgayA9IDEwMjQNCiAgICAgIGNvbnN0IHNpemVzID0gWydCeXRlcycsICdLQicsICdNQicsICdHQiddDQogICAgICBjb25zdCBpID0gTWF0aC5mbG9vcihNYXRoLmxvZyhieXRlcykgLyBNYXRoLmxvZyhrKSkNCiAgICAgIHJldHVybiBwYXJzZUZsb2F0KChieXRlcyAvIE1hdGgucG93KGssIGkpKS50b0ZpeGVkKDIpKSArICcgJyArIHNpemVzW2ldDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["up.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwiBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "up.vue", "sourceRoot": "src/views/store", "sourcesContent": ["<template>\r\n  <div class=\"up-container\">\r\n    <!-- 顶部标签页 -->\r\n    <div class=\"materials-tabs\">\r\n      <div class=\"tab-buttons\">\r\n        <el-button\r\n          :type=\"materialTab === 'sucai' ? 'primary' : 'default'\"\r\n          @click=\"switchTab('sucai')\"\r\n          class=\"tab-button\">\r\n          素材上传\r\n        </el-button>\r\n        <el-button\r\n          :type=\"materialTab === 'bgm' ? 'primary' : 'default'\"\r\n          @click=\"switchTab('bgm')\"\r\n          class=\"tab-button\">\r\n          BGM上传\r\n        </el-button>\r\n\r\n        <!-- BGM免费下载按钮 -->\r\n        <el-button\r\n          v-if=\"materialTab === 'bgm'\"\r\n          type=\"success\"\r\n          size=\"small\"\r\n          icon=\"el-icon-download\"\r\n          @click=\"openBGMDownloadSite\"\r\n          class=\"bgm-download-button\">\r\n          免费在线下载\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <div class=\"materials-main\">\r\n      <!-- 左侧文件夹树 -->\r\n      <div class=\"folder-sidebar\">\r\n        <div class=\"folder-header\">\r\n          <div class=\"folder-actions\">\r\n            <el-button\r\n              type=\"primary\"\r\n              size=\"small\"\r\n              icon=\"el-icon-upload2\"\r\n              @click=\"showUploadDialog\"\r\n              style=\"height: 32px !important; width: 100% !important; max-width: 120px !important;\">\r\n              上传\r\n            </el-button>\r\n            <el-button\r\n              type=\"success\"\r\n              size=\"small\"\r\n              icon=\"el-icon-folder-add\"\r\n              @click=\"showCreateFolderDialog\"\r\n              style=\"height: 32px !important; width: 100% !important; max-width: 120px !important;\">\r\n              新建文件夹\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"folder-list\">\r\n          <!-- 只有OSS配置成功后才显示文件夹 -->\r\n          <div v-if=\"ossInitialized\">\r\n            <div\r\n              v-for=\"folder in currentFolderTree\"\r\n              :key=\"folder.id\"\r\n              class=\"folder-item\"\r\n              :class=\"{ active: selectedFolder === folder.id }\"\r\n              @click=\"selectFolder(folder)\"\r\n              @contextmenu.prevent=\"showFolderContextMenu($event, folder)\">\r\n              <i class=\"folder-icon\">📁</i>\r\n              <span class=\"folder-name\">{{ folder.name }}</span>\r\n              <span class=\"folder-count\">{{ folder.count }}</span>\r\n            </div>\r\n          </div>\r\n\r\n\r\n          <!-- OSS未配置时的提示 -->\r\n          <div v-else class=\"no-oss-tip\">\r\n            <div class=\"tip-icon\">⚙️</div>\r\n            <div class=\"tip-text\">请先配置OSS存储</div>\r\n            <el-button type=\"primary\" size=\"small\" @click=\"openOSSConfig\">配置OSS</el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 文件夹右键菜单 -->\r\n        <div\r\n          v-show=\"contextMenuVisible\"\r\n          class=\"context-menu\"\r\n          :style=\"{ left: contextMenuX + 'px', top: contextMenuY + 'px' }\"\r\n          @click.stop>\r\n          <div class=\"menu-item\" @click=\"renameFolderAction\">\r\n            <i class=\"el-icon-edit\"></i>\r\n            重命名文件夹\r\n          </div>\r\n          <div class=\"menu-item\" @click=\"deleteFolderAction\" v-if=\"contextMenuFolder && (contextMenuFolder.name || contextMenuFolder) !== '总'\">\r\n            <i class=\"el-icon-delete\"></i>\r\n            删除文件夹\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧文件区域 -->\r\n      <div class=\"files-area\">\r\n        <!-- OSS配置成功后显示文件管理界面 -->\r\n        <div v-if=\"ossInitialized\" class=\"files-toolbar\">\r\n          <div class=\"toolbar-left\">\r\n            <el-checkbox v-model=\"selectAll\" @change=\"handleSelectAll\">全选</el-checkbox>\r\n            <span class=\"file-actions\">\r\n              <span class=\"action-text\" @click=\"handlePreview\" :disabled=\"selectedFiles.length !== 1\">预览</span>\r\n              <span class=\"action-text\" @click=\"handleRename\">重命名</span>\r\n              <span class=\"action-text\" @click=\"handleDelete\">删除</span>\r\n            </span>\r\n          </div>\r\n          <div class=\"toolbar-right\">\r\n            <span class=\"file-count\">共 {{ filteredMaterialList.length }} 项</span>\r\n            <div class=\"pagination-info\">\r\n              <span>{{ currentPage }}</span>\r\n              <span>/</span>\r\n              <span>{{ totalPages }}</span>\r\n              <span>页</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 文件列表 -->\r\n        <div class=\"files-content\">\r\n          <!-- OSS未配置时的提示 -->\r\n          <div v-if=\"!ossInitialized\" class=\"no-oss-files-tip\">\r\n            <div class=\"tip-content\">\r\n              <div class=\"tip-icon\">☁️</div>\r\n              <div class=\"tip-title\">请先配置OSS存储</div>\r\n              <div class=\"tip-description\">配置OSS后即可开始上传和管理文件</div>\r\n              <el-button type=\"primary\" @click=\"openOSSConfig\">配置OSS存储</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- OSS已配置时显示文件列表 -->\r\n          <div v-else-if=\"filteredMaterialList.length === 0\" class=\"empty-state\">\r\n            <div class=\"empty-icon\">📁</div>\r\n            <div class=\"empty-text\">暂无文件</div>\r\n          </div>\r\n          <div v-else>\r\n            <!-- 文件操作工具栏 -->\r\n            <div class=\"file-toolbar\">\r\n              <div class=\"toolbar-left\">\r\n                <el-checkbox v-model=\"selectAll\" @change=\"handleSelectAll\">全选</el-checkbox>\r\n                <span class=\"selected-count\" v-if=\"selectedFiles.length > 0\">\r\n                  已选择 {{ selectedFiles.length }} 个文件\r\n                </span>\r\n              </div>\r\n              <div class=\"toolbar-right\">\r\n                <el-button\r\n                  v-if=\"selectedFiles.length > 0\"\r\n                  type=\"danger\"\r\n                  size=\"small\"\r\n                  @click=\"handleDelete\">\r\n                  删除选中\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"file-grid\">\r\n            <div\r\n              v-for=\"file in paginatedMaterials\"\r\n              :key=\"file.id\"\r\n              class=\"file-card\"\r\n              :class=\"{\r\n                selected: selectedFiles.includes(file.id),\r\n                hovered: file.isHovered,\r\n                enlarged: file.isPlaying,\r\n                'scale-enlarged': file.isScaled\r\n              }\"\r\n              :style=\"file.isScaled && file.displayWidth && file.displayHeight ? {\r\n                width: file.displayWidth + 'px',\r\n                height: file.displayHeight + 'px'\r\n              } : {}\"\r\n              @click=\"toggleFileSelection(file.id)\"\r\n              @dblclick=\"handleFileDoubleClick(file)\">\r\n\r\n              <!-- 文件选择框 -->\r\n              <div class=\"file-checkbox\">\r\n                <el-checkbox :value=\"selectedFiles.includes(file.id)\" @change=\"toggleFileSelection(file.id)\"></el-checkbox>\r\n              </div>\r\n\r\n              <!-- 文件缩略图 -->\r\n              <div class=\"file-thumbnail\">\r\n                <!-- 视频缩略图 -->\r\n                <div v-if=\"file.type === 'video'\" class=\"video-thumbnail\"\r\n                     :class=\"{ playing: file.isPlaying }\"\r\n                     @mouseenter=\"onVideoMouseEnter(file)\"\r\n                     @mouseleave=\"onVideoMouseLeave(file)\"\r\n                     @mousemove=\"onVideoMouseMove(file)\"\r\n                     @click.stop=\"toggleVideoPlayWithScale(file)\">\r\n                  <video\r\n                    :ref=\"`video-${file.id}`\"\r\n                    :src=\"getFileUrl(file)\"\r\n                    preload=\"metadata\"\r\n                    :muted=\"!file.isPlaying\"\r\n                    class=\"thumbnail-video\"\r\n                    @loadedmetadata=\"onVideoLoaded\"\r\n                    @ended=\"onVideoEnded(file)\"\r\n                    @pause=\"onVideoPaused(file)\"\r\n                    @play=\"onVideoPlayed(file)\"\r\n                    @timeupdate=\"onVideoTimeUpdate(file)\"\r\n                    @loadeddata=\"onVideoLoadedData(file)\">\r\n                  </video>\r\n\r\n                  <!-- 小视频的简洁播放按钮 -->\r\n                  <div v-if=\"!file.isScaled\" class=\"simple-play-overlay\">\r\n                    <div class=\"play-button\">\r\n                      <i :class=\"file.isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'\"></i>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- 放大视频的进度条 - 悬浮在视频底部 -->\r\n                  <div v-if=\"file.isScaled\" class=\"video-controls-overlay\">\r\n                    <div class=\"progress-container\" @click.stop=\"seekVideo($event, file)\">\r\n                      <div class=\"progress-track\">\r\n                        <div class=\"progress-fill\" :style=\"{ width: getVideoProgress(file) + '%' }\"></div>\r\n                        <div class=\"progress-thumb\" :style=\"{ left: getVideoProgress(file) + '%' }\"></div>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"controls-bottom\">\r\n                      <div class=\"play-pause-btn\" @click.stop=\"toggleVideoPlayWithScale(file)\">\r\n                        <i :class=\"file.isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'\"></i>\r\n                      </div>\r\n                      <div class=\"time-display\">\r\n                        <span class=\"current-time\">{{ formatTime(getCurrentTime(file)) }}</span>\r\n                        <span class=\"separator\">/</span>\r\n                        <span class=\"total-time\">{{ formatTime(getDuration(file)) }}</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div class=\"duration-badge\" v-if=\"file.duration && !file.isScaled\">{{ file.duration }}</div>\r\n                </div>\r\n\r\n                <!-- 音频缩略图 -->\r\n                <div v-else-if=\"file.type === 'audio'\" class=\"audio-thumbnail\"\r\n                     :class=\"{ playing: file.isPlaying }\"\r\n                     @mouseenter=\"onAudioMouseEnter(file)\"\r\n                     @mouseleave=\"onAudioMouseLeave(file)\"\r\n                     @mousemove=\"onAudioMouseMove(file)\">\r\n                  <audio\r\n                    :ref=\"`audio-${file.id}`\"\r\n                    :src=\"getFileUrl(file)\"\r\n                    preload=\"metadata\"\r\n                    @ended=\"onAudioEnded(file)\"\r\n                    @pause=\"onAudioPaused(file)\"\r\n                    @play=\"onAudioPlayed(file)\">\r\n                  </audio>\r\n                  <div class=\"audio-icon\">🎵</div>\r\n                  <div class=\"audio-waveform\">\r\n                    <div class=\"wave-bar\" v-for=\"i in 12\" :key=\"i\"></div>\r\n                  </div>\r\n                  <div class=\"play-overlay-audio\"\r\n                       @click.stop=\"toggleAudioPlay(file)\"\r\n                       :class=\"{\r\n                         playing: file.isPlaying,\r\n                         visible: file.showControls || !file.isPlaying,\r\n                         hidden: file.isPlaying && !file.showControls\r\n                       }\">\r\n                    <i :class=\"file.isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'\"></i>\r\n                  </div>\r\n                  <div class=\"duration-badge\" v-if=\"file.duration\">{{ file.duration }}</div>\r\n                </div>\r\n\r\n                <!-- 图片缩略图 -->\r\n                <div v-else-if=\"file.type === 'image'\" class=\"image-thumbnail\"\r\n                     @click.stop=\"previewFile(file)\"\r\n                     @mouseenter=\"onImageMouseEnter(file)\"\r\n                     @mouseleave=\"onImageMouseLeave(file)\">\r\n                  <img\r\n                    :src=\"getFileUrl(file)\"\r\n                    :alt=\"file.name\"\r\n                    class=\"thumbnail-image\"\r\n                    @error=\"onImageError\">\r\n                  <div class=\"image-overlay\">\r\n                    <i class=\"el-icon-zoom-in\"></i>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 其他文件类型 -->\r\n                <div v-else class=\"file-icon-thumbnail\">\r\n                  <i :class=\"getSimpleFileIcon(file.type)\"></i>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 文件信息 -->\r\n              <div class=\"file-info\">\r\n                <div class=\"file-name\" :title=\"file.name\">{{ file.name }}</div>\r\n                <div class=\"file-meta\">\r\n                  <span class=\"file-size\">{{ formatFileSize(file.size) }}</span>\r\n                  <span class=\"file-time\">{{ file.uploadTime }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- OSS配置对话框 -->\r\n    <el-dialog\r\n      title=\"设置存储\"\r\n      :visible.sync=\"ossConfigVisible\"\r\n      width=\"600px\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"oss-config-dialog\"\r\n    >\r\n      <div class=\"oss-config-content\">\r\n        <!-- 存储方式选择 -->\r\n        <div class=\"storage-type-section\">\r\n          <div class=\"section-label\">存储方式</div>\r\n          <div class=\"storage-options\">\r\n            <el-radio v-model=\"ossConfig.storageType\" label=\"oss\" class=\"storage-radio\">\r\n              <span class=\"radio-text\">阿里云OSS</span>\r\n            </el-radio>\r\n          </div>\r\n          <div class=\"storage-description\">\r\n            切换阿里云OSS后，素材库需要重新上传至阿里云OSS\r\n          </div>\r\n        </div>\r\n\r\n        <!-- OSS配置表单 -->\r\n        <div class=\"oss-form-section\">\r\n          <div class=\"form-row\">\r\n            <div class=\"form-label required\">存储空间名称</div>\r\n            <el-input\r\n              v-model=\"ossConfig.bucket\"\r\n              placeholder=\"jkhghfgddedb\"\r\n              class=\"form-input\"\r\n            />\r\n          </div>\r\n\r\n          <div class=\"form-row\">\r\n            <div class=\"form-label required\">ACCESS_KEY</div>\r\n            <el-input\r\n              v-model=\"ossConfig.accessKeyId\"\r\n              placeholder=\"LTAI5tSgfoZwykU9M1qvThgq\"\r\n              class=\"form-input\"\r\n            />\r\n          </div>\r\n\r\n          <div class=\"form-row\">\r\n            <div class=\"form-label required\">SECRET_KEY</div>\r\n            <el-input\r\n              v-model=\"ossConfig.accessKeySecret\"\r\n              placeholder=\"******************************\"\r\n              class=\"form-input\"\r\n              show-password\r\n            />\r\n          </div>\r\n\r\n          <div class=\"form-row\">\r\n            <div class=\"form-label required\">空间域名</div>\r\n            <el-input\r\n              v-model=\"ossConfig.endpoint\"\r\n              placeholder=\"https://jkhghfgddedb.oss-cn-shanghai.aliyuncs.com\"\r\n              class=\"form-input\"\r\n            />\r\n            <div class=\"form-hint\">\r\n              请补全http://或https://，例如https://static.cloud.com\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 状态选择 -->\r\n          <div class=\"form-row\">\r\n            <div class=\"form-label\">状态</div>\r\n            <div class=\"status-options\">\r\n              <el-radio v-model=\"ossConfig.status\" label=\"disabled\" class=\"status-radio\">\r\n                <span class=\"radio-text\">关闭</span>\r\n              </el-radio>\r\n              <el-radio v-model=\"ossConfig.status\" label=\"enabled\" class=\"status-radio\">\r\n                <span class=\"radio-text\">开启</span>\r\n              </el-radio>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"ossConfigVisible = false\" class=\"cancel-btn\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveOSSConfig\" :loading=\"testingOSS\" class=\"confirm-btn\">\r\n          {{ testingOSS ? '测试连接中...' : '确定' }}\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 新建文件夹对话框 -->\r\n    <el-dialog\r\n      title=\"新建文件夹\"\r\n      :visible.sync=\"createFolderVisible\"\r\n      width=\"400px\"\r\n      :close-on-click-modal=\"false\">\r\n      <el-form :model=\"createFolderForm\" label-width=\"80px\">\r\n        <el-form-item label=\"文件夹名\">\r\n          <el-input\r\n            v-model=\"createFolderForm.name\"\r\n            placeholder=\"请输入文件夹名称\"\r\n            @keyup.enter.native=\"createFolder\">\r\n          </el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"createFolderVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"createFolder\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 文件预览对话框 -->\r\n    <el-dialog\r\n      title=\"文件预览\"\r\n      :visible.sync=\"previewVisible\"\r\n      width=\"80%\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"preview-dialog\"\r\n      @close=\"stopAllMedia\">\r\n      <div class=\"preview-content\" v-if=\"previewFile\">\r\n        <div class=\"preview-header\">\r\n          <h3>{{ previewFile.name }}</h3>\r\n          <div class=\"file-info\">\r\n            <span>大小: {{ formatFileSize(previewFile.size) }}</span>\r\n            <span>上传时间: {{ previewFile.uploadTime }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 视频预览 -->\r\n        <div v-if=\"currentPreviewFile && currentPreviewFile.type === 'video'\" class=\"video-preview\">\r\n          <video\r\n            ref=\"previewVideo\"\r\n            :src=\"currentPreviewFile.url\"\r\n            controls\r\n            preload=\"metadata\"\r\n            style=\"width: 100%; max-height: 500px;\">\r\n            您的浏览器不支持视频播放\r\n          </video>\r\n        </div>\r\n\r\n        <!-- 音频预览 -->\r\n        <div v-else-if=\"currentPreviewFile && currentPreviewFile.type === 'audio'\" class=\"audio-preview\">\r\n          <div class=\"audio-player\">\r\n            <audio\r\n              ref=\"previewAudio\"\r\n              :src=\"currentPreviewFile.url\"\r\n              controls\r\n              preload=\"metadata\"\r\n              style=\"width: 100%;\">\r\n              您的浏览器不支持音频播放\r\n            </audio>\r\n          </div>\r\n          <div class=\"audio-info\">\r\n            <div class=\"audio-icon\">🎵</div>\r\n            <div class=\"audio-details\">\r\n              <p><strong>{{ currentPreviewFile.name }}</strong></p>\r\n              <p v-if=\"currentPreviewFile.duration\">时长: {{ currentPreviewFile.duration }}</p>\r\n              <p v-if=\"currentPreviewFile.bitrate\">比特率: {{ currentPreviewFile.bitrate }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 图片预览 -->\r\n        <div v-else-if=\"currentPreviewFile && currentPreviewFile.type === 'image'\" class=\"image-preview\">\r\n          <img\r\n            :src=\"currentPreviewFile.url\"\r\n            :alt=\"currentPreviewFile.name\"\r\n            style=\"max-width: 100%; max-height: 500px;\">\r\n        </div>\r\n\r\n        <!-- 不支持预览的文件 -->\r\n        <div v-else class=\"unsupported-preview\">\r\n          <div class=\"unsupported-icon\">📄</div>\r\n          <p>此文件类型不支持预览</p>\r\n          <el-button type=\"primary\" @click=\"downloadFile(previewFile)\">下载文件</el-button>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 上传对话框 -->\r\n    <el-dialog :title=\"uploadDialogTitle\" :visible.sync=\"uploadDialogVisible\" width=\"600px\">\r\n      <div class=\"upload-content\">\r\n        <!-- 存储方式选择 -->\r\n        <div class=\"storage-selector\" style=\"margin-bottom: 20px;\">\r\n          <span style=\"margin-right: 10px;\">存储方式：</span>\r\n          <el-radio-group v-model=\"storageType\" size=\"small\">\r\n            <el-radio label=\"local\">本地存储</el-radio>\r\n            <el-radio label=\"oss\">阿里云OSS</el-radio>\r\n          </el-radio-group>\r\n          <el-button\r\n            v-if=\"storageType === 'oss'\"\r\n            type=\"text\"\r\n            size=\"small\"\r\n            @click=\"openOSSConfig\"\r\n            style=\"margin-left: 10px;\"\r\n          >\r\n            <i class=\"el-icon-setting\"></i> 配置OSS\r\n          </el-button>\r\n          <span\r\n            v-if=\"storageType === 'oss'\"\r\n            :class=\"ossInitialized ? 'text-success' : 'text-danger'\"\r\n            style=\"margin-left: 10px; font-size: 12px;\"\r\n          >\r\n            {{ ossInitialized ? '✓ 已配置' : '✗ 未配置' }}\r\n          </span>\r\n        </div>\r\n\r\n        <el-upload\r\n          class=\"upload-dragger\"\r\n          ref=\"upload\"\r\n          action=\"#\"\r\n          :multiple=\"true\"\r\n          :file-list=\"fileList\"\r\n          :before-upload=\"beforeUpload\"\r\n          :on-success=\"handleUploadSuccess\"\r\n          :on-error=\"handleUploadError\"\r\n          :on-progress=\"handleUploadProgress\"\r\n          :on-remove=\"handleRemove\"\r\n          :on-change=\"handleFileChange\"\r\n          :auto-upload=\"false\"\r\n          drag\r\n        >\r\n          <i class=\"el-icon-upload\"></i>\r\n          <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n          <div class=\"el-upload__tip\" slot=\"tip\">\r\n            {{ uploadTipText }}\r\n          </div>\r\n        </el-upload>\r\n\r\n        <!-- 上传进度显示 -->\r\n        <div v-if=\"uploading && Object.keys(uploadProgress).length > 0\" class=\"upload-progress\" style=\"margin-top: 20px;\">\r\n          <h4>上传进度</h4>\r\n          <div v-for=\"(progress, fileName) in uploadProgress\" :key=\"fileName\" class=\"progress-item\">\r\n            <div class=\"progress-info\">\r\n              <span class=\"file-name\">{{ fileName }}</span>\r\n              <span class=\"progress-text\">{{ progress }}%</span>\r\n            </div>\r\n            <el-progress :percentage=\"progress\" :show-text=\"false\"></el-progress>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"uploadDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitUpload\" :loading=\"uploading\">\r\n          {{ uploading ? '上传中...' : '开始上传' }}\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { initOSSClient, uploadFilesToOSS, getOSSFileList, deleteFileFromOSS, getOSSClient } from '@/utils/ossUpload'\r\nimport { getToken } from '@/utils/auth'\r\nimport store from '@/store'\r\n\r\nexport default {\r\n  name: 'StorerUp',\r\n  data() {\r\n    return {\r\n      // 界面控制\r\n      materialTab: 'sucai', // sucai, bgm\r\n      selectedFolder: 1,\r\n      selectAll: false,\r\n      selectedFiles: [],\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n\r\n      // 分别为BGM和素材维护文件夹结构\r\n      bgmFolderTree: [],\r\n      sucaiFolderTree: [],\r\n\r\n      // 上传相关\r\n      uploadDialogVisible: false,\r\n      uploading: false,\r\n      fileList: [],\r\n      uploadForm: {},\r\n      uploadProgress: {}, // 上传进度\r\n      storageType: 'oss', // 存储方式: local | oss\r\n\r\n      // OSS配置相关\r\n      ossConfigVisible: false,\r\n      testingOSS: false,\r\n      ossConfig: {\r\n        storageType: 'oss', // 存储方式\r\n        bucket: '', // 存储空间名称\r\n        accessKeyId: '', // ACCESS_KEY\r\n        accessKeySecret: '', // SECRET_KEY\r\n        endpoint: '', // 空间域名\r\n        status: 'enabled' // 状态：enabled/disabled\r\n      },\r\n      ossInitialized: false, // OSS是否已初始化\r\n\r\n      // 文件夹管理\r\n      currentFolder: '总', // 当前选中的文件夹\r\n      folderDialogVisible: false, // 文件夹管理对话框\r\n      newFolderName: '', // 新文件夹名称\r\n      renameFolderName: '', // 重命名文件夹名称\r\n      selectedFolder: '', // 选中的文件夹\r\n\r\n      // 右键菜单\r\n      contextMenuVisible: false,\r\n      contextMenuX: 0,\r\n      contextMenuY: 0,\r\n      contextMenuFolder: null,\r\n\r\n      // 新建文件夹\r\n      createFolderVisible: false,\r\n      createFolderForm: {\r\n        name: ''\r\n      },\r\n\r\n      // 文件预览\r\n      previewVisible: false,\r\n      currentPreviewFile: null,\r\n\r\n      // 控制按钮隐藏定时器\r\n      controlTimers: {},\r\n\r\n\r\n\r\n      // BGM文件数据 (abgm/admin/总/ 文件夹) - 添加测试数据\r\n      bgmList: [\r\n        {\r\n          id: 'bgm1',\r\n          name: '测试音频.mp3',\r\n          type: 'audio',\r\n          size: 512000,\r\n          uploadTime: '2024-01-01 12:00:00',\r\n          duration: '02:30',\r\n          url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'\r\n        }\r\n      ],\r\n\r\n      // 素材文件数据 (asucai/admin/总/ 文件夹) - 添加测试数据\r\n      sucaiList: [\r\n        {\r\n          id: 'sucai1',\r\n          name: '测试视频.mp4',\r\n          type: 'video',\r\n          size: 1024000,\r\n          uploadTime: '2024-01-01 12:00:00',\r\n          duration: '00:30',\r\n          url: 'https://www.w3schools.com/html/mov_bbb.mp4'\r\n        },\r\n        {\r\n          id: 'sucai2',\r\n          name: '测试图片.jpg',\r\n          type: 'image',\r\n          size: 256000,\r\n          uploadTime: '2024-01-01 12:00:00',\r\n          url: 'https://picsum.photos/400/300'\r\n        },\r\n        {\r\n          id: 'sucai3',\r\n          name: '测试音频.mp3',\r\n          type: 'audio',\r\n          size: 512000,\r\n          uploadTime: '2024-01-01 12:00:00',\r\n          duration: '01:45',\r\n          url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    // 根据当前标签页返回对应的文件列表\r\n    currentMaterialList() {\r\n      switch (this.materialTab) {\r\n        case 'bgm':\r\n          return this.bgmList\r\n        case 'sucai':\r\n          return this.sucaiList\r\n        default:\r\n          return this.sucaiList\r\n      }\r\n    },\r\n\r\n    // 根据当前标签页返回对应的文件夹列表\r\n    currentFolderTree() {\r\n      switch (this.materialTab) {\r\n        case 'bgm':\r\n          return this.bgmFolderTree\r\n        case 'sucai':\r\n          return this.sucaiFolderTree\r\n        default:\r\n          return this.sucaiFolderTree\r\n      }\r\n    },\r\n\r\n    filteredMaterialList() {\r\n      let list = this.currentMaterialList\r\n\r\n      // 按文件夹过滤（简化版本）\r\n      if (this.selectedFolder !== 1) {\r\n        // 这里可以根据实际需求添加过滤逻辑\r\n        // 目前显示所有文件\r\n      }\r\n\r\n      return list\r\n    },\r\n\r\n    paginatedMaterials() {\r\n      const start = (this.currentPage - 1) * this.pageSize\r\n      const end = start + this.pageSize\r\n      return this.filteredMaterialList.slice(start, end)\r\n    },\r\n\r\n    totalPages() {\r\n      return Math.ceil(this.filteredMaterialList.length / this.pageSize)\r\n    },\r\n\r\n    // 上传对话框相关计算属性\r\n    uploadDialogTitle() {\r\n      switch (this.materialTab) {\r\n        case 'bgm':\r\n          return '上传BGM文件'\r\n        case 'sucai':\r\n          return '上传素材文件'\r\n        default:\r\n          return '上传文件'\r\n      }\r\n    },\r\n\r\n    uploadTipText() {\r\n      switch (this.materialTab) {\r\n        case 'bgm':\r\n          return '支持 MP3、WAV、FLAC、AAC、M4A、OGG、WMA 等音频格式，单个文件不超过100MB'\r\n        case 'sucai':\r\n          return '支持各种视频、音频、图片格式，单个文件不超过500MB'\r\n        default:\r\n          return '请选择要上传的文件'\r\n      }\r\n    },\r\n\r\n\r\n  },\r\n  async created() {\r\n    // 先加载OSS配置\r\n    this.loadOSSConfig()\r\n\r\n    // 如果OSS已配置，则加载文件列表\r\n    if (this.ossInitialized) {\r\n      await this.loadMaterialList()\r\n    }\r\n  },\r\n  methods: {\r\n    // BGM免费下载\r\n    openBGMDownloadSite() {\r\n      window.open('https://www.buguyy.top/', '_blank')\r\n      this.$message.success('正在打开免费BGM下载网站...')\r\n    },\r\n\r\n    // 基础方法\r\n    async loadMaterialList() {\r\n      console.log('加载素材列表')\r\n\r\n      // 如果OSS已初始化，从OSS加载文件列表\r\n      if (this.ossInitialized) {\r\n        await this.loadFilesFromOSS()\r\n      } else {\r\n        console.log('OSS未初始化，跳过文件加载')\r\n      }\r\n\r\n      // 更新文件夹计数\r\n      this.updateCurrentTabFolderCounts()\r\n    },\r\n\r\n    // 从OSS加载文件列表\r\n    async loadFilesFromOSS() {\r\n      try {\r\n        const client = getOSSClient()\r\n        const user = this.getCurrentUser()\r\n        const currentFolder = this.getCurrentFolder()\r\n\r\n        // 清空现有列表\r\n        this.bgmList = []\r\n        this.sucaiList = []\r\n\r\n        // 加载BGM文件\r\n        await this.loadFilesFromFolder('abgm', user, currentFolder, 'bgm')\r\n\r\n        // 加载素材文件\r\n        await this.loadFilesFromFolder('asucai', user, currentFolder, 'sucai')\r\n\r\n        console.log(`从OSS加载文件完成 - BGM: ${this.bgmList.length}个, 素材: ${this.sucaiList.length}个`)\r\n\r\n      } catch (error) {\r\n        console.error('从OSS加载文件列表失败:', error)\r\n      }\r\n    },\r\n\r\n    // 从指定文件夹加载文件\r\n    async loadFilesFromFolder(baseFolder, user, folder, listType) {\r\n      try {\r\n        const client = getOSSClient()\r\n        const prefix = `${baseFolder}/${user}/${folder}/`\r\n\r\n        const result = await client.list({\r\n          prefix: prefix,\r\n          'max-keys': 1000\r\n        })\r\n\r\n        if (result.objects) {\r\n          const files = result.objects\r\n            .filter(obj => !obj.name.endsWith('.keep') && !obj.name.endsWith('/'))\r\n            .map((obj, index) => {\r\n              const fileName = obj.name.split('/').pop()\r\n              const fileExtension = fileName.toLowerCase().split('.').pop()\r\n\r\n              // 判断文件类型\r\n              const audioExts = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']\r\n              const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v']\r\n\r\n              let fileType = 'unknown'\r\n              if (audioExts.includes(fileExtension)) {\r\n                fileType = 'audio'\r\n              } else if (videoExts.includes(fileExtension)) {\r\n                fileType = 'video'\r\n              }\r\n\r\n              return {\r\n                id: Date.now() + Math.random() + index,\r\n                name: fileName,\r\n                type: fileType,\r\n                size: obj.size,\r\n                uploadTime: new Date(obj.lastModified).toLocaleString().slice(0, 16),\r\n                duration: fileType === 'video' ? '00:02:30' : '00:03:45', // 默认值，实际应该从文件元数据获取\r\n                resolution: fileType === 'video' ? '1920x1080' : undefined,\r\n                bitrate: fileType === 'audio' ? '128kbps' : undefined,\r\n                url: `https://${this.ossConfig.bucket}.${this.ossConfig.endpoint.replace('https://', '').replace(this.ossConfig.bucket + '.', '')}/${obj.name}`,\r\n                ossFileName: obj.name,\r\n                folder: `${baseFolder}/${user}/${folder}`\r\n              }\r\n            })\r\n\r\n          // 添加到对应的列表\r\n          if (listType === 'bgm') {\r\n            this.bgmList.push(...files)\r\n          } else if (listType === 'sucai') {\r\n            this.sucaiList.push(...files)\r\n          }\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error(`从文件夹 ${baseFolder}/${user}/${folder} 加载文件失败:`, error)\r\n      }\r\n    },\r\n\r\n    // OSS配置相关方法\r\n    loadOSSConfig() {\r\n      // 从localStorage加载OSS配置\r\n      const savedConfig = localStorage.getItem('ossConfig')\r\n      if (savedConfig) {\r\n        try {\r\n          this.ossConfig = { ...this.ossConfig, ...JSON.parse(savedConfig) }\r\n          if (this.ossConfig.accessKeyId && this.ossConfig.accessKeySecret) {\r\n            this.initializeOSS()\r\n          }\r\n        } catch (error) {\r\n          console.error('加载OSS配置失败:', error)\r\n        }\r\n      }\r\n    },\r\n\r\n    async saveOSSConfig() {\r\n      // 验证必填字段\r\n      if (!this.ossConfig.bucket) {\r\n        this.$message.error('请输入存储空间名称')\r\n        return\r\n      }\r\n      if (!this.ossConfig.accessKeyId) {\r\n        this.$message.error('请输入ACCESS_KEY')\r\n        return\r\n      }\r\n      if (!this.ossConfig.accessKeySecret) {\r\n        this.$message.error('请输入SECRET_KEY')\r\n        return\r\n      }\r\n      if (!this.ossConfig.endpoint) {\r\n        this.$message.error('请输入空间域名')\r\n        return\r\n      }\r\n\r\n      // 验证域名格式\r\n      if (!this.ossConfig.endpoint.startsWith('http://') && !this.ossConfig.endpoint.startsWith('https://')) {\r\n        this.$message.error('空间域名必须以http://或https://开头')\r\n        return\r\n      }\r\n\r\n      // 验证是否为标准的OSS域名格式\r\n      try {\r\n        const url = new URL(this.ossConfig.endpoint)\r\n        const hostname = url.hostname\r\n        const match = hostname.match(/^([^.]+)\\.oss-([^.]+)\\.aliyuncs\\.com$/)\r\n\r\n        if (!match) {\r\n          this.$message.error('请输入标准的OSS域名格式，如：https://bucket.oss-region.aliyuncs.com')\r\n          return\r\n        }\r\n\r\n        const [, bucket, region] = match\r\n        if (bucket !== this.ossConfig.bucket) {\r\n          this.$message.error(`域名中的存储桶名称(${bucket})与配置的存储桶名称(${this.ossConfig.bucket})不匹配`)\r\n          return\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('域名格式不正确')\r\n        return\r\n      }\r\n\r\n      this.testingOSS = true\r\n\r\n      try {\r\n        // 直接使用配置初始化OSS客户端\r\n        await this.initializeOSS()\r\n\r\n        // 保存配置到localStorage\r\n        localStorage.setItem('ossConfig', JSON.stringify(this.ossConfig))\r\n\r\n        this.ossConfigVisible = false\r\n        this.$message.success('OSS配置保存成功！')\r\n\r\n      } catch (error) {\r\n        console.error('OSS配置测试失败:', error)\r\n        this.$message.error(`OSS配置失败: ${error.message}`)\r\n      } finally {\r\n        this.testingOSS = false\r\n      }\r\n    },\r\n\r\n    async initializeOSS() {\r\n      try {\r\n        const client = initOSSClient(this.ossConfig)\r\n        this.ossInitialized = true\r\n\r\n        // OSS初始化成功后，获取用户的文件夹列表\r\n        await this.loadUserFoldersFromOSS()\r\n\r\n        console.log('OSS客户端初始化成功')\r\n        return client\r\n      } catch (error) {\r\n        this.ossInitialized = false\r\n        console.error('OSS客户端初始化失败:', error)\r\n        throw error\r\n      }\r\n    },\r\n\r\n    openOSSConfig() {\r\n      this.ossConfigVisible = true\r\n    },\r\n\r\n    // 显示新建文件夹对话框\r\n    showCreateFolderDialog() {\r\n      this.createFolderForm.name = ''\r\n      this.createFolderVisible = true\r\n    },\r\n\r\n    // 创建文件夹\r\n    async createFolder() {\r\n      console.log('🚀 开始创建文件夹')\r\n\r\n      if (!this.createFolderForm.name || this.createFolderForm.name.trim() === '') {\r\n        this.$message.error('文件夹名称不能为空')\r\n        return\r\n      }\r\n\r\n      const folderName = this.createFolderForm.name.trim()\r\n      console.log('📁 文件夹名称:', folderName)\r\n      console.log('📋 当前标签页:', this.materialTab)\r\n\r\n      // 检查当前标签页的文件夹是否已存在\r\n      const currentFolders = this.currentFolderTree\r\n      console.log('📂 当前文件夹列表:', currentFolders.map(f => f.name))\r\n\r\n      if (currentFolders.some(folder => folder.name === folderName)) {\r\n        this.$message.error(`${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹已存在`)\r\n        return\r\n      }\r\n\r\n      try {\r\n        console.log('✅ 开始创建本地文件夹记录')\r\n\r\n        // 先创建本地文件夹记录\r\n        const newFolder = {\r\n          id: Date.now() + Math.random(), // 使用时间戳避免ID冲突\r\n          name: folderName,\r\n          count: 0\r\n        }\r\n\r\n        if (this.materialTab === 'bgm') {\r\n          this.bgmFolderTree.push(newFolder)\r\n          console.log('📁 已添加到BGM文件夹树')\r\n        } else {\r\n          this.sucaiFolderTree.push(newFolder)\r\n          console.log('📁 已添加到素材文件夹树')\r\n        }\r\n\r\n        console.log('✅ 本地文件夹创建成功')\r\n        this.createFolderVisible = false\r\n        this.$message.success(`${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹创建成功`)\r\n\r\n        // 异步处理OSS创建，不阻塞UI\r\n        if (this.storageType === 'oss' && this.ossInitialized) {\r\n          this.createOSSFolderAsync(folderName)\r\n        }\r\n\r\n      } catch (error) {\r\n        console.error('❌ 创建文件夹失败:', error)\r\n        this.$message.error(`创建失败: ${error.message}`)\r\n      }\r\n    },\r\n\r\n    // 异步创建OSS文件夹，不阻塞UI\r\n    async createOSSFolderAsync(folderName) {\r\n      try {\r\n        console.log('🌐 开始异步创建OSS文件夹:', folderName)\r\n        await this.createOSSFolderForCurrentTab(folderName)\r\n        console.log('✅ OSS文件夹创建成功')\r\n      } catch (ossError) {\r\n        console.warn('⚠️ OSS文件夹创建失败:', ossError.message)\r\n      }\r\n    },\r\n\r\n    // OSS文件夹创建（旧方法，为两种类型都创建）\r\n    async createOSSFolder(folderName) {\r\n      try {\r\n        const client = getOSSClient()\r\n        const user = this.getCurrentUser()\r\n\r\n        // 为两种类型创建空文件夹（通过上传空文件实现）\r\n        const baseFolders = ['abgm', 'asucai']\r\n\r\n        for (const baseFolder of baseFolders) {\r\n          const folderPath = `${baseFolder}/${user}/${folderName}/.keep`\r\n\r\n          // 上传一个空文件来创建文件夹结构\r\n          await client.put(folderPath, Buffer.from(''))\r\n          console.log(`OSS文件夹创建: ${folderPath}`)\r\n        }\r\n\r\n        return true\r\n      } catch (error) {\r\n        console.error('OSS文件夹创建失败:', error)\r\n        throw error\r\n      }\r\n    },\r\n\r\n    // 只为当前标签页创建OSS文件夹\r\n    async createOSSFolderForCurrentTab(folderName) {\r\n      try {\r\n        console.log(`开始创建OSS文件夹: ${folderName} (${this.materialTab})`)\r\n\r\n        if (!folderName || !folderName.trim()) {\r\n          throw new Error('文件夹名称不能为空')\r\n        }\r\n\r\n        const client = getOSSClient()\r\n        if (!client) {\r\n          throw new Error('OSS客户端未初始化')\r\n        }\r\n\r\n        const user = this.getCurrentUser()\r\n        if (!user) {\r\n          throw new Error('无法获取当前用户信息')\r\n        }\r\n\r\n        // 根据当前标签页确定基础文件夹\r\n        const baseFolder = this.materialTab === 'bgm' ? 'abgm' : 'asucai'\r\n        const folderPath = `${baseFolder}/${user}/${folderName}/.keep`\r\n\r\n        console.log(`OSS文件夹路径: ${folderPath}`)\r\n\r\n        // 上传一个空文件来创建文件夹结构\r\n        await client.put(folderPath, Buffer.from(''))\r\n        console.log(`OSS文件夹创建成功 (${this.materialTab}): ${folderPath}`)\r\n\r\n        return true\r\n      } catch (error) {\r\n        console.error(`OSS文件夹创建失败 (${this.materialTab}):`, error)\r\n        throw new Error(`OSS文件夹创建失败: ${error.message}`)\r\n      }\r\n    },\r\n\r\n    // 处理文件双击事件\r\n    handleFileDoubleClick(file) {\r\n      // 只有视频、音频、图片类型才支持预览\r\n      if (['video', 'audio', 'image'].includes(file.type)) {\r\n        this.previewFile(file)\r\n      }\r\n    },\r\n\r\n    // 切换视频播放/暂停\r\n    toggleVideoPlay(file) {\r\n      const videoRef = this.$refs[`video-${file.id}`]\r\n      if (videoRef && videoRef.length > 0) {\r\n        const video = videoRef[0]\r\n\r\n        if (video.paused) {\r\n          // 暂停所有其他视频\r\n          this.pauseAllVideos()\r\n\r\n          // 取消静音并播放当前视频\r\n          video.muted = false\r\n          video.play()\r\n          this.$set(file, 'isPlaying', true)\r\n          this.$set(file, 'showControls', true)\r\n\r\n          // 开始3秒隐藏定时器\r\n          this.startControlTimer(file)\r\n        } else {\r\n          // 暂停当前视频并静音\r\n          video.pause()\r\n          video.muted = true\r\n          this.$set(file, 'isPlaying', false)\r\n          this.$set(file, 'showControls', true)\r\n\r\n          // 清除隐藏定时器\r\n          this.clearControlTimer(file.id)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 切换视频播放/暂停并缩放\r\n    toggleVideoPlayWithScale(file) {\r\n      const videoRef = this.$refs[`video-${file.id}`]\r\n      if (videoRef && videoRef.length > 0) {\r\n        const video = videoRef[0]\r\n\r\n        if (video.paused) {\r\n          // 暂停所有其他视频并取消缩放\r\n          this.pauseAllVideosAndResetScale()\r\n\r\n          // 等待视频元数据加载完成\r\n          const handleLoadedMetadata = () => {\r\n            // 获取视频的真实尺寸\r\n            const videoWidth = video.videoWidth\r\n            const videoHeight = video.videoHeight\r\n\r\n            if (videoWidth && videoHeight) {\r\n              // 计算合适的显示尺寸（最大宽度800px，最大高度600px）\r\n              const maxWidth = Math.min(800, window.innerWidth * 0.8)\r\n              const maxHeight = Math.min(600, window.innerHeight * 0.8)\r\n\r\n              const aspectRatio = videoWidth / videoHeight\r\n              let displayWidth, displayHeight\r\n\r\n              if (aspectRatio > maxWidth / maxHeight) {\r\n                // 视频比较宽，以宽度为准\r\n                displayWidth = maxWidth\r\n                displayHeight = maxWidth / aspectRatio\r\n              } else {\r\n                // 视频比较高，以高度为准\r\n                displayHeight = maxHeight\r\n                displayWidth = maxHeight * aspectRatio\r\n              }\r\n\r\n              // 设置视频的显示尺寸\r\n              this.$set(file, 'displayWidth', Math.round(displayWidth))\r\n              this.$set(file, 'displayHeight', Math.round(displayHeight))\r\n\r\n              console.log(`视频 ${file.name} 真实尺寸: ${videoWidth}x${videoHeight}, 显示尺寸: ${displayWidth}x${displayHeight}`)\r\n            }\r\n\r\n            // 移除事件监听器\r\n            video.removeEventListener('loadedmetadata', handleLoadedMetadata)\r\n          }\r\n\r\n          // 如果元数据已经加载，直接处理；否则等待加载\r\n          if (video.readyState >= 1) {\r\n            handleLoadedMetadata()\r\n          } else {\r\n            video.addEventListener('loadedmetadata', handleLoadedMetadata)\r\n          }\r\n\r\n          // 播放当前视频并放大\r\n          video.muted = false\r\n          video.play()\r\n          this.$set(file, 'isPlaying', true)\r\n          this.$set(file, 'isScaled', true) // 设置放大状态\r\n          this.$set(file, 'showControls', true)\r\n\r\n          console.log(`视频 ${file.name} 开始播放并放大，isScaled: ${file.isScaled}`)\r\n\r\n          // 添加背景遮罩\r\n          this.addBackdrop()\r\n\r\n          // 强制更新DOM以显示进度条\r\n          this.$nextTick(() => {\r\n            console.log('DOM更新完成，进度条应该显示')\r\n          })\r\n\r\n          // 开始3秒隐藏定时器\r\n          this.startControlTimer(file)\r\n        } else {\r\n          // 暂停视频并恢复大小\r\n          video.pause()\r\n          video.muted = true\r\n          this.$set(file, 'isPlaying', false)\r\n          this.$set(file, 'isScaled', false) // 取消放大状态\r\n          this.$set(file, 'displayWidth', null) // 清除显示宽度\r\n          this.$set(file, 'displayHeight', null) // 清除显示高度\r\n          this.$set(file, 'showControls', true)\r\n\r\n          console.log(`视频 ${file.name} 已暂停并恢复大小`)\r\n\r\n          // 移除背景遮罩\r\n          this.removeBackdrop()\r\n\r\n          // 清除隐藏定时器\r\n          this.clearControlTimer(file.id)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 暂停所有视频\r\n    pauseAllVideos() {\r\n      // 遍历所有文件，暂停正在播放的视频\r\n      this.paginatedMaterials.forEach(file => {\r\n        if (file.type === 'video' && file.isPlaying) {\r\n          const videoRef = this.$refs[`video-${file.id}`]\r\n          if (videoRef && videoRef.length > 0) {\r\n            const video = videoRef[0]\r\n            video.pause()\r\n            video.muted = true\r\n            this.$set(file, 'isPlaying', false)\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 暂停所有视频并重置缩放\r\n    pauseAllVideosAndResetScale() {\r\n      // 遍历所有文件，暂停正在播放的视频并重置缩放\r\n      this.paginatedMaterials.forEach(file => {\r\n        if (file.type === 'video') {\r\n          if (file.isPlaying) {\r\n            const videoRef = this.$refs[`video-${file.id}`]\r\n            if (videoRef && videoRef.length > 0) {\r\n              const video = videoRef[0]\r\n              video.pause()\r\n              video.muted = true\r\n              this.$set(file, 'isPlaying', false)\r\n            }\r\n          }\r\n          // 重置缩放状态和显示尺寸\r\n          this.$set(file, 'isScaled', false)\r\n          this.$set(file, 'displayWidth', null)\r\n          this.$set(file, 'displayHeight', null)\r\n        }\r\n      })\r\n\r\n      // 移除背景遮罩\r\n      this.removeBackdrop()\r\n    },\r\n\r\n    // 添加背景遮罩\r\n    addBackdrop() {\r\n      // 移除已存在的遮罩\r\n      this.removeBackdrop()\r\n\r\n      const backdrop = document.createElement('div')\r\n      backdrop.className = 'scale-enlarged-backdrop'\r\n      backdrop.id = 'video-scale-backdrop'\r\n      document.body.appendChild(backdrop)\r\n\r\n      // 点击遮罩关闭放大\r\n      backdrop.addEventListener('click', () => {\r\n        this.pauseAllVideosAndResetScale()\r\n      })\r\n    },\r\n\r\n    // 移除背景遮罩\r\n    removeBackdrop() {\r\n      const backdrop = document.getElementById('video-scale-backdrop')\r\n      if (backdrop) {\r\n        backdrop.remove()\r\n      }\r\n    },\r\n\r\n    // 获取视频播放进度百分比\r\n    getVideoProgress(file) {\r\n      const videoRef = this.$refs[`video-${file.id}`]\r\n      if (videoRef && videoRef.length > 0) {\r\n        const video = videoRef[0]\r\n        if (video.duration && video.currentTime) {\r\n          return (video.currentTime / video.duration) * 100\r\n        }\r\n      }\r\n      return 0\r\n    },\r\n\r\n    // 获取当前播放时间\r\n    getCurrentTime(file) {\r\n      const videoRef = this.$refs[`video-${file.id}`]\r\n      if (videoRef && videoRef.length > 0) {\r\n        const video = videoRef[0]\r\n        return video.currentTime || 0\r\n      }\r\n      return 0\r\n    },\r\n\r\n    // 获取视频总时长\r\n    getDuration(file) {\r\n      const videoRef = this.$refs[`video-${file.id}`]\r\n      if (videoRef && videoRef.length > 0) {\r\n        const video = videoRef[0]\r\n        return video.duration || 0\r\n      }\r\n      return 0\r\n    },\r\n\r\n    // 格式化时间显示\r\n    formatTime(seconds) {\r\n      if (!seconds || isNaN(seconds)) return '0:00'\r\n\r\n      const minutes = Math.floor(seconds / 60)\r\n      const remainingSeconds = Math.floor(seconds % 60)\r\n      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`\r\n    },\r\n\r\n    // 点击进度条跳转\r\n    seekVideo(event, file) {\r\n      const videoRef = this.$refs[`video-${file.id}`]\r\n      if (videoRef && videoRef.length > 0) {\r\n        const video = videoRef[0]\r\n        const progressBar = event.currentTarget.querySelector('.progress-track')\r\n        const rect = progressBar.getBoundingClientRect()\r\n        const clickX = event.clientX - rect.left\r\n        const percentage = clickX / rect.width\r\n        const newTime = percentage * video.duration\r\n\r\n        if (newTime >= 0 && newTime <= video.duration) {\r\n          video.currentTime = newTime\r\n        }\r\n      }\r\n    },\r\n\r\n    // 视频时间更新事件\r\n    onVideoTimeUpdate(file) {\r\n      // 强制更新进度条显示\r\n      this.$forceUpdate()\r\n    },\r\n\r\n    // 视频数据加载完成事件\r\n    onVideoLoadedData(file) {\r\n      // 视频数据加载完成，可以获取时长等信息\r\n      console.log(`视频 ${file.name} 数据加载完成`)\r\n    },\r\n\r\n    // 切换音频播放/暂停\r\n    toggleAudioPlay(file) {\r\n      const audioRef = this.$refs[`audio-${file.id}`]\r\n      if (audioRef && audioRef.length > 0) {\r\n        const audio = audioRef[0]\r\n\r\n        if (audio.paused) {\r\n          // 暂停所有其他音频和视频\r\n          this.pauseAllMedia()\r\n\r\n          // 播放当前音频\r\n          audio.play()\r\n          this.$set(file, 'isPlaying', true)\r\n          this.$set(file, 'showControls', true)\r\n\r\n          // 开始3秒隐藏定时器\r\n          this.startControlTimer(file)\r\n        } else {\r\n          // 暂停当前音频\r\n          audio.pause()\r\n          this.$set(file, 'isPlaying', false)\r\n          this.$set(file, 'showControls', true)\r\n\r\n          // 清除隐藏定时器\r\n          this.clearControlTimer(file.id)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 暂停所有媒体（音频和视频）\r\n    pauseAllMedia() {\r\n      this.pauseAllVideos()\r\n      this.pauseAllAudios()\r\n    },\r\n\r\n    // 暂停所有音频\r\n    pauseAllAudios() {\r\n      this.paginatedMaterials.forEach(file => {\r\n        if (file.type === 'audio' && file.isPlaying) {\r\n          const audioRef = this.$refs[`audio-${file.id}`]\r\n          if (audioRef && audioRef.length > 0) {\r\n            audioRef[0].pause()\r\n            this.$set(file, 'isPlaying', false)\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 预览文件\r\n    previewFile(file) {\r\n      // 构建文件URL\r\n      if (!file.url && this.ossInitialized) {\r\n        // 如果没有URL，构建OSS URL\r\n        const endpoint = this.ossConfig.endpoint.replace('https://', '').replace('http://', '')\r\n        file.url = `https://${endpoint}/${file.ossFileName}`\r\n      }\r\n\r\n      this.currentPreviewFile = file\r\n      this.previewVisible = true\r\n    },\r\n\r\n    // 获取文件URL\r\n    getFileUrl(file) {\r\n      if (file.url) {\r\n        return file.url\r\n      }\r\n\r\n      if (this.ossInitialized && file.ossFileName) {\r\n        const endpoint = this.ossConfig.endpoint.replace('https://', '').replace('http://', '')\r\n        return `https://${endpoint}/${file.ossFileName}`\r\n      }\r\n\r\n      return ''\r\n    },\r\n\r\n    // 停止所有媒体播放\r\n    stopAllMedia() {\r\n      // 停止预览对话框中的视频播放\r\n      if (this.$refs.previewVideo) {\r\n        this.$refs.previewVideo.pause()\r\n        this.$refs.previewVideo.currentTime = 0\r\n      }\r\n\r\n      // 停止预览对话框中的音频播放\r\n      if (this.$refs.previewAudio) {\r\n        this.$refs.previewAudio.pause()\r\n        this.$refs.previewAudio.currentTime = 0\r\n      }\r\n\r\n      // 停止缩略图中的所有媒体播放\r\n      this.pauseAllMedia()\r\n\r\n      // 清空预览文件\r\n      this.currentPreviewFile = null\r\n    },\r\n\r\n    // 视频加载完成\r\n    onVideoLoaded(event) {\r\n      // 可以在这里获取视频的第一帧作为缩略图\r\n      const video = event.target\r\n      video.currentTime = 1 // 跳到第1秒获取缩略图\r\n    },\r\n\r\n    // 视频播放结束\r\n    onVideoEnded(file) {\r\n      this.$set(file, 'isPlaying', false)\r\n      this.$set(file, 'isScaled', false)\r\n      this.$set(file, 'displayWidth', null)\r\n      this.$set(file, 'displayHeight', null)\r\n      this.removeBackdrop()\r\n    },\r\n\r\n    // 视频暂停\r\n    onVideoPaused(file) {\r\n      this.$set(file, 'isPlaying', false)\r\n      // 如果是放大状态，也要清理\r\n      if (file.isScaled) {\r\n        this.$set(file, 'isScaled', false)\r\n        this.$set(file, 'displayWidth', null)\r\n        this.$set(file, 'displayHeight', null)\r\n        this.removeBackdrop()\r\n      }\r\n\r\n      // 同步Portal视频\r\n      if (this.hoveredFile && this.hoveredFile.id === file.id) {\r\n        this.$nextTick(() => {\r\n          this.syncPortalVideo()\r\n        })\r\n      }\r\n    },\r\n\r\n    // 视频开始播放\r\n    onVideoPlayed(file) {\r\n      this.$set(file, 'isPlaying', true)\r\n\r\n      // 同步Portal视频\r\n      if (this.hoveredFile && this.hoveredFile.id === file.id) {\r\n        this.$nextTick(() => {\r\n          this.syncPortalVideo()\r\n        })\r\n      }\r\n    },\r\n\r\n    // 音频播放结束\r\n    onAudioEnded(file) {\r\n      this.$set(file, 'isPlaying', false)\r\n    },\r\n\r\n    // 音频暂停\r\n    onAudioPaused(file) {\r\n      this.$set(file, 'isPlaying', false)\r\n    },\r\n\r\n    // 音频开始播放\r\n    onAudioPlayed(file) {\r\n      this.$set(file, 'isPlaying', true)\r\n    },\r\n\r\n    // 图片加载错误\r\n    onImageError(event) {\r\n      // 图片加载失败时显示默认图标\r\n      event.target.style.display = 'none'\r\n      event.target.parentNode.innerHTML = '<i class=\"el-icon-picture-outline\"></i>'\r\n    },\r\n\r\n    // 视频鼠标进入事件\r\n    onVideoMouseEnter(file) {\r\n      console.log('视频鼠标进入:', file.name, '显示控制按钮')\r\n      this.$set(file, 'showControls', true)\r\n      this.clearControlTimer(file.id)\r\n    },\r\n\r\n    // 视频鼠标离开事件\r\n    onVideoMouseLeave(file) {\r\n      if (file.isPlaying) {\r\n        this.startControlTimer(file)\r\n      } else {\r\n        this.$set(file, 'showControls', false)\r\n      }\r\n    },\r\n\r\n    // 视频鼠标移动事件\r\n    onVideoMouseMove(file) {\r\n      this.$set(file, 'showControls', true)\r\n      if (file.isPlaying) {\r\n        this.clearControlTimer(file.id)\r\n        this.startControlTimer(file)\r\n      }\r\n    },\r\n\r\n    // 音频鼠标进入事件\r\n    onAudioMouseEnter(file) {\r\n      console.log('音频鼠标进入:', file.name)\r\n      this.$set(file, 'showControls', true)\r\n      this.clearControlTimer(file.id)\r\n    },\r\n\r\n    // 音频鼠标离开事件\r\n    onAudioMouseLeave(file) {\r\n      if (file.isPlaying) {\r\n        this.startControlTimer(file)\r\n      } else {\r\n        this.$set(file, 'showControls', false)\r\n      }\r\n    },\r\n\r\n    // 音频鼠标移动事件\r\n    onAudioMouseMove(file) {\r\n      this.$set(file, 'showControls', true)\r\n      if (file.isPlaying) {\r\n        this.clearControlTimer(file.id)\r\n        this.startControlTimer(file)\r\n      }\r\n    },\r\n\r\n    // 图片鼠标进入事件\r\n    onImageMouseEnter(file) {\r\n      console.log('图片鼠标进入:', file.name)\r\n    },\r\n\r\n    // 图片鼠标离开事件\r\n    onImageMouseLeave(file) {\r\n      console.log('图片鼠标离开:', file.name)\r\n    },\r\n\r\n    // 开始控制按钮隐藏定时器\r\n    startControlTimer(file) {\r\n      this.clearControlTimer(file.id)\r\n      this.controlTimers[file.id] = setTimeout(() => {\r\n        this.$set(file, 'showControls', false)\r\n      }, 3000) // 3秒后隐藏\r\n    },\r\n\r\n    // 清除控制按钮隐藏定时器\r\n    clearControlTimer(fileId) {\r\n      if (this.controlTimers[fileId]) {\r\n        clearTimeout(this.controlTimers[fileId])\r\n        delete this.controlTimers[fileId]\r\n      }\r\n    },\r\n\r\n\r\n\r\n\r\n\r\n    // 格式化文件大小\r\n    formatFileSize(bytes) {\r\n      if (bytes === 0) return '0 B'\r\n      const k = 1024\r\n      const sizes = ['B', 'KB', 'MB', 'GB']\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\r\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(file) {\r\n      if (file.url) {\r\n        const link = document.createElement('a')\r\n        link.href = file.url\r\n        link.download = file.name\r\n        link.target = '_blank'\r\n        document.body.appendChild(link)\r\n        link.click()\r\n        document.body.removeChild(link)\r\n      } else {\r\n        this.$message.error('文件链接不可用')\r\n      }\r\n    },\r\n\r\n    // 从OSS获取用户文件夹列表（分别为BGM和素材）\r\n    async loadUserFoldersFromOSS() {\r\n      try {\r\n        const client = getOSSClient()\r\n        const user = this.getCurrentUser()\r\n\r\n        // 分别获取BGM和素材的文件夹\r\n        await this.loadFoldersForType('abgm', 'bgmFolderTree')\r\n        await this.loadFoldersForType('asucai', 'sucaiFolderTree')\r\n\r\n        console.log('BGM文件夹:', this.bgmFolderTree.map(f => f.name))\r\n        console.log('素材文件夹:', this.sucaiFolderTree.map(f => f.name))\r\n\r\n      } catch (error) {\r\n        console.error('获取OSS文件夹列表失败:', error)\r\n        // 如果获取失败，至少显示默认的\"总\"文件夹\r\n        this.bgmFolderTree = [{ id: 1, name: '总', count: 0 }]\r\n        this.sucaiFolderTree = [{ id: 1, name: '总', count: 0 }]\r\n      }\r\n    },\r\n\r\n    // 为指定类型加载文件夹\r\n    async loadFoldersForType(baseFolder, treeProperty) {\r\n      try {\r\n        const client = getOSSClient()\r\n        const user = this.getCurrentUser()\r\n        const folderSet = new Set()\r\n        const prefix = `${baseFolder}/${user}/`\r\n\r\n        // 列出该前缀下的所有对象\r\n        const result = await client.list({\r\n          prefix: prefix,\r\n          delimiter: '/',\r\n          'max-keys': 1000\r\n        })\r\n\r\n        // 从commonPrefixes中提取文件夹名称\r\n        if (result.prefixes) {\r\n          result.prefixes.forEach(prefixInfo => {\r\n            const fullPrefix = prefixInfo.name || prefixInfo\r\n            // 提取文件夹名称：abgm/admin/总/ -> 总\r\n            if (fullPrefix && typeof fullPrefix === 'string') {\r\n              const folderName = fullPrefix.replace(prefix, '').replace('/', '')\r\n              if (folderName) {\r\n                folderSet.add(folderName)\r\n              }\r\n            }\r\n          })\r\n        }\r\n\r\n        // 如果有objects，也从中提取文件夹名称\r\n        if (result.objects) {\r\n          result.objects.forEach(obj => {\r\n            const objectKey = obj.name\r\n            if (objectKey.startsWith(prefix)) {\r\n              const relativePath = objectKey.replace(prefix, '')\r\n              const folderName = relativePath.split('/')[0]\r\n              if (folderName && folderName !== '.keep') {\r\n                folderSet.add(folderName)\r\n              }\r\n            }\r\n          })\r\n        }\r\n\r\n        // 转换为文件夹树格式\r\n        const folders = Array.from(folderSet).map((folderName, index) => ({\r\n          id: Date.now() + index, // 使用时间戳避免ID冲突\r\n          name: folderName,\r\n          count: 0 // 稍后可以计算实际文件数量\r\n        }))\r\n\r\n        // 确保\"总\"文件夹存在\r\n        if (!folderSet.has('总')) {\r\n          folders.unshift({ id: Date.now(), name: '总', count: 0 })\r\n        }\r\n\r\n        // 更新对应的文件夹树\r\n        if (baseFolder === 'abgm') {\r\n          this.bgmFolderTree = folders\r\n        } else {\r\n          this.sucaiFolderTree = folders\r\n        }\r\n\r\n        console.log(`${baseFolder} 文件夹加载完成:`, folders.map(f => f.name))\r\n\r\n      } catch (error) {\r\n        console.error(`获取 ${baseFolder} 文件夹列表失败:`, error)\r\n        // 如果获取失败，至少显示默认的\"总\"文件夹\r\n        const defaultFolder = [{ id: Date.now(), name: '总', count: 0 }]\r\n        if (baseFolder === 'abgm') {\r\n          this.bgmFolderTree = defaultFolder\r\n        } else {\r\n          this.sucaiFolderTree = defaultFolder\r\n        }\r\n      }\r\n    },\r\n\r\n    // 更新文件夹的文件数量\r\n    async updateFolderFileCounts() {\r\n      try {\r\n        const client = getOSSClient()\r\n        const user = this.getCurrentUser()\r\n\r\n        // 由于现在使用分离的文件夹树，这个方法暂时禁用\r\n        // 可以根据需要为每个标签页单独更新文件数量\r\n        console.log('文件夹数量更新已改为分离模式，请使用具体的标签页更新方法')\r\n        return\r\n\r\n        for (let folder of []) { // 临时禁用\r\n          let totalCount = 0\r\n\r\n          // 检查两种类型的文件夹中的文件数量\r\n          const baseFolders = ['abgm', 'asucai']\r\n\r\n          for (const baseFolder of baseFolders) {\r\n            const prefix = `${baseFolder}/${user}/${folder.name}/`\r\n\r\n            try {\r\n              const result = await client.list({\r\n                prefix: prefix,\r\n                'max-keys': 1000\r\n              })\r\n\r\n              if (result.objects) {\r\n                // 过滤掉.keep文件\r\n                const fileCount = result.objects.filter(obj =>\r\n                  !obj.name.endsWith('.keep') && !obj.name.endsWith('/')\r\n                ).length\r\n                totalCount += fileCount\r\n              }\r\n            } catch (error) {\r\n              console.warn(`获取文件夹 ${folder.name} 在 ${baseFolder} 中的文件数量失败:`, error)\r\n            }\r\n          }\r\n\r\n          folder.count = totalCount\r\n        }\r\n\r\n        console.log('文件夹文件数量更新完成:', this.simpleFolderTree)\r\n\r\n      } catch (error) {\r\n        console.error('更新文件夹文件数量失败:', error)\r\n      }\r\n    },\r\n\r\n    // 文件夹右键菜单相关方法\r\n    showFolderContextMenu(event, folder) {\r\n      this.contextMenuVisible = true\r\n      this.contextMenuX = event.clientX\r\n      this.contextMenuY = event.clientY\r\n      this.contextMenuFolder = folder\r\n\r\n      // 点击其他地方隐藏菜单\r\n      document.addEventListener('click', this.hideFolderContextMenu)\r\n    },\r\n\r\n    hideFolderContextMenu() {\r\n      this.contextMenuVisible = false\r\n      this.contextMenuFolder = null\r\n      document.removeEventListener('click', this.hideFolderContextMenu)\r\n    },\r\n\r\n    // 重命名文件夹\r\n    async renameFolderAction() {\r\n      if (!this.contextMenuFolder) {\r\n        this.$message.error('未选择文件夹')\r\n        return\r\n      }\r\n\r\n      const folderName = this.contextMenuFolder.name || this.contextMenuFolder\r\n\r\n      this.$prompt('请输入新的文件夹名称', '重命名文件夹', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        inputValue: folderName\r\n      }).then(async ({ value }) => {\r\n        try {\r\n          if (!value || value.trim() === '') {\r\n            this.$message.error('文件夹名称不能为空')\r\n            return\r\n          }\r\n\r\n          const newFolderName = value.trim()\r\n          const oldFolderName = this.contextMenuFolder.name || this.contextMenuFolder\r\n\r\n          if (newFolderName === oldFolderName) {\r\n            this.hideFolderContextMenu()\r\n            return\r\n          }\r\n\r\n          // 如果使用OSS存储，需要重命名OSS中的文件夹\r\n          if (this.storageType === 'oss' && this.ossInitialized) {\r\n            await this.renameOSSFolder(oldFolderName, newFolderName)\r\n          }\r\n\r\n          // 更新前端文件夹名称\r\n          if (typeof this.contextMenuFolder === 'object') {\r\n            this.contextMenuFolder.name = newFolderName\r\n          }\r\n\r\n          // 如果当前选中的是被重命名的文件夹，更新当前文件夹\r\n          if (this.currentFolder === oldFolderName) {\r\n            this.currentFolder = newFolderName\r\n          }\r\n\r\n          this.$message.success('文件夹重命名成功')\r\n          this.hideFolderContextMenu()\r\n\r\n        } catch (error) {\r\n          console.error('重命名文件夹失败:', error)\r\n          this.$message.error(`重命名失败: ${error.message}`)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 删除文件夹\r\n    async deleteFolderAction() {\r\n      console.log('删除文件夹操作开始，contextMenuFolder:', this.contextMenuFolder)\r\n\r\n      if (!this.contextMenuFolder) {\r\n        this.$message.error('未选择文件夹')\r\n        return\r\n      }\r\n\r\n      // 安全地获取文件夹名称\r\n      let folderName\r\n      if (typeof this.contextMenuFolder === 'string') {\r\n        folderName = this.contextMenuFolder\r\n      } else if (this.contextMenuFolder && this.contextMenuFolder.name) {\r\n        folderName = this.contextMenuFolder.name\r\n      } else {\r\n        console.error('无法获取文件夹名称，contextMenuFolder:', this.contextMenuFolder)\r\n        this.$message.error('无法获取文件夹名称')\r\n        return\r\n      }\r\n\r\n      console.log('准备删除文件夹:', folderName)\r\n\r\n      if (folderName === '总') {\r\n        this.$message.warning('不能删除\"总\"文件夹')\r\n        return\r\n      }\r\n\r\n      this.$confirm(`确定要删除文件夹\"${folderName}\"吗？文件夹内的所有文件也会被删除！`, '删除文件夹', {\r\n        confirmButtonText: '确定删除',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          // 使用之前获取的folderName，避免在异步操作中contextMenuFolder变为null\r\n          const targetFolderName = folderName\r\n\r\n          // 如果使用OSS存储，只删除当前标签页的OSS文件夹\r\n          if (this.storageType === 'oss' && this.ossInitialized) {\r\n            try {\r\n              await this.deleteOSSFolderForCurrentTab(targetFolderName)\r\n              console.log(`${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹删除成功`)\r\n            } catch (ossError) {\r\n              console.warn('OSS文件夹删除失败，但继续删除前端记录:', ossError.message)\r\n              // 即使OSS删除失败，也继续删除前端记录（可能是空文件夹）\r\n            }\r\n          }\r\n\r\n          // 从前端移除文件夹相关的文件\r\n          this.removeFilesFromFolder(targetFolderName)\r\n\r\n          // 如果当前选中的是被删除的文件夹，切换到\"总\"文件夹\r\n          if (this.currentFolder === targetFolderName) {\r\n            this.currentFolder = '总'\r\n          }\r\n\r\n          this.$message.success('文件夹删除成功')\r\n          this.hideFolderContextMenu()\r\n\r\n        } catch (error) {\r\n          console.error('删除文件夹失败:', error)\r\n          this.$message.error(`删除失败: ${error.message}`)\r\n        }\r\n      })\r\n    },\r\n\r\n    // 获取当前用户名\r\n    getCurrentUser() {\r\n      // 从store中获取用户信息\r\n      return this.$store.state.user.name || 'admin'\r\n    },\r\n\r\n    // 获取当前文件夹名称\r\n    getCurrentFolder() {\r\n      return this.currentFolder || '总'\r\n    },\r\n\r\n    // 构建OSS上传路径\r\n    buildOSSPath(baseFolder) {\r\n      const user = this.getCurrentUser()\r\n      const folder = this.getCurrentFolder()\r\n      return `${baseFolder}/${user}/${folder}`\r\n    },\r\n\r\n    // OSS文件复制和删除（用于重命名）\r\n    async copyAndDeleteOSSFile(oldPath, newPath) {\r\n      try {\r\n        const client = getOSSClient()\r\n\r\n        // 复制文件到新路径\r\n        await client.copy(newPath, oldPath)\r\n        console.log(`OSS文件复制成功: ${oldPath} -> ${newPath}`)\r\n\r\n        // 删除原文件\r\n        await client.delete(oldPath)\r\n        console.log(`OSS原文件删除成功: ${oldPath}`)\r\n\r\n        return true\r\n      } catch (error) {\r\n        console.error('OSS文件复制删除失败:', error)\r\n        throw error\r\n      }\r\n    },\r\n\r\n    // OSS文件夹重命名\r\n    async renameOSSFolder(oldFolderName, newFolderName) {\r\n      try {\r\n        const client = getOSSClient()\r\n        const user = this.getCurrentUser()\r\n\r\n        // 获取两种类型文件夹的路径\r\n        const baseFolders = ['abgm', 'asucai']\r\n\r\n        for (const baseFolder of baseFolders) {\r\n          const oldPrefix = `${baseFolder}/${user}/${oldFolderName}/`\r\n          const newPrefix = `${baseFolder}/${user}/${newFolderName}/`\r\n\r\n          // 列出文件夹中的所有文件\r\n          const result = await client.list({\r\n            prefix: oldPrefix,\r\n            'max-keys': 1000\r\n          })\r\n\r\n          if (result.objects && result.objects.length > 0) {\r\n            // 复制所有文件到新路径\r\n            for (const obj of result.objects) {\r\n              const newKey = obj.name.replace(oldPrefix, newPrefix)\r\n              await client.copy(newKey, obj.name)\r\n              console.log(`OSS文件夹重命名: ${obj.name} -> ${newKey}`)\r\n            }\r\n\r\n            // 删除原文件\r\n            for (const obj of result.objects) {\r\n              await client.delete(obj.name)\r\n            }\r\n          }\r\n        }\r\n\r\n        return true\r\n      } catch (error) {\r\n        console.error('OSS文件夹重命名失败:', error)\r\n        throw error\r\n      }\r\n    },\r\n\r\n    // OSS文件夹删除\r\n    async deleteOSSFolder(folderName) {\r\n      try {\r\n        console.log('开始删除OSS文件夹:', folderName)\r\n\r\n        if (!folderName) {\r\n          throw new Error('文件夹名称不能为空')\r\n        }\r\n\r\n        const client = getOSSClient()\r\n        if (!client) {\r\n          throw new Error('OSS客户端未初始化')\r\n        }\r\n\r\n        const user = this.getCurrentUser()\r\n        console.log('当前用户:', user)\r\n\r\n        // 获取两种类型文件夹的路径\r\n        const baseFolders = ['abgm', 'asucai']\r\n        let totalDeletedFiles = 0\r\n\r\n        for (const baseFolder of baseFolders) {\r\n          const prefix = `${baseFolder}/${user}/${folderName}/`\r\n          console.log('检查OSS路径:', prefix)\r\n\r\n          // 列出文件夹中的所有文件\r\n          const result = await client.list({\r\n            prefix: prefix,\r\n            'max-keys': 1000\r\n          })\r\n\r\n          console.log(`路径 ${prefix} 下找到 ${result.objects ? result.objects.length : 0} 个文件`)\r\n\r\n          if (result.objects && result.objects.length > 0) {\r\n            // 删除所有文件\r\n            for (const obj of result.objects) {\r\n              await client.delete(obj.name)\r\n              console.log(`OSS文件删除: ${obj.name}`)\r\n              totalDeletedFiles++\r\n            }\r\n          }\r\n        }\r\n\r\n        console.log(`OSS文件夹删除完成，共删除 ${totalDeletedFiles} 个文件`)\r\n        return true\r\n      } catch (error) {\r\n        console.error('OSS文件夹删除失败:', error)\r\n        throw new Error(`删除OSS文件夹失败: ${error.message}`)\r\n      }\r\n    },\r\n\r\n    // 只删除当前标签页的OSS文件夹\r\n    async deleteOSSFolderForCurrentTab(folderName) {\r\n      try {\r\n        console.log(`开始删除${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹:`, folderName)\r\n\r\n        if (!folderName) {\r\n          throw new Error('文件夹名称不能为空')\r\n        }\r\n\r\n        const client = getOSSClient()\r\n        if (!client) {\r\n          throw new Error('OSS客户端未初始化')\r\n        }\r\n\r\n        const user = this.getCurrentUser()\r\n        const baseFolder = this.materialTab === 'bgm' ? 'abgm' : 'asucai'\r\n        const prefix = `${baseFolder}/${user}/${folderName}/`\r\n\r\n        console.log('删除OSS路径:', prefix)\r\n\r\n        // 列出文件夹中的所有文件\r\n        const result = await client.list({\r\n          prefix: prefix,\r\n          'max-keys': 1000\r\n        })\r\n\r\n        let deletedFiles = 0\r\n        if (result.objects && result.objects.length > 0) {\r\n          // 删除所有文件\r\n          for (const obj of result.objects) {\r\n            await client.delete(obj.name)\r\n            console.log(`OSS文件删除: ${obj.name}`)\r\n            deletedFiles++\r\n          }\r\n        }\r\n\r\n        console.log(`${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹删除完成，共删除 ${deletedFiles} 个文件`)\r\n        return true\r\n      } catch (error) {\r\n        console.error(`删除${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹失败:`, error)\r\n        throw new Error(`删除${this.materialTab === 'bgm' ? 'BGM' : '素材'}文件夹失败: ${error.message}`)\r\n      }\r\n    },\r\n\r\n    // 从前端移除文件夹相关的文件（只删除当前标签页的）\r\n    removeFilesFromFolder(folderName) {\r\n      if (this.materialTab === 'bgm') {\r\n        // 只移除BGM列表中该文件夹的文件\r\n        this.bgmList = this.bgmList.filter(file => {\r\n          const fileFolderName = file.folder ? file.folder.split('/').pop() : '总'\r\n          return fileFolderName !== folderName\r\n        })\r\n\r\n        // 从BGM文件夹树中移除\r\n        const treeIndex = this.bgmFolderTree.findIndex(folder => folder.name === folderName)\r\n        if (treeIndex > -1) {\r\n          this.bgmFolderTree.splice(treeIndex, 1)\r\n          console.log(`从BGM文件夹树中移除: ${folderName}`)\r\n        }\r\n      } else {\r\n        // 只移除素材列表中该文件夹的文件\r\n        this.sucaiList = this.sucaiList.filter(file => {\r\n          const fileFolderName = file.folder ? file.folder.split('/').pop() : '总'\r\n          return fileFolderName !== folderName\r\n        })\r\n\r\n        // 从素材文件夹树中移除\r\n        const treeIndex = this.sucaiFolderTree.findIndex(folder => folder.name === folderName)\r\n        if (treeIndex > -1) {\r\n          this.sucaiFolderTree.splice(treeIndex, 1)\r\n          console.log(`从素材文件夹树中移除: ${folderName}`)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 文件夹选择\r\n    async selectFolder(folder) {\r\n      this.selectedFolder = folder.id\r\n      this.currentFolder = folder.name\r\n      console.log('选择文件夹:', folder.name)\r\n\r\n      // 切换文件夹时重新加载文件列表\r\n      if (this.ossInitialized) {\r\n        await this.loadMaterialList()\r\n      }\r\n\r\n      // 更新文件夹计数\r\n      this.updateCurrentTabFolderCounts()\r\n    },\r\n\r\n    // 更新当前标签页的文件夹计数\r\n    updateCurrentTabFolderCounts() {\r\n      const currentMaterials = this.currentMaterialList\r\n      const currentFolders = this.currentFolderTree\r\n\r\n      // 为每个文件夹计算文件数量\r\n      currentFolders.forEach(folder => {\r\n        if (folder.name === '总') {\r\n          // \"总\"文件夹显示所有文件数量\r\n          folder.count = currentMaterials.length\r\n        } else {\r\n          // 其他文件夹只计算属于该文件夹的文件\r\n          folder.count = currentMaterials.filter(file => {\r\n            const fileFolderName = file.folder ? file.folder.split('/').pop() : '总'\r\n            return fileFolderName === folder.name\r\n          }).length\r\n        }\r\n      })\r\n\r\n      console.log(`${this.materialTab} 文件夹计数更新:`, currentFolders.map(f => `${f.name}(${f.count})`))\r\n    },\r\n\r\n    // 文件选择相关方法\r\n    handleSelectAll(checked) {\r\n      if (checked) {\r\n        this.selectedFiles = this.paginatedMaterials.map(f => f.id)\r\n      } else {\r\n        this.selectedFiles = []\r\n      }\r\n    },\r\n\r\n    // 预览文件\r\n    handlePreview() {\r\n      if (this.selectedFiles.length !== 1) {\r\n        this.$message.warning('请选择一个文件进行预览')\r\n        return\r\n      }\r\n\r\n      const file = this.currentMaterialList.find(f => f.id === this.selectedFiles[0])\r\n      if (!file) {\r\n        this.$message.error('文件不存在')\r\n        return\r\n      }\r\n\r\n      // 构建文件URL\r\n      if (!file.url && this.ossInitialized) {\r\n        // 如果没有URL，构建OSS URL\r\n        const endpoint = this.ossConfig.endpoint.replace('https://', '').replace('http://', '')\r\n        file.url = `https://${endpoint}/${file.ossFileName}`\r\n      }\r\n\r\n      this.currentPreviewFile = file\r\n      this.previewVisible = true\r\n    },\r\n\r\n    toggleFileSelection(fileId) {\r\n      const index = this.selectedFiles.indexOf(fileId)\r\n      if (index > -1) {\r\n        this.selectedFiles.splice(index, 1)\r\n      } else {\r\n        this.selectedFiles.push(fileId)\r\n      }\r\n\r\n      // 更新全选状态\r\n      this.selectAll = this.selectedFiles.length === this.paginatedMaterials.length\r\n    },\r\n\r\n    getSimpleFileIcon(type) {\r\n      const iconMap = {\r\n        'video': '🎬',\r\n        'image': '🖼️',\r\n        'audio': '🎵'\r\n      }\r\n      return iconMap[type] || '📄'\r\n    },\r\n\r\n    // 标签页切换\r\n    switchTab(tab) {\r\n      this.materialTab = tab\r\n      this.currentPage = 1 // 重置页码\r\n      this.selectedFiles = [] // 清空选择\r\n      this.selectAll = false\r\n\r\n      const tabNames = {\r\n        'bgm': 'BGM',\r\n        'sucai': '素材'\r\n      }\r\n      this.$message.success(`切换到${tabNames[tab] || tab}上传页面`)\r\n\r\n      // 切换标签页后更新文件夹计数\r\n      this.$nextTick(() => {\r\n        this.updateCurrentTabFolderCounts()\r\n      })\r\n    },\r\n\r\n    // 文件操作\r\n    async handleRename() {\r\n      if (this.selectedFiles.length === 0) {\r\n        this.$message.warning('请先选择要重命名的文件')\r\n        return\r\n      }\r\n      if (this.selectedFiles.length > 1) {\r\n        this.$message.warning('一次只能重命名一个文件')\r\n        return\r\n      }\r\n\r\n      const file = this.currentMaterialList.find(f => f.id === this.selectedFiles[0])\r\n      this.$prompt('请输入新的文件名', '重命名文件', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        inputValue: file.name\r\n      }).then(async ({ value }) => {\r\n        try {\r\n          // 验证新文件名\r\n          if (!value || value.trim() === '') {\r\n            this.$message.error('文件名不能为空')\r\n            return\r\n          }\r\n\r\n          const newFileName = value.trim()\r\n\r\n          // 如果使用OSS存储，需要重命名OSS中的文件\r\n          if (this.storageType === 'oss' && this.ossInitialized) {\r\n            // 构建原文件路径\r\n            let oldOssFilePath = ''\r\n            if (file.ossFileName) {\r\n              oldOssFilePath = file.ossFileName\r\n            } else {\r\n              const baseFolderName = this.materialTab === 'bgm' ? 'abgm' : 'asucai'\r\n              const fullPath = this.buildOSSPath(baseFolderName)\r\n              oldOssFilePath = `${fullPath}/${file.name}`\r\n            }\r\n\r\n            // 构建新文件路径\r\n            const baseFolderName = this.materialTab === 'bgm' ? 'abgm' : 'asucai'\r\n            const fullPath = this.buildOSSPath(baseFolderName)\r\n            const newOssFilePath = `${fullPath}/${newFileName}`\r\n\r\n            console.log(`OSS重命名: ${oldOssFilePath} -> ${newOssFilePath}`)\r\n\r\n            try {\r\n              // OSS不支持直接重命名，需要复制后删除\r\n              await this.copyAndDeleteOSSFile(oldOssFilePath, newOssFilePath)\r\n\r\n              // 更新文件信息\r\n              file.name = newFileName\r\n              file.ossFileName = newOssFilePath\r\n\r\n              this.$message.success('重命名成功')\r\n            } catch (error) {\r\n              console.error('OSS重命名失败:', error)\r\n              this.$message.error(`重命名失败: ${error.message}`)\r\n            }\r\n          } else {\r\n            // 本地存储，直接更新文件名\r\n            file.name = newFileName\r\n            this.$message.success('重命名成功')\r\n          }\r\n\r\n        } catch (error) {\r\n          console.error('重命名失败:', error)\r\n          this.$message.error(`重命名失败: ${error.message}`)\r\n        }\r\n      })\r\n    },\r\n\r\n    async handleDelete() {\r\n      if (this.selectedFiles.length === 0) {\r\n        this.$message.warning('请先选择要删除的文件')\r\n        return\r\n      }\r\n\r\n      this.$confirm(`确定要删除选中的 ${this.selectedFiles.length} 个文件吗？删除后无法恢复！`, '删除文件', {\r\n        confirmButtonText: '确定删除',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          const currentList = this.materialTab === 'bgm' ? this.bgmList : this.sucaiList\r\n\r\n          // 获取要删除的文件信息\r\n          const filesToDelete = this.selectedFiles.map(fileId => {\r\n            return currentList.find(f => f.id === fileId)\r\n          }).filter(file => file) // 过滤掉找不到的文件\r\n\r\n          console.log('准备删除的文件:', filesToDelete)\r\n\r\n          // 如果使用OSS存储，删除OSS中的文件\r\n          if (this.storageType === 'oss' && this.ossInitialized) {\r\n            const deletePromises = filesToDelete.map(async (file) => {\r\n              try {\r\n                // 构建OSS文件路径\r\n                let ossFilePath = ''\r\n                if (file.ossFileName) {\r\n                  // 如果有OSS文件名，直接使用\r\n                  ossFilePath = file.ossFileName\r\n                } else {\r\n                  // 否则根据文件夹和文件名构建路径\r\n                  const baseFolderName = this.materialTab === 'bgm' ? 'abgm' : 'asucai'\r\n                  const fullPath = this.buildOSSPath(baseFolderName)\r\n                  ossFilePath = `${fullPath}/${file.name}`\r\n                }\r\n\r\n                console.log(`删除OSS文件: ${ossFilePath}`)\r\n                await deleteFileFromOSS(ossFilePath)\r\n                return { success: true, file: file.name }\r\n              } catch (error) {\r\n                console.error(`删除OSS文件失败: ${file.name}`, error)\r\n                return { success: false, file: file.name, error: error.message }\r\n              }\r\n            })\r\n\r\n            const deleteResults = await Promise.all(deletePromises)\r\n\r\n            // 检查删除结果\r\n            const failedDeletes = deleteResults.filter(result => !result.success)\r\n            if (failedDeletes.length > 0) {\r\n              console.warn('部分文件删除失败:', failedDeletes)\r\n              this.$message.warning(`${failedDeletes.length} 个文件删除失败，但已从列表中移除`)\r\n            }\r\n          }\r\n\r\n          // 从前端列表中删除文件\r\n          this.selectedFiles.forEach(fileId => {\r\n            const index = currentList.findIndex(f => f.id === fileId)\r\n            if (index > -1) {\r\n              currentList.splice(index, 1)\r\n            }\r\n          })\r\n\r\n          this.selectedFiles = []\r\n          this.selectAll = false\r\n          this.$message.success('删除成功')\r\n\r\n        } catch (error) {\r\n          console.error('删除失败:', error)\r\n          this.$message.error(`删除失败: ${error.message}`)\r\n        }\r\n      })\r\n    },\r\n    showUploadDialog() {\r\n      this.uploadDialogVisible = true\r\n      this.fileList = []\r\n      this.uploadForm = {}\r\n    },\r\n\r\n    beforeUpload(file) {\r\n      // 添加调试信息\r\n      console.log('上传文件信息:', {\r\n        name: file.name,\r\n        type: file.type,\r\n        size: file.size,\r\n        currentTab: this.materialTab\r\n      })\r\n\r\n      // 根据标签页检查文件大小\r\n      let maxSize = 500 // 默认500MB\r\n      if (this.materialTab === 'bgm') {\r\n        maxSize = 100 // BGM文件100MB\r\n      }\r\n\r\n      const isValidSize = file.size / 1024 / 1024 < maxSize\r\n      if (!isValidSize) {\r\n        this.$message.error(`文件大小不能超过 ${maxSize}MB！`)\r\n        return false\r\n      }\r\n\r\n      // 根据标签页检查文件格式\r\n      const extension = file.name.toLowerCase().split('.').pop()\r\n      const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v']\r\n      const audioExts = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']\r\n      const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']\r\n\r\n      if (this.materialTab === 'bgm' && !audioExts.includes(extension)) {\r\n        this.$message.error(`BGM只支持音频文件！支持格式：${audioExts.join(', ')}`)\r\n        return false\r\n      }\r\n\r\n      if (this.materialTab === 'sucai') {\r\n        const allExts = [...videoExts, ...audioExts, ...imageExts]\r\n        if (!allExts.includes(extension)) {\r\n          this.$message.error(`素材支持视频、音频、图片文件！支持格式：${allExts.join(', ')}`)\r\n          return false\r\n        }\r\n      }\r\n\r\n      return true\r\n    },\r\n\r\n    isValidFileType(type, fileName) {\r\n      // 获取文件扩展名作为备用验证\r\n      const extension = fileName.toLowerCase().split('.').pop()\r\n\r\n      if (this.materialTab === 'video') {\r\n        const videoTypes = [\r\n          'video/mp4', 'video/mpeg', 'video/quicktime', 'video/x-msvideo',\r\n          'video/x-ms-wmv', 'video/x-flv', 'video/webm', 'video/3gpp',\r\n          'video/mp2t', 'video/x-m4v'\r\n        ]\r\n        const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v', 'ts']\r\n\r\n        return videoTypes.includes(type) || videoExtensions.includes(extension)\r\n      } else {\r\n        const audioTypes = [\r\n          'audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/x-wav',\r\n          'audio/flac', 'audio/aac', 'audio/mp4', 'audio/x-m4a',\r\n          'audio/ogg', 'audio/webm'\r\n        ]\r\n        const audioExtensions = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']\r\n\r\n        return audioTypes.includes(type) || audioExtensions.includes(extension)\r\n      }\r\n    },\r\n    handleUploadSuccess(response, file) {\r\n      // 创建新的文件对象\r\n      const newFile = {\r\n        id: Date.now() + Math.random(),\r\n        name: file.name,\r\n        type: this.materialTab === 'video' ? 'video' : 'audio',\r\n        size: file.size,\r\n        uploadTime: new Date().toLocaleString().slice(0, 16),\r\n        duration: '00:00:00', // 实际应用中应该从服务器返回\r\n        resolution: this.materialTab === 'video' ? '1920x1080' : undefined,\r\n        bitrate: this.materialTab === 'music' ? '128kbps' : undefined\r\n      }\r\n\r\n      // 添加到对应的列表\r\n      if (this.materialTab === 'video') {\r\n        this.videoList.unshift(newFile)\r\n      } else {\r\n        this.musicList.unshift(newFile)\r\n      }\r\n\r\n      this.$message.success(`${file.name} 上传成功！`)\r\n    },\r\n\r\n    handleUploadError(err, file) {\r\n      this.$message.error(`${file.name} 上传失败！`)\r\n    },\r\n\r\n    handleUploadProgress(event, file) {\r\n      // 计算上传进度百分比\r\n      const progress = Math.round((event.loaded / event.total) * 100)\r\n\r\n      // 更新进度条显示\r\n      this.$set(this.uploadProgress, file.name, progress)\r\n\r\n      console.log(`文件 ${file.name} 上传进度: ${progress}%`)\r\n    },\r\n\r\n    handleFileChange(file, fileList) {\r\n      console.log('文件变化:', file)\r\n      console.log('当前文件列表:', fileList)\r\n      this.fileList = fileList\r\n    },\r\n\r\n    handleRemove(file, fileList) {\r\n      console.log('移除文件:', file)\r\n      console.log('更新后的文件列表:', fileList)\r\n      this.fileList = fileList\r\n    },\r\n\r\n    async submitUpload() {\r\n      console.log('当前文件列表:', this.fileList)\r\n      console.log('文件列表长度:', this.fileList.length)\r\n\r\n      if (!this.fileList || this.fileList.length === 0) {\r\n        this.$message.warning('请先选择要上传的文件')\r\n        return\r\n      }\r\n\r\n      this.uploading = true\r\n      const fileCount = this.fileList.length\r\n\r\n      // 根据标签页确定文件夹和类型\r\n      let baseFolderName = ''\r\n      let fileType = ''\r\n\r\n      switch (this.materialTab) {\r\n        case 'bgm':\r\n          baseFolderName = 'abgm'\r\n          fileType = 'audio'\r\n          break\r\n        case 'sucai':\r\n          baseFolderName = 'asucai'\r\n          fileType = 'mixed' // 混合类型\r\n          break\r\n        default:\r\n          baseFolderName = 'abgm'\r\n          fileType = 'audio'\r\n      }\r\n\r\n      // 构建完整的OSS路径：baseFolderName/用户名/文件夹名\r\n      const folderName = this.buildOSSPath(baseFolderName)\r\n\r\n      console.log('当前标签页:', this.materialTab)\r\n      console.log('基础文件夹:', baseFolderName)\r\n      console.log('完整上传路径:', folderName)\r\n      console.log('文件类型:', fileType)\r\n\r\n      // 获取实际的文件对象\r\n      const actualFiles = this.fileList.map(fileItem => {\r\n        // Element UI 上传组件的文件对象可能有不同的结构\r\n        return fileItem.raw || fileItem.file || fileItem\r\n      }).filter(file => file instanceof File)\r\n\r\n      console.log('实际文件对象:', actualFiles)\r\n\r\n      if (actualFiles.length === 0) {\r\n        this.$message.warning('没有找到有效的文件对象')\r\n        return\r\n      }\r\n\r\n      try {\r\n        // 初始化所有文件的进度条\r\n        actualFiles.forEach(file => {\r\n          this.$set(this.uploadProgress, file.name, 0)\r\n        })\r\n\r\n        if (this.storageType === 'oss') {\r\n          // OSS上传\r\n          if (!this.ossInitialized) {\r\n            this.$message.error('OSS未配置，请先配置OSS')\r\n            this.openOSSConfig()\r\n            this.uploading = false\r\n            return\r\n          }\r\n\r\n          // 使用OSS上传\r\n          const uploadResults = await uploadFilesToOSS(\r\n            actualFiles,\r\n            fileType,\r\n            folderName, // 使用对应的文件夹\r\n            (index, progress, fileName, result, error) => {\r\n              if (error) {\r\n                this.$set(this.uploadProgress, fileName, -1) // 表示失败\r\n              } else {\r\n                this.$set(this.uploadProgress, fileName, progress)\r\n              }\r\n            }\r\n          )\r\n\r\n          // 处理上传结果\r\n          uploadResults.forEach((result, index) => {\r\n            if (result.success) {\r\n              // 根据文件扩展名确定文件类型\r\n              const fileExtension = result.originalName.toLowerCase().split('.').pop()\r\n              const audioExts = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']\r\n              const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v']\r\n              const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']\r\n\r\n              let actualFileType = 'unknown'\r\n              if (audioExts.includes(fileExtension)) {\r\n                actualFileType = 'audio'\r\n              } else if (videoExts.includes(fileExtension)) {\r\n                actualFileType = 'video'\r\n              } else if (imageExts.includes(fileExtension)) {\r\n                actualFileType = 'image'\r\n              }\r\n\r\n              const newFile = {\r\n                id: Date.now() + Math.random() + index,\r\n                name: result.originalName,\r\n                type: actualFileType,\r\n                size: result.size,\r\n                uploadTime: new Date().toLocaleString().slice(0, 16),\r\n                duration: actualFileType === 'video' ? '00:02:30' : (actualFileType === 'audio' ? '00:03:45' : undefined),\r\n                resolution: actualFileType === 'video' ? '1920x1080' : undefined,\r\n                bitrate: actualFileType === 'audio' ? '128kbps' : undefined,\r\n                url: result.url,\r\n                ossFileName: result.fileName,\r\n                folder: folderName\r\n              }\r\n\r\n              // 根据标签页添加到对应列表\r\n              switch (this.materialTab) {\r\n                case 'bgm':\r\n                  this.bgmList.unshift(newFile)\r\n                  break\r\n                case 'sucai':\r\n                  this.sucaiList.unshift(newFile)\r\n                  break\r\n              }\r\n            }\r\n          })\r\n\r\n          this.$message.success(`成功上传 ${uploadResults.filter(r => r.success).length} 个文件到阿里云OSS！`)\r\n\r\n        } else {\r\n          // 本地模拟上传\r\n          actualFiles.forEach((file) => {\r\n            // 进度已在上面初始化，这里直接开始模拟进度更新\r\n\r\n            // 模拟进度更新\r\n            const progressInterval = setInterval(() => {\r\n              const currentProgress = this.uploadProgress[file.name] || 0\r\n              if (currentProgress < 100) {\r\n                this.$set(this.uploadProgress, file.name, Math.min(currentProgress + Math.random() * 30, 100))\r\n              } else {\r\n                clearInterval(progressInterval)\r\n              }\r\n            }, 200)\r\n          })\r\n\r\n          // 模拟上传完成\r\n          setTimeout(() => {\r\n            // 为每个文件创建新记录\r\n            actualFiles.forEach((file, index) => {\r\n              // 根据文件扩展名确定文件类型\r\n              const fileExtension = file.name.toLowerCase().split('.').pop()\r\n              const audioExts = ['mp3', 'wav', 'flac', 'aac', 'm4a', 'ogg', 'wma']\r\n              const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'mkv', 'm4v']\r\n              const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']\r\n\r\n              let actualFileType = 'unknown'\r\n              if (audioExts.includes(fileExtension)) {\r\n                actualFileType = 'audio'\r\n              } else if (videoExts.includes(fileExtension)) {\r\n                actualFileType = 'video'\r\n              } else if (imageExts.includes(fileExtension)) {\r\n                actualFileType = 'image'\r\n              }\r\n\r\n              const newFile = {\r\n                id: Date.now() + Math.random() + index,\r\n                name: file.name,\r\n                type: actualFileType,\r\n                size: file.size,\r\n                uploadTime: new Date().toLocaleString().slice(0, 16),\r\n                duration: actualFileType === 'video' ? '00:02:30' : (actualFileType === 'audio' ? '00:03:45' : undefined),\r\n                resolution: actualFileType === 'video' ? '1920x1080' : undefined,\r\n                bitrate: actualFileType === 'audio' ? '128kbps' : undefined,\r\n                folder: folderName,\r\n                url: URL.createObjectURL(file) // 为本地文件创建预览URL\r\n              }\r\n\r\n              // 根据标签页添加到对应列表\r\n              switch (this.materialTab) {\r\n                case 'bgm':\r\n                  this.bgmList.unshift(newFile)\r\n                  break\r\n                case 'sucai':\r\n                  this.sucaiList.unshift(newFile)\r\n                  break\r\n              }\r\n            })\r\n\r\n            this.$message.success(`成功上传 ${fileCount} 个文件！`)\r\n\r\n            // 上传完成后更新文件夹计数\r\n            this.updateCurrentTabFolderCounts()\r\n          }, 2000)\r\n        }\r\n\r\n        this.uploading = false\r\n        this.uploadDialogVisible = false\r\n        this.fileList = []\r\n        this.uploadProgress = {}\r\n\r\n      } catch (error) {\r\n        this.uploading = false\r\n        this.$message.error(`上传失败：${error.message}`)\r\n        console.error('上传错误:', error)\r\n      }\r\n    },\r\n\r\n    formatFileSize(bytes) {\r\n      if (bytes === 0) return '0 Bytes'\r\n      const k = 1024\r\n      const sizes = ['Bytes', 'KB', 'MB', 'GB']\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\r\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 素材管理界面 - 完全按照图片设计 */\r\n.up-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: #f8f9fa;\r\n  margin: 0;\r\n  height: 100vh;\r\n}\r\n\r\n/* 顶部标签页 */\r\n.materials-tabs {\r\n  background: white;\r\n  padding: 0;\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n\r\n.tab-buttons {\r\n  display: flex;\r\n  gap: 0;\r\n  align-items: center;\r\n}\r\n\r\n.tab-button {\r\n  border-radius: 20px !important;\r\n  margin: 10px 5px;\r\n  padding: 8px 20px;\r\n  border: none;\r\n  font-size: 14px;\r\n}\r\n\r\n.tab-button.el-button--primary {\r\n  background: #4a90e2;\r\n  color: white;\r\n}\r\n\r\n.tab-button.el-button--default {\r\n  background: #e8e8e8;\r\n  color: #666;\r\n}\r\n\r\n/* BGM免费下载按钮样式 */\r\n.bgm-download-button {\r\n  margin-left: 20px !important;\r\n  border-radius: 15px !important;\r\n  padding: 6px 16px !important;\r\n  font-size: 13px !important;\r\n  background: linear-gradient(135deg, #67b26f 0%, #4ca2cd 100%) !important;\r\n  border: none !important;\r\n  color: white !important;\r\n  box-shadow: 0 2px 8px rgba(76, 162, 205, 0.3) !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.bgm-download-button:hover {\r\n  transform: translateY(-1px) !important;\r\n  box-shadow: 0 4px 12px rgba(76, 162, 205, 0.4) !important;\r\n  background: linear-gradient(135deg, #5a9f63 0%, #3d8bb8 100%) !important;\r\n}\r\n\r\n.bgm-download-button:active {\r\n  transform: translateY(0) !important;\r\n}\r\n\r\n/* 主要内容区域 */\r\n.materials-main {\r\n  flex: 1;\r\n  display: flex;\r\n  background: white;\r\n}\r\n\r\n/* 左侧文件夹树 - 按照图片样式 */\r\n.folder-sidebar {\r\n  width: 200px;\r\n  background: white;\r\n  border-right: 1px solid #e8e8e8;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.folder-header {\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.folder-actions {\r\n  display: flex !important;\r\n  flex-direction: column !important; /* 垂直排列 */\r\n  align-items: flex-start !important; /* 左对齐 */\r\n  gap: 8px; /* 上下间距 */\r\n  padding: 8px 0;\r\n}\r\n\r\n/* 使用更强的选择器覆盖Element UI样式 */\r\n.folder-actions .el-button,\r\n.folder-actions .el-button.el-button--small,\r\n.folder-actions .el-button.el-button--primary,\r\n.folder-actions .el-button.el-button--success {\r\n  border-radius: 6px !important;\r\n  font-weight: 500 !important;\r\n  font-size: 13px !important;\r\n  height: 32px !important;\r\n  width: 100% !important; /* 垂直排列时占满宽度 */\r\n  max-width: 120px !important; /* 限制最大宽度 */\r\n  min-height: 32px !important;\r\n  max-height: 32px !important;\r\n  line-height: 1 !important;\r\n  display: inline-flex !important;\r\n  align-items: center !important;\r\n  justify-content: center !important;\r\n  padding: 0 16px !important;\r\n  margin: 0 !important;\r\n  transition: all 0.3s ease;\r\n  flex-shrink: 0;\r\n  vertical-align: baseline !important;\r\n  box-sizing: border-box !important;\r\n  border-width: 1px !important;\r\n}\r\n\r\n.folder-actions .el-button:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 图标样式 */\r\n.folder-actions .el-button i {\r\n  margin-right: 4px !important;\r\n  line-height: 1 !important;\r\n  vertical-align: middle !important;\r\n}\r\n\r\n/* 按钮文字样式 */\r\n.folder-actions .el-button span {\r\n  line-height: 1 !important;\r\n  vertical-align: middle !important;\r\n}\r\n\r\n.add-folder-icon {\r\n  font-size: 16px;\r\n  color: #666;\r\n  cursor: pointer;\r\n}\r\n\r\n.folder-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  cursor: pointer;\r\n}\r\n\r\n.folder-list {\r\n  flex: 1;\r\n  padding: 10px 0;\r\n}\r\n\r\n.folder-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 15px;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  gap: 8px;\r\n}\r\n\r\n.folder-item:hover {\r\n  background: #f5f5f5;\r\n}\r\n\r\n.folder-item.active {\r\n  background: #e6f3ff;\r\n}\r\n\r\n.folder-icon {\r\n  font-size: 16px;\r\n}\r\n\r\n.folder-name {\r\n  flex: 1;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.folder-count {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n/* 右侧文件区域 */\r\n.files-area {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: white;\r\n}\r\n\r\n/* 文件操作栏 */\r\n.files-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 20px;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  background: #fafafa;\r\n}\r\n\r\n.toolbar-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  gap: 15px;\r\n}\r\n\r\n.action-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  cursor: pointer;\r\n}\r\n\r\n.action-text:hover {\r\n  color: #4a90e2;\r\n}\r\n\r\n.toolbar-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.pagination-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 2px;\r\n}\r\n\r\n/* 文件内容区域 */\r\n.files-content {\r\n  flex: 1;\r\n  overflow: auto;\r\n}\r\n\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 300px;\r\n  color: #999;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 48px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 14px;\r\n}\r\n\r\n/* 文件表格 */\r\n.files-table {\r\n  width: 100%;\r\n}\r\n\r\n.table-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 20px;\r\n  background: #fafafa;\r\n  border-bottom: 1px solid #e8e8e8;\r\n  font-size: 14px;\r\n  color: #666;\r\n  font-weight: 500;\r\n}\r\n\r\n.table-body {\r\n  background: white;\r\n}\r\n\r\n.table-row {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px 20px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.table-row:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.table-row.selected {\r\n  background: #e6f3ff;\r\n}\r\n\r\n.header-cell,\r\n.cell {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.checkbox-cell {\r\n  width: 50px;\r\n  justify-content: center;\r\n}\r\n\r\n.name-cell {\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.size-cell {\r\n  width: 100px;\r\n}\r\n\r\n.time-cell {\r\n  width: 150px;\r\n}\r\n\r\n.file-icon {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n/* 上传进度样式 */\r\n.upload-progress {\r\n  .progress-item {\r\n    margin-bottom: 15px;\r\n\r\n    .progress-info {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 5px;\r\n\r\n      .file-name {\r\n        font-size: 14px;\r\n        color: #333;\r\n        flex: 1;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .progress-text {\r\n        font-size: 12px;\r\n        color: #666;\r\n        margin-left: 10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.storage-selector {\r\n  padding: 15px;\r\n  background: #f8f9fa;\r\n  border-radius: 4px;\r\n  border: 1px solid #e8e8e8;\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n}\r\n\r\n.text-success {\r\n  color: #67c23a;\r\n  font-weight: 500;\r\n}\r\n\r\n.text-danger {\r\n  color: #f56c6c;\r\n  font-weight: 500;\r\n}\r\n\r\n/* OSS配置对话框样式 */\r\n.oss-config-dialog {\r\n  .el-dialog__header {\r\n    padding: 20px 20px 0;\r\n    border-bottom: 1px solid #f0f0f0;\r\n  }\r\n\r\n  .el-dialog__body {\r\n    padding: 0;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    padding: 20px;\r\n    border-top: 1px solid #f0f0f0;\r\n  }\r\n}\r\n\r\n.oss-config-content {\r\n  padding: 20px;\r\n}\r\n\r\n.storage-type-section {\r\n  margin-bottom: 30px;\r\n\r\n  .section-label {\r\n    font-size: 14px;\r\n    color: #333;\r\n    margin-bottom: 15px;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .storage-options {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .storage-radio {\r\n    .radio-text {\r\n      font-size: 14px;\r\n      color: #333;\r\n    }\r\n  }\r\n\r\n  .storage-description {\r\n    font-size: 12px;\r\n    color: #999;\r\n    line-height: 1.5;\r\n  }\r\n}\r\n\r\n.oss-form-section {\r\n  .form-row {\r\n    margin-bottom: 20px;\r\n\r\n    .form-label {\r\n      font-size: 14px;\r\n      color: #333;\r\n      margin-bottom: 8px;\r\n\r\n      &.required::before {\r\n        content: '*';\r\n        color: #f56c6c;\r\n        margin-right: 4px;\r\n      }\r\n    }\r\n\r\n    .form-input {\r\n      width: 100%;\r\n\r\n      .el-input__inner {\r\n        border-radius: 4px;\r\n        border: 1px solid #dcdfe6;\r\n        padding: 0 15px;\r\n        height: 40px;\r\n        line-height: 40px;\r\n\r\n        &:focus {\r\n          border-color: #409eff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .form-hint {\r\n      font-size: 12px;\r\n      color: #999;\r\n      margin-top: 5px;\r\n      line-height: 1.4;\r\n    }\r\n  }\r\n\r\n  .status-options {\r\n    display: flex;\r\n    gap: 20px;\r\n\r\n    .status-radio {\r\n      .radio-text {\r\n        font-size: 14px;\r\n        color: #333;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.dialog-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n\r\n  .cancel-btn {\r\n    padding: 8px 20px;\r\n    border-radius: 4px;\r\n    border: 1px solid #dcdfe6;\r\n    background: #fff;\r\n    color: #606266;\r\n\r\n    &:hover {\r\n      color: #409eff;\r\n      border-color: #c6e2ff;\r\n      background-color: #ecf5ff;\r\n    }\r\n  }\r\n\r\n  .confirm-btn {\r\n    padding: 8px 20px;\r\n    border-radius: 4px;\r\n    background: #409eff;\r\n    border-color: #409eff;\r\n    color: #fff;\r\n\r\n    &:hover {\r\n      background: #66b1ff;\r\n      border-color: #66b1ff;\r\n    }\r\n  }\r\n}\r\n\r\n/* 右键菜单样式 */\r\n.context-menu {\r\n  position: fixed;\r\n  background: #fff;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  z-index: 9999;\r\n  min-width: 120px;\r\n  padding: 4px 0;\r\n\r\n  .menu-item {\r\n    padding: 8px 16px;\r\n    cursor: pointer;\r\n    display: flex;\r\n    align-items: center;\r\n    font-size: 14px;\r\n    color: #606266;\r\n    transition: all 0.3s;\r\n\r\n    i {\r\n      margin-right: 8px;\r\n      font-size: 14px;\r\n    }\r\n\r\n    &:hover {\r\n      background-color: #f5f7fa;\r\n      color: #409eff;\r\n    }\r\n\r\n    &:active {\r\n      background-color: #e6f7ff;\r\n    }\r\n  }\r\n}\r\n\r\n/* 文件夹项样式增强 */\r\n.folder-item {\r\n  position: relative;\r\n  user-select: none;\r\n\r\n  &:hover {\r\n    background-color: #f5f7fa;\r\n  }\r\n\r\n  &.active {\r\n    background-color: #e6f7ff;\r\n    color: #409eff;\r\n  }\r\n}\r\n\r\n/* 文件夹操作按钮样式已在上面定义，这里删除重复定义 */\r\n\r\n/* OSS未配置提示样式 */\r\n.no-oss-tip {\r\n  padding: 20px;\r\n  text-align: center;\r\n  color: #909399;\r\n\r\n  .tip-icon {\r\n    font-size: 32px;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .tip-text {\r\n    margin-bottom: 15px;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n.no-oss-files-tip {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 400px;\r\n\r\n  .tip-content {\r\n    text-align: center;\r\n\r\n    .tip-icon {\r\n      font-size: 64px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .tip-title {\r\n      font-size: 18px;\r\n      font-weight: 500;\r\n      color: #303133;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .tip-description {\r\n      font-size: 14px;\r\n      color: #909399;\r\n      margin-bottom: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n/* 文件工具栏样式 */\r\n.file-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 16px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n\r\n  .toolbar-left {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 16px;\r\n\r\n    .selected-count {\r\n      color: #409eff;\r\n      font-size: 14px;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n\r\n  .toolbar-right {\r\n    display: flex;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n/* 文件网格样式 */\r\n.file-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));\r\n  gap: 16px;\r\n  padding: 16px;\r\n}\r\n\r\n.file-card {\r\n  position: relative;\r\n  background: #fff;\r\n  border: 2px solid transparent;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  cursor: pointer;\r\n  transition: all 0.4s ease;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  z-index: 1;\r\n\r\n  &:hover {\r\n    border-color: #409eff;\r\n    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);\r\n    transform: translateY(-2px);\r\n  }\r\n\r\n  &.selected {\r\n    border-color: #409eff;\r\n    background-color: #f0f8ff;\r\n  }\r\n\r\n  // 播放时放大效果\r\n  &.enlarged {\r\n    transform: scale(1.15) translateY(-8px);\r\n    z-index: 10;\r\n    box-shadow: 0 8px 32px rgba(64, 158, 255, 0.4);\r\n    border-color: #409eff;\r\n\r\n    .file-thumbnail {\r\n      border-radius: 8px;\r\n    }\r\n  }\r\n\r\n\r\n}\r\n\r\n// 视频放大样式 - 现代播放器设计\r\n.file-card.scale-enlarged {\r\n  position: fixed !important;\r\n  top: 50vh !important;\r\n  left: 50vw !important;\r\n  transform: translate(-50%, -50%) !important;\r\n  z-index: 9999999 !important;\r\n  border-radius: 12px !important;\r\n  overflow: hidden !important;\r\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4),\r\n              0 0 0 2px rgba(255, 255, 255, 0.1) !important;\r\n  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;\r\n\r\n  // 隐藏文件信息\r\n  .file-info {\r\n    display: none !important;\r\n  }\r\n\r\n  // 视频区域\r\n  .file-thumbnail {\r\n    height: 100% !important;\r\n    border-radius: 12px !important;\r\n    overflow: hidden !important;\r\n    position: relative !important;\r\n\r\n    .video-thumbnail {\r\n      border-radius: 12px !important;\r\n      position: relative !important;\r\n\r\n      .thumbnail-video {\r\n        border-radius: 12px !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 背景遮罩\r\n.scale-enlarged-backdrop {\r\n  position: fixed !important;\r\n  top: 0 !important;\r\n  left: 0 !important;\r\n  width: 100vw !important;\r\n  height: 100vh !important;\r\n  background: rgba(0, 0, 0, 0.7) !important;\r\n  backdrop-filter: blur(8px) !important;\r\n  z-index: 9999998 !important;\r\n  animation: backdropFadeIn 0.3s ease !important;\r\n}\r\n\r\n// 发光边框动画\r\n@keyframes borderGlow {\r\n  0%, 100% {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    opacity: 0.8;\r\n    transform: scale(1.02);\r\n  }\r\n}\r\n\r\n// 背景淡入动画\r\n@keyframes backdropFadeIn {\r\n  from {\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n// 小视频简洁播放按钮\r\n.simple-play-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border-radius: 12px;\r\n  transition: all 0.3s ease;\r\n\r\n  .play-button {\r\n    width: 40px;\r\n    height: 40px;\r\n    background: rgba(255, 255, 255, 0.9);\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\r\n    transition: all 0.3s ease;\r\n\r\n    i {\r\n      font-size: 16px;\r\n      color: #333;\r\n      margin-left: 2px; // 播放图标视觉居中\r\n    }\r\n\r\n    &:hover {\r\n      transform: scale(1.1);\r\n      background: white;\r\n    }\r\n  }\r\n}\r\n\r\n// 放大视频的现代控件\r\n.video-controls-overlay {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));\r\n  padding: 20px 16px 12px;\r\n  border-radius: 0 0 12px 12px;\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n\r\n  .progress-container {\r\n    margin-bottom: 12px;\r\n    cursor: pointer;\r\n\r\n    .progress-track {\r\n      position: relative;\r\n      height: 3px;\r\n      background: rgba(255, 255, 255, 0.3);\r\n      border-radius: 2px;\r\n\r\n      .progress-fill {\r\n        height: 100%;\r\n        background: #fff;\r\n        border-radius: 2px;\r\n        transition: width 0.1s ease;\r\n      }\r\n\r\n      .progress-thumb {\r\n        position: absolute;\r\n        top: 50%;\r\n        width: 10px;\r\n        height: 10px;\r\n        background: white;\r\n        border-radius: 50%;\r\n        transform: translate(-50%, -50%);\r\n        opacity: 0;\r\n        transition: all 0.2s ease;\r\n      }\r\n    }\r\n\r\n    &:hover .progress-thumb {\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .controls-bottom {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .play-pause-btn {\r\n      width: 32px;\r\n      height: 32px;\r\n      background: rgba(255, 255, 255, 0.2);\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      cursor: pointer;\r\n      transition: all 0.2s ease;\r\n\r\n      i {\r\n        font-size: 14px;\r\n        color: white;\r\n        margin-left: 1px;\r\n      }\r\n\r\n      &:hover {\r\n        background: rgba(255, 255, 255, 0.3);\r\n      }\r\n    }\r\n\r\n    .time-display {\r\n      font-size: 12px;\r\n      color: white;\r\n      font-family: 'SF Mono', Monaco, monospace;\r\n\r\n      .separator {\r\n        margin: 0 4px;\r\n        opacity: 0.7;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 鼠标悬停时显示控件\r\n.video-thumbnail:hover .video-controls-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n// Portal悬停放大层样式\r\n.hover-portal-layer {\r\n  position: fixed !important;\r\n  z-index: 999999 !important;\r\n  pointer-events: auto !important;\r\n  transition: all 0.3s ease !important;\r\n\r\n  .hover-file-card {\r\n    width: 100% !important;\r\n    height: 100% !important;\r\n    border-radius: 12px !important;\r\n    overflow: hidden !important;\r\n    box-shadow: 0 20px 60px rgba(64, 158, 255, 0.8) !important;\r\n    border: 3px solid #409eff !important;\r\n    background: #ffffff !important;\r\n    transform: scale(1) !important;\r\n    animation: portalFadeIn 0.3s ease !important;\r\n\r\n    // 确保可见性\r\n    opacity: 1 !important;\r\n    display: block !important;\r\n\r\n    // 视频样式\r\n    .video-thumbnail {\r\n      width: 100% !important;\r\n      height: calc(100% - 60px) !important;\r\n      position: relative !important;\r\n      background: #000 !important;\r\n      border-radius: 8px 8px 0 0 !important;\r\n\r\n      .thumbnail-video {\r\n        width: 100% !important;\r\n        height: 100% !important;\r\n        object-fit: cover !important;\r\n        border-radius: 8px 8px 0 0 !important;\r\n      }\r\n\r\n      .duration-badge {\r\n        position: absolute !important;\r\n        bottom: 8px !important;\r\n        right: 8px !important;\r\n        background: rgba(0, 0, 0, 0.8) !important;\r\n        color: white !important;\r\n        padding: 4px 8px !important;\r\n        border-radius: 4px !important;\r\n        font-size: 12px !important;\r\n      }\r\n\r\n      // Portal视频控制按钮\r\n      .portal-video-controls {\r\n        position: absolute !important;\r\n        bottom: 0 !important;\r\n        left: 0 !important;\r\n        right: 0 !important;\r\n        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8)) !important;\r\n        padding: 20px 16px 12px !important;\r\n        opacity: 0 !important;\r\n        transform: translateY(10px) !important;\r\n        transition: all 0.3s ease !important;\r\n        pointer-events: auto !important;\r\n\r\n        &.visible {\r\n          opacity: 1 !important;\r\n          transform: translateY(0) !important;\r\n        }\r\n\r\n        .portal-play-btn {\r\n          position: absolute !important;\r\n          top: -30px !important;\r\n          left: 50% !important;\r\n          transform: translateX(-50%) !important;\r\n          width: 48px !important;\r\n          height: 48px !important;\r\n          background: rgba(64, 158, 255, 0.9) !important;\r\n          border: none !important;\r\n          border-radius: 50% !important;\r\n          display: flex !important;\r\n          align-items: center !important;\r\n          justify-content: center !important;\r\n          cursor: pointer !important;\r\n          transition: all 0.3s ease !important;\r\n\r\n          &:hover {\r\n            background: #409eff !important;\r\n            transform: translateX(-50%) scale(1.1) !important;\r\n          }\r\n\r\n          i {\r\n            font-size: 20px !important;\r\n            color: white !important;\r\n          }\r\n        }\r\n\r\n        .portal-info {\r\n          color: white !important;\r\n          font-size: 12px !important;\r\n          text-align: center !important;\r\n          background: rgba(0, 0, 0, 0.5) !important;\r\n          padding: 4px 8px !important;\r\n          border-radius: 4px !important;\r\n          margin-top: 8px !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 音频样式\r\n    .audio-thumbnail {\r\n      width: 100% !important;\r\n      height: calc(100% - 60px) !important;\r\n      position: relative !important;\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\r\n      border-radius: 8px 8px 0 0 !important;\r\n      display: flex !important;\r\n      flex-direction: column !important;\r\n      align-items: center !important;\r\n      justify-content: center !important;\r\n\r\n      .audio-icon {\r\n        font-size: 48px !important;\r\n        margin-bottom: 16px !important;\r\n        color: white !important;\r\n      }\r\n\r\n      .audio-waveform {\r\n        display: flex !important;\r\n        align-items: center !important;\r\n        gap: 3px !important;\r\n\r\n        .wave-bar {\r\n          width: 4px !important;\r\n          background: rgba(255, 255, 255, 0.8) !important;\r\n          border-radius: 2px !important;\r\n          animation: audioWave 1.5s ease-in-out infinite !important;\r\n\r\n          &:nth-child(1) { height: 20px !important; animation-delay: 0s !important; }\r\n          &:nth-child(2) { height: 35px !important; animation-delay: 0.1s !important; }\r\n          &:nth-child(3) { height: 25px !important; animation-delay: 0.2s !important; }\r\n          &:nth-child(4) { height: 40px !important; animation-delay: 0.3s !important; }\r\n          &:nth-child(5) { height: 30px !important; animation-delay: 0.4s !important; }\r\n          &:nth-child(6) { height: 45px !important; animation-delay: 0.5s !important; }\r\n          &:nth-child(7) { height: 35px !important; animation-delay: 0.6s !important; }\r\n          &:nth-child(8) { height: 25px !important; animation-delay: 0.7s !important; }\r\n          &:nth-child(9) { height: 40px !important; animation-delay: 0.8s !important; }\r\n          &:nth-child(10) { height: 30px !important; animation-delay: 0.9s !important; }\r\n          &:nth-child(11) { height: 35px !important; animation-delay: 1.0s !important; }\r\n          &:nth-child(12) { height: 20px !important; animation-delay: 1.1s !important; }\r\n        }\r\n      }\r\n\r\n      .duration-badge {\r\n        position: absolute !important;\r\n        bottom: 8px !important;\r\n        right: 8px !important;\r\n        background: rgba(0, 0, 0, 0.8) !important;\r\n        color: white !important;\r\n        padding: 4px 8px !important;\r\n        border-radius: 4px !important;\r\n        font-size: 12px !important;\r\n      }\r\n    }\r\n\r\n    // 图片样式\r\n    .image-thumbnail {\r\n      width: 100% !important;\r\n      height: calc(100% - 60px) !important;\r\n      position: relative !important;\r\n      border-radius: 8px 8px 0 0 !important;\r\n      overflow: hidden !important;\r\n\r\n      .thumbnail-image {\r\n        width: 100% !important;\r\n        height: 100% !important;\r\n        object-fit: cover !important;\r\n        border-radius: 8px 8px 0 0 !important;\r\n      }\r\n    }\r\n\r\n    // 文件信息\r\n    .file-info {\r\n      height: 60px !important;\r\n      padding: 12px 16px !important;\r\n      background: #f8f9fa !important;\r\n      border-radius: 0 0 8px 8px !important;\r\n\r\n      .file-name {\r\n        font-size: 14px !important;\r\n        font-weight: 600 !important;\r\n        color: #333 !important;\r\n        margin-bottom: 4px !important;\r\n        white-space: nowrap !important;\r\n        overflow: hidden !important;\r\n        text-overflow: ellipsis !important;\r\n      }\r\n\r\n      .file-meta {\r\n        display: flex !important;\r\n        gap: 12px !important;\r\n        font-size: 12px !important;\r\n        color: #666 !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// Portal动画\r\n@keyframes portalFadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: scale(0.8);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n@keyframes audioWave {\r\n  0%, 100% {\r\n    transform: scaleY(0.5);\r\n    opacity: 0.7;\r\n  }\r\n  50% {\r\n    transform: scaleY(1);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.file-checkbox {\r\n  position: absolute;\r\n  top: 8px;\r\n  left: 8px;\r\n  z-index: 10;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  border-radius: 4px;\r\n  padding: 2px;\r\n}\r\n\r\n.file-thumbnail {\r\n  width: 100%;\r\n  height: 120px;\r\n  position: relative;\r\n  overflow: hidden;\r\n  background: #f5f7fa;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n\r\n}\r\n\r\n/* 视频缩略图样式 */\r\n.video-thumbnail {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .thumbnail-video {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n  }\r\n\r\n\r\n\r\n  .duration-badge {\r\n    position: absolute;\r\n    bottom: 8px;\r\n    right: 8px;\r\n    background: rgba(0, 0, 0, 0.7);\r\n    color: white;\r\n    padding: 2px 6px;\r\n    border-radius: 4px;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n/* 音频缩略图样式 */\r\n.audio-thumbnail {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n  transition: all 0.4s ease;\r\n\r\n  &:hover {\r\n    transform: scale(1.05);\r\n  }\r\n\r\n  &.playing {\r\n    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\r\n  }\r\n\r\n  .audio-icon {\r\n    font-size: 32px;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .audio-waveform {\r\n    display: flex;\r\n    align-items: flex-end;\r\n    gap: 2px;\r\n    height: 20px;\r\n\r\n    .wave-bar {\r\n      width: 3px;\r\n      background: rgba(255, 255, 255, 0.8);\r\n      border-radius: 2px;\r\n      animation: wave 1.5s ease-in-out infinite;\r\n\r\n      &:nth-child(1) { height: 8px; animation-delay: 0s; }\r\n      &:nth-child(2) { height: 12px; animation-delay: 0.1s; }\r\n      &:nth-child(3) { height: 16px; animation-delay: 0.2s; }\r\n      &:nth-child(4) { height: 10px; animation-delay: 0.3s; }\r\n      &:nth-child(5) { height: 14px; animation-delay: 0.4s; }\r\n      &:nth-child(6) { height: 18px; animation-delay: 0.5s; }\r\n      &:nth-child(7) { height: 12px; animation-delay: 0.6s; }\r\n      &:nth-child(8) { height: 8px; animation-delay: 0.7s; }\r\n      &:nth-child(9) { height: 15px; animation-delay: 0.8s; }\r\n      &:nth-child(10) { height: 11px; animation-delay: 0.9s; }\r\n      &:nth-child(11) { height: 9px; animation-delay: 1s; }\r\n      &:nth-child(12) { height: 13px; animation-delay: 1.1s; }\r\n    }\r\n  }\r\n\r\n  .play-overlay-audio {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    background: rgba(0, 0, 0, 0.7);\r\n    border-radius: 50%;\r\n    width: 40px;\r\n    height: 40px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: white;\r\n    font-size: 16px;\r\n    opacity: 1;\r\n    transition: all 0.3s ease;\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      background: rgba(0, 0, 0, 0.9);\r\n      transform: translate(-50%, -50%) scale(1.1);\r\n    }\r\n\r\n    &.playing {\r\n      background: rgba(255, 0, 0, 0.8);\r\n\r\n      &:hover {\r\n        background: rgba(255, 0, 0, 1);\r\n      }\r\n    }\r\n\r\n    &.visible {\r\n      opacity: 1;\r\n      visibility: visible;\r\n    }\r\n\r\n    &.hidden {\r\n      opacity: 0;\r\n      visibility: hidden;\r\n    }\r\n  }\r\n\r\n  .duration-badge {\r\n    position: absolute;\r\n    bottom: 8px;\r\n    right: 8px;\r\n    background: rgba(0, 0, 0, 0.7);\r\n    color: white;\r\n    padding: 2px 6px;\r\n    border-radius: 4px;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n@keyframes wave {\r\n  0%, 100% { transform: scaleY(0.5); }\r\n  50% { transform: scaleY(1); }\r\n}\r\n\r\n/* 图片缩略图样式 */\r\n.image-thumbnail {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  cursor: pointer;\r\n  overflow: hidden;\r\n\r\n  &:hover {\r\n    .thumbnail-image {\r\n      transform: scale(1.05);\r\n    }\r\n\r\n    .image-overlay {\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  .thumbnail-image {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n    transition: transform 0.3s ease;\r\n  }\r\n\r\n  .image-overlay {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.4);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    opacity: 0;\r\n    transition: opacity 0.3s ease;\r\n\r\n    i {\r\n      color: white;\r\n      font-size: 24px;\r\n    }\r\n  }\r\n}\r\n\r\n/* 文件图标缩略图样式 */\r\n.file-icon-thumbnail {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 48px;\r\n  color: #909399;\r\n}\r\n\r\n\r\n\r\n/* 文件信息样式 */\r\n.file-card .file-info {\r\n  padding: 12px;\r\n\r\n  .file-name {\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    color: #303133;\r\n    margin-bottom: 4px;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n  }\r\n\r\n  .file-meta {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    font-size: 12px;\r\n    color: #909399;\r\n\r\n    .file-size {\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n/* 文件预览对话框样式 */\r\n.preview-dialog {\r\n  .el-dialog__body {\r\n    padding: 20px;\r\n  }\r\n}\r\n\r\n.preview-content {\r\n  .preview-header {\r\n    margin-bottom: 20px;\r\n\r\n    h3 {\r\n      margin: 0 0 10px 0;\r\n      color: #303133;\r\n      font-size: 18px;\r\n    }\r\n\r\n    .file-info {\r\n      display: flex;\r\n      gap: 20px;\r\n      color: #909399;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  .video-preview {\r\n    text-align: center;\r\n\r\n    video {\r\n      border-radius: 8px;\r\n      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    }\r\n  }\r\n\r\n  .audio-preview {\r\n    .audio-player {\r\n      margin-bottom: 20px;\r\n      text-align: center;\r\n    }\r\n\r\n    .audio-info {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 20px;\r\n      background-color: #f8f9fa;\r\n      border-radius: 8px;\r\n\r\n      .audio-icon {\r\n        font-size: 48px;\r\n        margin-right: 20px;\r\n      }\r\n\r\n      .audio-details {\r\n        p {\r\n          margin: 5px 0;\r\n          color: #606266;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .image-preview {\r\n    text-align: center;\r\n\r\n    img {\r\n      border-radius: 8px;\r\n      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    }\r\n  }\r\n\r\n  .unsupported-preview {\r\n    text-align: center;\r\n    padding: 40px;\r\n\r\n    .unsupported-icon {\r\n      font-size: 64px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    p {\r\n      color: #909399;\r\n      margin-bottom: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n/* OSS未配置提示样式 */\r\n.no-oss-tip {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40px 20px;\r\n  text-align: center;\r\n  color: #909399;\r\n\r\n  .tip-icon {\r\n    font-size: 48px;\r\n    margin-bottom: 16px;\r\n  }\r\n\r\n  .tip-text {\r\n    font-size: 14px;\r\n    margin-bottom: 16px;\r\n    color: #606266;\r\n  }\r\n}\r\n\r\n.no-oss-files-tip {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100%;\r\n  min-height: 400px;\r\n\r\n  .tip-content {\r\n    text-align: center;\r\n\r\n    .tip-icon {\r\n      font-size: 64px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .tip-title {\r\n      font-size: 18px;\r\n      font-weight: 500;\r\n      color: #303133;\r\n      margin-bottom: 12px;\r\n    }\r\n\r\n    .tip-description {\r\n      font-size: 14px;\r\n      color: #909399;\r\n      margin-bottom: 24px;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .folder-sidebar {\r\n    width: 150px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .materials-main {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .folder-sidebar {\r\n    width: 100%;\r\n    height: 200px;\r\n    border-right: none;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .files-toolbar {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 10px;\r\n  }\r\n\r\n  .toolbar-right {\r\n    align-self: flex-end;\r\n  }\r\n}\r\n</style>\r\n"]}]}