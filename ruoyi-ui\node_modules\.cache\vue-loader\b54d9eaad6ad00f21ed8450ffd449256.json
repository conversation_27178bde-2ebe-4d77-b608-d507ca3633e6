{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\editor.vue?vue&type=template&id=2fd681f5&scoped=true", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\editor.vue", "mtime": 1755003111667}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753759474020}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}