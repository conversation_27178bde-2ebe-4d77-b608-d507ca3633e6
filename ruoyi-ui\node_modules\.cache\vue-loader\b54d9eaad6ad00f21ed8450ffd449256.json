{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\editor.vue?vue&type=template&id=2fd681f5&scoped=true", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\editor.vue", "mtime": 1755003926731}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753759474020}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}