{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\editor.vue?vue&type=template&id=2fd681f5&scoped=true", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\editor.vue", "mtime": 1755002651803}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753759474020}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}