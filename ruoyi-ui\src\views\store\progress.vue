<template>
  <div class="video-progress">
    <!-- 头部工具栏 -->
    <div class="header-toolbar">
      <div class="toolbar-left">
        <el-button icon="el-icon-arrow-left" @click="goBack">返回</el-button>
        <h2 class="page-title">
          <i class="el-icon-video-camera"></i>
          剪辑进度 - {{ templateInfo.name }}
        </h2>
      </div>
      <div class="toolbar-right">
        <el-button icon="el-icon-refresh" @click="refreshProgress">
          刷新
        </el-button>
        <el-button type="primary" icon="el-icon-plus" @click="startNewBatch">
          新建批次
        </el-button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon completed">
          <i class="el-icon-check"></i>
        </div>
        <div class="stat-info">
          <div class="stat-number">{{ stats.completed }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon processing">
          <i class="el-icon-loading"></i>
        </div>
        <div class="stat-info">
          <div class="stat-number">{{ stats.processing }}</div>
          <div class="stat-label">处理中</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon failed">
          <i class="el-icon-close"></i>
        </div>
        <div class="stat-info">
          <div class="stat-number">{{ stats.failed }}</div>
          <div class="stat-label">失败</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon total">
          <i class="el-icon-document"></i>
        </div>
        <div class="stat-info">
          <div class="stat-number">{{ stats.total }}</div>
          <div class="stat-label">总计</div>
        </div>
      </div>
    </div>

    <!-- 视频列表 -->
    <div class="videos-container">
      <div class="container-header">
        <h3>生成的视频</h3>
        <div class="filter-controls">
          <el-select v-model="statusFilter" placeholder="状态筛选" size="small" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="已完成" value="completed"></el-option>
            <el-option label="处理中" value="processing"></el-option>
            <el-option label="失败" value="failed"></el-option>
          </el-select>
        </div>
      </div>

      <div class="videos-grid">
        <div 
          v-for="video in filteredVideos" 
          :key="video.id"
          class="video-card"
          :class="video.status"
        >
          <!-- 视频缩略图 -->
          <div class="video-thumbnail">
            <img v-if="video.thumbnail" :src="video.thumbnail" :alt="video.name" />
            <div v-else class="thumbnail-placeholder">
              <i class="el-icon-video-camera"></i>
            </div>
            
            <!-- 状态覆盖层 -->
            <div class="status-overlay" v-if="video.status !== 'completed'">
              <div class="status-content">
                <i v-if="video.status === 'processing'" class="el-icon-loading rotating"></i>
                <i v-else-if="video.status === 'failed'" class="el-icon-warning-outline"></i>
                <p class="status-text">{{ getStatusText(video.status) }}</p>
                <div v-if="video.status === 'processing'" class="progress-bar">
                  <div class="progress-fill" :style="{ width: video.progress + '%' }"></div>
                </div>
              </div>
            </div>

            <!-- 播放按钮 -->
            <div v-if="video.status === 'completed'" class="play-button" @click="playVideo(video)">
              <i class="el-icon-video-play"></i>
            </div>

            <!-- 视频时长 -->
            <div v-if="video.duration" class="video-duration">{{ video.duration }}</div>
          </div>

          <!-- 视频信息 -->
          <div class="video-info">
            <h4 class="video-name">{{ video.name }}</h4>
            <div class="video-meta">
              <span class="create-time">{{ video.createTime }}</span>
              <span class="video-size" v-if="video.fileSize">{{ video.fileSize }}</span>
            </div>
            
            <!-- 操作按钮 -->
            <div class="video-actions" v-if="video.status === 'completed'">
              <el-button size="mini" icon="el-icon-download" @click="downloadVideo(video)">
                下载
              </el-button>
              <el-button size="mini" icon="el-icon-share" @click="shareVideo(video)">
                分享
              </el-button>
              <el-button size="mini" icon="el-icon-delete" type="danger" @click="deleteVideo(video)">
                删除
              </el-button>
            </div>
            
            <!-- 重试按钮 -->
            <div class="video-actions" v-if="video.status === 'failed'">
              <el-button size="mini" icon="el-icon-refresh" type="primary" @click="retryVideo(video)">
                重试
              </el-button>
              <el-button size="mini" icon="el-icon-delete" type="danger" @click="deleteVideo(video)">
                删除
              </el-button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredVideos.length === 0" class="empty-state">
          <i class="el-icon-video-camera-solid"></i>
          <h3>暂无视频</h3>
          <p>还没有生成任何视频，点击"新建批次"开始剪辑</p>
          <el-button type="primary" @click="startNewBatch">
            新建批次
          </el-button>
        </div>
      </div>
    </div>

    <!-- 新建批次对话框 -->
    <el-dialog
      title="新建剪辑批次"
      :visible.sync="batchDialogVisible"
      width="400px"
    >
      <div class="batch-config">
        <h4>{{ templateInfo.name }}</h4>
        <p>请选择要生成的视频数量：</p>
        <el-input-number
          v-model="batchCount"
          :min="1"
          :max="50"
          label="生成数量"
        ></el-input-number>
        <p class="batch-tip">最多可生成50条视频</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmNewBatch">开始剪辑</el-button>
      </div>
    </el-dialog>

    <!-- 视频播放对话框 -->
    <el-dialog
      title="视频预览"
      :visible.sync="playDialogVisible"
      width="800px"
      @close="stopVideo"
    >
      <div class="video-player" v-if="currentVideo">
        <video 
          ref="videoPlayer"
          :src="currentVideo.url" 
          controls 
          width="100%"
          @loadedmetadata="onVideoLoaded"
        ></video>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'VideoProgress',
  data() {
    return {
      // 模板信息
      templateInfo: {
        id: '',
        name: '视频模板'
      },
      
      // 对话框状态
      batchDialogVisible: false,
      playDialogVisible: false,
      
      // 批量剪辑数量
      batchCount: 1,
      
      // 当前播放视频
      currentVideo: null,
      
      // 状态筛选
      statusFilter: '',
      
      // 统计数据
      stats: {
        completed: 8,
        processing: 2,
        failed: 1,
        total: 11
      },
      
      // 视频列表
      videos: [
        {
          id: 'v001',
          name: '产品展示视频_001',
          status: 'completed',
          thumbnail: 'https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=Video+1',
          url: 'https://example.com/video1.mp4',
          duration: '00:32',
          fileSize: '15.2MB',
          createTime: '2025-01-18 14:30',
          progress: 100
        },
        {
          id: 'v002',
          name: '产品展示视频_002',
          status: 'completed',
          thumbnail: 'https://via.placeholder.com/300x200/50C878/FFFFFF?text=Video+2',
          url: 'https://example.com/video2.mp4',
          duration: '00:28',
          fileSize: '12.8MB',
          createTime: '2025-01-18 14:32',
          progress: 100
        },
        {
          id: 'v003',
          name: '产品展示视频_003',
          status: 'processing',
          thumbnail: null,
          url: null,
          duration: null,
          fileSize: null,
          createTime: '2025-01-18 14:35',
          progress: 65
        },
        {
          id: 'v004',
          name: '产品展示视频_004',
          status: 'failed',
          thumbnail: null,
          url: null,
          duration: null,
          fileSize: null,
          createTime: '2025-01-18 14:36',
          progress: 0
        }
      ]
    }
  },
  
  computed: {
    // 筛选后的视频列表
    filteredVideos() {
      if (!this.statusFilter) {
        return this.videos
      }
      return this.videos.filter(video => video.status === this.statusFilter)
    }
  },
  
  created() {
    // 获取模板ID
    const templateId = this.$route.query.templateId
    if (templateId) {
      this.loadTemplate(templateId)
    }
    
    // 定时刷新处理中的视频状态
    this.startProgressPolling()
  },
  
  beforeDestroy() {
    // 清理定时器
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer)
    }
  },
  
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },
    
    // 加载模板信息
    loadTemplate(templateId) {
      this.templateInfo.id = templateId
      // TODO: 从API加载模板数据
      console.log('加载模板:', templateId)
    },
    
    // 刷新进度
    refreshProgress() {
      this.$message.success('进度已刷新')
      // TODO: 调用API刷新数据
    },
    
    // 开始新批次
    startNewBatch() {
      this.batchDialogVisible = true
    },
    
    // 确认新建批次
    confirmNewBatch() {
      this.$message.success(`开始生成 ${this.batchCount} 条新视频`)
      this.batchDialogVisible = false
      
      // TODO: 调用批量剪辑API
      // 模拟添加新的处理中视频
      for (let i = 0; i < this.batchCount; i++) {
        const newVideo = {
          id: 'v' + Date.now() + i,
          name: `产品展示视频_${this.videos.length + i + 1}`,
          status: 'processing',
          thumbnail: null,
          url: null,
          duration: null,
          fileSize: null,
          createTime: new Date().toLocaleString().slice(0, 16),
          progress: 0
        }
        this.videos.unshift(newVideo)
      }
      
      // 更新统计
      this.updateStats()
    },
    
    // 播放视频
    playVideo(video) {
      this.currentVideo = video
      this.playDialogVisible = true
    },
    
    // 停止视频
    stopVideo() {
      if (this.$refs.videoPlayer) {
        this.$refs.videoPlayer.pause()
      }
      this.currentVideo = null
    },
    
    // 视频加载完成
    onVideoLoaded() {
      // 视频元数据加载完成
    },
    
    // 下载视频
    downloadVideo(video) {
      this.$message.success(`开始下载: ${video.name}`)
      // TODO: 实现下载逻辑
    },
    
    // 分享视频
    shareVideo(video) {
      this.$message.success(`分享链接已复制: ${video.name}`)
      // TODO: 实现分享逻辑
    },
    
    // 删除视频
    deleteVideo(video) {
      this.$confirm(`确定要删除视频 "${video.name}" 吗？`, '确认删除', {
        type: 'warning'
      }).then(() => {
        const index = this.videos.findIndex(v => v.id === video.id)
        if (index > -1) {
          this.videos.splice(index, 1)
          this.updateStats()
          this.$message.success('删除成功')
        }
      }).catch(() => {})
    },
    
    // 重试视频
    retryVideo(video) {
      video.status = 'processing'
      video.progress = 0
      this.updateStats()
      this.$message.success(`重新开始处理: ${video.name}`)
      // TODO: 调用重试API
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'completed': '已完成',
        'processing': '处理中',
        'failed': '处理失败'
      }
      return statusMap[status] || status
    },
    
    // 更新统计数据
    updateStats() {
      this.stats = {
        completed: this.videos.filter(v => v.status === 'completed').length,
        processing: this.videos.filter(v => v.status === 'processing').length,
        failed: this.videos.filter(v => v.status === 'failed').length,
        total: this.videos.length
      }
    },
    
    // 开始轮询进度
    startProgressPolling() {
      this.pollingTimer = setInterval(() => {
        // 模拟进度更新
        this.videos.forEach(video => {
          if (video.status === 'processing' && video.progress < 100) {
            video.progress += Math.random() * 10
            if (video.progress >= 100) {
              video.status = 'completed'
              video.progress = 100
              video.thumbnail = `https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=Video+${video.id}`
              video.url = `https://example.com/${video.id}.mp4`
              video.duration = '00:' + (25 + Math.floor(Math.random() * 20))
              video.fileSize = (10 + Math.random() * 10).toFixed(1) + 'MB'
              this.updateStats()
            }
          }
        })
      }, 3000)
    }
  }
}
</script>

<style scoped>
.video-progress {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 头部工具栏 */
.header-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.page-title {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.page-title i {
  margin-right: 8px;
  color: #409EFF;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stat-icon.completed {
  background: #67C23A;
}

.stat-icon.processing {
  background: #409EFF;
}

.stat-icon.failed {
  background: #F56C6C;
}

.stat-icon.total {
  background: #909399;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

/* 视频容器 */
.videos-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.container-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e4e7ed;
}

.container-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

/* 视频网格 */
.videos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  padding: 24px;
}

/* 视频卡片 */
.video-card {
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.video-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 视频缩略图 */
.video-thumbnail {
  position: relative;
  width: 100%;
  height: 160px;
  background: #000;
  overflow: hidden;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ddd;
  color: #999;
}

.thumbnail-placeholder i {
  font-size: 32px;
}

/* 状态覆盖层 */
.status-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.status-content {
  text-align: center;
}

.status-content i {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}

.status-text {
  margin: 0 0 12px 0;
  font-size: 14px;
}

.progress-bar {
  width: 120px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
  margin: 0 auto;
}

.progress-fill {
  height: 100%;
  background: #409EFF;
  transition: width 0.3s ease;
}

/* 播放按钮 */
.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 48px;
  height: 48px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-card:hover .play-button {
  opacity: 1;
}

.play-button:hover {
  background: rgba(0, 0, 0, 0.9);
}

/* 视频时长 */
.video-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

/* 视频信息 */
.video-info {
  padding: 16px;
}

.video-name {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 12px;
  color: #909399;
}

.video-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 旋转动画 */
.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 空状态 */
.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
}

.empty-state i {
  font-size: 48px;
  color: #C0C4CC;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.empty-state p {
  margin: 0 0 20px 0;
  color: #909399;
  font-size: 14px;
}

/* 批量配置 */
.batch-config {
  text-align: center;
  padding: 20px 0;
}

.batch-config h4 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 18px;
}

.batch-config p {
  margin: 0 0 16px 0;
  color: #606266;
}

.batch-tip {
  margin-top: 12px !important;
  color: #909399;
  font-size: 12px;
}

/* 视频播放器 */
.video-player {
  text-align: center;
}

.video-player video {
  max-width: 100%;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-progress {
    padding: 15px;
  }

  .header-toolbar {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .toolbar-right {
    justify-content: center;
  }

  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .videos-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
