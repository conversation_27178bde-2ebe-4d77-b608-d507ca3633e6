{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\up.vue?vue&type=template&id=ee083fba&scoped=true", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\up.vue", "mtime": 1754971237942}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753759474020}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}