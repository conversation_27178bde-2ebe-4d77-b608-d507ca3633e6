{"remainingRequest": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\editor.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\src\\views\\store\\editor.vue", "mtime": 1755002651803}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753759483709}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753759481353}, {"path": "E:\\ry-vue-flowable-xg-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753759473978}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnVmlkZW9FZGl0b3InLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDmqKHmnb/kv6Hmga8KICAgICAgdGVtcGxhdGVJbmZvOiB7CiAgICAgICAgaWQ6ICcnLAogICAgICAgIG5hbWU6ICfop4bpopHmqKHmnb8nLAogICAgICAgIHJlc29sdXRpb246ICcxMDgweDE5MjAnLAogICAgICAgIGZwczogJzMwJwogICAgICB9LAogICAgICAKICAgICAgLy8g5om56YeP5Ymq6L6RCiAgICAgIGJhdGNoRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGJhdGNoQ291bnQ6IDEsCgogICAgICAvLyDlnLrmma/nrqHnkIblr7nor53moYYKICAgICAgYWRkU2NlbmVEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgc2NlbmVGb3JtOiB7CiAgICAgICAgbmFtZTogJycsCiAgICAgICAgZHVyYXRpb246IDEwLAogICAgICAgIHZpZGVvTGlicmFyeUlkOiAnJwogICAgICB9LAoKICAgICAgLy8g6KeG6aKR5bqT6YCJ5oup5a+56K+d5qGGCiAgICAgIHZpZGVvTGlicmFyeURpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBzZWxlY3RlZFZpZGVvTGlicmFyeTogbnVsbCwKCiAgICAgIC8vIOWxnuaAp+mdouadvwogICAgICBhY3RpdmVDb2xsYXBzZTogWyd2aWRlbyddLAoKICAgICAgLy8g6K6+572u6aG5CiAgICAgIHZpZGVvU2V0dGluZ3M6IHsKICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjMDAwMDAwJywKICAgICAgICBicmlnaHRuZXNzOiAwLAogICAgICAgIGNvbnRyYXN0OiAwLAogICAgICAgIHNhdHVyYXRpb246IDAKICAgICAgfSwKCiAgICAgIGF1ZGlvU2V0dGluZ3M6IHsKICAgICAgICBtYXN0ZXJWb2x1bWU6IDgwLAogICAgICAgIGJnbVZvbHVtZTogNjAsCiAgICAgICAgc2Z4Vm9sdW1lOiA3MAogICAgICB9LAoKICAgICAgdGV4dFNldHRpbmdzOiB7CiAgICAgICAgZm9udEZhbWlseTogJ01pY3Jvc29mdCBZYUhlaScsCiAgICAgICAgZm9udFNpemU6IDI0LAogICAgICAgIGNvbG9yOiAnI0ZGRkZGRicKICAgICAgfSwKCiAgICAgIC8vIOmAieS4reeahOWFg+e0oAogICAgICBzZWxlY3RlZENsaXA6IG51bGwsCiAgICAgIHNlbGVjdGVkU2NlbmU6IG51bGwsCiAgICAgIHNlbGVjdGVkVGV4dFNlZ21lbnQ6IG51bGwsCgogICAgICAvLyDml7bpl7TovbTorr7nva4KICAgICAgdGltZWxpbmVEdXJhdGlvbjogNjAsIC8vIOaXtumXtOi9tOaAu+mVv+W6pjYw56eSCiAgICAgIHBpeGVsc1BlclNlY29uZDogMTAsIC8vIOavj+enkjEw5YOP57Sg77yMNjDnp5IgPSA2MDDlg4/ntKDvvIjnqIDnlo/mmL7npLrvvIkKICAgICAgY3VycmVudFRpbWU6IDAsIC8vIOW9k+WJjeaSreaUvuaXtumXtO+8iOenku+8iQogICAgICAKICAgICAgLy8g5Zy65pmv5YiX6KGoCiAgICAgIHNjZW5lczogWwogICAgICAgIHsKICAgICAgICAgIGlkOiAnc2NlbmUxJywKICAgICAgICAgIG5hbWU6ICflvIDlnLrlnLrmma8nLAogICAgICAgICAgc3RhcnRUaW1lOiAwLAogICAgICAgICAgZHVyYXRpb246IDgsCiAgICAgICAgICBtYXhEdXJhdGlvbjogMTIsIC8vIOinhumikee0oOadkOeahOacgOWkp+aXtumVvwogICAgICAgICAgdmlkZW9MaWJyYXJ5SWQ6ICdsaWJf5byA5Zy6JywKICAgICAgICAgIHRleHRTZWdtZW50OiAn5qyi6L+O5p2l5Yiw5oiR5Lus55qE5Lqn5ZOB5bGV56S677yM5LuK5aSp5Li65aSn5a625LuL57uN5LiA5qy+6Z2p5ZG95oCn55qE5Lqn5ZOB44CCJywKICAgICAgICAgIGN1cnJlbnRWaWRlbzogbnVsbAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6ICdzY2VuZTInLAogICAgICAgICAgbmFtZTogJ+S6p+WTgeS7i+e7jScsCiAgICAgICAgICBzdGFydFRpbWU6IDgsCiAgICAgICAgICBkdXJhdGlvbjogMTAsCiAgICAgICAgICBtYXhEdXJhdGlvbjogMTUsIC8vIOinhumikee0oOadkOeahOacgOWkp+aXtumVvwogICAgICAgICAgdmlkZW9MaWJyYXJ5SWQ6ICdsaWJf5Lqn5ZOB5LuL57uNJywKICAgICAgICAgIHRleHRTZWdtZW50OiAn6L+Z5qy+5Lqn5ZOB5YW35pyJ54us54m555qE6K6+6K6h55CG5b+15ZKM5YWI6L+b55qE5oqA5pyv77yM6IO95aSf5Li655So5oi35bim5p2l5YWo5paw55qE5L2T6aqM44CCJywKICAgICAgICAgIGN1cnJlbnRWaWRlbzogbnVsbAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6ICdzY2VuZTMnLAogICAgICAgICAgbmFtZTogJ+WKn+iDveWxleekuicsCiAgICAgICAgICBzdGFydFRpbWU6IDE4LAogICAgICAgICAgZHVyYXRpb246IDksCiAgICAgICAgICBtYXhEdXJhdGlvbjogMjAsIC8vIOinhumikee0oOadkOeahOacgOWkp+aXtumVvwogICAgICAgICAgdmlkZW9MaWJyYXJ5SWQ6ICdsaWJf5Yqf6IO95bGV56S6JywKICAgICAgICAgIHRleHRTZWdtZW50OiAn6K6p5oiR5Lus5p2l55yL55yL5a6D55qE5qC45b+D5Yqf6IO95ZKM5L2/55So5pa55rOV44CCJywKICAgICAgICAgIGN1cnJlbnRWaWRlbzogbnVsbAogICAgICAgIH0KICAgICAgXSwKCiAgICAgIC8vIOS7jnVw6aG16Z2i6I635Y+W55qE57Sg5p2Q5bqT77yI6KeG6aKR5bqT77yJCiAgICAgIHN1Y2FpTGlzdDogW10sIC8vIOS7jnVw6aG16Z2i55qE57Sg5p2Q5YiX6KGo6I635Y+WCgogICAgICAvLyDop4bpopHlupPliIbnsbvvvIjln7rkuo7mlofku7blpLnvvIkKICAgICAgdmlkZW9MaWJyYXJpZXM6IFtdLAoKICAgICAgLy8gQUnmlofmoYjlupMKICAgICAgdGV4dExpYnJhcnk6IHsKICAgICAgICBpZDogJ3RleHQxJywKICAgICAgICBuYW1lOiAnQUnliarovpHmlofmoYjlupMnLAogICAgICAgIGZ1bGxUZXh0OiAn5qyi6L+O5p2l5Yiw5oiR5Lus55qE5Lqn5ZOB5bGV56S677yM5LuK5aSp5Li65aSn5a625LuL57uN5LiA5qy+6Z2p5ZG95oCn55qE5Lqn5ZOB44CC6L+Z5qy+5Lqn5ZOB5YW35pyJ54us54m555qE6K6+6K6h55CG5b+15ZKM5YWI6L+b55qE5oqA5pyv77yM6IO95aSf5Li655So5oi35bim5p2l5YWo5paw55qE5L2T6aqM44CC6K6p5oiR5Lus5p2l55yL55yL5a6D55qE5qC45b+D5Yqf6IO95ZKM5L2/55So5pa55rOV44CC6YCa6L+H566A5Y2V55qE5pON5L2c77yM5oKo5bCx6IO95L2T6aqM5Yiw5YmN5omA5pyq5pyJ55qE5L6/5Yip5oCn44CCJywKICAgICAgICBzZWdtZW50czogW10gLy8g5bCG6YCa6L+H6Zi/6YeM5LqR5o6l5Y+j5YiG5q61CiAgICAgIH0sCgogICAgICAvLyDovajpgZPmlbDmja7vvIjnroDljJbniYjvvIkKICAgICAgdHJhY2tzOiBbCiAgICAgICAgewogICAgICAgICAgaWQ6ICd2aWRlbycsCiAgICAgICAgICBuYW1lOiAn6KeG6aKR5Zy65pmvJywKICAgICAgICAgIHR5cGU6ICd2aWRlbycsCiAgICAgICAgICBpY29uOiAnZWwtaWNvbi12aWRlby1jYW1lcmEnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpZDogJ3RleHQnLAogICAgICAgICAgbmFtZTogJ+aWh+ahiCcsCiAgICAgICAgICB0eXBlOiAndGV4dCcsCiAgICAgICAgICBpY29uOiAnZWwtaWNvbi1lZGl0JwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWQ6ICdtdXNpYycsCiAgICAgICAgICBuYW1lOiAn6IOM5pmv6Z+z5LmQJywKICAgICAgICAgIHR5cGU6ICdhdWRpbycsCiAgICAgICAgICBpY29uOiAnZWwtaWNvbi1oZWFkc2V0JywKICAgICAgICAgIGNsaXBzOiBbCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICBpZDogJ20xJywKICAgICAgICAgICAgICBuYW1lOiAn6IOM5pmv6Z+z5LmQLm1wMycsCiAgICAgICAgICAgICAgc3RhcnRUaW1lOiAwLAogICAgICAgICAgICAgIGR1cmF0aW9uOiA0NSwgLy8g5b2T5YmN5L2/55So5pe26ZW/CiAgICAgICAgICAgICAgbWF4RHVyYXRpb246IDE4MCwgLy8g6Z+z6aKR5paH5Lu255qE5a6e6ZmF5pe26ZW/77yIM+WIhumSn++8iQogICAgICAgICAgICAgIG9yaWdpbmFsRHVyYXRpb246IDE4MCwgLy8g5Y6f5aeL5paH5Lu25pe26ZW/CiAgICAgICAgICAgICAgdHlwZTogJ2F1ZGlvJwogICAgICAgICAgICB9CiAgICAgICAgICBdCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpZDogJ3NvdW5kJywKICAgICAgICAgIG5hbWU6ICfpn7PmlYgnLAogICAgICAgICAgdHlwZTogJ2F1ZGlvJywKICAgICAgICAgIGljb246ICdlbC1pY29uLWJlbGwnLAogICAgICAgICAgY2xpcHM6IFsKICAgICAgICAgICAgewogICAgICAgICAgICAgIGlkOiAnczEnLAogICAgICAgICAgICAgIG5hbWU6ICfngrnlh7vpn7PmlYgud2F2JywKICAgICAgICAgICAgICBzdGFydFRpbWU6IDgsCiAgICAgICAgICAgICAgZHVyYXRpb246IDEsCiAgICAgICAgICAgICAgbWF4RHVyYXRpb246IDIsIC8vIOmfs+aViOaWh+S7tuaXtumVvwogICAgICAgICAgICAgIG9yaWdpbmFsRHVyYXRpb246IDIsCiAgICAgICAgICAgICAgdHlwZTogJ2F1ZGlvJwogICAgICAgICAgICB9LAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgaWQ6ICdzMicsCiAgICAgICAgICAgICAgbmFtZTogJ+i9rOWcuumfs+aViC53YXYnLAogICAgICAgICAgICAgIHN0YXJ0VGltZTogMTgsCiAgICAgICAgICAgICAgZHVyYXRpb246IDIsCiAgICAgICAgICAgICAgbWF4RHVyYXRpb246IDMsCiAgICAgICAgICAgICAgb3JpZ2luYWxEdXJhdGlvbjogMywKICAgICAgICAgICAgICB0eXBlOiAnYXVkaW8nCiAgICAgICAgICAgIH0KICAgICAgICAgIF0KICAgICAgICB9CiAgICAgIF0KICAgIH0KICB9LAoKICBjb21wdXRlZDogewogICAgLy8g5pe26Ze06L205oC75a695bqmCiAgICB0aW1lbGluZVdpZHRoKCkgewogICAgICByZXR1cm4gdGhpcy50aW1lbGluZUR1cmF0aW9uICogdGhpcy5waXhlbHNQZXJTZWNvbmQKICAgIH0KICB9LAoKICBjcmVhdGVkKCkgewogICAgLy8g6I635Y+W5qih5p2/SUQKICAgIGNvbnN0IHRlbXBsYXRlSWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS50ZW1wbGF0ZUlkCiAgICBpZiAodGVtcGxhdGVJZCkgewogICAgICB0aGlzLmxvYWRUZW1wbGF0ZSh0ZW1wbGF0ZUlkKQogICAgfQoKICAgIC8vIOWKoOi9vee0oOadkOW6kwogICAgdGhpcy5sb2FkTWF0ZXJpYWxzRnJvbVVwUGFnZSgpCgogICAgLy8g5Yid5aeL5YyW5Zy65pmv6KeG6aKRCiAgICB0aGlzLmluaXRpYWxpemVTY2VuZVZpZGVvcygpCiAgfSwKICAKICBtZXRob2RzOiB7CiAgICAvLyDov5Tlm57kuIrkuIDpobUKICAgIGdvQmFjaygpIHsKICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKQogICAgfSwKICAgIAogICAgLy8g5Yqg6L295qih5p2/CiAgICBsb2FkVGVtcGxhdGUodGVtcGxhdGVJZCkgewogICAgICAvLyBUT0RPOiDku45BUEnliqDovb3mqKHmnb/mlbDmja4KICAgICAgdGhpcy50ZW1wbGF0ZUluZm8uaWQgPSB0ZW1wbGF0ZUlkCiAgICAgIGNvbnNvbGUubG9nKCfliqDovb3mqKHmnb86JywgdGVtcGxhdGVJZCkKICAgIH0sCiAgICAKICAgIC8vIOS/neWtmOaooeadvwogICAgc2F2ZVRlbXBsYXRlKCkgewogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aooeadv+S/neWtmOaIkOWKnycpCiAgICAgIC8vIFRPRE86IOiwg+eUqOS/neWtmEFQSQogICAgfSwKICAgIAogICAgLy8g5pi+56S65om56YeP5Ymq6L6R5a+56K+d5qGGCiAgICBzaG93QmF0Y2hEaWFsb2coKSB7CiAgICAgIHRoaXMuYmF0Y2hEaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgfSwKICAgIAogICAgLy8g5byA5aeL5om56YeP5Ymq6L6RCiAgICBzdGFydEJhdGNoQ2xpcCgpIHsKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDlvIDlp4vnlJ/miJAgJHt0aGlzLmJhdGNoQ291bnR9IOadoeinhumikWApCiAgICAgIHRoaXMuYmF0Y2hEaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgICAgCiAgICAgIC8vIOi3s+i9rOWIsOi/m+W6pumhtemdogogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgcGF0aDogJy9zdG9yZXIvcHJvZ3Jlc3MnLAogICAgICAgIHF1ZXJ5OiB7IHRlbXBsYXRlSWQ6IHRoaXMudGVtcGxhdGVJbmZvLmlkIH0KICAgICAgfSkKICAgIH0sCiAgICAKICAgIC8vIOmAieaLqeeJh+autQogICAgc2VsZWN0Q2xpcChjbGlwKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRDbGlwID0gY2xpcAogICAgfSwKICAgIAogICAgLy8g5qC85byP5YyW5pe26Ze0CiAgICBmb3JtYXRUaW1lKHNlY29uZHMpIHsKICAgICAgY29uc3QgbWlucyA9IE1hdGguZmxvb3Ioc2Vjb25kcyAvIDYwKQogICAgICBjb25zdCBzZWNzID0gc2Vjb25kcyAlIDYwCiAgICAgIHJldHVybiBgJHttaW5zLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgJzAnKX06JHtzZWNzLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgJzAnKX1gCiAgICB9LAogICAgCiAgICAvLyDojrflj5bniYfmrrXmoLflvI8KICAgIGdldENsaXBTdHlsZShjbGlwKSB7CiAgICAgIHJldHVybiB7CiAgICAgICAgbGVmdDogY2xpcC5zdGFydFRpbWUgKiB0aGlzLnBpeGVsc1BlclNlY29uZCArICdweCcsCiAgICAgICAgd2lkdGg6IGNsaXAuZHVyYXRpb24gKiB0aGlzLnBpeGVsc1BlclNlY29uZCArICdweCcKICAgICAgfQogICAgfSwKICAgIAogICAgLy8g5ouW5ou95byA5aeLCiAgICBvbkRyYWdTdGFydChldmVudCwgY2xpcCkgewogICAgICBldmVudC5kYXRhVHJhbnNmZXIuc2V0RGF0YSgndGV4dC9wbGFpbicsIEpTT04uc3RyaW5naWZ5KGNsaXApKQogICAgfSwKICAgIAogICAgLy8g5ouW5ou95pS+572uCiAgICBvbkRyb3AoZXZlbnQsIHRyYWNrKSB7CiAgICAgIGNvbnN0IGNsaXBEYXRhID0gSlNPTi5wYXJzZShldmVudC5kYXRhVHJhbnNmZXIuZ2V0RGF0YSgndGV4dC9wbGFpbicpKQogICAgICBjb25zb2xlLmxvZygn5ouW5ou95Yiw6L2o6YGTOicsIHRyYWNrLm5hbWUsIGNsaXBEYXRhKQogICAgICAvLyBUT0RPOiDlrp7njrDmi5bmi73pgLvovpEKICAgIH0sCiAgICAKICAgIC8vIOW8gOWni+iwg+aVtOWkp+WwjwogICAgc3RhcnRSZXNpemUoZXZlbnQsIGNsaXAsIGRpcmVjdGlvbikgewogICAgICBjb25zb2xlLmxvZygn6LCD5pW05aSn5bCPOicsIGNsaXAubmFtZSwgZGlyZWN0aW9uKQoKICAgICAgLy8g5qOA5p+l5piv5ZCm5Y+v5Lul6LCD5pW0CiAgICAgIGlmICghY2xpcC5tYXhEdXJhdGlvbiAmJiAhY2xpcC5vcmlnaW5hbER1cmF0aW9uKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor6XntKDmnZDmsqHmnInml7bplb/pmZDliLbkv6Hmga8nKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICBjb25zdCBtYXhBbGxvd2VkRHVyYXRpb24gPSBjbGlwLm1heER1cmF0aW9uIHx8IGNsaXAub3JpZ2luYWxEdXJhdGlvbgoKICAgICAgaWYgKGRpcmVjdGlvbiA9PT0gJ3JpZ2h0JykgewogICAgICAgIC8vIOWQkeWPs+aLluaLve+8iOWinuWKoOaXtumVv++8iQogICAgICAgIGlmIChjbGlwLmR1cmF0aW9uID49IG1heEFsbG93ZWREdXJhdGlvbikgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKGDntKDmnZDmnIDlpKfml7bplb/kuLogJHt0aGlzLmZvcm1hdFRpbWUobWF4QWxsb3dlZER1cmF0aW9uKX3vvIzml6Dms5Xnu6fnu63lu7bplb9gKQogICAgICAgICAgcmV0dXJuCiAgICAgICAgfQogICAgICB9CgogICAgICAvLyBUT0RPOiDlrp7njrDlhbfkvZPnmoTmi5bmi73osIPmlbTpgLvovpEKICAgICAgdGhpcy5zdGFydFJlc2l6ZVdpdGhMaW1pdChldmVudCwgY2xpcCwgZGlyZWN0aW9uLCBtYXhBbGxvd2VkRHVyYXRpb24pCiAgICB9LAoKICAgIC8vIOW4puaXtumVv+mZkOWItueahOiwg+aVtOWkp+WwjwogICAgc3RhcnRSZXNpemVXaXRoTGltaXQoZXZlbnQsIGNsaXAsIGRpcmVjdGlvbiwgbWF4RHVyYXRpb24pIHsKICAgICAgY29uc3Qgc3RhcnRYID0gZXZlbnQuY2xpZW50WAogICAgICBjb25zdCBzdGFydER1cmF0aW9uID0gY2xpcC5kdXJhdGlvbgogICAgICBjb25zdCBwaXhlbHNQZXJTZWNvbmQgPSB0aGlzLnBpeGVsc1BlclNlY29uZAoKICAgICAgY29uc3Qgb25Nb3VzZU1vdmUgPSAoZSkgPT4gewogICAgICAgIGNvbnN0IGRlbHRhWCA9IGUuY2xpZW50WCAtIHN0YXJ0WAogICAgICAgIGNvbnN0IGRlbHRhU2Vjb25kcyA9IGRlbHRhWCAvIHBpeGVsc1BlclNlY29uZAoKICAgICAgICBsZXQgbmV3RHVyYXRpb24gPSBzdGFydER1cmF0aW9uCgogICAgICAgIGlmIChkaXJlY3Rpb24gPT09ICdyaWdodCcpIHsKICAgICAgICAgIG5ld0R1cmF0aW9uID0gTWF0aC5tYXgoMSwgTWF0aC5taW4obWF4RHVyYXRpb24sIHN0YXJ0RHVyYXRpb24gKyBkZWx0YVNlY29uZHMpKQogICAgICAgIH0gZWxzZSBpZiAoZGlyZWN0aW9uID09PSAnbGVmdCcpIHsKICAgICAgICAgIG5ld0R1cmF0aW9uID0gTWF0aC5tYXgoMSwgTWF0aC5taW4obWF4RHVyYXRpb24sIHN0YXJ0RHVyYXRpb24gLSBkZWx0YVNlY29uZHMpKQogICAgICAgIH0KCiAgICAgICAgY2xpcC5kdXJhdGlvbiA9IE1hdGgucm91bmQobmV3RHVyYXRpb24pCgogICAgICAgIC8vIOaYvuekuuaXtumVv+aPkOekugogICAgICAgIHRoaXMuc2hvd0R1cmF0aW9uVGlwKGNsaXAsIG1heER1cmF0aW9uKQogICAgICB9CgogICAgICBjb25zdCBvbk1vdXNlVXAgPSAoKSA9PiB7CiAgICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2Vtb3ZlJywgb25Nb3VzZU1vdmUpCiAgICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2V1cCcsIG9uTW91c2VVcCkKICAgICAgICB0aGlzLmhpZGVEdXJhdGlvblRpcCgpCiAgICAgIH0KCiAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlbW92ZScsIG9uTW91c2VNb3ZlKQogICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdtb3VzZXVwJywgb25Nb3VzZVVwKQogICAgfSwKCiAgICAvLyDmmL7npLrml7bplb/mj5DnpLoKICAgIHNob3dEdXJhdGlvblRpcChjbGlwLCBtYXhEdXJhdGlvbikgewogICAgICBjb25zdCBwZXJjZW50YWdlID0gKGNsaXAuZHVyYXRpb24gLyBtYXhEdXJhdGlvbiAqIDEwMCkudG9GaXhlZCgxKQogICAgICBjb25zb2xlLmxvZyhgJHtjbGlwLm5hbWV9OiAke3RoaXMuZm9ybWF0VGltZShjbGlwLmR1cmF0aW9uKX0gLyAke3RoaXMuZm9ybWF0VGltZShtYXhEdXJhdGlvbil9ICgke3BlcmNlbnRhZ2V9JSlgKQogICAgfSwKCiAgICAvLyDpmpDol4/ml7bplb/mj5DnpLoKICAgIGhpZGVEdXJhdGlvblRpcCgpIHsKICAgICAgLy8g5riF6Zmk5o+Q56S6CiAgICB9LAoKICAgIC8vIOiOt+WPlumihOiniOWIhui+qOeOh+aYvuekuuaWh+acrAogICAgZ2V0UHJldmlld1Jlc29sdXRpb24oKSB7CiAgICAgIC8vIOagueaNruaooeadv+WIhui+qOeOh+i/lOWbnuWvueW6lOeahOaKlumfs+WwuuWvuAogICAgICBjb25zdCByZXNvbHV0aW9uTWFwID0gewogICAgICAgICcxOTIweDEwODAnOiAnMTA4MHgxOTIwJywKICAgICAgICAnMTI4MHg3MjAnOiAnNzIweDEyODAnLAogICAgICAgICcxMDgweDE5MjAnOiAnMTA4MHgxOTIwJywKICAgICAgICAnNzIweDEyODAnOiAnNzIweDEyODAnCiAgICAgIH0KICAgICAgcmV0dXJuIHJlc29sdXRpb25NYXBbdGhpcy50ZW1wbGF0ZUluZm8ucmVzb2x1dGlvbl0gfHwgJzEwODB4MTkyMCcKICAgIH0sCgogICAgLy8g5LuOdXDpobXpnaLliqDovb3ntKDmnZDlupMKICAgIGFzeW5jIGxvYWRNYXRlcmlhbHNGcm9tVXBQYWdlKCkgewogICAgICB0cnkgewogICAgICAgIC8vIOaooeaLn+S7jnVw6aG16Z2i6I635Y+W57Sg5p2Q5pWw5o2uCiAgICAgICAgLy8g5a6e6ZmF5bqU55So5Lit77yM6L+Z6YeM5bqU6K+l6LCD55SoQVBJ5oiW5LuObG9jYWxTdG9yYWdl6I635Y+WCiAgICAgICAgY29uc3QgbW9ja1N1Y2FpTGlzdCA9IFsKICAgICAgICAgIHsKICAgICAgICAgICAgaWQ6ICdzdWNhaTEnLAogICAgICAgICAgICBuYW1lOiAn5byA5Zy66KeG6aKRMS5tcDQnLAogICAgICAgICAgICB0eXBlOiAndmlkZW8nLAogICAgICAgICAgICBzaXplOiAxMDI0MDAwLAogICAgICAgICAgICB1cGxvYWRUaW1lOiAnMjAyNC0wMS0wMSAxMjowMDowMCcsCiAgICAgICAgICAgIGR1cmF0aW9uOiAnMDA6MTInLAogICAgICAgICAgICB1cmw6ICdodHRwczovL3d3dy53M3NjaG9vbHMuY29tL2h0bWwvbW92X2JiYi5tcDQnLAogICAgICAgICAgICBmb2xkZXI6ICdhc3VjYWkvYWRtaW4v5byA5Zy6JywKICAgICAgICAgICAgdGh1bWJuYWlsOiAnaHR0cHM6Ly92aWEucGxhY2Vob2xkZXIuY29tLzE2MHg5MC80QTkwRTIvRkZGRkZGP3RleHQ9T3BlbmluZzEnCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBpZDogJ3N1Y2FpMicsCiAgICAgICAgICAgIG5hbWU6ICflvIDlnLrop4bpopEyLm1wNCcsCiAgICAgICAgICAgIHR5cGU6ICd2aWRlbycsCiAgICAgICAgICAgIHNpemU6IDEwMjQwMDAsCiAgICAgICAgICAgIHVwbG9hZFRpbWU6ICcyMDI0LTAxLTAxIDEyOjAwOjAwJywKICAgICAgICAgICAgZHVyYXRpb246ICcwMDoxMCcsCiAgICAgICAgICAgIHVybDogJ2h0dHBzOi8vd3d3Lnczc2Nob29scy5jb20vaHRtbC9tb3ZfYmJiLm1wNCcsCiAgICAgICAgICAgIGZvbGRlcjogJ2FzdWNhaS9hZG1pbi/lvIDlnLonLAogICAgICAgICAgICB0aHVtYm5haWw6ICdodHRwczovL3ZpYS5wbGFjZWhvbGRlci5jb20vMTYweDkwLzRBOTBFMi9GRkZGRkY/dGV4dD1PcGVuaW5nMicKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGlkOiAnc3VjYWkzJywKICAgICAgICAgICAgbmFtZTogJ+S6p+WTgeS7i+e7jTEubXA0JywKICAgICAgICAgICAgdHlwZTogJ3ZpZGVvJywKICAgICAgICAgICAgc2l6ZTogMTAyNDAwMCwKICAgICAgICAgICAgdXBsb2FkVGltZTogJzIwMjQtMDEtMDEgMTI6MDA6MDAnLAogICAgICAgICAgICBkdXJhdGlvbjogJzAwOjE4JywKICAgICAgICAgICAgdXJsOiAnaHR0cHM6Ly93d3cudzNzY2hvb2xzLmNvbS9odG1sL21vdl9iYmIubXA0JywKICAgICAgICAgICAgZm9sZGVyOiAnYXN1Y2FpL2FkbWluL+S6p+WTgeS7i+e7jScsCiAgICAgICAgICAgIHRodW1ibmFpbDogJ2h0dHBzOi8vdmlhLnBsYWNlaG9sZGVyLmNvbS8xNjB4OTAvNTBDODc4L0ZGRkZGRj90ZXh0PVByb2R1Y3QxJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgaWQ6ICdzdWNhaTQnLAogICAgICAgICAgICBuYW1lOiAn5Lqn5ZOB5LuL57uNMi5tcDQnLAogICAgICAgICAgICB0eXBlOiAndmlkZW8nLAogICAgICAgICAgICBzaXplOiAxMDI0MDAwLAogICAgICAgICAgICB1cGxvYWRUaW1lOiAnMjAyNC0wMS0wMSAxMjowMDowMCcsCiAgICAgICAgICAgIGR1cmF0aW9uOiAnMDA6MjInLAogICAgICAgICAgICB1cmw6ICdodHRwczovL3d3dy53M3NjaG9vbHMuY29tL2h0bWwvbW92X2JiYi5tcDQnLAogICAgICAgICAgICBmb2xkZXI6ICdhc3VjYWkvYWRtaW4v5Lqn5ZOB5LuL57uNJywKICAgICAgICAgICAgdGh1bWJuYWlsOiAnaHR0cHM6Ly92aWEucGxhY2Vob2xkZXIuY29tLzE2MHg5MC81MEM4NzgvRkZGRkZGP3RleHQ9UHJvZHVjdDInCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBpZDogJ3N1Y2FpNScsCiAgICAgICAgICAgIG5hbWU6ICflip/og73lsZXnpLoxLm1wNCcsCiAgICAgICAgICAgIHR5cGU6ICd2aWRlbycsCiAgICAgICAgICAgIHNpemU6IDEwMjQwMDAsCiAgICAgICAgICAgIHVwbG9hZFRpbWU6ICcyMDI0LTAxLTAxIDEyOjAwOjAwJywKICAgICAgICAgICAgZHVyYXRpb246ICcwMDoyNScsCiAgICAgICAgICAgIHVybDogJ2h0dHBzOi8vd3d3Lnczc2Nob29scy5jb20vaHRtbC9tb3ZfYmJiLm1wNCcsCiAgICAgICAgICAgIGZvbGRlcjogJ2FzdWNhaS9hZG1pbi/lip/og73lsZXnpLonLAogICAgICAgICAgICB0aHVtYm5haWw6ICdodHRwczovL3ZpYS5wbGFjZWhvbGRlci5jb20vMTYweDkwL0ZGNkI2Qi9GRkZGRkY/dGV4dD1GZWF0dXJlMScKICAgICAgICAgIH0KICAgICAgICBdCgogICAgICAgIHRoaXMuc3VjYWlMaXN0ID0gbW9ja1N1Y2FpTGlzdAoKICAgICAgICAvLyDmoLnmja7mlofku7blpLnliIbnu4TliJvlu7rop4bpopHlupMKICAgICAgICB0aGlzLmNyZWF0ZVZpZGVvTGlicmFyaWVzRnJvbVN1Y2FpKCkKCiAgICAgICAgY29uc29sZS5sb2coJ+e0oOadkOW6k+WKoOi9veWujOaIkDonLCB0aGlzLnZpZGVvTGlicmFyaWVzKQoKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3ntKDmnZDlupPlpLHotKU6JywgZXJyb3IpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Yqg6L2957Sg5p2Q5bqT5aSx6LSlJykKICAgICAgfQogICAgfSwKCiAgICAvLyDmoLnmja7ntKDmnZDliJfooajliJvlu7rop4bpopHlupMKICAgIGNyZWF0ZVZpZGVvTGlicmFyaWVzRnJvbVN1Y2FpKCkgewogICAgICBjb25zdCBmb2xkZXJNYXAgPSBuZXcgTWFwKCkKCiAgICAgIC8vIOaMieaWh+S7tuWkueWIhue7hAogICAgICB0aGlzLnN1Y2FpTGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgIGlmIChpdGVtLnR5cGUgPT09ICd2aWRlbycpIHsKICAgICAgICAgIC8vIOaPkOWPluaWh+S7tuWkueWQjeensAogICAgICAgICAgY29uc3QgZm9sZGVyUGF0aCA9IGl0ZW0uZm9sZGVyIHx8ICdhc3VjYWkvYWRtaW4v5oC7JwogICAgICAgICAgY29uc3QgZm9sZGVyTmFtZSA9IGZvbGRlclBhdGguc3BsaXQoJy8nKS5wb3AoKSB8fCAn5oC7JwoKICAgICAgICAgIGlmICghZm9sZGVyTWFwLmhhcyhmb2xkZXJOYW1lKSkgewogICAgICAgICAgICBmb2xkZXJNYXAuc2V0KGZvbGRlck5hbWUsIHsKICAgICAgICAgICAgICBpZDogJ2xpYl8nICsgZm9sZGVyTmFtZSwKICAgICAgICAgICAgICBuYW1lOiBmb2xkZXJOYW1lICsgJ+inhumikeW6kycsCiAgICAgICAgICAgICAgdmlkZW9zOiBbXQogICAgICAgICAgICB9KQogICAgICAgICAgfQoKICAgICAgICAgIC8vIOi9rOaNouS4uuinhumikeW6k+agvOW8jwogICAgICAgICAgY29uc3QgdmlkZW8gPSB7CiAgICAgICAgICAgIGlkOiBpdGVtLmlkLAogICAgICAgICAgICBuYW1lOiBpdGVtLm5hbWUsCiAgICAgICAgICAgIHVybDogaXRlbS51cmwsCiAgICAgICAgICAgIGR1cmF0aW9uOiB0aGlzLnBhcnNlRHVyYXRpb24oaXRlbS5kdXJhdGlvbiksCiAgICAgICAgICAgIHRodW1ibmFpbDogaXRlbS50aHVtYm5haWwgfHwgJ2h0dHBzOi8vdmlhLnBsYWNlaG9sZGVyLmNvbS8xNjB4OTAvOTk5L0ZGRkZGRj90ZXh0PVZpZGVvJwogICAgICAgICAgfQoKICAgICAgICAgIGZvbGRlck1hcC5nZXQoZm9sZGVyTmFtZSkudmlkZW9zLnB1c2godmlkZW8pCiAgICAgICAgfQogICAgICB9KQoKICAgICAgLy8g6L2s5o2i5Li65pWw57uECiAgICAgIHRoaXMudmlkZW9MaWJyYXJpZXMgPSBBcnJheS5mcm9tKGZvbGRlck1hcC52YWx1ZXMoKSkKICAgIH0sCgogICAgLy8g6Kej5p6Q5pe26ZW/5a2X56ym5Liy5Li656eS5pWwCiAgICBwYXJzZUR1cmF0aW9uKGR1cmF0aW9uU3RyKSB7CiAgICAgIGlmICghZHVyYXRpb25TdHIpIHJldHVybiAxMAoKICAgICAgY29uc3QgcGFydHMgPSBkdXJhdGlvblN0ci5zcGxpdCgnOicpCiAgICAgIGlmIChwYXJ0cy5sZW5ndGggPT09IDIpIHsKICAgICAgICBjb25zdCBtaW51dGVzID0gcGFyc2VJbnQocGFydHNbMF0pIHx8IDAKICAgICAgICBjb25zdCBzZWNvbmRzID0gcGFyc2VJbnQocGFydHNbMV0pIHx8IDAKICAgICAgICByZXR1cm4gbWludXRlcyAqIDYwICsgc2Vjb25kcwogICAgICB9CgogICAgICByZXR1cm4gMTAgLy8g6buY6K6kMTDnp5IKICAgIH0sCgogICAgLy8g5Yid5aeL5YyW5Zy65pmv6KeG6aKR77yI5Li65q+P5Liq5Zy65pmv6ZqP5py66YCJ5oup5LiA5Liq6KeG6aKR77yJCiAgICBpbml0aWFsaXplU2NlbmVWaWRlb3MoKSB7CiAgICAgIHRoaXMuc2NlbmVzLmZvckVhY2goc2NlbmUgPT4gewogICAgICAgIGNvbnN0IGxpYnJhcnkgPSB0aGlzLnZpZGVvTGlicmFyaWVzLmZpbmQobGliID0+IGxpYi5pZCA9PT0gc2NlbmUudmlkZW9MaWJyYXJ5SWQpCiAgICAgICAgaWYgKGxpYnJhcnkgJiYgbGlicmFyeS52aWRlb3MubGVuZ3RoID4gMCkgewogICAgICAgICAgLy8g6ZqP5py66YCJ5oup5LiA5Liq6KeG6aKRCiAgICAgICAgICBjb25zdCByYW5kb21JbmRleCA9IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIGxpYnJhcnkudmlkZW9zLmxlbmd0aCkKICAgICAgICAgIGNvbnN0IHNlbGVjdGVkVmlkZW8gPSBsaWJyYXJ5LnZpZGVvc1tyYW5kb21JbmRleF0KICAgICAgICAgIHNjZW5lLmN1cnJlbnRWaWRlbyA9IHNlbGVjdGVkVmlkZW8KCiAgICAgICAgICAvLyDorr7nva7lnLrmma/nmoTmnIDlpKfml7bplb/kuLrop4bpopHml7bplb8KICAgICAgICAgIHNjZW5lLm1heER1cmF0aW9uID0gc2VsZWN0ZWRWaWRlby5kdXJhdGlvbgoKICAgICAgICAgIC8vIOWmguaenOW9k+WJjeWcuuaZr+aXtumVv+i2hei/h+inhumikeaXtumVv++8jOiwg+aVtOS4uuinhumikeaXtumVvwogICAgICAgICAgaWYgKHNjZW5lLmR1cmF0aW9uID4gc2VsZWN0ZWRWaWRlby5kdXJhdGlvbikgewogICAgICAgICAgICBzY2VuZS5kdXJhdGlvbiA9IHNlbGVjdGVkVmlkZW8uZHVyYXRpb24KICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pCgogICAgICAvLyDph43mlrDorqHnrpfml7bpl7TovbQKICAgICAgdGhpcy5yZWNhbGN1bGF0ZVNjZW5lVGltZWxpbmUoKQogICAgfSwKCiAgICAvLyDmmL7npLrmt7vliqDlnLrmma/lr7nor53moYYKICAgIHNob3dBZGRTY2VuZURpYWxvZygpIHsKICAgICAgdGhpcy5zY2VuZUZvcm0gPSB7CiAgICAgICAgbmFtZTogJycsCiAgICAgICAgZHVyYXRpb246IDEwLAogICAgICAgIHZpZGVvTGlicmFyeUlkOiAnJwogICAgICB9CiAgICAgIHRoaXMuYWRkU2NlbmVEaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgfSwKCiAgICAvLyDnoa7orqTmt7vliqDlnLrmma8KICAgIGNvbmZpcm1BZGRTY2VuZSgpIHsKICAgICAgaWYgKCF0aGlzLnNjZW5lRm9ybS5uYW1lIHx8ICF0aGlzLnNjZW5lRm9ybS52aWRlb0xpYnJhcnlJZCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+Whq+WGmeWujOaVtOeahOWcuuaZr+S/oeaBrycpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIC8vIOiuoeeul+aWsOWcuuaZr+eahOW8gOWni+aXtumXtAogICAgICBjb25zdCBsYXN0U2NlbmUgPSB0aGlzLnNjZW5lc1t0aGlzLnNjZW5lcy5sZW5ndGggLSAxXQogICAgICBjb25zdCBzdGFydFRpbWUgPSBsYXN0U2NlbmUgPyBsYXN0U2NlbmUuc3RhcnRUaW1lICsgbGFzdFNjZW5lLmR1cmF0aW9uIDogMAoKICAgICAgY29uc3QgbmV3U2NlbmUgPSB7CiAgICAgICAgaWQ6ICdzY2VuZScgKyBEYXRlLm5vdygpLAogICAgICAgIG5hbWU6IHRoaXMuc2NlbmVGb3JtLm5hbWUsCiAgICAgICAgc3RhcnRUaW1lOiBzdGFydFRpbWUsCiAgICAgICAgZHVyYXRpb246IHRoaXMuc2NlbmVGb3JtLmR1cmF0aW9uLAogICAgICAgIHZpZGVvTGlicmFyeUlkOiB0aGlzLnNjZW5lRm9ybS52aWRlb0xpYnJhcnlJZCwKICAgICAgICB0ZXh0U2VnbWVudDogJ+aWsOWcuuaZr+eahOaWh+ahiOWGheWuuS4uLicsCiAgICAgICAgY3VycmVudFZpZGVvOiBudWxsCiAgICAgIH0KCiAgICAgIC8vIOS4uuaWsOWcuuaZr+maj+acuumAieaLqeinhumikQogICAgICBjb25zdCBsaWJyYXJ5ID0gdGhpcy52aWRlb0xpYnJhcmllcy5maW5kKGxpYiA9PiBsaWIuaWQgPT09IHRoaXMuc2NlbmVGb3JtLnZpZGVvTGlicmFyeUlkKQogICAgICBpZiAobGlicmFyeSAmJiBsaWJyYXJ5LnZpZGVvcy5sZW5ndGggPiAwKSB7CiAgICAgICAgY29uc3QgcmFuZG9tSW5kZXggPSBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBsaWJyYXJ5LnZpZGVvcy5sZW5ndGgpCiAgICAgICAgbmV3U2NlbmUuY3VycmVudFZpZGVvID0gbGlicmFyeS52aWRlb3NbcmFuZG9tSW5kZXhdCiAgICAgIH0KCiAgICAgIHRoaXMuc2NlbmVzLnB1c2gobmV3U2NlbmUpCiAgICAgIHRoaXMuYWRkU2NlbmVEaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflnLrmma/mt7vliqDmiJDlip8nKQogICAgfSwKCiAgICAvLyDpgInmi6nlnLrmma8KICAgIHNlbGVjdFNjZW5lKHNjZW5lKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRTY2VuZSA9IHNjZW5lCiAgICAgIHRoaXMuc2VsZWN0ZWRDbGlwID0gbnVsbAogICAgICB0aGlzLnNlbGVjdGVkVGV4dFNlZ21lbnQgPSBudWxsCgogICAgICAvLyDmmL7npLrop4bpopHlupPpgInmi6nlr7nor53moYYKICAgICAgY29uc3QgbGlicmFyeSA9IHRoaXMudmlkZW9MaWJyYXJpZXMuZmluZChsaWIgPT4gbGliLmlkID09PSBzY2VuZS52aWRlb0xpYnJhcnlJZCkKICAgICAgaWYgKGxpYnJhcnkpIHsKICAgICAgICB0aGlzLnNlbGVjdGVkVmlkZW9MaWJyYXJ5ID0gbGlicmFyeQogICAgICAgIHRoaXMudmlkZW9MaWJyYXJ5RGlhbG9nVmlzaWJsZSA9IHRydWUKICAgICAgfQogICAgfSwKCiAgICAvLyDku47op4bpopHlupPpgInmi6nop4bpopEKICAgIHNlbGVjdFZpZGVvRnJvbUxpYnJhcnkodmlkZW8pIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRTY2VuZSkgewogICAgICAgIHRoaXMuc2VsZWN0ZWRTY2VuZS5jdXJyZW50VmlkZW8gPSB2aWRlbwogICAgICAgIC8vIOabtOaWsOWcuuaZr+eahOacgOWkp+aXtumVv+mZkOWItgogICAgICAgIHRoaXMuc2VsZWN0ZWRTY2VuZS5tYXhEdXJhdGlvbiA9IHZpZGVvLmR1cmF0aW9uCgogICAgICAgIC8vIOWmguaenOW9k+WJjeaXtumVv+i2hei/h+aWsOinhumikeeahOaXtumVv++8jOWImeiwg+aVtOS4uuaWsOinhumikeeahOaXtumVvwogICAgICAgIGlmICh0aGlzLnNlbGVjdGVkU2NlbmUuZHVyYXRpb24gPiB2aWRlby5kdXJhdGlvbikgewogICAgICAgICAgdGhpcy5zZWxlY3RlZFNjZW5lLmR1cmF0aW9uID0gdmlkZW8uZHVyYXRpb24KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhg5Zy65pmv5pe26ZW/5bey6LCD5pW05Li66KeG6aKR5pe26ZW/OiAke3RoaXMuZm9ybWF0VGltZSh2aWRlby5kdXJhdGlvbil9YCkKICAgICAgICB9CgogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5bey5Li65Zy65pmvIiR7dGhpcy5zZWxlY3RlZFNjZW5lLm5hbWV9IumAieaLqeinhumikTogJHt2aWRlby5uYW1lfSAoJHt0aGlzLmZvcm1hdFRpbWUodmlkZW8uZHVyYXRpb24pfSlgKQogICAgICB9CiAgICAgIHRoaXMudmlkZW9MaWJyYXJ5RGlhbG9nVmlzaWJsZSA9IGZhbHNlCiAgICB9LAoKICAgIC8vIOmAieaLqeaWh+ahiOeJh+autQogICAgc2VsZWN0VGV4dFNlZ21lbnQoc2NlbmUsIGluZGV4KSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRUZXh0U2VnbWVudCA9IHNjZW5lCiAgICAgIHRoaXMuc2VsZWN0ZWRDbGlwID0gbnVsbAogICAgICB0aGlzLnNlbGVjdGVkU2NlbmUgPSBudWxsCiAgICB9LAoKICAgIC8vIOiOt+WPluWcuuaZr+agt+W8jwogICAgZ2V0U2NlbmVTdHlsZShzY2VuZSkgewogICAgICByZXR1cm4gewogICAgICAgIGxlZnQ6IHNjZW5lLnN0YXJ0VGltZSAqIHRoaXMucGl4ZWxzUGVyU2Vjb25kICsgJ3B4JywKICAgICAgICB3aWR0aDogc2NlbmUuZHVyYXRpb24gKiB0aGlzLnBpeGVsc1BlclNlY29uZCArICdweCcKICAgICAgfQogICAgfSwKCiAgICAvLyDojrflj5bmlofmoYjpooTop4gKICAgIGdldFRleHRQcmV2aWV3KHRleHQpIHsKICAgICAgcmV0dXJuIHRleHQubGVuZ3RoID4gMjAgPyB0ZXh0LnN1YnN0cmluZygwLCAyMCkgKyAnLi4uJyA6IHRleHQKICAgIH0sCgogICAgLy8g5paH5qGI5YiG5q6177yI6LCD55So6Zi/6YeM5LqR5o6l5Y+j77yJCiAgICBhc3luYyBzZWdtZW50VGV4dCgpIHsKICAgICAgdHJ5IHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+ato+WcqOiwg+eUqOmYv+mHjOS6keaOpeWPo+i/m+ihjOaWh+ahiOWIhuautS4uLicpCgogICAgICAgIC8vIFRPRE86IOiwg+eUqOmYv+mHjOS6keaWh+acrOWIhuauteaOpeWPowogICAgICAgIC8vIGNvbnN0IHNlZ21lbnRzID0gYXdhaXQgdGhpcy5jYWxsQWxpQ2xvdWRUZXh0U2VnbWVudGF0aW9uKHRoaXMudGV4dExpYnJhcnkuZnVsbFRleHQpCgogICAgICAgIC8vIOaooeaLn+WIhuautee7k+aenAogICAgICAgIGNvbnN0IG1vY2tTZWdtZW50cyA9IFsKICAgICAgICAgICfmrKLov47mnaXliLDmiJHku6znmoTkuqflk4HlsZXnpLrvvIzku4rlpKnkuLrlpKflrrbku4vnu43kuIDmrL7pnanlkb3mgKfnmoTkuqflk4HjgIInLAogICAgICAgICAgJ+i/measvuS6p+WTgeWFt+acieeLrOeJueeahOiuvuiuoeeQhuW/teWSjOWFiOi/m+eahOaKgOacr++8jOiDveWkn+S4uueUqOaIt+W4puadpeWFqOaWsOeahOS9k+mqjOOAgicsCiAgICAgICAgICAn6K6p5oiR5Lus5p2l55yL55yL5a6D55qE5qC45b+D5Yqf6IO95ZKM5L2/55So5pa55rOV44CCJywKICAgICAgICAgICfpgJrov4fnroDljZXnmoTmk43kvZzvvIzmgqjlsLHog73kvZPpqozliLDliY3miYDmnKrmnInnmoTkvr/liKnmgKfjgIInCiAgICAgICAgXQoKICAgICAgICAvLyDmm7TmlrDlnLrmma/nmoTmlofmoYjniYfmrrUKICAgICAgICBtb2NrU2VnbWVudHMuZm9yRWFjaCgoc2VnbWVudCwgaW5kZXgpID0+IHsKICAgICAgICAgIGlmICh0aGlzLnNjZW5lc1tpbmRleF0pIHsKICAgICAgICAgICAgdGhpcy5zY2VuZXNbaW5kZXhdLnRleHRTZWdtZW50ID0gc2VnbWVudAogICAgICAgICAgfQogICAgICAgIH0pCgogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5paH5qGI5YiG5q615a6M5oiQJykKCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5paH5qGI5YiG5q615aSx6LSlOicsIGVycm9yKQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aWh+ahiOWIhuauteWksei0pe+8jOivt+mHjeivlScpCiAgICAgIH0KICAgIH0sCgogICAgLy8g5Zy65pmv5ouW5ou95byA5aeLCiAgICBvbkRyYWdTdGFydFNjZW5lKGV2ZW50LCBzY2VuZSkgewogICAgICBldmVudC5kYXRhVHJhbnNmZXIuc2V0RGF0YSgndGV4dC9wbGFpbicsIEpTT04uc3RyaW5naWZ5KHNjZW5lKSkKICAgIH0sCgogICAgLy8g5Zy65pmv5ouW5ou95pS+572uCiAgICBvbkRyb3BTY2VuZShldmVudCkgewogICAgICBjb25zdCBzY2VuZURhdGEgPSBKU09OLnBhcnNlKGV2ZW50LmRhdGFUcmFuc2Zlci5nZXREYXRhKCd0ZXh0L3BsYWluJykpCiAgICAgIGNvbnNvbGUubG9nKCflnLrmma/mi5bmi706Jywgc2NlbmVEYXRhKQogICAgICAvLyBUT0RPOiDlrp7njrDlnLrmma/mi5bmi73pgLvovpEKICAgIH0sCgogICAgLy8g5byA5aeL6LCD5pW05Zy65pmv5aSn5bCPCiAgICBzdGFydFJlc2l6ZVNjZW5lKGV2ZW50LCBzY2VuZSwgZGlyZWN0aW9uKSB7CiAgICAgIGNvbnNvbGUubG9nKCfosIPmlbTlnLrmma/lpKflsI86Jywgc2NlbmUubmFtZSwgZGlyZWN0aW9uKQoKICAgICAgLy8g5qOA5p+l5Zy65pmv5piv5ZCm5pyJ6KeG6aKR5ZKM5pe26ZW/6ZmQ5Yi2CiAgICAgIGlmICghc2NlbmUuY3VycmVudFZpZGVvIHx8ICFzY2VuZS5tYXhEdXJhdGlvbikgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI5Li65Zy65pmv6YCJ5oup6KeG6aKRJykKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgY29uc3QgbWF4QWxsb3dlZER1cmF0aW9uID0gc2NlbmUubWF4RHVyYXRpb24KCiAgICAgIGlmIChkaXJlY3Rpb24gPT09ICdyaWdodCcpIHsKICAgICAgICAvLyDlkJHlj7Pmi5bmi73vvIjlop7liqDml7bplb/vvIkKICAgICAgICBpZiAoc2NlbmUuZHVyYXRpb24gPj0gbWF4QWxsb3dlZER1cmF0aW9uKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoYOWcuuaZr+acgOWkp+aXtumVv+S4uiAke3RoaXMuZm9ybWF0VGltZShtYXhBbGxvd2VkRHVyYXRpb24pfe+8iOinhumikeaXtumVv+mZkOWItu+8iWApCiAgICAgICAgICByZXR1cm4KICAgICAgICB9CiAgICAgIH0KCiAgICAgIC8vIOWunueOsOWcuuaZr+Wkp+Wwj+iwg+aVtAogICAgICB0aGlzLnN0YXJ0UmVzaXplU2NlbmVXaXRoTGltaXQoZXZlbnQsIHNjZW5lLCBkaXJlY3Rpb24sIG1heEFsbG93ZWREdXJhdGlvbikKICAgIH0sCgogICAgLy8g5bim5pe26ZW/6ZmQ5Yi255qE5Zy65pmv6LCD5pW0CiAgICBzdGFydFJlc2l6ZVNjZW5lV2l0aExpbWl0KGV2ZW50LCBzY2VuZSwgZGlyZWN0aW9uLCBtYXhEdXJhdGlvbikgewogICAgICBjb25zdCBzdGFydFggPSBldmVudC5jbGllbnRYCiAgICAgIGNvbnN0IHN0YXJ0RHVyYXRpb24gPSBzY2VuZS5kdXJhdGlvbgogICAgICBjb25zdCBwaXhlbHNQZXJTZWNvbmQgPSB0aGlzLnBpeGVsc1BlclNlY29uZAoKICAgICAgY29uc3Qgb25Nb3VzZU1vdmUgPSAoZSkgPT4gewogICAgICAgIGNvbnN0IGRlbHRhWCA9IGUuY2xpZW50WCAtIHN0YXJ0WAogICAgICAgIGNvbnN0IGRlbHRhU2Vjb25kcyA9IGRlbHRhWCAvIHBpeGVsc1BlclNlY29uZAoKICAgICAgICBsZXQgbmV3RHVyYXRpb24gPSBzdGFydER1cmF0aW9uCgogICAgICAgIGlmIChkaXJlY3Rpb24gPT09ICdyaWdodCcpIHsKICAgICAgICAgIG5ld0R1cmF0aW9uID0gTWF0aC5tYXgoMSwgTWF0aC5taW4obWF4RHVyYXRpb24sIHN0YXJ0RHVyYXRpb24gKyBkZWx0YVNlY29uZHMpKQogICAgICAgIH0gZWxzZSBpZiAoZGlyZWN0aW9uID09PSAnbGVmdCcpIHsKICAgICAgICAgIG5ld0R1cmF0aW9uID0gTWF0aC5tYXgoMSwgTWF0aC5taW4obWF4RHVyYXRpb24sIHN0YXJ0RHVyYXRpb24gLSBkZWx0YVNlY29uZHMpKQogICAgICAgIH0KCiAgICAgICAgc2NlbmUuZHVyYXRpb24gPSBNYXRoLnJvdW5kKG5ld0R1cmF0aW9uKQoKICAgICAgICAvLyDmmL7npLrlnLrmma/ml7bplb/mj5DnpLoKICAgICAgICB0aGlzLnNob3dTY2VuZUR1cmF0aW9uVGlwKHNjZW5lLCBtYXhEdXJhdGlvbikKICAgICAgfQoKICAgICAgY29uc3Qgb25Nb3VzZVVwID0gKCkgPT4gewogICAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlbW92ZScsIG9uTW91c2VNb3ZlKQogICAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNldXAnLCBvbk1vdXNlVXApCiAgICAgICAgdGhpcy5oaWRlU2NlbmVEdXJhdGlvblRpcCgpCgogICAgICAgIC8vIOiwg+aVtOWujOaIkOWQjumHjeaWsOiuoeeul+WQjue7reWcuuaZr+eahOW8gOWni+aXtumXtAogICAgICAgIHRoaXMucmVjYWxjdWxhdGVTY2VuZVRpbWVsaW5lKCkKICAgICAgfQoKICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignbW91c2Vtb3ZlJywgb25Nb3VzZU1vdmUpCiAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNldXAnLCBvbk1vdXNlVXApCiAgICB9LAoKICAgIC8vIOaYvuekuuWcuuaZr+aXtumVv+aPkOekugogICAgc2hvd1NjZW5lRHVyYXRpb25UaXAoc2NlbmUsIG1heER1cmF0aW9uKSB7CiAgICAgIGNvbnN0IHBlcmNlbnRhZ2UgPSAoc2NlbmUuZHVyYXRpb24gLyBtYXhEdXJhdGlvbiAqIDEwMCkudG9GaXhlZCgxKQogICAgICBjb25zb2xlLmxvZyhgJHtzY2VuZS5uYW1lfTogJHt0aGlzLmZvcm1hdFRpbWUoc2NlbmUuZHVyYXRpb24pfSAvICR7dGhpcy5mb3JtYXRUaW1lKG1heER1cmF0aW9uKX0gKCR7cGVyY2VudGFnZX0lKWApCiAgICB9LAoKICAgIC8vIOmakOiXj+WcuuaZr+aXtumVv+aPkOekugogICAgaGlkZVNjZW5lRHVyYXRpb25UaXAoKSB7CiAgICAgIC8vIOa4hemZpOaPkOekugogICAgfSwKCiAgICAvLyDph43mlrDorqHnrpflnLrmma/ml7bpl7TovbQKICAgIHJlY2FsY3VsYXRlU2NlbmVUaW1lbGluZSgpIHsKICAgICAgbGV0IGN1cnJlbnRUaW1lID0gMAogICAgICB0aGlzLnNjZW5lcy5mb3JFYWNoKHNjZW5lID0+IHsKICAgICAgICBzY2VuZS5zdGFydFRpbWUgPSBjdXJyZW50VGltZQogICAgICAgIGN1cnJlbnRUaW1lICs9IHNjZW5lLmR1cmF0aW9uCiAgICAgIH0pCiAgICAgIGNvbnNvbGUubG9nKCflnLrmma/ml7bpl7TovbTlt7Lph43mlrDorqHnrpcnKQogICAgfSwKCiAgICAvLyDngrnlh7vml7bpl7TovbQKICAgIG9uVGltZWxpbmVDbGljayhldmVudCkgewogICAgICBjb25zdCByZWN0ID0gZXZlbnQuY3VycmVudFRhcmdldC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKQogICAgICBjb25zdCBjbGlja1ggPSBldmVudC5jbGllbnRYIC0gcmVjdC5sZWZ0CiAgICAgIGNvbnN0IGNsaWNrZWRUaW1lID0gTWF0aC5mbG9vcihjbGlja1ggLyB0aGlzLnBpeGVsc1BlclNlY29uZCkKCiAgICAgIC8vIOmZkOWItuWcqDAtNjDnp5LojIPlm7TlhoUKICAgICAgdGhpcy5jdXJyZW50VGltZSA9IE1hdGgubWF4KDAsIE1hdGgubWluKDYwLCBjbGlja2VkVGltZSkpCgogICAgICBjb25zb2xlLmxvZyhg54K55Ye75pe26Ze06L20OiAke3RoaXMuY3VycmVudFRpbWV956eSYCkKCiAgICAgIC8vIOagueaNruW9k+WJjeaXtumXtOaJvuWIsOWvueW6lOeahOWcuuaZr+W5tumihOiniAogICAgICB0aGlzLnByZXZpZXdBdFRpbWUodGhpcy5jdXJyZW50VGltZSkKICAgIH0sCgogICAgLy8g5qC55o2u5pe26Ze06aKE6KeI5a+55bqU5Zy65pmvCiAgICBwcmV2aWV3QXRUaW1lKHRpbWUpIHsKICAgICAgLy8g5om+5Yiw5b2T5YmN5pe26Ze05a+55bqU55qE5Zy65pmvCiAgICAgIGNvbnN0IGN1cnJlbnRTY2VuZSA9IHRoaXMuc2NlbmVzLmZpbmQoc2NlbmUgPT4gewogICAgICAgIHJldHVybiB0aW1lID49IHNjZW5lLnN0YXJ0VGltZSAmJiB0aW1lIDwgKHNjZW5lLnN0YXJ0VGltZSArIHNjZW5lLmR1cmF0aW9uKQogICAgICB9KQoKICAgICAgaWYgKGN1cnJlbnRTY2VuZSkgewogICAgICAgIHRoaXMuc2VsZWN0ZWRTY2VuZSA9IGN1cnJlbnRTY2VuZQogICAgICAgIGNvbnNvbGUubG9nKGDpooTop4jlnLrmma86ICR7Y3VycmVudFNjZW5lLm5hbWV9ICgke3RpbWV956eSKWApCgogICAgICAgIC8vIOWmguaenOWcuuaZr+acieinhumike+8jOWPr+S7peiuoeeul+inhumikeWGheeahOaSreaUvuS9jee9rgogICAgICAgIGlmIChjdXJyZW50U2NlbmUuY3VycmVudFZpZGVvKSB7CiAgICAgICAgICBjb25zdCBzY2VuZVByb2dyZXNzID0gdGltZSAtIGN1cnJlbnRTY2VuZS5zdGFydFRpbWUKICAgICAgICAgIGNvbnN0IHZpZGVvUHJvZ3Jlc3MgPSAoc2NlbmVQcm9ncmVzcyAvIGN1cnJlbnRTY2VuZS5kdXJhdGlvbiAqIDEwMCkudG9GaXhlZCgxKQogICAgICAgICAgY29uc29sZS5sb2coYOinhumikeaSreaUvui/m+W6pjogJHt2aWRlb1Byb2dyZXNzfSVgKQogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlpoLmnpzmsqHmnInmib7liLDlnLrmma/vvIzmuIXpmaTpgInmi6kKICAgICAgICB0aGlzLnNlbGVjdGVkU2NlbmUgPSBudWxsCiAgICAgICAgY29uc29sZS5sb2coJ+W9k+WJjeaXtumXtOayoeacieWvueW6lOeahOWcuuaZrycpCiAgICAgIH0KICAgIH0sCgogICAgLy8g5byA5aeL5ouW5ou95pe26Ze06L20CiAgICBvblRpbWVsaW5lRHJhZ1N0YXJ0KGV2ZW50KSB7CiAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCkKCiAgICAgIGNvbnN0IHN0YXJ0WCA9IGV2ZW50LmNsaWVudFgKICAgICAgY29uc3QgcmVjdCA9IGV2ZW50LmN1cnJlbnRUYXJnZXQuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCkKICAgICAgY29uc3Qgc3RhcnRDbGlja1ggPSBldmVudC5jbGllbnRYIC0gcmVjdC5sZWZ0CiAgICAgIGNvbnN0IHN0YXJ0VGltZSA9IE1hdGguZmxvb3Ioc3RhcnRDbGlja1ggLyB0aGlzLnBpeGVsc1BlclNlY29uZCkKCiAgICAgIC8vIOiuvue9ruWIneWni+aXtumXtAogICAgICB0aGlzLmN1cnJlbnRUaW1lID0gTWF0aC5tYXgoMCwgTWF0aC5taW4oNjAsIHN0YXJ0VGltZSkpCiAgICAgIHRoaXMucHJldmlld0F0VGltZSh0aGlzLmN1cnJlbnRUaW1lKQoKICAgICAgY29uc3Qgb25Nb3VzZU1vdmUgPSAoZSkgPT4gewogICAgICAgIGNvbnN0IGN1cnJlbnRYID0gZS5jbGllbnRYCiAgICAgICAgY29uc3QgZGVsdGFYID0gY3VycmVudFggLSBzdGFydFgKICAgICAgICBjb25zdCBuZXdDbGlja1ggPSBzdGFydENsaWNrWCArIGRlbHRhWAogICAgICAgIGNvbnN0IG5ld1RpbWUgPSBNYXRoLmZsb29yKG5ld0NsaWNrWCAvIHRoaXMucGl4ZWxzUGVyU2Vjb25kKQoKICAgICAgICAvLyDpmZDliLblnKgwLTYw56eS6IyD5Zu05YaFCiAgICAgICAgdGhpcy5jdXJyZW50VGltZSA9IE1hdGgubWF4KDAsIE1hdGgubWluKDYwLCBuZXdUaW1lKSkKICAgICAgICB0aGlzLnByZXZpZXdBdFRpbWUodGhpcy5jdXJyZW50VGltZSkKICAgICAgfQoKICAgICAgY29uc3Qgb25Nb3VzZVVwID0gKCkgPT4gewogICAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlbW92ZScsIG9uTW91c2VNb3ZlKQogICAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNldXAnLCBvbk1vdXNlVXApCiAgICAgICAgY29uc29sZS5sb2coYOaLluaLvee7k+adnzogJHt0aGlzLmN1cnJlbnRUaW1lfeenkmApCiAgICAgIH0KCiAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlbW92ZScsIG9uTW91c2VNb3ZlKQogICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdtb3VzZXVwJywgb25Nb3VzZVVwKQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["editor.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "editor.vue", "sourceRoot": "src/views/store", "sourcesContent": ["<template>\n  <div class=\"video-editor\">\n    <!-- 头部工具栏 -->\n    <div class=\"editor-header\">\n      <div class=\"header-left\">\n        <el-button icon=\"el-icon-arrow-left\" @click=\"goBack\">返回</el-button>\n        <h3 class=\"template-title\">{{ templateInfo.name }}</h3>\n      </div>\n      <div class=\"header-right\">\n        <el-button @click=\"saveTemplate\">保存</el-button>\n        <el-button type=\"primary\" @click=\"showBatchDialog\">开始剪辑</el-button>\n      </div>\n    </div>\n\n    <!-- 主编辑区域 -->\n    <div class=\"editor-main\">\n      <!-- 预览面板 -->\n      <div class=\"preview-panel\">\n        <div class=\"preview-container\">\n          <div class=\"video-preview\">\n            <div class=\"preview-frame\">\n              <!-- 显示选中场景的视频 -->\n              <div v-if=\"selectedScene && selectedScene.currentVideo\" class=\"scene-preview\">\n                <img :src=\"selectedScene.currentVideo.thumbnail\" :alt=\"selectedScene.name\" />\n                <div class=\"scene-overlay\">\n                  <div class=\"scene-title\">{{ selectedScene.name }}</div>\n                  <div class=\"scene-video-name\">{{ selectedScene.currentVideo.name }}</div>\n                  <div class=\"scene-time-info\">\n                    <span>当前时间: {{ formatTime(currentTime) }}</span>\n                    <span>场景时间: {{ formatTime(currentTime - selectedScene.startTime) }}</span>\n                  </div>\n                  <div class=\"scene-text-preview\">{{ selectedScene.textSegment }}</div>\n                </div>\n              </div>\n\n              <!-- 默认预览占位符 -->\n              <div v-else class=\"preview-placeholder\">\n                <i class=\"el-icon-video-play\"></i>\n                <p>视频预览</p>\n                <p class=\"preview-info\">{{ getPreviewResolution() }} • {{ templateInfo.fps }}fps</p>\n                <div class=\"current-time-display\">\n                  <p>当前时间: {{ formatTime(currentTime) }}</p>\n                </div>\n                <div class=\"preview-tips\">\n                  <p>9:16 竖屏比例</p>\n                  <p>适配短视频平台</p>\n                  <p>点击时间轴查看预览</p>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div class=\"preview-controls\">\n            <el-button-group>\n              <el-button icon=\"el-icon-video-play\" size=\"small\">播放</el-button>\n              <el-button icon=\"el-icon-video-pause\" size=\"small\">暂停</el-button>\n              <el-button icon=\"el-icon-refresh-left\" size=\"small\">重置</el-button>\n            </el-button-group>\n            <div class=\"time-display\">00:00 / 02:30</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 属性设置面板 -->\n      <div class=\"properties-panel\">\n        <div class=\"panel-header\">\n          <h4>属性设置</h4>\n        </div>\n        <div class=\"panel-content\">\n          <el-collapse v-model=\"activeCollapse\">\n            <el-collapse-item title=\"视频设置\" name=\"video\">\n              <el-form label-width=\"80px\" size=\"small\">\n                <el-form-item label=\"背景色\">\n                  <el-color-picker v-model=\"videoSettings.backgroundColor\"></el-color-picker>\n                </el-form-item>\n                <el-form-item label=\"亮度\">\n                  <el-slider v-model=\"videoSettings.brightness\" :min=\"-100\" :max=\"100\"></el-slider>\n                </el-form-item>\n                <el-form-item label=\"对比度\">\n                  <el-slider v-model=\"videoSettings.contrast\" :min=\"-100\" :max=\"100\"></el-slider>\n                </el-form-item>\n                <el-form-item label=\"饱和度\">\n                  <el-slider v-model=\"videoSettings.saturation\" :min=\"-100\" :max=\"100\"></el-slider>\n                </el-form-item>\n              </el-form>\n            </el-collapse-item>\n            \n            <el-collapse-item title=\"音频设置\" name=\"audio\">\n              <el-form label-width=\"80px\" size=\"small\">\n                <el-form-item label=\"主音量\">\n                  <el-slider v-model=\"audioSettings.masterVolume\" :max=\"100\"></el-slider>\n                </el-form-item>\n                <el-form-item label=\"背景音乐\">\n                  <el-slider v-model=\"audioSettings.bgmVolume\" :max=\"100\"></el-slider>\n                </el-form-item>\n                <el-form-item label=\"音效\">\n                  <el-slider v-model=\"audioSettings.sfxVolume\" :max=\"100\"></el-slider>\n                </el-form-item>\n              </el-form>\n            </el-collapse-item>\n            \n            <el-collapse-item title=\"文字设置\" name=\"text\">\n              <el-form label-width=\"80px\" size=\"small\">\n                <el-form-item label=\"字体\">\n                  <el-select v-model=\"textSettings.fontFamily\" size=\"small\">\n                    <el-option label=\"微软雅黑\" value=\"Microsoft YaHei\"></el-option>\n                    <el-option label=\"宋体\" value=\"SimSun\"></el-option>\n                    <el-option label=\"黑体\" value=\"SimHei\"></el-option>\n                  </el-select>\n                </el-form-item>\n                <el-form-item label=\"字号\">\n                  <el-input-number v-model=\"textSettings.fontSize\" :min=\"12\" :max=\"72\" size=\"small\"></el-input-number>\n                </el-form-item>\n                <el-form-item label=\"颜色\">\n                  <el-color-picker v-model=\"textSettings.color\"></el-color-picker>\n                </el-form-item>\n              </el-form>\n            </el-collapse-item>\n          </el-collapse>\n        </div>\n      </div>\n    </div>\n\n    <!-- 时间轴区域 -->\n    <div class=\"timeline-area\">\n      <div class=\"timeline-header\">\n        <h4>时间轴</h4>\n        <div class=\"timeline-controls\">\n          <el-button-group size=\"small\">\n            <el-button icon=\"el-icon-zoom-in\">放大</el-button>\n            <el-button icon=\"el-icon-zoom-out\">缩小</el-button>\n          </el-button-group>\n        </div>\n      </div>\n      \n      <div class=\"timeline-container\">\n        <!-- 时间刻度 -->\n        <div class=\"time-ruler-row\">\n          <div class=\"time-ruler-label\"></div>\n          <div class=\"time-ruler\" @click=\"onTimelineClick\" @mousedown=\"onTimelineDragStart\">\n            <div class=\"time-marks-container\" :style=\"{ width: timelineWidth + 'px' }\">\n              <!-- 每15秒一个主刻度 -->\n              <div class=\"time-mark\" v-for=\"i in 5\" :key=\"i\" :style=\"{left: (i-1) * 150 + 'px'}\">\n                {{ formatTime((i-1) * 15) }}\n              </div>\n              <!-- 每5秒一个小刻度 -->\n              <div class=\"time-tick\" v-for=\"i in 11\" :key=\"'tick-' + i\" :style=\"{left: (i * 50 + 25) + 'px'}\"></div>\n              <!-- 播放指针 -->\n              <div class=\"playhead\" :style=\"{left: currentTime * pixelsPerSecond + 'px'}\"></div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 轨道区域 -->\n        <div class=\"tracks-container\">\n          <!-- 视频场景轨道 -->\n          <div class=\"track-row video\">\n            <div class=\"track-label\">\n              <i class=\"el-icon-video-camera\"></i>\n              <span>视频场景</span>\n              <el-button size=\"mini\" type=\"primary\" @click=\"showAddSceneDialog\" style=\"margin-left: 8px;\">\n                <i class=\"el-icon-plus\"></i>\n              </el-button>\n            </div>\n            <div class=\"track-content\" @drop=\"onDropScene($event)\" @dragover.prevent\">\n              <div class=\"track-timeline\" :style=\"{ width: timelineWidth + 'px' }\">\n                <div\n                  v-for=\"scene in scenes\"\n                  :key=\"scene.id\"\n                  class=\"scene-clip\"\n                  :style=\"getSceneStyle(scene)\"\n                  @click=\"selectScene(scene)\"\n                  :class=\"{ active: selectedScene && selectedScene.id === scene.id }\"\n                  draggable=\"true\"\n                  @dragstart=\"onDragStartScene($event, scene)\"\n                >\n                  <div class=\"scene-content\">\n                    <span class=\"scene-name\">{{ scene.name }}</span>\n                    <div class=\"scene-duration\">\n                      {{ formatTime(scene.duration) }}\n                      <span v-if=\"scene.maxDuration\" class=\"max-duration\">\n                        / {{ formatTime(scene.maxDuration) }}\n                      </span>\n                    </div>\n                    <div class=\"scene-video-info\" v-if=\"scene.currentVideo\">\n                      {{ scene.currentVideo.name }}\n                    </div>\n                  </div>\n                  <!-- 调整手柄 -->\n                  <div class=\"resize-handle left\" @mousedown=\"startResizeScene($event, scene, 'left')\"></div>\n                  <div class=\"resize-handle right\" @mousedown=\"startResizeScene($event, scene, 'right')\"></div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 文案轨道 -->\n          <div class=\"track-row text\">\n            <div class=\"track-label\">\n              <i class=\"el-icon-edit\"></i>\n              <span>文案</span>\n              <el-button size=\"mini\" type=\"success\" @click=\"segmentText\" style=\"margin-left: 8px;\">\n                分段\n              </el-button>\n            </div>\n            <div class=\"track-content\">\n              <div class=\"track-timeline\" :style=\"{ width: timelineWidth + 'px' }\">\n                <div\n                  v-for=\"(scene, index) in scenes\"\n                  :key=\"'text-' + scene.id\"\n                  class=\"text-clip\"\n                  :style=\"getSceneStyle(scene)\"\n                  @click=\"selectTextSegment(scene, index)\"\n                  :class=\"{ active: selectedTextSegment && selectedTextSegment.id === scene.id }\"\n                >\n                  <div class=\"text-content\">\n                    <span class=\"text-preview\">{{ getTextPreview(scene.textSegment) }}</span>\n                    <div class=\"text-duration\">{{ formatTime(scene.duration) }}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 其他轨道 -->\n          <div\n            v-for=\"track in tracks.filter(t => t.type !== 'video' && t.type !== 'text')\"\n            :key=\"track.id\"\n            class=\"track-row\"\n            :class=\"track.type\"\n          >\n            <div class=\"track-label\">\n              <i :class=\"track.icon\"></i>\n              <span>{{ track.name }}</span>\n            </div>\n            <div class=\"track-content\" @drop=\"onDrop($event, track)\" @dragover.prevent>\n              <div class=\"track-timeline\" :style=\"{ width: timelineWidth + 'px' }\">\n                <div\n                  v-for=\"clip in track.clips\"\n                  :key=\"clip.id\"\n                  class=\"clip\"\n                  :style=\"getClipStyle(clip)\"\n                  @click=\"selectClip(clip)\"\n                  :class=\"{ active: selectedClip && selectedClip.id === clip.id }\"\n                  draggable=\"true\"\n                  @dragstart=\"onDragStart($event, clip)\"\n                >\n                  <div class=\"clip-content\">\n                    <span class=\"clip-name\">{{ clip.name }}</span>\n                    <div class=\"clip-duration\">\n                      {{ formatTime(clip.duration) }}\n                      <span v-if=\"clip.maxDuration || clip.originalDuration\" class=\"max-duration\">\n                        / {{ formatTime(clip.maxDuration || clip.originalDuration) }}\n                      </span>\n                    </div>\n                  </div>\n                  <!-- 调整手柄 -->\n                  <div class=\"resize-handle left\" @mousedown=\"startResize($event, clip, 'left')\"></div>\n                  <div class=\"resize-handle right\" @mousedown=\"startResize($event, clip, 'right')\"></div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 对话框区域 -->\n    <!-- 批量剪辑对话框 -->\n    <el-dialog\n      title=\"开始批量剪辑\"\n      :visible.sync=\"batchDialogVisible\"\n      width=\"400px\"\n    >\n      <div class=\"batch-config\">\n        <h4>{{ templateInfo.name }}</h4>\n        <p>请选择要生成的视频数量：</p>\n        <el-input-number\n          v-model=\"batchCount\"\n          :min=\"1\"\n          :max=\"50\"\n          label=\"生成数量\"\n        ></el-input-number>\n        <p class=\"batch-tip\">最多可生成50条视频</p>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"batchDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"startBatchClip\">开始剪辑</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 添加场景对话框 -->\n    <el-dialog\n      title=\"添加视频场景\"\n      :visible.sync=\"addSceneDialogVisible\"\n      width=\"500px\"\n    >\n      <el-form :model=\"sceneForm\" label-width=\"100px\">\n        <el-form-item label=\"场景名称\">\n          <el-input v-model=\"sceneForm.name\" placeholder=\"请输入场景名称\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"场景时长\">\n          <el-input-number v-model=\"sceneForm.duration\" :min=\"1\" :max=\"60\" label=\"秒\"></el-input-number>\n          <span style=\"margin-left: 8px; color: #909399;\">秒</span>\n        </el-form-item>\n        <el-form-item label=\"视频库\">\n          <el-select v-model=\"sceneForm.videoLibraryId\" placeholder=\"选择视频库\">\n            <el-option\n              v-for=\"lib in videoLibraries\"\n              :key=\"lib.id\"\n              :label=\"lib.name\"\n              :value=\"lib.id\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"addSceneDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"confirmAddScene\">确定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 视频库选择对话框 -->\n    <el-dialog\n      title=\"选择视频库\"\n      :visible.sync=\"videoLibraryDialogVisible\"\n      width=\"800px\"\n    >\n      <div class=\"video-library-content\" v-if=\"selectedVideoLibrary\">\n        <h4>{{ selectedVideoLibrary.name }}</h4>\n        <div class=\"video-grid\">\n          <div\n            v-for=\"video in selectedVideoLibrary.videos\"\n            :key=\"video.id\"\n            class=\"video-item\"\n            @click=\"selectVideoFromLibrary(video)\"\n          >\n            <div class=\"video-thumbnail\">\n              <img :src=\"video.thumbnail\" :alt=\"video.name\" />\n              <div class=\"video-duration-badge\">{{ formatTime(video.duration) }}</div>\n            </div>\n            <div class=\"video-info\">\n              <p class=\"video-name\">{{ video.name }}</p>\n              <p class=\"video-duration-text\">时长: {{ formatTime(video.duration) }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"videoLibraryDialogVisible = false\">关闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'VideoEditor',\n  data() {\n    return {\n      // 模板信息\n      templateInfo: {\n        id: '',\n        name: '视频模板',\n        resolution: '1080x1920',\n        fps: '30'\n      },\n      \n      // 批量剪辑\n      batchDialogVisible: false,\n      batchCount: 1,\n\n      // 场景管理对话框\n      addSceneDialogVisible: false,\n      sceneForm: {\n        name: '',\n        duration: 10,\n        videoLibraryId: ''\n      },\n\n      // 视频库选择对话框\n      videoLibraryDialogVisible: false,\n      selectedVideoLibrary: null,\n\n      // 属性面板\n      activeCollapse: ['video'],\n\n      // 设置项\n      videoSettings: {\n        backgroundColor: '#000000',\n        brightness: 0,\n        contrast: 0,\n        saturation: 0\n      },\n\n      audioSettings: {\n        masterVolume: 80,\n        bgmVolume: 60,\n        sfxVolume: 70\n      },\n\n      textSettings: {\n        fontFamily: 'Microsoft YaHei',\n        fontSize: 24,\n        color: '#FFFFFF'\n      },\n\n      // 选中的元素\n      selectedClip: null,\n      selectedScene: null,\n      selectedTextSegment: null,\n\n      // 时间轴设置\n      timelineDuration: 60, // 时间轴总长度60秒\n      pixelsPerSecond: 10, // 每秒10像素，60秒 = 600像素（稀疏显示）\n      currentTime: 0, // 当前播放时间（秒）\n      \n      // 场景列表\n      scenes: [\n        {\n          id: 'scene1',\n          name: '开场场景',\n          startTime: 0,\n          duration: 8,\n          maxDuration: 12, // 视频素材的最大时长\n          videoLibraryId: 'lib_开场',\n          textSegment: '欢迎来到我们的产品展示，今天为大家介绍一款革命性的产品。',\n          currentVideo: null\n        },\n        {\n          id: 'scene2',\n          name: '产品介绍',\n          startTime: 8,\n          duration: 10,\n          maxDuration: 15, // 视频素材的最大时长\n          videoLibraryId: 'lib_产品介绍',\n          textSegment: '这款产品具有独特的设计理念和先进的技术，能够为用户带来全新的体验。',\n          currentVideo: null\n        },\n        {\n          id: 'scene3',\n          name: '功能展示',\n          startTime: 18,\n          duration: 9,\n          maxDuration: 20, // 视频素材的最大时长\n          videoLibraryId: 'lib_功能展示',\n          textSegment: '让我们来看看它的核心功能和使用方法。',\n          currentVideo: null\n        }\n      ],\n\n      // 从up页面获取的素材库（视频库）\n      sucaiList: [], // 从up页面的素材列表获取\n\n      // 视频库分类（基于文件夹）\n      videoLibraries: [],\n\n      // AI文案库\n      textLibrary: {\n        id: 'text1',\n        name: 'AI剪辑文案库',\n        fullText: '欢迎来到我们的产品展示，今天为大家介绍一款革命性的产品。这款产品具有独特的设计理念和先进的技术，能够为用户带来全新的体验。让我们来看看它的核心功能和使用方法。通过简单的操作，您就能体验到前所未有的便利性。',\n        segments: [] // 将通过阿里云接口分段\n      },\n\n      // 轨道数据（简化版）\n      tracks: [\n        {\n          id: 'video',\n          name: '视频场景',\n          type: 'video',\n          icon: 'el-icon-video-camera'\n        },\n        {\n          id: 'text',\n          name: '文案',\n          type: 'text',\n          icon: 'el-icon-edit'\n        },\n        {\n          id: 'music',\n          name: '背景音乐',\n          type: 'audio',\n          icon: 'el-icon-headset',\n          clips: [\n            {\n              id: 'm1',\n              name: '背景音乐.mp3',\n              startTime: 0,\n              duration: 45, // 当前使用时长\n              maxDuration: 180, // 音频文件的实际时长（3分钟）\n              originalDuration: 180, // 原始文件时长\n              type: 'audio'\n            }\n          ]\n        },\n        {\n          id: 'sound',\n          name: '音效',\n          type: 'audio',\n          icon: 'el-icon-bell',\n          clips: [\n            {\n              id: 's1',\n              name: '点击音效.wav',\n              startTime: 8,\n              duration: 1,\n              maxDuration: 2, // 音效文件时长\n              originalDuration: 2,\n              type: 'audio'\n            },\n            {\n              id: 's2',\n              name: '转场音效.wav',\n              startTime: 18,\n              duration: 2,\n              maxDuration: 3,\n              originalDuration: 3,\n              type: 'audio'\n            }\n          ]\n        }\n      ]\n    }\n  },\n\n  computed: {\n    // 时间轴总宽度\n    timelineWidth() {\n      return this.timelineDuration * this.pixelsPerSecond\n    }\n  },\n\n  created() {\n    // 获取模板ID\n    const templateId = this.$route.query.templateId\n    if (templateId) {\n      this.loadTemplate(templateId)\n    }\n\n    // 加载素材库\n    this.loadMaterialsFromUpPage()\n\n    // 初始化场景视频\n    this.initializeSceneVideos()\n  },\n  \n  methods: {\n    // 返回上一页\n    goBack() {\n      this.$router.go(-1)\n    },\n    \n    // 加载模板\n    loadTemplate(templateId) {\n      // TODO: 从API加载模板数据\n      this.templateInfo.id = templateId\n      console.log('加载模板:', templateId)\n    },\n    \n    // 保存模板\n    saveTemplate() {\n      this.$message.success('模板保存成功')\n      // TODO: 调用保存API\n    },\n    \n    // 显示批量剪辑对话框\n    showBatchDialog() {\n      this.batchDialogVisible = true\n    },\n    \n    // 开始批量剪辑\n    startBatchClip() {\n      this.$message.success(`开始生成 ${this.batchCount} 条视频`)\n      this.batchDialogVisible = false\n      \n      // 跳转到进度页面\n      this.$router.push({\n        path: '/storer/progress',\n        query: { templateId: this.templateInfo.id }\n      })\n    },\n    \n    // 选择片段\n    selectClip(clip) {\n      this.selectedClip = clip\n    },\n    \n    // 格式化时间\n    formatTime(seconds) {\n      const mins = Math.floor(seconds / 60)\n      const secs = seconds % 60\n      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`\n    },\n    \n    // 获取片段样式\n    getClipStyle(clip) {\n      return {\n        left: clip.startTime * this.pixelsPerSecond + 'px',\n        width: clip.duration * this.pixelsPerSecond + 'px'\n      }\n    },\n    \n    // 拖拽开始\n    onDragStart(event, clip) {\n      event.dataTransfer.setData('text/plain', JSON.stringify(clip))\n    },\n    \n    // 拖拽放置\n    onDrop(event, track) {\n      const clipData = JSON.parse(event.dataTransfer.getData('text/plain'))\n      console.log('拖拽到轨道:', track.name, clipData)\n      // TODO: 实现拖拽逻辑\n    },\n    \n    // 开始调整大小\n    startResize(event, clip, direction) {\n      console.log('调整大小:', clip.name, direction)\n\n      // 检查是否可以调整\n      if (!clip.maxDuration && !clip.originalDuration) {\n        this.$message.warning('该素材没有时长限制信息')\n        return\n      }\n\n      const maxAllowedDuration = clip.maxDuration || clip.originalDuration\n\n      if (direction === 'right') {\n        // 向右拖拽（增加时长）\n        if (clip.duration >= maxAllowedDuration) {\n          this.$message.warning(`素材最大时长为 ${this.formatTime(maxAllowedDuration)}，无法继续延长`)\n          return\n        }\n      }\n\n      // TODO: 实现具体的拖拽调整逻辑\n      this.startResizeWithLimit(event, clip, direction, maxAllowedDuration)\n    },\n\n    // 带时长限制的调整大小\n    startResizeWithLimit(event, clip, direction, maxDuration) {\n      const startX = event.clientX\n      const startDuration = clip.duration\n      const pixelsPerSecond = this.pixelsPerSecond\n\n      const onMouseMove = (e) => {\n        const deltaX = e.clientX - startX\n        const deltaSeconds = deltaX / pixelsPerSecond\n\n        let newDuration = startDuration\n\n        if (direction === 'right') {\n          newDuration = Math.max(1, Math.min(maxDuration, startDuration + deltaSeconds))\n        } else if (direction === 'left') {\n          newDuration = Math.max(1, Math.min(maxDuration, startDuration - deltaSeconds))\n        }\n\n        clip.duration = Math.round(newDuration)\n\n        // 显示时长提示\n        this.showDurationTip(clip, maxDuration)\n      }\n\n      const onMouseUp = () => {\n        document.removeEventListener('mousemove', onMouseMove)\n        document.removeEventListener('mouseup', onMouseUp)\n        this.hideDurationTip()\n      }\n\n      document.addEventListener('mousemove', onMouseMove)\n      document.addEventListener('mouseup', onMouseUp)\n    },\n\n    // 显示时长提示\n    showDurationTip(clip, maxDuration) {\n      const percentage = (clip.duration / maxDuration * 100).toFixed(1)\n      console.log(`${clip.name}: ${this.formatTime(clip.duration)} / ${this.formatTime(maxDuration)} (${percentage}%)`)\n    },\n\n    // 隐藏时长提示\n    hideDurationTip() {\n      // 清除提示\n    },\n\n    // 获取预览分辨率显示文本\n    getPreviewResolution() {\n      // 根据模板分辨率返回对应的抖音尺寸\n      const resolutionMap = {\n        '1920x1080': '1080x1920',\n        '1280x720': '720x1280',\n        '1080x1920': '1080x1920',\n        '720x1280': '720x1280'\n      }\n      return resolutionMap[this.templateInfo.resolution] || '1080x1920'\n    },\n\n    // 从up页面加载素材库\n    async loadMaterialsFromUpPage() {\n      try {\n        // 模拟从up页面获取素材数据\n        // 实际应用中，这里应该调用API或从localStorage获取\n        const mockSucaiList = [\n          {\n            id: 'sucai1',\n            name: '开场视频1.mp4',\n            type: 'video',\n            size: 1024000,\n            uploadTime: '2024-01-01 12:00:00',\n            duration: '00:12',\n            url: 'https://www.w3schools.com/html/mov_bbb.mp4',\n            folder: 'asucai/admin/开场',\n            thumbnail: 'https://via.placeholder.com/160x90/4A90E2/FFFFFF?text=Opening1'\n          },\n          {\n            id: 'sucai2',\n            name: '开场视频2.mp4',\n            type: 'video',\n            size: 1024000,\n            uploadTime: '2024-01-01 12:00:00',\n            duration: '00:10',\n            url: 'https://www.w3schools.com/html/mov_bbb.mp4',\n            folder: 'asucai/admin/开场',\n            thumbnail: 'https://via.placeholder.com/160x90/4A90E2/FFFFFF?text=Opening2'\n          },\n          {\n            id: 'sucai3',\n            name: '产品介绍1.mp4',\n            type: 'video',\n            size: 1024000,\n            uploadTime: '2024-01-01 12:00:00',\n            duration: '00:18',\n            url: 'https://www.w3schools.com/html/mov_bbb.mp4',\n            folder: 'asucai/admin/产品介绍',\n            thumbnail: 'https://via.placeholder.com/160x90/50C878/FFFFFF?text=Product1'\n          },\n          {\n            id: 'sucai4',\n            name: '产品介绍2.mp4',\n            type: 'video',\n            size: 1024000,\n            uploadTime: '2024-01-01 12:00:00',\n            duration: '00:22',\n            url: 'https://www.w3schools.com/html/mov_bbb.mp4',\n            folder: 'asucai/admin/产品介绍',\n            thumbnail: 'https://via.placeholder.com/160x90/50C878/FFFFFF?text=Product2'\n          },\n          {\n            id: 'sucai5',\n            name: '功能展示1.mp4',\n            type: 'video',\n            size: 1024000,\n            uploadTime: '2024-01-01 12:00:00',\n            duration: '00:25',\n            url: 'https://www.w3schools.com/html/mov_bbb.mp4',\n            folder: 'asucai/admin/功能展示',\n            thumbnail: 'https://via.placeholder.com/160x90/FF6B6B/FFFFFF?text=Feature1'\n          }\n        ]\n\n        this.sucaiList = mockSucaiList\n\n        // 根据文件夹分组创建视频库\n        this.createVideoLibrariesFromSucai()\n\n        console.log('素材库加载完成:', this.videoLibraries)\n\n      } catch (error) {\n        console.error('加载素材库失败:', error)\n        this.$message.error('加载素材库失败')\n      }\n    },\n\n    // 根据素材列表创建视频库\n    createVideoLibrariesFromSucai() {\n      const folderMap = new Map()\n\n      // 按文件夹分组\n      this.sucaiList.forEach(item => {\n        if (item.type === 'video') {\n          // 提取文件夹名称\n          const folderPath = item.folder || 'asucai/admin/总'\n          const folderName = folderPath.split('/').pop() || '总'\n\n          if (!folderMap.has(folderName)) {\n            folderMap.set(folderName, {\n              id: 'lib_' + folderName,\n              name: folderName + '视频库',\n              videos: []\n            })\n          }\n\n          // 转换为视频库格式\n          const video = {\n            id: item.id,\n            name: item.name,\n            url: item.url,\n            duration: this.parseDuration(item.duration),\n            thumbnail: item.thumbnail || 'https://via.placeholder.com/160x90/999/FFFFFF?text=Video'\n          }\n\n          folderMap.get(folderName).videos.push(video)\n        }\n      })\n\n      // 转换为数组\n      this.videoLibraries = Array.from(folderMap.values())\n    },\n\n    // 解析时长字符串为秒数\n    parseDuration(durationStr) {\n      if (!durationStr) return 10\n\n      const parts = durationStr.split(':')\n      if (parts.length === 2) {\n        const minutes = parseInt(parts[0]) || 0\n        const seconds = parseInt(parts[1]) || 0\n        return minutes * 60 + seconds\n      }\n\n      return 10 // 默认10秒\n    },\n\n    // 初始化场景视频（为每个场景随机选择一个视频）\n    initializeSceneVideos() {\n      this.scenes.forEach(scene => {\n        const library = this.videoLibraries.find(lib => lib.id === scene.videoLibraryId)\n        if (library && library.videos.length > 0) {\n          // 随机选择一个视频\n          const randomIndex = Math.floor(Math.random() * library.videos.length)\n          const selectedVideo = library.videos[randomIndex]\n          scene.currentVideo = selectedVideo\n\n          // 设置场景的最大时长为视频时长\n          scene.maxDuration = selectedVideo.duration\n\n          // 如果当前场景时长超过视频时长，调整为视频时长\n          if (scene.duration > selectedVideo.duration) {\n            scene.duration = selectedVideo.duration\n          }\n        }\n      })\n\n      // 重新计算时间轴\n      this.recalculateSceneTimeline()\n    },\n\n    // 显示添加场景对话框\n    showAddSceneDialog() {\n      this.sceneForm = {\n        name: '',\n        duration: 10,\n        videoLibraryId: ''\n      }\n      this.addSceneDialogVisible = true\n    },\n\n    // 确认添加场景\n    confirmAddScene() {\n      if (!this.sceneForm.name || !this.sceneForm.videoLibraryId) {\n        this.$message.error('请填写完整的场景信息')\n        return\n      }\n\n      // 计算新场景的开始时间\n      const lastScene = this.scenes[this.scenes.length - 1]\n      const startTime = lastScene ? lastScene.startTime + lastScene.duration : 0\n\n      const newScene = {\n        id: 'scene' + Date.now(),\n        name: this.sceneForm.name,\n        startTime: startTime,\n        duration: this.sceneForm.duration,\n        videoLibraryId: this.sceneForm.videoLibraryId,\n        textSegment: '新场景的文案内容...',\n        currentVideo: null\n      }\n\n      // 为新场景随机选择视频\n      const library = this.videoLibraries.find(lib => lib.id === this.sceneForm.videoLibraryId)\n      if (library && library.videos.length > 0) {\n        const randomIndex = Math.floor(Math.random() * library.videos.length)\n        newScene.currentVideo = library.videos[randomIndex]\n      }\n\n      this.scenes.push(newScene)\n      this.addSceneDialogVisible = false\n      this.$message.success('场景添加成功')\n    },\n\n    // 选择场景\n    selectScene(scene) {\n      this.selectedScene = scene\n      this.selectedClip = null\n      this.selectedTextSegment = null\n\n      // 显示视频库选择对话框\n      const library = this.videoLibraries.find(lib => lib.id === scene.videoLibraryId)\n      if (library) {\n        this.selectedVideoLibrary = library\n        this.videoLibraryDialogVisible = true\n      }\n    },\n\n    // 从视频库选择视频\n    selectVideoFromLibrary(video) {\n      if (this.selectedScene) {\n        this.selectedScene.currentVideo = video\n        // 更新场景的最大时长限制\n        this.selectedScene.maxDuration = video.duration\n\n        // 如果当前时长超过新视频的时长，则调整为新视频的时长\n        if (this.selectedScene.duration > video.duration) {\n          this.selectedScene.duration = video.duration\n          this.$message.warning(`场景时长已调整为视频时长: ${this.formatTime(video.duration)}`)\n        }\n\n        this.$message.success(`已为场景\"${this.selectedScene.name}\"选择视频: ${video.name} (${this.formatTime(video.duration)})`)\n      }\n      this.videoLibraryDialogVisible = false\n    },\n\n    // 选择文案片段\n    selectTextSegment(scene, index) {\n      this.selectedTextSegment = scene\n      this.selectedClip = null\n      this.selectedScene = null\n    },\n\n    // 获取场景样式\n    getSceneStyle(scene) {\n      return {\n        left: scene.startTime * this.pixelsPerSecond + 'px',\n        width: scene.duration * this.pixelsPerSecond + 'px'\n      }\n    },\n\n    // 获取文案预览\n    getTextPreview(text) {\n      return text.length > 20 ? text.substring(0, 20) + '...' : text\n    },\n\n    // 文案分段（调用阿里云接口）\n    async segmentText() {\n      try {\n        this.$message.info('正在调用阿里云接口进行文案分段...')\n\n        // TODO: 调用阿里云文本分段接口\n        // const segments = await this.callAliCloudTextSegmentation(this.textLibrary.fullText)\n\n        // 模拟分段结果\n        const mockSegments = [\n          '欢迎来到我们的产品展示，今天为大家介绍一款革命性的产品。',\n          '这款产品具有独特的设计理念和先进的技术，能够为用户带来全新的体验。',\n          '让我们来看看它的核心功能和使用方法。',\n          '通过简单的操作，您就能体验到前所未有的便利性。'\n        ]\n\n        // 更新场景的文案片段\n        mockSegments.forEach((segment, index) => {\n          if (this.scenes[index]) {\n            this.scenes[index].textSegment = segment\n          }\n        })\n\n        this.$message.success('文案分段完成')\n\n      } catch (error) {\n        console.error('文案分段失败:', error)\n        this.$message.error('文案分段失败，请重试')\n      }\n    },\n\n    // 场景拖拽开始\n    onDragStartScene(event, scene) {\n      event.dataTransfer.setData('text/plain', JSON.stringify(scene))\n    },\n\n    // 场景拖拽放置\n    onDropScene(event) {\n      const sceneData = JSON.parse(event.dataTransfer.getData('text/plain'))\n      console.log('场景拖拽:', sceneData)\n      // TODO: 实现场景拖拽逻辑\n    },\n\n    // 开始调整场景大小\n    startResizeScene(event, scene, direction) {\n      console.log('调整场景大小:', scene.name, direction)\n\n      // 检查场景是否有视频和时长限制\n      if (!scene.currentVideo || !scene.maxDuration) {\n        this.$message.warning('请先为场景选择视频')\n        return\n      }\n\n      const maxAllowedDuration = scene.maxDuration\n\n      if (direction === 'right') {\n        // 向右拖拽（增加时长）\n        if (scene.duration >= maxAllowedDuration) {\n          this.$message.warning(`场景最大时长为 ${this.formatTime(maxAllowedDuration)}（视频时长限制）`)\n          return\n        }\n      }\n\n      // 实现场景大小调整\n      this.startResizeSceneWithLimit(event, scene, direction, maxAllowedDuration)\n    },\n\n    // 带时长限制的场景调整\n    startResizeSceneWithLimit(event, scene, direction, maxDuration) {\n      const startX = event.clientX\n      const startDuration = scene.duration\n      const pixelsPerSecond = this.pixelsPerSecond\n\n      const onMouseMove = (e) => {\n        const deltaX = e.clientX - startX\n        const deltaSeconds = deltaX / pixelsPerSecond\n\n        let newDuration = startDuration\n\n        if (direction === 'right') {\n          newDuration = Math.max(1, Math.min(maxDuration, startDuration + deltaSeconds))\n        } else if (direction === 'left') {\n          newDuration = Math.max(1, Math.min(maxDuration, startDuration - deltaSeconds))\n        }\n\n        scene.duration = Math.round(newDuration)\n\n        // 显示场景时长提示\n        this.showSceneDurationTip(scene, maxDuration)\n      }\n\n      const onMouseUp = () => {\n        document.removeEventListener('mousemove', onMouseMove)\n        document.removeEventListener('mouseup', onMouseUp)\n        this.hideSceneDurationTip()\n\n        // 调整完成后重新计算后续场景的开始时间\n        this.recalculateSceneTimeline()\n      }\n\n      document.addEventListener('mousemove', onMouseMove)\n      document.addEventListener('mouseup', onMouseUp)\n    },\n\n    // 显示场景时长提示\n    showSceneDurationTip(scene, maxDuration) {\n      const percentage = (scene.duration / maxDuration * 100).toFixed(1)\n      console.log(`${scene.name}: ${this.formatTime(scene.duration)} / ${this.formatTime(maxDuration)} (${percentage}%)`)\n    },\n\n    // 隐藏场景时长提示\n    hideSceneDurationTip() {\n      // 清除提示\n    },\n\n    // 重新计算场景时间轴\n    recalculateSceneTimeline() {\n      let currentTime = 0\n      this.scenes.forEach(scene => {\n        scene.startTime = currentTime\n        currentTime += scene.duration\n      })\n      console.log('场景时间轴已重新计算')\n    },\n\n    // 点击时间轴\n    onTimelineClick(event) {\n      const rect = event.currentTarget.getBoundingClientRect()\n      const clickX = event.clientX - rect.left\n      const clickedTime = Math.floor(clickX / this.pixelsPerSecond)\n\n      // 限制在0-60秒范围内\n      this.currentTime = Math.max(0, Math.min(60, clickedTime))\n\n      console.log(`点击时间轴: ${this.currentTime}秒`)\n\n      // 根据当前时间找到对应的场景并预览\n      this.previewAtTime(this.currentTime)\n    },\n\n    // 根据时间预览对应场景\n    previewAtTime(time) {\n      // 找到当前时间对应的场景\n      const currentScene = this.scenes.find(scene => {\n        return time >= scene.startTime && time < (scene.startTime + scene.duration)\n      })\n\n      if (currentScene) {\n        this.selectedScene = currentScene\n        console.log(`预览场景: ${currentScene.name} (${time}秒)`)\n\n        // 如果场景有视频，可以计算视频内的播放位置\n        if (currentScene.currentVideo) {\n          const sceneProgress = time - currentScene.startTime\n          const videoProgress = (sceneProgress / currentScene.duration * 100).toFixed(1)\n          console.log(`视频播放进度: ${videoProgress}%`)\n        }\n      } else {\n        // 如果没有找到场景，清除选择\n        this.selectedScene = null\n        console.log('当前时间没有对应的场景')\n      }\n    },\n\n    // 开始拖拽时间轴\n    onTimelineDragStart(event) {\n      event.preventDefault()\n\n      const startX = event.clientX\n      const rect = event.currentTarget.getBoundingClientRect()\n      const startClickX = event.clientX - rect.left\n      const startTime = Math.floor(startClickX / this.pixelsPerSecond)\n\n      // 设置初始时间\n      this.currentTime = Math.max(0, Math.min(60, startTime))\n      this.previewAtTime(this.currentTime)\n\n      const onMouseMove = (e) => {\n        const currentX = e.clientX\n        const deltaX = currentX - startX\n        const newClickX = startClickX + deltaX\n        const newTime = Math.floor(newClickX / this.pixelsPerSecond)\n\n        // 限制在0-60秒范围内\n        this.currentTime = Math.max(0, Math.min(60, newTime))\n        this.previewAtTime(this.currentTime)\n      }\n\n      const onMouseUp = () => {\n        document.removeEventListener('mousemove', onMouseMove)\n        document.removeEventListener('mouseup', onMouseUp)\n        console.log(`拖拽结束: ${this.currentTime}秒`)\n      }\n\n      document.addEventListener('mousemove', onMouseMove)\n      document.addEventListener('mouseup', onMouseUp)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.video-editor {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: #f5f7fa;\n}\n\n/* 头部工具栏 */\n.editor-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 20px;\n  background: white;\n  border-bottom: 1px solid #e4e7ed;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.template-title {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.header-right {\n  display: flex;\n  gap: 12px;\n}\n\n/* 主编辑区域 */\n.editor-main {\n  display: flex;\n  flex: 1;\n  min-height: 0;\n}\n\n/* 预览面板 */\n.preview-panel {\n  flex: 1;\n  padding: 20px;\n  background: white;\n  border-right: 1px solid #e4e7ed;\n}\n\n.preview-container {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.video-preview {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 16px;\n  padding: 20px;\n}\n\n.preview-frame {\n  width: 300px;\n  height: 533px; /* 300 * 16/9 = 533.33 */\n  background: #000;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);\n  border: 1px solid #ddd;\n  overflow: hidden;\n}\n\n/* 场景预览样式 */\n.scene-preview {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n\n.scene-preview img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.scene-overlay {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));\n  color: white;\n  padding: 20px 16px 16px;\n}\n\n.scene-title {\n  font-size: 16px;\n  font-weight: 600;\n  margin-bottom: 4px;\n}\n\n.scene-video-name {\n  font-size: 12px;\n  color: #ccc;\n  margin-bottom: 4px;\n}\n\n.scene-time-info {\n  font-size: 11px;\n  color: #ffd700;\n  margin-bottom: 8px;\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n}\n\n.scene-text-preview {\n  font-size: 11px;\n  line-height: 1.4;\n  color: #ddd;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.preview-placeholder {\n  text-align: center;\n  color: #909399;\n  padding: 40px 20px;\n}\n\n.preview-placeholder i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  display: block;\n  color: #666;\n}\n\n.preview-placeholder p {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n  font-weight: 500;\n  color: #909399;\n}\n\n.preview-info {\n  font-size: 12px !important;\n  margin: 12px 0 !important;\n  color: #999 !important;\n}\n\n.preview-tips {\n  margin-top: 20px;\n  padding-top: 16px;\n  border-top: 1px solid #333;\n}\n\n.preview-tips p {\n  font-size: 11px !important;\n  color: #666 !important;\n  margin: 4px 0 !important;\n}\n\n.current-time-display {\n  margin: 16px 0;\n  padding: 8px 12px;\n  background: rgba(255, 215, 0, 0.1);\n  border: 1px solid #ffd700;\n  border-radius: 4px;\n}\n\n.current-time-display p {\n  margin: 0 !important;\n  font-size: 14px !important;\n  color: #ffd700 !important;\n  font-weight: 600 !important;\n}\n\n.preview-controls {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 0;\n}\n\n.time-display {\n  font-family: monospace;\n  font-size: 14px;\n  color: #606266;\n}\n\n/* 属性设置面板 */\n.properties-panel {\n  width: 300px;\n  background: white;\n  border-left: 1px solid #e4e7ed;\n  display: flex;\n  flex-direction: column;\n}\n\n.panel-header {\n  padding: 16px 20px;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.panel-header h4 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.panel-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 16px;\n}\n\n/* 时间轴区域 */\n.timeline-area {\n  height: 300px;\n  background: white;\n  border-top: 1px solid #e4e7ed;\n  display: flex;\n  flex-direction: column;\n  min-width: 0; /* 允许收缩 */\n}\n\n.timeline-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 20px;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.timeline-header h4 {\n  margin: 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.timeline-container {\n  flex: 1;\n  position: relative;\n  overflow: hidden;\n}\n\n/* 时间刻度行 */\n.time-ruler-row {\n  display: flex;\n  height: 30px;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.time-ruler-label {\n  width: 150px;\n  background: #f8f9fa;\n  border-right: 1px solid #e4e7ed;\n  flex-shrink: 0;\n}\n\n/* 时间刻度 */\n.time-ruler {\n  flex: 1;\n  height: 100%;\n  background: #fafafa;\n  position: relative;\n  cursor: pointer;\n  user-select: none;\n  overflow: hidden;\n}\n\n.time-marks-container {\n  position: relative;\n  height: 100%;\n  width: 100%;\n}\n\n.time-mark {\n  position: absolute;\n  top: 0;\n  height: 100%;\n  font-size: 11px;\n  color: #606266;\n  display: flex;\n  align-items: center;\n  padding-left: 6px;\n  border-left: 2px solid #c0c4cc;\n  pointer-events: none;\n  font-weight: 500;\n}\n\n.time-tick {\n  position: absolute;\n  top: 22px;\n  width: 1px;\n  height: 8px;\n  background: #f0f0f0;\n  pointer-events: none;\n}\n\n.playhead {\n  position: absolute;\n  top: 0;\n  width: 2px;\n  height: 100%;\n  background: #ff4757;\n  z-index: 10;\n  pointer-events: none;\n}\n\n.playhead::before {\n  content: '';\n  position: absolute;\n  top: -4px;\n  left: -4px;\n  width: 10px;\n  height: 10px;\n  background: #ff4757;\n  border-radius: 50%;\n}\n\n/* 轨道容器 */\n.tracks-container {\n  flex: 1;\n}\n\n.track-row {\n  height: 40px;\n  display: flex;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.track-label {\n  width: 150px;\n  display: flex;\n  align-items: center;\n  padding: 0 12px;\n  background: #f8f9fa;\n  border-right: 1px solid #e4e7ed;\n  font-size: 14px;\n  color: #606266;\n  flex-shrink: 0;\n}\n\n.track-label i {\n  margin-right: 8px;\n  font-size: 16px;\n}\n\n.track-content {\n  flex: 1;\n  position: relative;\n  background: white;\n  overflow: hidden;\n}\n\n.track-timeline {\n  position: relative;\n  height: 100%;\n  width: 100%;\n}\n\n/* 片段样式 */\n.clip {\n  position: absolute;\n  top: 4px;\n  height: 32px;\n  background: #409EFF;\n  border-radius: 4px;\n  cursor: pointer;\n  user-select: none;\n  display: flex;\n  align-items: center;\n  padding: 0 8px;\n  color: white;\n  font-size: 12px;\n  transition: all 0.2s ease;\n}\n\n.clip:hover {\n  background: #337ecc;\n  transform: translateY(-1px);\n}\n\n.clip.active {\n  background: #E6A23C;\n  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.3);\n}\n\n/* 场景片段样式 */\n.scene-clip {\n  position: absolute;\n  top: 2px;\n  height: 36px;\n  background: #67C23A;\n  border-radius: 6px;\n  cursor: pointer;\n  user-select: none;\n  display: flex;\n  align-items: center;\n  padding: 0;\n  color: white;\n  font-size: 11px;\n  transition: all 0.2s ease;\n  overflow: hidden;\n}\n\n.scene-clip:hover {\n  background: #5daf34;\n  transform: translateY(-1px);\n}\n\n.scene-clip.active {\n  background: #E6A23C;\n  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.3);\n}\n\n.scene-content {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  padding: 4px 8px;\n  overflow: hidden;\n}\n\n.scene-name {\n  display: block;\n  font-weight: 600;\n  font-size: 12px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  line-height: 1.2;\n  margin-bottom: 2px;\n}\n\n.scene-duration {\n  font-size: 10px;\n  opacity: 0.8;\n  line-height: 1;\n  margin-bottom: 1px;\n}\n\n.scene-duration .max-duration {\n  color: rgba(255, 255, 255, 0.6);\n  font-size: 9px;\n}\n\n.scene-video-info {\n  font-size: 9px;\n  opacity: 0.7;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n/* 文案片段样式 */\n.text-clip {\n  position: absolute;\n  top: 4px;\n  height: 32px;\n  background: #F56C6C;\n  border-radius: 4px;\n  cursor: pointer;\n  user-select: none;\n  display: flex;\n  align-items: center;\n  padding: 0 8px;\n  color: white;\n  font-size: 11px;\n  transition: all 0.2s ease;\n}\n\n.text-clip:hover {\n  background: #f45656;\n  transform: translateY(-1px);\n}\n\n.text-clip.active {\n  background: #E6A23C;\n  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.3);\n}\n\n.text-content {\n  flex: 1;\n  overflow: hidden;\n}\n\n.text-preview {\n  display: block;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  font-weight: 500;\n  line-height: 1.2;\n}\n\n.text-duration {\n  font-size: 10px;\n  opacity: 0.8;\n}\n\n.clip-content {\n  flex: 1;\n  overflow: hidden;\n}\n\n.clip-name {\n  display: block;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  font-weight: 500;\n}\n\n.clip-duration {\n  font-size: 10px;\n  opacity: 0.8;\n}\n\n.max-duration {\n  color: rgba(255, 255, 255, 0.6);\n  font-size: 9px;\n}\n\n/* 调整手柄 */\n.resize-handle {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  width: 4px;\n  background: rgba(255, 255, 255, 0.3);\n  cursor: ew-resize;\n  opacity: 0;\n  transition: opacity 0.2s ease;\n}\n\n.resize-handle.left {\n  left: 0;\n}\n\n.resize-handle.right {\n  right: 0;\n}\n\n.clip:hover .resize-handle {\n  opacity: 1;\n}\n\n/* 轨道类型样式 */\n.track-row.video .clip {\n  background: #409EFF;\n}\n\n.track-row.audio .clip {\n  background: #67C23A;\n}\n\n.track-row.text .clip {\n  background: #E6A23C;\n}\n\n.track-row.effect .clip {\n  background: #F56C6C;\n}\n\n/* 批量配置 */\n.batch-config {\n  text-align: center;\n  padding: 20px 0;\n}\n\n.batch-config h4 {\n  margin: 0 0 20px 0;\n  color: #303133;\n  font-size: 18px;\n}\n\n.batch-config p {\n  margin: 0 0 16px 0;\n  color: #606266;\n}\n\n.batch-tip {\n  margin-top: 12px !important;\n  color: #909399;\n  font-size: 12px;\n}\n\n/* 视频库对话框样式 */\n.video-library-content h4 {\n  margin: 0 0 16px 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.video-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));\n  gap: 16px;\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.video-item {\n  cursor: pointer;\n  border-radius: 8px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  background: white;\n}\n\n.video-item:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  border-color: #409EFF;\n}\n\n.video-thumbnail {\n  position: relative;\n  width: 100%;\n  height: 90px;\n}\n\n.video-thumbnail img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  display: block;\n}\n\n.video-duration-badge {\n  position: absolute;\n  bottom: 4px;\n  right: 4px;\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-size: 10px;\n  font-weight: 500;\n}\n\n.video-info {\n  padding: 8px;\n  background: #f8f9fa;\n}\n\n.video-name {\n  margin: 0 0 4px 0;\n  font-size: 12px;\n  color: #303133;\n  font-weight: 500;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.video-duration-text {\n  margin: 0;\n  font-size: 11px;\n  color: #909399;\n}\n\n/* 响应式设计 - 9:16 视频预览适配 */\n@media (max-width: 1200px) {\n  .preview-frame {\n    width: 250px;\n    height: 444px; /* 250 * 16/9 = 444.44 */\n  }\n}\n\n@media (max-width: 768px) {\n  .editor-main {\n    flex-direction: column;\n  }\n\n  .preview-panel {\n    border-right: none;\n    border-bottom: 1px solid #e4e7ed;\n  }\n\n  .properties-panel {\n    width: 100%;\n    max-height: 300px;\n  }\n\n  .preview-frame {\n    width: 200px;\n    height: 356px; /* 200 * 16/9 = 355.56 */\n  }\n\n  .timeline-area {\n    height: 250px;\n  }\n}\n\n@media (max-width: 480px) {\n  .preview-frame {\n    width: 150px;\n    height: 267px; /* 150 * 16/9 = 266.67 */\n  }\n\n  .preview-placeholder {\n    padding: 20px 10px;\n  }\n\n  .preview-placeholder i {\n    font-size: 32px;\n  }\n\n  .preview-placeholder p {\n    font-size: 14px;\n  }\n\n  .preview-tips p {\n    font-size: 10px !important;\n  }\n}\n</style>\n"]}]}